import Vue from 'vue';
import VueI18n from 'vue-i18n';

Vue.use(VueI18n);

export default ({ route, app, store, req }) => {
  const routeLanguage = route.params.lang;
  app.i18n = new VueI18n({
    locale: routeLanguage,
    fallbackLocale: 'en',
    messages: {
      ja: require('@/assets/locales/jp.json'),
      zh: require('@/assets/locales/zh.json'),
      en: require('@/assets/locales/en.json'),
    },
  });
  if (process.server) {
    req.headers['accept-language'] = routeLanguage;
    const useLanguage = req.headers['accept-language'];
    store.commit('SET_LANGUAGE', useLanguage);
  }
  if (process.client) {
    const useLanguage = routeLanguage;
    store.commit('SET_LANGUAGE', useLanguage);
  }
};
