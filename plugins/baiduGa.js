//百度统计
import Qs from 'qs';
export default ({ app: { router }, query, req }) => {
  if (process.client) {
    // debugger;
    window.sessionStorage.setItem('query', Qs.stringify(query));
  }
  /* 每次路由变更时进行pv统计 */
  router.afterEach((to, from) => {
    /* 告诉增加一个PV */
    try {
      const isProduction = window.location.host.indexOf('cn') > -1;
      if (isProduction) {
        window._hmt = window._hmt || [];
        window._hmt.push(['_trackPageview', to.fullPath]);
      }
    } catch (e) {}
  });
};
