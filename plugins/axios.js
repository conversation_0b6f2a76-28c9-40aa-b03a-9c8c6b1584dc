import Vue from 'vue';
import axios from 'axios';
import { Message } from 'element-ui';
import Qs from 'qs';
import router from 'vue-router';
// axios 默认配置

const instance = axios.create({
  baseURL: process.env.baseUrl,
});

instance.interceptors.request.use(
  config => {
    if (
      config.method === 'post' &&
      config.headers['Content-Type'] === 'application/x-www-form-urlencoded'
    ) {
      config.data = Qs.stringify(config.data);
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);
instance.interceptors.response.use(
  res => {
    return res.data;
  },
  err => {
    // Message.error({
    //   showClose: true,
    //   message: '信息获取失败',
    // });
    return Promise.reject(err);
  }
);

export default ({ app }, inject) => {
  inject('axios', instance);
};
