import Vue from 'vue';
import MessageToast from '../components/MessageToast/MessageToast.vue';

const MessageToastNew = function(options) {
  options = options || {};

  if (typeof options === 'string') {
    options = {
      message: options,
    };
  }

  const ToastController = Vue.extend(MessageToast);

  let instance = new ToastController({
    data: options,
  });

  instance.vm = instance.$mount(document.createElement('div'));
  instance.dispatch = dispatchCallBack;

  if (typeof options === 'object') {
    document.body.appendChild(instance.vm.$el);
  }

  let bridge = null;

  // return instance.vm;
  return new Promise((resolve, reject) => {
    bridge = { resolve, reject };
  });

  function dispatchCallBack(action) {
    if (action === 'resolve') {
      bridge.resolve('confirm');
    } else {
      bridge.reject('cancel');
    }
  }
};
['success', 'warning', 'info', 'error'].forEach(type => {
  MessageToastNew[type] = options => {
    if (typeof options === 'string') {
      options = {
        message: options,
      };
    }
    options.type = type;
    return MessageToastNew(options);
  };
});

const messageToast = {
  install(Vue, options = {}) {
    // 在Vue原型实现toast的DOM挂载、以及功能实现
    // 用户可以在Vue实例（Vue单文件就是一个Vue实例）通过this.$toast来访问以下内容
    Vue.prototype.$MessageToast = MessageToastNew;
  },
};
// 最后将以上内容导出，即可在其他地方进行使用
Vue.use(messageToast);
