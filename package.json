{"name": "bestsign", "version": "1.0.0", "description": "上上签电子签约云平台", "author": "<PERSON><PERSON><PERSON><PERSON>", "private": true, "scripts": {"dev": "cross-env NODE_ENV=development nodemon server/index.js --watch server", "build": "BASE_URL=https://www.bestsign.info nuxt build --no-lock", "build-p": "BASE_URL=https://www.bestsign.cn nuxt build --no-lock", "build-k": "BASE_URL=https://www.bestsign.com nuxt build --no-lock", "build-w": "BASE_URL=https://www2-hwy.bestsign.info nuxt build --no-lock", "build-n": "BASE_URL=https://www.bestsign.info nuxt build --no-lock", "build-o": "BASE_URL=https://www.bestsign.cn nuxt build --no-lock", "build-m": "BASE_URL=https://www2.bestsign.info nuxt build --no-lock", "build-j": "BASE_URL=https://www.bestsign.tech nuxt build --no-lock", "start": "cross-env NODE_ENV=production node server/index.js", "start1": "cross-env NODE_ENV=production PORT=3001 node server/index.js", "start2": "cross-env NODE_ENV=production PORT=3002 node server/index.js", "start3": "cross-env NODE_ENV=production PORT=3003 node server/index.js", "generate": "nuxt generate", "lint": "eslint --ext .js,.vue --ignore-path .gitignore .", "precommit": "npm run lint"}, "dependencies": {"@nuxtjs/component-cache": "^1.1.5", "@nuxtjs/google-tag-manager": "^2.1.2", "@nuxtjs/proxy": "^1.3.1", "animejs": "^2.2.0", "ant-design-vue": "^1.4.11", "axios": "^0.18.0", "clipboard": "^2.0.6", "countup.js": "^2.0.4", "cross-env": "^5.2.0", "crypto-js": "^3.1.9-1", "element-ui": "^2.14.1", "html2canvas": "^1.0.0-alpha.12", "koa": "^2.7.0", "koa-body": "^4.1.1", "koa-log4": "^2.3.2", "koa-logger": "^3.2.0", "koa-router": "^7.4.0", "lodash": "^4.17.11", "log4js": "^4.4.0", "lru-cache": "^5.1.1", "moment": "^2.22.2", "node-cache": "^5.0.2", "node-sass": "^4.14.1", "nuxt": "^2.6.2", "qrcode": "^1.4.4", "qrcode.vue": "^1.7.0", "qrcodejs2": "^0.0.2", "request": "^2.88.0", "request-promise": "^4.2.4", "request-promise-native": "^1.0.7", "vant": "^1.6.13", "vue-awesome-swiper": "^3.1.3", "vue-cookies": "^1.7.4", "vue-countup-v2": "^4.0.0", "vue-i18n": "^8.23.0", "vue-lazyload": "^1.2.6", "vue-scrollto": "^2.13.0", "vue-touch": "^2.0.0-beta.4", "weixin-js-sdk": "^1.4.0-test", "xml2js": "^0.4.23"}, "devDependencies": {"@nuxtjs/style-resources": "^1.2.1", "acorn": "^8.8.1", "babel-eslint": "^8.2.1", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.11.0", "eslint": "^5.0.1", "eslint-config-prettier": "^3.1.0", "eslint-loader": "^2.0.0", "eslint-plugin-prettier": "2.6.2", "eslint-plugin-vue": "^4.0.0", "less": "^3.9.0", "less-loader": "^4.1.0", "nodemon": "^1.11.0", "nuxt-sass-resources-loader": "^2.0.5", "prettier": "1.14.3", "sass-loader": "^7.1.0", "webpack": "^4.28.2"}, "config": {"nuxt": {"host": "0.0.0.0", "port": "3000"}}}