<template>
  <div class="qyl-history section">
    <div class="container">
      <h2 class="section-headline headline--main">2019第一届签引力大赛<br>精彩案例回顾</h2>
      <div class="type-title" v-if="!isMobile">
        <div class="choose-title" @click="chooseType">全部</div>
        <div class="choose-title" @click="chooseType(1)">房地产行业</div>
        <div class="choose-title" @click="chooseType(2)">零售制造行业</div>
        <div class="choose-title" @click="chooseType(3)">电商行业</div>
        <div class="choose-title" @click="chooseType(4)">人力资源行业</div>
        <div class="choose-title" @click="chooseType(5)">物流行业</div>
        <div class="choose-title" @click="chooseType(6)">互联网行业</div>
        <div class="choose-title" @click="chooseType(7)">金融行业</div>
      </div>
      <div class="user-dsc" v-if="!isMobile">
        <div class="main-content">
          <div class="logo">
            <img :src='partnerInfo.logo'>
          </div>
          <h1>{{partnerInfo.name}}</h1>
          <div class="biaoShi">
            <img src='./images/biaoshi.png'>
          </div>
          <div class="dsc-qyl" v-html="partnerInfo.desc"></div>
        </div>
		    <div class="name">
          <!-- <p class="title">{{ partnerInfo.name }}</p>
          <p class="label">{{ partnerInfo.desc }}</p> -->
          <!-- <el-tabs v-model="activeName">
            <el-tab-pane label="项目背景" name="first" v-html="partnerInfo.background"></el-tab-pane>
            <el-tab-pane label="应用价值" name="second" v-html="partnerInfo.apply"></el-tab-pane>
            <el-tab-pane label="成效与心得" name="third" v-html="partnerInfo.realize"></el-tab-pane>
          </el-tabs> -->
          <div class="btn_switch">
                    <div class="btn_anniu" @mouseenter="change(0)" :class="{ newStyle:0===number}">项目背景</div>
                    <div class="btn_anniu" @mouseenter="change(1)" :class="{ newStyle:1===number}">应用价值</div>
                    <div class="btn_anniu" @mouseenter="change(2)" :class="{ newStyle:2===number}">成效与心得</div>
                  </div>
                  <div class="user-content">
                    <div v-if="0===number" v-html="partnerInfo.background"></div>
                  <div v-if="1===number" v-html="partnerInfo.apply"></div>
                  <div v-if="2===number" v-html="partnerInfo.realize"></div>
                  </div>
		    </div>
      </div>
      <!-- <div class="main-content" v-if="!isMobile">{{ partnerInfo.content }}</div>
		<div class="name" v-if="!isMobile">
			<p class="title">{{ partnerInfo.name }}</p>
			<p class="label">{{ partnerInfo.desc }}</p>
		</div> -->
      <div class="content" v-if="!isMobile">
        <div class="swiper-container" :key="qylData.length" v-if="qylData.length > 0" v-swiper:myQylSwiper="isMobile?qylswiperOptionMobile:qylswiperOption">
          <div class="swiper-wrapper">
				<div class="swiper-slide" v-for="(item, index) in qylData" :key="item.name">
					<div class="item">
						   <div class="top">
								<!-- <div :class="['default-avatar', activeIndex === index? 'active-avatar':'']" :style="{backgroundImage: `url(${item.logo})`}"> -->
								<div :class="['default-avatar', activeIndex === index? 'active-avatar':'']">
									<img :src="item.logo" alt="" width="100%" height="100%">
								</div>
						   </div>
					</div>
				</div>
          </div>
        </div>
        <div class="arrow">
          <div class="ssq-button-prev-qyl">
            <i class="el-icon-arrow-left"></i>
          </div>
          <div class="ssq-button-next-qyl">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
      <div class="content" v-if="isMobile">
        <div class="swiper-container" v-if="qylData.length > 0" v-swiper:myQylSwiper="isMobile?qylswiperOptionMobile:qylswiperOption">
          <div class="swiper-wrapper">
				<div class="swiper-slide" v-for="(item, index) in qylData" :key="item.name">
          <div class="item">
						   <div class="top">
								<!-- <div :class="['default-avatar', activeIndex === index? 'active-avatar':'']" :style="{backgroundImage: `url(${item.logo})`}"> -->
								<div :class="['default-avatar', activeIndex === index? 'active-avatar':'']">
									<img :src="item.logo" alt="" width="100%" height="100%">
                  <div class="user-name">{{item.name}}</div>
                  <!-- <div class="user-say">他们说：</div> -->
                  <div class="userSayContent" v-html="item.desc"></div>
                  <div class="btn_switch">
                    <div class="btn_anniu1" @click="change(0)" :class="{ newStyle:0===number}">项目背景</div>
                    <div class="btn_anniu2" @click="change(1)" :class="{ newStyle:1===number}">应用价值</div>
                    <div class="btn_anniu2" @click="change(2)" :class="{ newStyle:2===number}">成效与心得</div>
                  </div>
                  <div class="user-content">
                    <div v-if="0===number" v-html="item.background"></div>
                  <div v-if="1===number" v-html="item.apply"></div>
                  <div v-if="2===number" v-html="item.realize"></div>
                  </div>
								</div>
						   </div>
					</div>
					
				</div>
          </div>
        </div>
        <div class="arrow">
          <div class="ssq-button-prev-qyl">
            <i class="el-icon-arrow-left"></i>
          </div>
          <div class="ssq-button-next-qyl">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
        <!-- <div class="swiper-pagination swiper-pagination__evaluate" slot="pagination"></div> -->
      </div>
	 	<!-- <div class="main-content" v-if="!isMobile">{{ partnerInfo.content }}</div>
		<div class="name" v-if="!isMobile">
			<p class="title">{{ partnerInfo.name }}</p>
			<p class="label">{{ partnerInfo.desc }}</p>
		</div> -->
      <!-- <div v-if="!demo" class="ssq-button-more transparent" @click="toCase">进一步了解我们的合作伙伴 <i class="iconfont icon-xiangyoujiantou"></i></div> -->
      <!-- <div v-if="demo" class="ssq-button-more" style="color: #fff" @click="toDemo">免费试用 ></div> -->
	<!-- <div v-if="isMobile" class="swiper-pagination swiper-pagination__evaluate" slot="pagination"></div> -->
    </div>
  </div>
</template>

<script>
const getJson = () => import('@/static/_qyl.json').then(m => m.default || m);
export default {
  name: 'qyl-history',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      number: 0,
      activeIndex: 2,
      qylData: [],
      activeName: 'first',
      qylswiperOption: {
        loop: true,
        initialSlide: 2,
        centeredSlides: true,
        slideToClickedSlide: true,
        speed: 700,
        slidesPerView: 5,
        navigation: {
          nextEl: '.ssq-button-next-qyl',
          prevEl: '.ssq-button-prev-qyl',
        },
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        // onSlideChangeEnd: function(swiper) {
        //   swiper.update();
        //   qylswiperOption.startAutoplay();
        //   qylswiperOption.reLoop();
        // },
      },
      qylswiperOptionMobile: {
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        // pagination: {
        //   el: '.swiper-pagination.swiper-pagination__evaluate',
        // },
        navigation: {
          nextEl: '.ssq-button-next-qyl',
          prevEl: '.ssq-button-prev-qyl',
        },
      },
      partnerInfo: {
        content: '',
        name: '',
        desc: '',
        background: '',
        apply: '',
        realize: '',
        type: '',
      },
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {
    // handleClick(tab, event) {
    //   console.log(tab, event);
    // },
    chooseType(num) {
      // debugger;
      this.$nextTick(() => {
        this.qylData = this.qylData.filter((item, index) => item.type === num);
        this.activeIndex = 2;
        const _this = this;
        this.$nextTick(() => {
          this.myQylSwiper.on('slideChange', function() {
            _this.handleChange(this);
            ijinug;
          });
        });
        // this.myQylSwiper.update();
      });
      this.getInfo();
    },
    chooseAll() {
      // debugger;
      this.$nextTick(() => {
        const response = await getJson();
    this.qylData = response;
        this.activeIndex = 2;
        const _this = this;
        this.$nextTick(() => {
          this.myQylSwiper.on('slideChange', function() {
            _this.handleChange(this);
            ijinug;
          });
        });
        // this.myQylSwiper.update();
      });
      this.getInfo();
    },
    change(num) {
      this.number = num;
    },
    toCase() {
      this.$router.push('/evaluate');
    },
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const query = sessionStorage.getItem('query');
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          ...Qs.parse(query),
        },
      });
    },
    handleChange(cur) {
      // debugger;
      const activeIndex = cur.realIndex;
      this.activeIndex = activeIndex;
      this.getInfo();
      // console.log(this.activeIndex);
    },
    getInfo() {
      this.$nextTick(() => {
        this.partnerInfo = this.qylData.filter(
          (item, index) => index == this.activeIndex
        )[0];
      });
      // console.log(this.partnerInfo);
    },
  },
  async mounted() {
    // debugger;
    // console.log('debugger');
    //此处的轮播的loop：true不起作用的原因是，执行myQylSwiper的时候qylData是默认的空数组，所以若没有await this.$nextTick();的时候就报错，因为qylData的长度是0，所以就报错，加了await this.$nextTick()的话就是this.myQylSwiper.on在this.qylData = response;之后执行，否则就同时执行，这个和首页的evaluate的区别就是data和qylData的定义区别
    const response = await getJson();
    this.qylData = response;
    await this.$nextTick();
    const _this = this;
    this.myQylSwiper.on('slideChange', function() {
      _this.handleChange(this);
    });
    this.getInfo();
  },
};
</script>

<style scoped lang="scss">
.qyl-history {
  //   padding-top: 5rem;
  //   padding-bottom: 2rem;
  background-color: #fff;
  text-align: center;
  .content {
    width: 65%;
    position: relative;
    margin: 3rem auto 3rem;
    .item {
      width: 100%;
      background-color: #fff;
      padding: 20px 10px;
      .top {
        display: flex;
        align-items: center;
        img {
          width: 90px;
          //   margin-right: 30px;
          border-radius: 50%;
          border: 1px solid #eee;
        }
        .default-avatar {
          margin: 0 auto;
          img {
            height: 90px;
            border-radius: 50%;
            width: 90px;
          }
        }

        .name {
          text-align: left;
          font-size: 20px;
          line-height: 1.3;
        }
      }
    }
    .swiper-slide-active .default-avatar img {
      -ms-transform: scale(1.4); /* IE 9 */
      -webkit-transform: scale(1.4); /* Safari */
      transform: scale(1.4); /* 标准语法 */
      transition: transfrom 0.4s;
    }
    .swiper-slide-prev {
      .item {
        .default-avatar {
          margin-left: 0;
        }
      }
    }
    .swiper-slide-next {
      .item {
        transition: all 0.5s;
        .default-avatar {
          margin-right: 0;
          transition: all 0.5s;
        }
      }
    }
  }
  .section-headline {
    color: #090909;
    font-size: 2rem;
    line-height: 3rem;
    font-weight: 400;
    padding: 4.375rem 0 2.5rem;
  }
  .type-title {
    display: flex;
    justify-content: space-around;
    border: 1px solid #e8e8e8;
    border-radius: 2px;
    .choose-title {
      color: #000;
      border: 1px solid #e8e8e8;
      padding: 20px;
      width: 12.5%;
      font-size: 1.1rem;
    }
  }
  .user-dsc {
    display: flex;
    // align-content: space-around;
    // align-items: center;
    .main-content {
      // max-width: 40rem;
      text-align: center;
      text-align: justify;
      line-height: 1.75;
      margin: 2rem 5rem 0;
      // margin: 2.5rem 10rem 0 0;
      background: url(./images/iphone.jpg) no-repeat;
      width: 70%;
      height: 44vw;
      color: #86868b;
      background-size: 100% 100%;
      .logo {
        margin: 7rem auto 10px;
        width: 7rem;
        img {
          height: 7rem;
          border-radius: 50%;
          width: 7rem;
        }
      }
      .biaoShi {
        margin: 1rem auto 10px;
        width: 2rem;
        img {
          width: 2rem;
        }
      }
      h1 {
        width: 60%;
        text-align: center;
        margin: 2rem auto 0;
        font-size: 2rem;
        color: #fff;
      }
      .dsc-qyl {
        width: 60%;
        /* text-align: center; */
        margin: 2rem auto 0;
        font-size: 1rem;
        color: #fff;
      }
    }
    .name {
      width: 100%;
      // /deep/ .el-tabs__item {
      //   font-size: 1.5rem;
      // }
      // /deep/ .el-tabs__content {
      //   font-size: 1rem;
      //   text-align: left;
      //   line-height: 1.8rem;
      //   margin-top: 3rem;
      // }
      .btn_switch {
        margin: 1rem 0 0;
        display: flex;
        justify-content: center;
        .btn_anniu {
          padding: 13px;
          font-size: 1.5rem;
          border-bottom: 1px solid #e8e8e8;
          color: #000;
          outline: none;
          width: 50%;
          height: 45px;
          cursor: pointer;
        }
        .newStyle {
          // border: 2px solid #808080;
          // background-color: #f4f4f4;
          font-size: 1.5rem;
          font-weight: bold;
          border-bottom: 1px solid #1b99fa;
        }
      }
      .user-content {
        font-size: 1rem;
        text-align: left;
        line-height: 1.8rem;
        margin-top: 3rem;
      }
    }
  }

  .name {
    margin-top: 2.5rem;
    // margin: 2.5rem 10rem;
    .title {
      font-size: 20px;
      font-weight: 600;
    }
    .label {
      font-size: 18px;
      margin: 1rem 0 2rem 0;
    }
  }
  .ssq-button-more {
    color: #00aa64;
    font-size: 14px;
    .iconfont {
      font-size: 14px;
    }
  }
  .ssq-button-prev-qyl,
  .ssq-button-next-qyl {
    position: absolute;
    top: 50%;
    transform: translate3d(0, -50%, 0);
    background: transparent;
    cursor: pointer;
    > i {
      font-size: 24px;
      color: rgba(35, 24, 21, 0.5);
      outline: none;
      background: #eee;
      border-radius: 50%;
      padding: 4px;
      &:hover {
        color: rgba(35, 24, 21, 1);
      }
      &:focus {
        outline: none;
      }
    }
    &:focus {
      outline: none;
    }
  }
  .ssq-button-prev-qyl {
    left: -60px;
  }
  .ssq-button-next-qyl {
    right: -60px;
  }
  .ssq-button-primary {
    display: inline-block;
  }
}
.swiper-pagination {
  width: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 20px;
  /deep/ .swiper-pagination-bullet {
    margin: 0 10px;
  }
  /deep/ .swiper-pagination-bullet-active {
    background: #62686f;
  }
}
@media screen and (max-width: 1500px) {
  .qyl-history {
    .user-dsc {
      display: flex;
      // align-items: center;
      // align-content: space-around;
      .main-content {
        // max-width: 40rem;
        text-align: center;
        text-align: justify;
        line-height: 1.75;
        margin: 2rem 5rem 0;
        // margin: 2.5rem 10rem 0 0;
        background: url(./images/iphone.jpg) no-repeat;
        width: 70%;
        height: 44vw;
        color: #86868b;
        background-size: 100% 100%;
        .logo {
          margin: 7rem auto 10px;
          width: 7rem;
          img {
            height: 7rem;
            border-radius: 50%;
            width: 7rem;
          }
        }
        .biaoShi {
          margin: 0.4rem auto 10px;
          width: 2rem;
          img {
            width: 2rem;
          }
        }
        h1 {
          width: 60%;
          text-align: center;
          margin: 1.5rem auto;
          font-size: 1.5rem;
          color: #fff;
        }
        .dsc-qyl {
          width: 60%;
          /* text-align: center; */
          margin: 1rem auto 0;
          font-size: 1rem;
          color: #fff;
        }
      }
      .name {
        width: 100%;
        /deep/ .el-tabs__item {
          font-size: 1.5rem;
        }
        /deep/ .el-tabs__content {
          font-size: 1rem;
          text-align: left;
          line-height: 1.8rem;
          margin-top: 3rem;
        }
      }
    }
  }
}
@media screen and (max-width: 1300px) {
  .qyl-history {
    .user-dsc {
      display: flex;
      // align-items: center;
      // align-content: space-around;
      .main-content {
        // max-width: 40rem;
        text-align: center;
        text-align: justify;
        line-height: 1.75;
        margin: 2rem 5rem 0;
        // margin: 2.5rem 10rem 0 0;
        background: url(./images/iphone.jpg) no-repeat;
        width: 70%;
        height: 44vw;
        color: #86868b;
        background-size: 100% 100%;
        .logo {
          margin: 5rem auto 10px;
          width: 6rem;
          img {
            height: 6rem;
            border-radius: 50%;
            width: 6rem;
          }
        }
        .biaoShi {
          margin: 0.4rem auto 10px;
          width: 2rem;
          img {
            width: 2rem;
          }
        }
        h1 {
          width: 60%;
          text-align: center;
          margin: 1rem auto;
          font-size: 1.5rem;
          color: #fff;
        }
        .dsc-qyl {
          width: 60%;
          /* text-align: center; */
          margin: 1rem auto 0;
          font-size: 0.8rem;
          color: #fff;
        }
      }
      .name {
        width: 100%;
        /deep/ .el-tabs__item {
          font-size: 1.5rem;
        }
        /deep/ .el-tabs__content {
          font-size: 1rem;
          text-align: left;
          line-height: 1.8rem;
          margin-top: 3rem;
        }
      }
    }
  }
}
@media screen and (max-width: 1024px) {
  .qyl-history {
    padding: 0 10px;
    .content {
      position: relative;
      width: 100%;
      padding: 0 10px;
      margin: 0;
      .main-content {
        font-size: 14px;
        color: #86868b;
      }
      .iconfont {
        font-size: 14px;
      }
      .swiper-slide-active .default-avatar img {
        -ms-transform: scale(1); /* IE 9 */
        -webkit-transform: scale(1); /* Safari */
        transform: scale(1); /* 标准语法 */
        transition: transfrom 0.4s;
      }

      .item {
        width: 100%;
        padding: 0 15px 20px 15px;
        .top {
          margin: 3rem auto;
          .user-name {
            font-size: 2rem;
            margin: 2rem auto;
          }
          .user-say {
            font-size: 2rem;
            // margin: 1rem auto;
          }
          .userSayContent {
            font-size: 1.2rem;
            line-height: 2rem;
            text-align: left;
          }
          .user-content {
            font-size: 1.2rem;
            line-height: 2rem;
            border-left: 1px solid #e8e8e8;
            border-right: 1px solid #e8e8e8;
            border-bottom: 1px solid #e8e8e8;
            padding: 20px 10px;
            text-align: left;
          }
          img {
            width: 60px;
            // margin-right: 20px;
            border-radius: 50%;
            border: 1px solid #eee;
          }
          .name {
            text-align: left;
            font-size: 16px;
            line-height: 1.3;
          }
          .btn_switch {
            margin: 1rem 0 0;
            display: flex;
            justify-content: center;
            .btn_anniu1 {
              // width: 50%;
              padding: 13px;
              font-size: 13px;
              border: 1px solid #e8e8e8;
              color: #000;
              outline: none;
              background: #fff;
              width: 50%;
              height: 45px;
              border-radius: 2px 0 0 2px;
            }
            .btn_anniu2 {
              // width: 50%;
              padding: 13px;
              font-size: 13px;
              border-top: 1px solid #e8e8e8;
              border-right: 1px solid #e8e8e8;
              border-bottom: 1px solid #e8e8e8;
              color: #000;
              outline: none;
              background: #fff;
              width: 50%;
              height: 45px;
              border-radius: 0 2px 2px 0;
            }
            .newStyle {
              // border: 2px solid #808080;
              background-color: #f4f4f4;
              font-size: 13px;
              font-weight: bold;
            }
          }
        }
        .main-content {
          text-align: justify;
          line-height: 1.75;
          margin-top: 1.5rem;
          color: #717171;
        }
      }
    }
    .swiper-slide {
      height: 0;
    }
    .swiper-slide-active {
      height: 100%;
    }
    .ssq-button-prev-qyl,
    .ssq-button-next-qyl {
      position: absolute;
      top: 50%;
      > i {
        font-size: 14px;
      }
    }
    .ssq-button-prev-qyl {
      left: -12px;
    }
    .ssq-button-next-qyl {
      right: -12px;
    }
  }
}
</style>
