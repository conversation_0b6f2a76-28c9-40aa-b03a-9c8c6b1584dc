export const state = () => ({
  path: '',
  checkResult: {},
  serviceVisible: false,
  isMobile: false, //手机和iPad是一套
  isLandMobile: false, //pc和iPad是一套
  menuVisible: false,
  qylmenuVisible: false,
  signImageBase64: '',
  solutions: [],
  isIos: false,
  friendLink: [],
  recentNews: {},
  locale: 'en',
});

export const mutations = {
  SET_LANGUAGE(state, language) {
    state.locale = language;
  },
  changePath(state, payload) {
    state.path = payload.path;
  },
  setCheckResult(state, payload) {
    state.checkResult = Object.assign({}, payload.result);
  },
  setServiceVisible(state, visible) {
    state.serviceVisible = visible;
  },
  setIsMobile(state, status) {
    state.isMobile = status;
  },
  setFriendLink(state, status) {
    state.friendLink = status;
  },
  setRecentNews(state, status) {
    state.recentNews = status;
  },
  setIsLandMobile(state, status) {
    state.isLandMobile = status;
  },
  setIsIos(state, status) {
    state.isIos = status;
  },
  setMenuVisible(state, visible) {
    state.menuVisible = visible;
  },
  setqylMenuVisible(state, visible) {
    state.qylmenuVisible = visible;
  },
  setSignImageBase64(state, base64) {
    state.signImageBase64 = base64;
  },
};

export const actions = {
  // 招聘
  getRecruit() {
    return this.$axios.get('/www/api/web/getRecruit');
  },
  // 统计次数
  setViewTimes(state, payload) {
    this.$axios.post('/www/api/web/viewTimes/add', { ...payload });
  },
};
