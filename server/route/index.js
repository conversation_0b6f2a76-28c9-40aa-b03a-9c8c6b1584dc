const Router = require('koa-router');
const request = require('request-promise');
const moment = require('moment');
const koaBody = require('koa-body');
const path = require('path');
const fs = require('fs');
const wxService = require('../service/wxService');
var xml2js = require('xml2js');
var jsonBuilder = new xml2js.Builder(); // JSON->xml
var xmlParser = new xml2js.Parser(); //xml -> json
// const fileRouter = require('./file')['router'];

const rooPath = process.cwd();

const router = new Router({
  prefix: '/www-api',
});

// router.use('/file', fileRouter.routes(), fileRouter.allowedMethods());

const computedData = data => {
  if (!data) return {};
  const totalTree = Math.round(
      (parseInt(data.totalSendCount, 10) * 10) / 30000
    ),
    totalTreeBefore = Math.round(
      (parseInt(data.totalSendCountBefore, 10) * 10) / 30000
    );
  return {
    date: data.statsDay,
    total: parseInt(data.totalSendCount, 10) * 21,
    totalBefore: parseInt(data.totalSendCountBefore, 10) * 21,
    totalTree,
    totalTreeBefore,
    totalCarton: Math.round(totalTree * 17.9),
    totalCartonBefore: Math.round(totalTreeBefore * 17.9),
  };
};

const fetch = options => {
  return new Promise((resolve, reject) => {
    request(options)
      .then(res => resolve(res))
      .catch(err => reject(err));
  });
};

router.get('/saving-data', async (ctx, next) => {
  const day = moment()
    .subtract(1, 'days')
    .format('YYYY-MM-DD');
  const host =
    process.env.NODE_ENV === 'production' &&
    ctx.request.header.host.indexOf('cn') > -1
      ? 'ent.bestsign.cn'
      : 'ent.bestsign.info';
  const options = {
    method: 'POST',
    uri: `https://${host}/api/dwh/searchCustomerData`,
    body: {
      ruleId: 19,
      parameters: { statsDay: day },
    },
    json: true, // Automatically stringifies the body to JSON
  };
  try {
    const res = await fetch(options);
    if (res.code === 0) {
      ctx.body = {
        msg: res.msg,
        code: res.code,
        data: computedData(res.data[0]),
      };
    } else {
      ctx.body = res;
    }
  } catch (e) {
    ctx.body = e;
  }
});

router.post('/weixin/jssdk', async (ctx, next) => {
  const header = ctx.header;
  ctx.body = await wxService.getConfig(header.referer);
});

router.post(
  '/h5-upload',
  koaBody({
    multipart: true,
    formidable: {
      // uploadDir: path.join(process.cwd(), 'server/public'),
      keepExtensions: true,
    },
  }),
  async ctx => {
    const file = ctx.request.files.file;
    const reader = fs.createReadStream(file.path);
    const targetPath = path.join(rooPath, `static/${file.name}`);
    const upStream = fs.createWriteStream(targetPath);
    reader.pipe(upStream);
    return (ctx.body = {
      code: 200,
      message: '上传成功',
    });
  }
);

// 修改网站地图文案内容，添加动态写网站地图xml的接口
router.post(
  '/updateMapText',
  koaBody({
    json: true,
  }),
  async ctx => {
    // 修改文案
    let bodyData = ctx.request.body;
    let xmlJson = {
      urlset: {
        $: {
          xmlns: 'http://www.sitemaps.org/schemas/sitemap/0.9',
        },
        url: [],
      },
    };
    xmlJson.urlset.url = bodyData.list;
    var json2xml = jsonBuilder.buildObject(xmlJson);
    // console.log('json解析成xml:' + json2xml);
    const targetPath = path.join(
      rooPath,
      `static/sitemap/${bodyData.column}.xml`
    );
    // fs.readFile(path.join(rooPath, `static/sitemap.xml`), 'utf-8', function(
    //   err,
    //   result
    // ) {
    //   xmlParser.parseString(result, function(err, result) {
    //     console.log('xml解析成json:' + JSON.stringify(result));
    //   });
    // });
    await new Promise(resolve => {
      fs.writeFile(targetPath, json2xml, err => {
        if (err) return err;
        ctx.body = {
          code: 200,
          msg: '成功',
        };
        resolve();
      });
    });
  }
);

module.exports = router;
