// const Router = require('koa-router');
// const fs = require('fs');
// const path = require('path');
// const LRU = require('lru-cache');

// const cache = new LRU({
//   max: 20,
//   maxAge: 3600000,
// });

// const router = new Router();

// const rooPath = path.join(process.cwd(), 'static');

// async function walk(_path) {
//   let result = [];
//   const dir = await fs.promises.opendir(_path);
//   for await (const dirent of dir) {
//     if (dirent.isDirectory()) {
//       const child = await walk(path.join(_path, dirent.name));
//       result.push({
//         type: 'dir',
//         name: dirent.name,
//         child,
//       });
//     } else {
//       result.push({
//         type: 'file',
//         name: dirent.name,
//       });
//     }
//   }
//   return result;
// }

// async function getDir() {
//   let result = null;
//   if (cache.has('all-dir')) {
//     result = cache.get('all-dir');
//   } else {
//     result = JSON.stringify(await readDir(rooPath));
//     cache.set('all-dir', JSON.stringify(result));
//   }
//   return result;
// }

// router.get('/all', async ctx => {
//   const result = await getDir();
//   ctx.body = result;
// });

// todo
// router.post('/add', async ctx => {

// });

// router.post('/', async ctx => {

// });

// module.exports = {
//   router,
// };
