const request = require('request-promise');
const crypto = require('crypto');
const CryptoJS = require('crypto-js');
const moment = require('moment');
const NodeCache = require('node-cache');
const myCache = new NodeCache({ checkperiod: 7200 });

const appid = 'wx542ff336c83dfd1d';
const appsecret = '3989f3b57fd9fb80dc77c07f0e69e000';
const tokenUrl = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appid}&secret=${appsecret}`;
const ticketUrl =
  'https://api.weixin.qq.com/cgi-bin/ticket/getticket?type=jsapi';

function getAccessToken() {
  return new Promise((resolve, reject) => {
    const access_token = myCache.get('access_token');
    if (!access_token) {
      request({
        method: 'GET',
        uri: tokenUrl,
        json: true, // Automatically stringifies the body to JSON
      })
        .then(res => {
          if (res.errcode) {
            reject(res);
          } else {
            resolve(res.access_token);
            myCache.set('access_token', res.access_token);
          }
        })
        .catch(e => reject(e));
    } else {
      resolve(access_token);
    }
  });
}

async function getTicket() {
  const access_token = await getAccessToken();
  return new Promise((resolve, reject) => {
    const ticket = myCache.get('ticket');
    if (ticket == undefined) {
      request({
        method: 'GET',
        uri: `${ticketUrl}&access_token=${access_token}`,
        json: true, // Automatically stringifies the body to JSON
      })
        .then(res => {
          if (res.errcode === 0) {
            resolve(res.ticket);
            myCache.set('ticket', res.ticket);
          } else {
            reject(res);
          }
        })
        .catch(e => reject(e));
    } else {
      resolve(ticket);
    }
  });
}

async function getConfig(url) {
  const ticket = await getTicket();
  const noncestr = crypto.randomBytes(8);
  const timestamp = moment().unix();
  const option = {
    jsapi_ticket: ticket,
    noncestr: noncestr.toString('hex'),
    timestamp,
    url,
  };
  const string1 = `jsapi_ticket=${ticket}&noncestr=${
    option.noncestr
  }&timestamp=${timestamp}&url=${url}`;
  const signature = CryptoJS.SHA1(string1).toString();
  return {
    appId: appid,
    signature,
    timestamp,
    noncestr: option.noncestr,
  };
}

module.exports = {
  getConfig,
};
