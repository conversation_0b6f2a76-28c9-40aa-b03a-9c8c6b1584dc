const Koa = require('koa');
const { Nuxt, Builder } = require('nuxt');

const router = require('./route');

const { logger, accessLogger, errorLogger } = require('./logs/logger');

const app = new Koa();

const host = process.env.HOST || '0.0.0.0';
const port = process.env.PORT || 3004;

// Import and Set Nuxt.js options
let config = require('../nuxt.config.js');
config.dev = !(app.env === 'production');

async function start() {
  // Instantiate nuxt.js
  const nuxt = new Nuxt(config);

  //请求前端资源添加响应头
  // app.use(async (ctx, next) => {
  //   ctx.response.set('X-Content-Type-Options', 'nosniff');
  //   await next();
  // });
  app.use(router.routes());
  app.use(router.allowedMethods());

  // Make sure to wait for <PERSON>ux<PERSON> to load @nuxt/typescript-build before proceeding
  await nuxt.ready();

  // Build in development
  if (config.dev) {
    const builder = new Builder(nuxt);
    await builder.build();
  }

  app.use(accessLogger()).use(ctx => {
    ctx.status = 200; // koa defaults to 404 when it sees that status is unset

    return new Promise((resolve, reject) => {
      ctx.res.on('close', resolve);
      ctx.res.on('finish', resolve);
      nuxt.render(ctx.req, ctx.res, promise => {
        // nuxt.render passes a rejected promise into callback on error.
        promise
          .then(() => {
            resolve();
          })
          .catch(err => {
            logger.error(err);
            reject();
          });
      });
    });
  });
  app.on('error', err => logger.error(err));

  app.listen(port, host);
}

start();
