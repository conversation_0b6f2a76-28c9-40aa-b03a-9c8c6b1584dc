<!DOCTYPE html>
<html {{ HTML_ATTRS }}>
<head>
  <meta name="baidu-site-verification" content="457n1fQrpn" />
  <meta name="360-site-verification" content="602a2b97d734e8d88edba45e9896f1a9" />
  <meta name="shenma-site-verification" content="96cce171e78a6762d6309d0a2a8bb583_1577935183" />
  <meta name="sogou_site_verification" content="fp3LjvtJlU"/>
  <meta name="format-detection" charset="telephone=yes">
  {{ HEAD }}
  <!-- <script type="text/javascript">
  (function() {
    var isCom = window.location.host.indexOf('.com') > -1;
    var isZh = (navigator.language || navigator.browserLanguage).toLowerCase() === "zh-cn";
    if (isCom && isZh) {
      window.location.href = 'https://www.bestsign.cn';
    }
  })()
  </script> -->
  <!-- 百度统计 -->
  <script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?8b6f6b56113090139c89b5e4d1506a81";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
  </script>
  <!-- GA统计 -->
  <!-- Global site tag (gtag.js) - Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-GSX652TSDS"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-GSX652TSDS');
  </script>
 <!-- Google Tag Manager -->
  <script>
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-KLPKVWW');
  </script>
  <!-- End Google Tag Manager -->

  <!--adobe font-->
  <script>
    (function(d) {
      const asyncLoad = function () {
        let config = {
            kitId: 'mei5bed',
            scriptTimeout: 1000,
            async: true
          },
          h=d.documentElement,
          t=setTimeout(function(){h.className=h.className.replace(/\bwf-loading\b/g,"")+" wf-inactive";},config.scriptTimeout),
          tk=d.createElement("script"),
          f=false,
          s=d.getElementsByTagName("script")[0],
          a;
        h.className+=" wf-loading";
        tk.src='https://use.typekit.net/'+config.kitId+'.js';
        tk.async=true;
        a=this.readyState;
        if(f||a&&a!="complete"&&a!="loaded")return;
        f=true;
        clearTimeout(t);
        try{Typekit.load(config)}catch(e){}
        s.parentNode.insertBefore(tk,s)
      };
      if (window && window.attachEvent) {
        window.attachEvent("load", asyncLoad);
      } else {
        window.addEventListener("load", asyncLoad);
      }
	})(document);
  </script>
</head>
<body {{ BODY_ATTRS }}>
{{ APP }}
<!-- Google Tag Manager (noscript) -->
<noscript>
  <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KLPKVWW"
  height="0" width="0" style="display:none;visibility:hidden"></iframe>
</noscript>
  <!-- End Google Tag Manager (noscript) -->
</body>
</html>
