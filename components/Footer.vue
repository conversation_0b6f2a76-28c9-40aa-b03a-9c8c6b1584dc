<template>
  <footer class="site-footer">
    <div class="menu-collapse" v-if="isMobile">
      <el-collapse v-if="!h5" accordion>
        <el-collapse-item :title="$tc('header.production')" name="1">
          <div class="item-content">
            <nuxt-link :to="`/${language}/product2/service`">{{$t('header.service')}}</nuxt-link>
            <nuxt-link :to="`/${language}/product2/function`">{{$t('header.proFunction')}}</nuxt-link>
            <nuxt-link :to="`/${language}/product2`">{{$t('header.signType')}}</nuxt-link>
          </div>
        </el-collapse-item>
        <div class="item-content-1" v-if="!isEN">
             <nuxt-link :to="`/${language}/cost`">{{$t('header.cost')}}</nuxt-link>
          </div>
        <div class="item-content-1">
             <nuxt-link :to="`/${language}/case`">{{$t('header.case')}}</nuxt-link>
          </div>
        <el-collapse-item :title="$t('header.content')" name="4">
          <div class="item-content" v-if="!isEN">
            <a href="https://static.bestsign.cn/43d5a41628cb37d3866a9495e16f8c4683a771ec.pdf" target="_blank">{{$t('header.privacy')}}</a>
            <a href="https://static.bestsign.cn/d39ee102c85c5f8fe11c3132bf316d26bdc3c106.pdf" target="_blank">{{$t('header.serve')}}</a>
            
          </div>
          <div class="item-content" v-if="isEN">
            <a href="https://static.bestsign.cn:443/6a3e27c66752b796723819dc11a24c9f92378308.pdf" target="_blank">{{$t('header.privacy')}}</a>
            <a href="https://static.bestsign.cn:443/5ddd04c909fb61364bd648a6e601c74add8c9ac3.pdf" target="_blank">{{$t('header.serve')}}</a>
            
          </div>
        </el-collapse-item>
        <div class="item-content-1">
            <nuxt-link :to="`/${language}/about-us`">{{$t('header.about')}}</nuxt-link>
          </div>
      </el-collapse>
      <div class="copyright">
        <LangSwitch :isFooter="true"></LangSwitch>
        <div class="logo-img">
            <img src="@/assets/images/logo-bestsign.svg" alt="">
        </div>
        <div class="e-mial">
            {{isEN?'E-mail：<EMAIL>':'E-mail：<EMAIL>'}}
        </div>
				<div class="line">
          <span>Copyright © 2014-2025 BestSign Inc. All rights reserved.</span>
        </div>
				<div class="line" style="display:none">
          <span>浙公网安备 33010602006436号</span>
          <span style="margin-left:10px">浙ICP备14031930号-1</span>
        </div>
      </div>
    </div>
    <div class="footer-main" v-if="!isMobile">
      <div v-if="!h5" class="footer-nav">
        <div class="nav-group-desc">
          <div class="logo-img">
            <img src="@/assets/images/logo-bestsign.svg" alt="">
          </div>
          <div class="e-mial">
            {{isEN?'E-mail：<EMAIL>':'E-mail：<EMAIL>'}}
          </div>
          <LangSwitch :isFooter="true"></LangSwitch>
          <div class="copyrightNew">
          Copyright © 2014-2025 BestSign Inc. All rights reserved.
          </div>
        </div>
        <ul class="nav-group">
          <h4>{{$t('header.production')}}</h4>
          <li>
            <nuxt-link :to="`/${language}/product2/service`">{{$t('header.service')}}</nuxt-link>
          </li>
          <li>
            <nuxt-link :to="`/${language}/product2/function`">{{$t('header.proFunction')}}</nuxt-link>
          </li>
		    <li>
            <nuxt-link :to="`/${language}/product2`">{{$t('header.signType')}}</nuxt-link>
          </li>
        </ul>
        <ul class="nav-group">
          <nuxt-link :to="`/${language}/cost`" v-if="!isEN"><h4 class="title">{{$t('header.cost')}}</h4></nuxt-link>
          <nuxt-link :to="`/${language}/case`"><h4 class="title">{{$t('header.case')}}</h4></nuxt-link>
          <nuxt-link :to="`/${language}/about-us`"><h4 class="title">{{$t('header.about')}}</h4></nuxt-link>
        </ul>
        <ul class="nav-group">
          <h4>{{$t('header.content')}}</h4>
          <div class="columns">
            <div class="left" v-if="!isEN">
              <!-- <li>
                <a href="/#jpUse">用户协议</a>
              </li> -->
              <li>
                <a href="https://static.bestsign.cn/43d5a41628cb37d3866a9495e16f8c4683a771ec.pdf" target="_blank">{{$t('header.privacy')}}</a>
              </li>
              <li>
                <a href="https://static.bestsign.cn/d39ee102c85c5f8fe11c3132bf316d26bdc3c106.pdf" target="_blank">{{$t('header.serve')}}</a>
              </li>
            </div>
            <div class="left" v-if="isEN">
              <!-- <li>
                <a href="/#jpUse">用户协议</a>
              </li> -->
              <li>
                <a href="https://static.bestsign.cn:443/6a3e27c66752b796723819dc11a24c9f92378308.pdf" target="_blank">{{$t('header.privacy')}}</a>
              </li>
              <li>
                <a href="https://static.bestsign.cn:443/5ddd04c909fb61364bd648a6e601c74add8c9ac3.pdf" target="_blank">{{$t('header.serve')}}</a>
              </li>
            </div>
          </div>

        </ul>
      </div>
    </div>
    
  </footer>
</template>

<script>
import LangSwitch from './LangSwitch.vue';
const getJson = () =>
  import('@/static/_jpSolution.json').then(m => m.default || m);
const industries = {
  42: 'hr',
  40: 'law',
  41: 'houseRent',
};
export default {
  name: 'Footer',
  props: {
    h5: {
      type: Boolean,
      default: false,
    },
    // showlink: {
    //   type: Boolean,
    //   default: false,
    // },
  },
  components: {
    LangSwitch,
  },
  data() {
    return {
      activeNames: ['1'],
      solutions: [],
      link: [],
      showlink: false,
      industries,
      isEN: false,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  watch: {
    '$route.path': function(newValue, oldValue) {
      // console.log(newValue + '新鲜');
      if (newValue === '/') {
        this.showlink = true;
      } else {
        this.showlink = false;
      }
    },
    $route() {
      this.goAnchor(window.location.hash);
    },
  },
  beforeUpdate() {
    // console.log(this.$route.path + 'soifjrsjf');
    if (this.$route.path === '/') {
      this.showlink = true;
    } else {
      this.showlink = false;
    }
  },
  async mounted() {
    const response = await getJson();
    this.solutions = response.map(o => ({
      id: o.id,
      solutionName: o.solutionName,
    }));
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
    // if (this.$route.path === '/') {
    //   this.showlink = true;
    //   lineshow = true;
    // } else {
    //   this.showlink = false;
    //   this.noline = true;
    // }
    // this.friendLink();
    // if (this.showlink == true) {
    //   this.lineshow = true;
    // } else {
    //   this.noline = true;
    // }
  },
  // beforeDestroy() {
  //   window.removeEventListener('load', this.handleResize);
  //   window.removeEventListener('resize', this.handleResize);
  // },
  methods: {
    goAnchor(selector) {
      // 最好加个定时器给页面缓冲时间,不等的话dom没渲染完，获取锚点元素拿不到
      if (window.location.hash) {
        setTimeout(() => {
          // 获取锚点元素
          // debugger;
          let anchor = document.querySelector(selector);
          anchor && anchor.scrollIntoView({ behavior: 'smooth' });
        }, 100);
      }
    },
    loginOpen() {
      const url =
        window.location.host.indexOf('cn') > -1
          ? 'https://openapi.bestsign.cn'
          : 'https://openapi.bestsign.info';
      window.open(url);
    },
    handleResize() {
      if (document.body.clientWidth <= 768) {
        this.$store.commit('setIsMobile', true);
      } else {
        this.$store.commit('setIsMobile', false);
      }
    },
    friendLink() {
      this.$axios({
        // url: '/www/api/web/college2?column=friendLink&pageNum=1&pageSize=10',
        url: '/www/api/web/getFriendLink',
        method: 'get',
      }).then(res => {
        const link = res;
        this.link = link;
        // console.log(res);
      });
    },
    handleCommand(command) {
      if (command === 'japanese') {
        window.open('https://www.bestsign.com/');
      } else if (command === 'chinese') {
        window.open('https://www.bestsign.cn/');
      } else if (command === 'english') {
        window.open('https://www.bestsign.com/en');
      }
    },
  },
};
</script>
<style lang="scss">
.site-footer {
  background-color: $-color-deep;
  .el-collapse-item__header {
    font-weight: 400;
    background-color: $-color-deep;
    color: #fff;
    height: 60px;
    line-height: 60px;
    font-size: 16px;
    border-bottom: 1px solid #363a39;
    &.is-active {
      color: $-color-main;
    }
  }
  .el-collapse-item__arrow {
    color: #c1c2c3;
  }
  .el-collapse-item__content {
    background-color: $-color-deep;
  }
  .el-collapse-item__wrap {
    border-bottom: 1px solid #363a39;
  }
}
</style>
<style scoped lang="scss">
.site-footer {
  padding: 50px 0;
  .menu-collapse {
    padding: 0 16px;
    .item-content {
      padding-left: 32px;
      a {
        display: block;
        padding: 10px 0;
        color: #fff;
      }
    }
    .item-content-1 {
      padding-left: 0;

      a {
        display: block;
        padding: 20px 0;
        color: #fff;
        border-bottom: 1px solid #363a39;
        height: 60px;
        font-size: 16px;
        &:hover {
          color: $-color-main;
        }
      }
    }
    .copyright {
      flex-direction: column;
      padding: 12px 0;
      .line {
        padding: 5px 0;
        display: flex;
        justify-content: center;
        align-items: center;
        a {
          padding: 0 10px;
        }
        span {
          font-size: 10px;
        }
        img {
          width: 60px;
        }
        .kexinyun {
          img {
            width: 50px;
          }
          span {
            display: block;
            font-size: 10px;
          }
        }
        .beian {
          img {
            width: 40px;
          }
        }
      }
    }
  }
}
.site-footer .footer-nav {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  .nav-group-desc {
    width: 35%;
    .logo-img {
      img {
        width: 100px;
      }
    }
    .e-mial {
      color: #fff;
      margin: 20px 0;
    }
    .language-con {
      display: flex;
      .iconfont {
        font-size: 1rem;
        font-weight: 800;
        margin-right: 5px;
      }
      .changeLanguage {
        display: flex;
        align-items: center;
        &:hover {
          color: $-color-main;
          cursor: pointer;
        }
        .el-dropdown {
          // margin: 0 10px;
          font-size: 0.8rem;
          &:hover {
            color: $-color-main;
            cursor: pointer;
          }
        }
      }
    }
    .copyrightNew {
      color: #767e88;
      font-size: 12px;
      margin-top: 20px;
    }
  }
  .nav-group:first-child {
    // padding-left: 0;
  }
  .nav-group:last-child {
    padding-right: 0;
  }
}

.site-footer .footer-main {
  max-width: 80%;
  padding: 0;
  margin: 0 auto;
  box-sizing: border-box;
}

@media (max-width: 1440px) {
  .site-footer .footer-main {
    max-width: 80%;
    box-sizing: border-box;
  }
}

@media (max-width: 768px) {
  .site-footer {
    // background-color: #fff;
    background-color: $-color-deep;
    // color: #fff;
    .footer-main {
      padding: 0;
    }
    .menu-collapse {
      .item-content {
        padding-left: 16px;
        font-size: 14px;
      }
    }
    .el-collapse {
      border-top: none;
      border-bottom: none;
    }
    // .el-collapse-item__header {
    //   font-size: 16px;
    //   align-items: center;
    // }

    .el-collapse-item {
      .el-collapse-item__header {
        align-items: center !important;
        font-size: 16px;
        align-items: center;
        .el-icon-arrow-right {
          margin-top: 0px;
        }
      }
    }
  }
}

.site-footer .footer-nav > ul.nav-group {
  margin-bottom: 40px;
  padding: 0 1rem;
  text-align: left;
  //   width: 17%;
  flex: 1;
  .qrcode {
    // min-width: 200px;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    img {
      width: 120px;
      height: 120px;
    }
    p {
      margin-top: 0.75rem;
      font-size: 14px;
    }
  }
  & .columns {
    display: flex;
    .left {
      margin-right: 30px;
    }
    li {
      cursor: pointer;
      margin-bottom: 20px;
      a {
        color: #808080;
      }
      &:hover {
        a {
          color: $-color-main;
        }
      }
    }
  }
}

.site-footer .footer-nav > ul.nav-group h4 {
  margin-bottom: 28px;
  font-size: 1rem;
  font-weight: 600;
  color: #fff;
}
.site-footer .footer-nav > ul.nav-group .title {
  margin-bottom: 28px;
  font-size: 1rem;
  font-weight: 600;
  color: #fff;
  &:hover {
    color: $-color-main;
  }
}

.site-footer .footer-nav > ul.nav-group > li {
  margin-bottom: 20px;
  font-size: 1rem;
}

.site-footer .footer-nav > ul.nav-group > li:last-child {
  margin-bottom: 0;
}

.site-footer .footer-nav > ul.nav-group > li .small {
  display: block;
  color: #a6a6a6;
  margin-top: 14px;
}

.site-footer .footer-nav > ul.nav-group > li .small > a,
.site-footer .footer-nav > ul.nav-group > li > a {
  color: #767e88;
  transition: all 218ms;
}

.number {
  color: #515151;
  transition: all 218ms;
  font-size: 18px;
}

.site-footer .footer-nav > ul.nav-group > li .small > a:hover,
.site-footer .footer-nav > ul.nav-group > li > a:hover {
  color: $-color-main;
}

.site-footer .copyright {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  padding: 12px 0;
  color: #6e6c6c;
  font-size: 12px;
  border-top: 1px solid #e5e5e5;
  img {
    vertical-align: middle;
  }
  a {
    color: #767e88;
    padding-right: 5px;
  }
}
.site-footer .copyrightlink {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  color: #6e6c6c;
  font-size: 12px;
  img {
    vertical-align: middle;
  }
  a {
    color: #767e88;
    padding-right: 5px;
  }
}
.site-footer .copyright .copyandlang {
  display: flex;
  align-items: center;
  > a {
    margin-right: 24px;
  }
  .beian > img {
    width: 39px;
    height: 39px;
    margin-right: 5px;
  }
  .beian > span {
    vertical-align: middle;
  }
}

.site-footer .copyright .copyandlang .kexinyun span {
  display: block;
}

.site-footer .copyright .copyandlang,
.site-footer .copyright .copyandlang a {
  color: #6e6c6c;
}

.site-footer .copyright .copyandlang .small {
  margin-left: 8px;
}

.site-footer .link-wrap {
  width: 100%;
  background-color: #434343;
  padding: 12px;
  .link {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    > span {
      color: #6e6c6c;
      padding-right: 12px;
    }
    > a {
      padding: 6px 12px;
      color: #8c8b8b;
    }
  }
}

.site-footer.for-mobile {
  display: none;
}
.protocol {
  border-top: 1px solid #e5e5e5;
  padding: 12px 0 0 0;
  a {
    color: #333;
    font-size: 12px;
    border-right: 1px solid #e5e5e5;
    color: #333;
    padding: 0 15px 0 8px;
    font-size: 12px;
    border-right: 1px solid #e5e5e5;
  }
  a:last-child {
    border-right: none;
  }
  a:first-child {
    padding-left: 0;
  }
}

@media (max-width: 768px) {
  .site-footer {
    padding: 0;
    margin-top: 0;
    .link-wrap .link > a {
      text-align: center;
    }
  }
  .site-footer .copyright {
    flex-direction: column-reverse;
    border-top: none;
    padding: 0 16px 40px;
    text-align: center;
    .logo-img {
      margin: 10px 0 20px;
      img {
        width: 100px;
      }
    }
    .e-mial {
      font-size: 1.2rem;
      color: #fff;
      margin-bottom: 20px;
    }
    .line {
      color: #767e88;
    }
  }
  .site-footer .copyright .copyandlang {
    line-height: 20px;
    padding: 0;
  }

  .site-footer .copyright .copyandlang span.small {
    display: block;
  }
  .protocol {
    border-top: none;
    padding: 12px 0 0 0;
    margin-top: 2rem;
    a {
      color: #333;
      font-size: 12px;
      border-right: 1px solid #e5e5e5;
      color: #333;
      padding: 0 8px 0 8px;
      font-size: 12px;
      border-right: 1px solid #e5e5e5;
      margin: 5px 0;
      display: inline-block;
    }
    a:last-child {
      border-right: none;
    }
    a:first-child {
      padding-left: 8px;
    }
  }
}
@media screen and (min-width: 1088px) {
}
</style>
