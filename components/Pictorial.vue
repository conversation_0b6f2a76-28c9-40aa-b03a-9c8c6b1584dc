<template>
  <div class="pictorial"
       :style="`width: ${width}px;`">
    <!--<transition-group :name="needAnimate?'fade':''" mode="out-in">-->
      <div class="page-box" :key="pageNum" v-for="pageNum in srcArray.length" :style="`width: ${width}px; height: ${height}px;`">
          <!-- 背景前景图片 -->
          <img :src="srcArray[pageNum-1]" :width="width" class="front-image"
               alt="annual_portrait">
          <!-- 背景氛围图片 -->
          <div :style="{backgroundImage: `url(${backImage})`}" class="back-image"></div>
          <div class="text-box" :style="`transform: scale(${scale});transform-origin: 0 0 0`">
            <!--<transition-group name="fade" mode="out-in">-->
            <div v-if="pageNum===i[1].page"
                 v-for="i in Object.entries(textSettings)"
                 :key="i[0]"
                 :style="{ left: i[1].x+'px', top: i[1].y+'px', width: i[1].width===undefined ? '' : ` ${i[1].width}` }"
                 :class="'text' + (i[1].class===undefined ? '' : ` ${i[1].class}`)"
            >
              <span :key="index" v-for="(span, index) in textData[i[0]]"
                    :class="i[1].content[index].class"
                    :style="{
                    color: i[1].content[index].color,
                    fontSize: i[1].content[index].fontSize+'px',
                    fontFamily: i[1].content[index].fontFamily
                    }">{{span}}</span>
            </div>
            <!--</transition-group>-->
          </div>
      </div>
    <!--</transition-group>-->
  </div>
</template>

<script>
export default {
  name: 'Pictorial',
  props: {
    height: {
      default: 1,
    },
    width: {
      default: 1,
    },
    showPages: {
      default() {
        return [1];
      },
    },
    srcArray: {
      default() {
        return [];
      },
    },
    textSettings: {
      default() {
        return {};
      },
    },
    textData: {
      default() {
        return {};
      },
    },
    needAnimate: {
      default: true,
    },
    backImage: {
      default: '',
    },
    scale: {
      default: 1,
    },
  },
};
</script>

<style scoped lang="scss">
.page-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  .text-box {
    left: 0;
    top: 0;
    position: absolute;
    transform-origin: 0 0 0;
    white-space: nowrap;
    line-height: 1;
    font-family: 'PingFang SC';
    /*font-weight: bold;*/
    .text {
      padding: 0;
      position: absolute;
    }
  }
  .front-image {
    position: absolute;
    bottom: 0;
  }
  .back-image {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: cover;
  }
}
.fade-enter-active {
  transition: opacity 0.3s;
}
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter {
  opacity: 0.6;
}
.fade-leave-to {
  opacity: 0.6;
}
</style>
