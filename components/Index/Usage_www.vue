<template>
<!-- 此组件和Usage.vue的不同就是没有背景图 -->
  <section class="index-usage" :style="{background: isWhiteBg ? '#fff' : '#f7f8fa'}">
    <div class="container">
      <h2 class="section-headline headline--main">1273万+标杆企业选择
        <svg class="icon logo-text" aria-hidden="true">
          <use xlink:href="#icon-logo-text"></use>
        </svg>
      </h2>
      <el-tabs v-model="activeName" class="usage-content" v-if="!isMobile">
        <el-tab-pane v-for="item in data" :label="item.solutionName" :name="item.id" :key="item.id">
          <img :src="item.pc" :alt="`上上签电子合同${item.solutionName}行业案例`">
        </el-tab-pane>
      </el-tabs>
      <div class="mobile-imgs" v-if="isMobile">
        <img v-for="img in imgs" :src="img" :key="img" alt="">
      </div>
      <div class="section-content">
        <button v-if="!demo" class="ssq-button-primary transparent">
          <nuxt-link to='/case'>
            更多案例
          </nuxt-link>
        </button>
        <button v-if="demo" class="ssq-button-primary" @click="toDemo">
          免费试用
        </button>
      </div>
    </div>
  </section>
</template>

<script>
import find from 'lodash/find';
const getJson = () => import('@/static/_new.json').then(m => m.default || m);
const imgs = [
  'https://static.bestsign.cn:443/98b4ba9486f7ffc7f1420cda560e2cadae899c9a.jpg',
  // 'https://static.bestsign.cn/84dc4147aa38f6da60aead381a9067db2fabdd8c.jpg',
  // 'https://static.bestsign.cn/be2dae3da606bebb830448cfa2d88d1467960360.jpg',
  // 'https://static.bestsign.cn/b754ad6e9eff20d8d764dd9ba7c50e4ab44a290b.jpg',
  // 'https://static.bestsign.cn/5d4d44aa226b3bfabaf5a3e8e3aefbcb95021c3f.jpg',
  // 'https://static.bestsign.cn/1f712180f8c8f059e7777de4fff66f8edba4f4c3.jpg',
  // 'https://static.bestsign.cn/42b04fda0a9e715e21fd35f31ab74b5b54ba1bfb.jpg',
  // 'https://static.bestsign.cn/fd67fa159cfcaffbb303d59d494fc9ef91e40db6.jpg',
  // 'https://static.bestsign.cn/1fb937668479e26086bc8ba4ed076328eee60ebf.jpg',
];
export default {
  name: 'IndexUsage',
  props: {
    isWhiteBg: {
      type: Boolean,
      default: false,
    },
    demo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeName: '18',
      data: [],
      imgs,
    };
  },
  computed: {
    feature() {
      const item = find(this.data, o => o.name === this.activeName);
      return item && item.features;
    },
    caseName() {
      const solutions = {
        31: 'other',
        25: 'car',
        30: 'business',
        22: 'logistics',
        26: 'it',
        23: 'retail',
        21: 'fang',
        19: 'hr',
        18: 'finance',
      };
      return solutions[this.activeName];
    },
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const query = sessionStorage.getItem('query');
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          ...Qs.parse(query),
        },
      });
    },
  },
  async mounted() {
    const response = await getJson();
    this.data = response.map(o => ({
      id: o.id.toString(),
      solutionName: o.solutionName,
      pc: o.usage && o.usage.pc,
    }));
  },
};
</script>

<style lang="scss">
.landscape .paragraph {
  .section-headline {
    color: #515151;
    font-weight: 400;
  }
}
.index-usage {
  .el-tabs__nav {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  .el-tabs__active-bar {
    display: none;
  }
  .el-tabs__item {
    font-size: 16px;
    padding: 0;
    text-align: center;
    font-weight: 400;
    &.is-active {
      color: #00a664;
      border-bottom: 3px solid #00a664;
    }
    &:hover {
      color: #00a664;
    }
  }
  .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: #00a664;
  }
  .section-headline {
    padding-top: 0;
  }
}
@media screen and (max-width: 767px) {
  .index-usage {
    .el-tabs__nav-wrap::after {
      bottom: 2px;
    }
    .el-tabs__item {
      transform: scale(0.9);
      font-size: 12px;
    }
    .section-headline {
      padding-top: 2rem;
    }
  }
}
</style>
<style scoped lang="scss">
.index-usage {
  text-align: center;
  padding: 5rem 0;
  background-color: #f7f8fa;
  .container {
    width: 100%;
    max-width: 1080px;
  }
  .section-headline {
    line-height: 2rem;
    // padding-top: 0;
    .logo-text {
      width: 7.25rem;
      height: 2.25rem;
      vertical-align: -0.2em;
      position: relative;
      left: -3px;
    }
    padding-bottom: 5rem;
  }
  .usage-content img {
    width: 100%;
    padding-top: 2rem;
    padding-bottom: 4rem;
  }
  .mobile-imgs {
    width: 100%;
    padding: 0 10px;
    img {
      width: 100%;
      padding-bottom: 10px;
    }
  }
}
.ssq-button-primary.is-text {
  display: inline-block;
}
.ssq-button-primary.is-text .el-icon-arrow-right {
  margin-left: 5px;
  font-size: 20px;
  vertical-align: bottom;
}
@media screen and (max-width: 767px) {
  .container {
    margin: 0 auto;
  }
  .index-usage {
    padding: 50px 2px;
    .section-headline {
      padding-bottom: 3rem;
      .logo-text {
        width: 6.125rem;
        height: 2.75rem;
        vertical-align: -0.35em;
        position: relative;
        left: -3px;
      }
      .img-text {
        vertical-align: top;
      }
    }
    .mobile-imgs {
      width: 100%;
      padding: 0 10px;
      img {
        width: 100%;
        padding-bottom: 10px;
      }
    }
    .ssq-button-primary {
      width: 130px;
      height: 44px;
      line-height: 44px;
      margin-top: 2rem;
    }
  }
}
</style>
