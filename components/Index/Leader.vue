<template>
  <section class="index-leader section">
    <div class="container">
      <h2 class="section-headline headline--main">两种签约形式</h2>
      <div class="content">
        <div class="block">
			<i class="iconfont icon-qijian"></i>
          <h3>签约管理系列</h3>
          <p>00开发成本，即充即用，合同秒发秒签，满足各类合同工作流管理，适应各终端（Web、APP、H5、小程序等）与平台（OA、HR、CRM等）</p>
          <div class="ssq-button-primary" v-if="!demo" @click="toDemoQJ">免费试用</div>
          <!-- <p class="tips" v-if="!demo">免费送10份电子合同</p> -->
        </div>
        <div class="block">
      <i class="iconfont icon-API1"></i>
          <h3>签约工具系列</h3>
          <p>提供丰富的API接口嵌入，可根据业务深度与使用习惯深度自定义</p>
          <div class="ssq-button-primary" v-if="!demo" @click="toDemoAPI">免费试用</div>
          <!-- <p class="tips" v-if="!demo">1分钟了解电子签约全流程</p> -->
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import Qs from 'qs';
export default {
  name: 'index-leader',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    toReg() {
      const query = sessionStorage.getItem('query');
      const href = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push([
            '_trackEvent',
            'click_register_mobile',
            'click',
            href,
          ]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_register_pc', 'click', href]);
      }
      const url =
        window.location.host.indexOf('cn') > -1
          ? 'https://ent.bestsign.cn/register'
          : 'https://ent.bestsign.info/register';
      window.open(url + '?' + 'index_saas&' + query);
    },
    toDemoAPI() {
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/demo',
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
          index_api: '',
        },
      });
    },
    toDemoQJ() {
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/demo',
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
          zindex_qijian: '',
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.index-leader {
  .section-headline {
    color: #090909;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
  }
  padding-bottom: 0;
  background-color: #f2f2f2;
  .content {
    display: flex;
    flex-wrap: nowrap;
    border-bottom: 1px solid #ddd;
    padding: 0 2rem;
    margin: 0 auto;
    .block {
      //   flex: 1;
      width: 50%;
      text-align: center;
      background-color: #fff;
      margin: 0 0px 100px;
      padding: 30px 0;
      .icon {
        font-size: 5rem;
      }
      .iconfont {
        font-size: 5rem;
        cursor: pointer;
        color: #888988;
      }
      .iconfont:hover {
        color: #00aa64;
      }
      img {
        width: 90px;
        height: 85px;
      }
      h3 {
        font-size: 1.6rem;
        font-weight: normal;
        margin: 1.5rem auto;
        color: #1d1d1f;
        font-weight: 400;
      }
      p {
        max-width: 62%;
        margin: 0 auto;
        line-height: 1.5;
        color: #86868b;
      }
      .ssq-button-primary {
        display: inline-block;
        margin: 2.5rem auto 0.5rem;
        font-size: 16px;
      }
      .tips {
        font-size: 14px;
        color: #00aa64;
      }
      &:nth-child(1) {
        margin-right: 10px;
      }
      &:nth-child(2) {
        margin-left: 10px;
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .index-leader {
    // padding-top: 210px;
    .container {
      padding: 0;
    }
    .section-headline {
      color: #090909;
      font-size: 1.9rem;
      line-height: 2.5rem;
      font-weight: 400;
    }
    .content {
      display: flex;
      flex-wrap: wrap;
      border-bottom: 0px solid #ddd;
      justify-content: center;
      padding: 0 0 2rem;
      .block {
        background-color: #f2f2f2;
        margin: 0 10px 50px;
        padding: 0 0;
        width: 100%;
        .ssq-button-primary {
          margin: 1.5rem auto 1rem;
        }
        img {
          width: 50px;
          height: 45px;
        }
        h3 {
          font-size: 18px;
        }
        p {
          max-width: 100%;
          height: 25%;
          font-size: 14px;
        }
        .tips {
          max-width: 100%;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
