<template>
  <div class="index-evaluate section">
    <div class="container">
      <h2 class="section-headline headline--main">上上签的伙伴这样说</h2>
      <div class="content">
        <div class="swiper-container" v-swiper:mySwiper="swiperOption">
          <div class="swiper-wrapper">
            <div class="swiper-slide" v-for="(item) in data" :key="item.name">
              <div class="item">
                <div class="top">
                  <img :src="item.logo" alt="">
                  <div class="name">
                    <p>{{ item.name }}</p>
                    <p>{{ item.desc }}</p>
                  </div>
                </div>
                <div class="main-content">{{ item.content }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="arrow">
          <div class="ssq-button-prev-a">
            <i class="el-icon-arrow-left"></i>
          </div>
          <div class="ssq-button-next-a">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
      <!-- <div v-if="!demo" class="ssq-button-primary transparent" @click="toCase">更多证言</div>
      <div v-if="demo" class="ssq-button-primary" style="color: #fff" @click="toDemo">免费试用</div> -->
    </div>
  </div>
</template>

<script>
const data = [
  {
    logo:
      'https://static.bestsign.cn/ffead3336d3e670a3b52f137f811582f68af8dd3.png',
    name: '张剑君',
    desc: '好丽友 中国区法务与知识产权负责人',
    content:
      '“好丽友选择与上上签携手，一方面因为上上签的技术出众，产品体验好；另一方面也因为上上签积累了非常多的大客户服务经验。通过上上签强大的实名认证体系，好丽友与经销商签署协议更加合法合规，同时也实现了合同的智能化管理以及内部数据的系统整合，从而提升了渠道管理效率。 后续，我们希望与上上签继续深化合作，积极拓展在人力资源管理、物流等更多场景的应用。”',
  },
  {
    logo:
      'https://static.bestsign.cn/ffa85915ecaf5941161beb6739740d3c55c4eb1f.png',
    name: '孙弦',
    desc: '贝壳找房 人力资源共享服务中心高级经理',
    content:
      '“贝壳找房由链家网升级而来，是以技术驱动的品质居住服务平台，聚合和赋能全行业的优质服务者，致力于为两亿家庭提供包括二手房、新房、租赁和装修全方位居住服务。目前公司在员工入职新签合同、合同的续改签、辞职书的签署均通过上上签平台实现了电子签署。合同管理的效能提升非常明显。”',
  },
  {
    logo:
      'https://static.bestsign.cn/dec8caa14d0646b84d29ebaa772cd70b846e1229.png',
    name: '殷晟翔',
    desc: '丹鸟 法务高级专家',
    content:
      '“上上签无论在产品功能还是服务能力上都业界领先，尤其在物流领域积累了很多头部客户的服务经验，这是丹鸟最终选择与上上签携手合作的原因。通过上上签，丹鸟与快递、仓配、驿站等业务线上的众多生态合作伙伴，可以更高效地完成各类合同文件的电子化签署，从而打破数据孤岛，实现产业链多方协同。对消费者而言，物流服务的整体效率大为提高，体验也更加优质。”',
  },
];
export default {
  name: 'index-evaluate',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeIndex: 2,
      data,
      swiperOption: {
        loop: true,
        speed: 700,
        navigation: {
          nextEl: '.ssq-button-next-a',
          prevEl: '.ssq-button-prev-a',
        },
      },
    };
  },
  methods: {
    toCase() {
      this.$router.push('/evaluate');
    },
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const query = sessionStorage.getItem('query');
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          ...Qs.parse(query),
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.index-evaluate {
  padding-top: 5rem;
  padding-bottom: 2rem;
  background-color: #f7f8fa;
  text-align: center;
  .content {
    width: 90%;
    position: relative;
    margin: 3rem auto;
    .item {
      width: 100%;
      background-color: #fff;
      padding: 20px 40px;
      .top {
        display: flex;
        align-items: center;
        img {
          width: 90px;
          margin-right: 30px;
          border-radius: 50%;
          border: 1px solid #eee;
        }
        .name {
          text-align: left;
          font-size: 20px;
          line-height: 1.3;
        }
      }
      .main-content {
        text-align: justify;
        line-height: 1.75;
        margin-top: 1.5rem;
        color: #717171;
      }
    }
  }
  .ssq-button-prev-a,
  .ssq-button-next-a {
    position: absolute;
    top: 50%;
    transform: translate3d(0, -50%, 0);
    background: transparent;
    cursor: pointer;
    > i {
      font-size: 46px;
      color: rgba(35, 24, 21, 0.5);
      outline: none;
      &:hover {
        color: rgba(35, 24, 21, 1);
      }
      &:focus {
        outline: none;
      }
    }
    &:focus {
      outline: none;
    }
  }
  .ssq-button-prev-a {
    left: -60px;
  }
  .ssq-button-next-a {
    right: -60px;
  }
  .ssq-button-primary {
    display: inline-block;
  }
}
@media screen and (max-width: 767px) {
  .index-evaluate {
    .content {
      position: relative;
      width: 100%;
      padding: 0 10px;
      .item {
        width: 100%;
        padding: 20px 15px;
        .top {
          img {
            width: 60px;
            margin-right: 20px;
            border-radius: 50%;
            border: 1px solid #eee;
          }
          .name {
            text-align: left;
            font-size: 16px;
            line-height: 1.3;
          }
        }
        .main-content {
          text-align: justify;
          line-height: 1.75;
          margin-top: 1.5rem;
          color: #717171;
        }
      }
    }
    .ssq-button-prev-a,
    .ssq-button-next-a {
      > i {
        font-size: 22px;
      }
    }
    .ssq-button-prev-a {
      left: -12px;
    }
    .ssq-button-next-a {
      right: -12px;
    }
  }
}
</style>
