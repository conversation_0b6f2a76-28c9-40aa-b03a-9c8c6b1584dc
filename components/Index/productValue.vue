<template>
<div>
  <section class="section section-1">
    <div class="container">
      <div class="content flex bc">
       <div class="about">
		   <div class="main-content">
			<h1 class="main-title">{{$t('productionValue.title')}}</h1>
			<div class="about-desc">
				<p>{{$t('productionValue.desc')}}</p>
			</div>
      <div class="produce-value-content" v-if="!isMobile">
        <el-row>
          <el-col :xs="6" :sm="6">
            <div class="box-wrap">
              <i class="iconfont icon-tishenggongzuoxiaoshuai"></i>
              <div class="box-title">{{$tc('productionValue.workEfficiency',1)}}</div>
              <p class="box-desc">{{$tc('productionValue.workEfficiency',2)}}</p>
            </div>
          </el-col>
          <el-col :xs="6" :sm="6">
            <div class="box-wrap">
              <i class="iconfont icon-tixianhuanbaojiazhi"></i>
              <div class="box-title">{{$tc('productionValue.environment',1)}}</div>
              <p class="box-desc">{{$tc('productionValue.environment',2)}}</p>
            </div>
          </el-col>
          <el-col :xs="6" :sm="6">
            <div class="box-wrap">
              <i class="iconfont icon-jiangdiyunyingchengben"></i>
              <div class="box-title">{{$tc('productionValue.cost',1)}}</div>
              <p class="box-desc">{{$tc('productionValue.cost',2)}}</p>
            </div>
          </el-col>
          <el-col :xs="6" :sm="6">
            <div class="box-wrap">
              <i class="iconfont icon-shixiananquanguanli"></i>
              <div class="box-title">{{$tc('productionValue.safety',1)}}</div>
              <p class="box-desc">{{$tc('productionValue.safety',2)}}</p>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="produce-value-content" v-if="isMobile">
        <el-row>
          <el-col :xs="24" :sm="24">
            <div class="box-wrap">
              <i class="iconfont icon-tishenggongzuoxiaoshuai"></i>
              <div class="box-title">{{$tc('productionValue.workEfficiency',1)}}</div>
              <p class="box-desc">{{$tc('productionValue.workEfficiency',2)}}</p>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24">
            <div class="box-wrap">
              <i class="iconfont icon-tixianhuanbaojiazhi"></i>
              <div class="box-title">{{$tc('productionValue.environment',1)}}</div>
              <p class="box-desc">{{$tc('productionValue.environment',2)}}</p>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24">
            <div class="box-wrap">
              <i class="iconfont icon-jiangdiyunyingchengben"></i>
              <div class="box-title">{{$tc('productionValue.cost',1)}}</div>
              <p class="box-desc">{{$tc('productionValue.cost',2)}}</p>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24">
            <div class="box-wrap">
              <i class="iconfont icon-shixiananquanguanli"></i>
              <div class="box-title">{{$tc('productionValue.safety',1)}}</div>
              <p class="box-desc">{{$tc('productionValue.safety',2)}}</p>
            </div>
          </el-col>
        </el-row>
      </div>
    	</div>
	   </div>
      </div>
    </div>
  </section>

</div>
  
  
</template>

<script>
export default {
  name: 'product-value',
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
};
</script>

<style scoped lang="scss">
.section-1 {
  .about {
    .main-title {
      padding: 2rem 0 2rem 0;
      text-align: center;
      font-size: 2rem;
      line-height: 2.5rem;
      font-weight: 400;
      color: #090909;
    }

    .about-desc {
      width: 70%;
      line-height: 1.7;
      text-align: center;
      font-size: 16px;
      margin: 0 auto;
    }
    .produce-value-content {
      padding: 3rem 2rem 0;
      .box-wrap {
        max-width: 320px;
        margin: 0 auto;
        text-align: center;
        .iconfont {
          font-size: 4rem;
          color: #00aa64;
          padding-bottom: 1rem;
          display: block;
        }
        &:hover .iconfont {
          color: #00aa64;
          cursor: pointer;
        }
        .box-title {
          font-size: 20px;
          margin: 0.75rem auto 1.75rem;
          font-weight: 400;
          color: #1d1d1f;
        }
        .box-desc {
          color: #86868b;
          line-height: 1.7;
          padding: 0 1rem;
        }
      }
    }
  }
}
@media only screen and (max-width: 767px) {
  .section-1 {
    padding-top: 0;
    .about {
      .main-title {
        color: #090909;
        font-size: 2rem;
        line-height: 2.5rem;
        font-weight: 400;
        padding: 4.375rem 0 2.5rem;
        margin: 0;
      }

      .about-desc {
        width: 100%;
        line-height: 1.7;
        text-align: center;
        font-size: 14px;
        margin: 0 auto;
        p {
          font-size: 14px;
          color: #86868b;
        }
      }
      .produce-value-content {
        .box-wrap {
          margin: 20px auto;
        }
      }
    }
  }
}
</style>
