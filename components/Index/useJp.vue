<template>
  <section class="index-reason section">
    <div class="container">
      <h2 class="section-headline-use headline--main">{{$tc('use.useTitle',1)}}<br>{{$tc('use.useTitle',2)}}</h2>
      <div class="user" v-if="!isLandMobile">
        <img src="https://static.bestsign.cn:443/2e36fe8cb336da85527e4eca90eef1d83df19df3.png" alt="" width="100%">
      </div>
      <div class="user" v-if="isLandMobile">
        <img src="https://static.bestsign.cn:443/cb9b6efb06f463f4c806bb03f6c259b03c8213d9.png" alt="" width="100%">
      </div>
    </div>
  </section>
</template>

<script>
import Qs from 'qs';
export default {
  name: 'review',
  props: {},
  data() {
    return {
      activeIndex: 0,
      activeClass: 'iconfont',
    };
  },
  computed: {
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {
    toVote() {
      window.open('http://xsjbpn.v.vote8.cn', '_blank');
    },
    join(item) {
      this.$emit('toJoin', item);
    },
    downLoad() {
      window.location.href =
        'https://static.bestsign.cn/2020%E7%AC%AC%E4%BA%8C%E5%B1%8A%E7%AD%BE%E5%BC%95%E5%8A%9B%E5%A4%A7%E8%B5%9B%E6%A1%88%E4%BE%8B%E6%A8%A1%E6%9D%BF.doc';
    },
    headerStyle({ row, rowIndex }) {
      if (rowIndex == 0) {
        return 'background:#333;color:#fff;font-size:16px;border-right:none;text-align:center';
      } else {
        return '';
      }
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 0) {
        return 'background:#d2d2d2;color:#262626;font-size:16px;border-right:none;font-weight:500';
      } else {
        return '';
      }
    },
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          index_youshi: '',
        },
      });
    },
    advantage(page) {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/advantage/' + (page + 1),
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
        },
      });
    },
    openAdvantage(item) {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: `/advantage/${item}`,
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
          hp_productnav: '',
        },
      });
    },
  },
  mounted() {},
};
</script>

<style scoped lang="scss">
.index-reason {
  //   padding-top: 5rem;
  //   padding-bottom: 5rem;
  padding: 5rem 0;
  background-color: #f9f9f9;
  .section-headline-use {
    color: #090909;
    font-size: 2rem;
    line-height: 1.5;
    font-weight: 400;
    padding: 4.375rem 0 2.5rem;
    text-align: center;
  }
  .min-title {
    text-align: center;
    padding-bottom: 2.5rem;
  }
  .qyl-button-container-b {
    display: flex;
    padding: 30px 0;
    // width: 270px;
    justify-content: center;
    .qyl-button-white-b {
      cursor: pointer;
      width: 200px;
      height: 40px;
      line-height: 30px;
      background: #fff;
      border: 1px solid #a6acfb;
      color: #a6acfb;
      margin-right: 30px;
      border-radius: 5px;
      font-size: 18px;
      font-weight: 500;
    }
    .qyl-button-b {
      cursor: pointer;
      width: 200px;
      height: 40px;
      line-height: 30px;
      background: #a6acfb;
      border: 1px solid #a6acfb;
      color: #fff;
      border-radius: 5px;
      font-size: 18px;
      font-weight: 500;
    }
  }

  .ssq-button-primary {
    margin: 3rem auto;
  }
}
@media screen and (max-width: 1280px) {
  .index-reason {
  }
}
@media screen and (max-width: 767px) {
  .index-reason {
    padding: 5rem 0;
    .section-headline {
      color: #090909;
      font-size: 1.9rem;
      line-height: 2.5rem;
      font-weight: 400;
    }
    .ssq-button-primary {
      width: 130px;
      height: 44px;
      line-height: 44px;
    }
  }
}
</style>
