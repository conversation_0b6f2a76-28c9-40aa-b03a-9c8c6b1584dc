<template>
<div class="demoLogo-con">
  <section class="demoLogo" v-if="!isMobile">
        <div v-swiper:demoLogoSwiper="demoLogo.demoLogoSwiperOption" v-if="demoLogo.logoList.length">
            <div class="swiper-wrapper">
                <div class="swiper-slide" v-for="(item,index) in demoLogo.logoList" :key="index">
                    <div class="swiper-slide-item">
                        <img :src="item" alt="500强企业客户" width="100%">
                    </div>
                </div>
            
            </div>
        </div>
    </section>
    <section class="demoLogo" v-if="isMobile">
        <!-- <div class="swiper-slide-item">
          <img :src="demoLogo.mobileImg" alt="500强企业客户" width="100%">
        </div> -->
        <div v-swiper:demoLogoSwiper="demoLogo.demoLogoSwiperOption" v-if="demoLogo.logoListWap.length">
            <div class="swiper-wrapper">
                <div class="swiper-slide" v-for="(item,index) in demoLogo.logoListWap" :key="index">
                    <div class="swiper-slide-item">
                        <img :src="item" alt="500强企业客户" width="100%">
                    </div>
                </div>
            
            </div>
        </div>
    </section>
</div>
      
</template>
<script>
export default {
  data() {
    return {
      demoLogo: {
        demoLogoSwiperActive: 0,
        demoLogoSwiperOption: {
          loop: true,
          initialSlide: 0,
          slidesPerView: 1,
          speed: 300,
          direction: 'vertical',
          // autoplay: true,
          autoplay: {
            delay: 3000,
            disableOnInteraction: false,
          },
          observer: true,
          observeParents: true,
          height: 100, //你的slide高度
        },
        mobileImg:
          'https://static.bestsign.cn:443/16f2e57132c19e41da91328ab2fd148eead1a96f.png',
        logoList: [
          'https://static.bestsign.cn:443/1405d03e486cb1b55bde21ebd387990d6cc6e521.png',
          'https://static.bestsign.cn:443/ecafa0f4fe26d3c12b2c9b09cef0c46bd268f174.png',
          'https://static.bestsign.cn:443/7d5055473d32d0d0acae17558d569be6bb5eb2e3.png',
          'https://static.bestsign.cn:443/cbcbd8269a6fce430c138c72671643da6eb2dc96.png',
        ],
        logoListWap: [
          'https://static.bestsign.cn:443/890136dc7bc2503e39bb007067cfda78489dc1db.png',
          'https://static.bestsign.cn:443/40c960e4e78937a582bb2eb2e5de69fdaae85ca0.png',
          'https://static.bestsign.cn:443/7eecc50423d044199f7099a7708b197b5144c00.png',
          'https://static.bestsign.cn:443/4eda3508f7efba0dbdcb74605374fd846fb3eec3.png',
          'https://static.bestsign.cn:443/8c861625b201de5bc2c894134f3e89874cba402b.png',
          'https://static.bestsign.cn:443/e3c41875f3cdff7945285266c4a1eafe16ac7676.png',
          'https://static.bestsign.cn:443/9dab1daf350be378d73700affdb58d83869fed7e.png',
        ],
      },
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {
    godemoLogoSwiper(index) {
      this.demoLogoSwiper.slideTo(index);
      this.demoLogo.demoLogoSwiperActive = index;
    },
  },
  crated() {
    const self = this;
    if (this.demoLogo.logoList.length) {
      if (!isMobile) {
        this.demoLogoSwiper.on('slideChange', function() {
          self.demoLogo.demoLogoSwiperActive = this.activeIndex;
        });
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.demoLogo-con {
  padding-bottom: 5rem;
  .demoLogo {
    height: 100px;
    overflow: hidden;
    .swiper-container {
      .swiper-slide-item {
        width: 70%;
        height: 100px;
        position: relative;
        margin: 0 auto;
        .demoLogo-swiper-logo {
          margin-bottom: 8px;
        }
        .demoLogo-swiper-bg {
          position: absolute;
          bottom: 0;
          right: 20px;
        }
        p {
          color: #9ca7b4;
          width: 120px;
          padding-top: 30px;
        }
      }
    }
    .demoLogo-swiper-indicator {
      text-align: center;
      margin-top: 30px;
      span {
        display: inline-block;
        width: 44px;
        height: 44px;
        background-color: #fbfbfc;
        color: #c5c9cd;
        border-radius: 24px;
        line-height: 44px;
        text-align: center;
        font-size: 14px;
        margin: 0 3px;
        &.focus {
          background-color: #f6f7fa;
          color: #8c939b;
        }
      }
    }
  }
}

@media screen and (max-width: 767px) {
  .demoLogo-con {
    padding-bottom: 0;
    .demoLogo {
      height: 100px;
      width: 100%;
      margin: 0 auto;
      .swiper-container {
        .swiper-slide-item {
          width: 90%;
          height: 100px;
          position: relative;
          margin: 0 auto;
          .demoLogo-swiper-logo {
            margin-bottom: 8px;
          }
          .demoLogo-swiper-bg {
            position: absolute;
            bottom: 0;
            right: 20px;
          }
          p {
            color: #9ca7b4;
            width: 120px;
            padding-top: 30px;
          }
        }
      }
    }
  }
}
</style>
