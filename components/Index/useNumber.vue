<!-- 用户数据 -->
<template>
  <article class="use-number">
    <section class="section section-1">
      <div class="container">
        <!-- <div class="use-number-type">
          <div v-for="(item,index) in numberType" :key="index" class="package-con">
            <div class="type">{{item.title}}</div>
            <div class="desc">{{item.desc}}</div>
          </div>
        </div> -->
        <div class="use-number-type" :class="{'use-number-type-en':isEN}">
				<div class="item">
				<div class="top">{{$t('aboutDesc.num5')}}</div>
				<div class="bottom">{{$tc('aboutDesc.useNumber',2)}}</div>
				</div>
        <div class="item">
				<div class="top">{{$t('aboutDesc.num2')}}</div>
				<div class="bottom">{{$t('aboutDesc.loginNum')}}</div>
				</div>
        <div class="item">
				<div class="top">{{$t('aboutDesc.num3')}}</div>
				<div class="bottom">{{$tc('aboutDesc.contractNumber',2)}}</div>
				</div>
        <div class="item">
				<div class="top">{{$t('aboutDesc.num4')}}</div>
				<div class="bottom">{{$tc('aboutDesc.signNumber2')}}</div>
        <div class="bottom1" v-if="!isEN">{{$tc('aboutDesc.signNumber1')}}</div>
				</div>
			</div>
      </div>
    </section> 
  </article>
</template>

<script>
//import x from ''
export default {
  components: {},
  data() {
    return {
      isEN: false,
    };
  },
  computed: {
    language() {
      return this.$store.state.locale;
    },
  },
  methods: {},
  mounted() {
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>

<style lang='scss' scoped>
//@import url()
.use-number {
  .section-1 {
    padding: 5rem 0;
    .container {
      .use-number-type {
        display: flex;
        justify-content: space-around;
        flex-wrap: nowrap;
        .item {
          .top {
            font-size: 2.5rem;
            font-weight: 500;
            color: $-color-main;
            text-align: center;
          }
          .bottom {
            font-size: 1.2rem;
            margin: 20px 0 0;
          }
          .bottom1 {
            margin: 10px 0;
            font-size: 1.2rem;
          }
        }
      }
      .use-number-type-en {
        width: 100%;
        .item {
          margin: 10px 0;
          width: 25%;
          .top {
            font-weight: 600;
          }
        }
      }
    }
  }
}
@media (max-width: 768px) {
  .use-number {
    .section-1 {
      padding: 5rem 0;
      .container {
        .use-number-type {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          .item {
            width: 100%;
            .bottom {
              font-size: 1.3rem;
              line-height: 1.5;
              margin: 10px 0;
            }
          }
        }
      }
    }
  }
}
</style>
