<template>
  <article class="homepage">
    <index-banner></index-banner>
    <!--    <section class="section section-2">-->
    <!--      <template v-for="item in banner">-->
    <!--        <a class="image-link" :key="item.id" :href="item.pictureUrl">-->
    <!--          <img :src="item.videoUrl" :alt="item.speaker">-->
    <!--        </a>-->
    <!--      </template>-->
    <!--    </section>-->
    <index-usage :isWhiteBg="isMobile"></index-usage>
    <section class="section section-3">
      <div class="container">
        <div class="section-content">
          <h2 class="section-headline">
            <span class="headline--num">3</span>大环节 <span class="headline--num">12</span>大服务<br>
          </h2>
          <h3 class="headline--main">合同全生命周期智能管理服务</h3>
          <el-row v-if="!isMobile" class="process">
            <el-col :span="6" class="block">
              <h3 class="headline">签约前</h3>
              <h4 class="headline--main__sub">·实名认证</h4>
              <p class="comment">公安局、工商局、银联多系统交叉验证，三大权威CA机构颁发数字证书</p>
              <h4 class="headline--main__sub">·合同起草</h4>
              <p class="comment">多人在线编辑，合同智能比对，合同起草过程清晰可控</p>
              <h4 class="headline--main__sub">·智能审批</h4>
              <p class="comment">建立合同智能化审批流程，可添加多个审批人，审批完成后自动发起签署</p>
            </el-col>
            <el-col :span="6" class="block">
              <h3 class="headline">签约中</h3>
              <h4 class="headline--main__sub">·模板智能匹配</h4>
              <p class="comment">根据签约人信息智能匹配相应合同模板</p>
              <h4 class="headline--main__sub">·模板批量上传发送</h4>
              <p class="comment">模板只需填充关键词，可一键生成上千份合同</p>
              <h4 class="headline--main__sub">·意愿验证</h4>
              <p class="comment">活体识别、人脸比对、签约密码、短信验证码等方式进行校验</p>
              <h4 class="headline--main__sub">·在线实时签署</h4>
              <p class="comment">与国家授时中心同步的可信时间戳，区块链技术存证，合同内容无法篡改</p>
            </el-col>
            <el-col :span="6" class="block">
              <h3 class="headline">签约后</h3>
              <h4 class="headline--main__sub">·合同管理</h4>
              <p class="comment">按合同类别、时间等规则自动归档，多标签检索下载，线上查验合同真伪</p>
              <h4 class="headline--main__sub">·履约提醒</h4>
              <p class="comment">合同履约智能跟进，经营明细预警</p>
            </el-col>
            <el-col :span="6" class="block">
              <h3 class="headline">签约全程</h3>
              <h4 class="headline--main__sub">·法律服务</h4>
              <p class="comment">合同风险点智能审核，证据链保全、出证，提供诉讼支援</p>
              <h4 class="headline--main__sub">·印章管控</h4>
              <p class="comment">设立多级账号，<br>印章权限严格管控</p>
              <h4 class="headline--main__sub">·纸质合同AI识别</h4>
              <p class="comment">授权后，可将存量纸质合同AI识别为电子合同，统一管理</p>
            </el-col>
          </el-row>
          <button
            v-if="!isMobile"
            style="margin-top: 50px;"
            class="ssq-button-primary"
            @click="toDemo('hp_indexlifecycle')">
            免费试用
          </button>
          <div v-if="isMobile" class="mobile-content">
            <img src="@/assets/images/index/mobile1.png" alt="">
          </div>
        </div>
      </div>
    </section>
    <section class="section section-6" >
      <div class="container">
        <div class="section-content">
          <h2 class="section-headline headline--main">
            <span class="headline--num">4</span>大安全体系，合同终身保安全<br>
          </h2>
          <div v-if="!isMobile" class="content">
            <el-row>
              <el-col :xs="24" :sm="12">
                <div class="block">
                  <div class="block-header">
                    <div class="left">
                      <h4 class="headline">认证安全</h4>
                    </div>
                    <div class="right">
                      <h4 class="headline--main__sub">行业率先获得<br>全球七大安全认证</h4>
                    </div>
                  </div>
                  <div class="block-body">
                    <div class="item">
                      <div class="item--img">
                        <img src="@/assets/images/index/s7-2.jpg" alt="">
                      </div>
                      <div class="comment">ISO/IEC 27018<br>隐私数据保护认证</div>
                    </div>
                    <div class="item">
                      <div class="item--img">
                        <img src="@/assets/images/index/s7-1.jpg" alt="">
                      </div>
                      <div class="comment">ISO/IEC 27001<br>安全管理体系认证</div>
                    </div>
                    <div class="item">
                      <div class="item--img">
                        <img src="@/assets/images/index/s7-3.png" alt="">
                      </div>
                      <div class="comment">ISO 38505-1:2017<br>数据治理认证</div>
                    </div>
                    <div class="item">
                      <div class="item--img">
                        <svg class="icon" aria-hidden="true">
                          <use xlink:href="#icon-kexinyun"></use>
                        </svg>
                      </div>
                      <div class="comment">工信部<br>可信云认证</div>
                    </div>
                    <div class="item">
                      <div class="item--img">
                        <svg class="icon" aria-hidden="true">
                          <use xlink:href="#icon-gonganbu"></use>
                        </svg>
                      </div>
                      <div class="comment">公安部信息系统<br>安全等级保护<br>三级认证</div>
                    </div>
                    <div class="item">
                      <div class="item--img">
                        <img src="@/assets/images/index/s7-4.jpg" alt="">
                      </div>
                      <div class="comment">云计算SaaS<br>服务能力符合性<br>评估三级认证</div>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12">
                <div class="block">
                  <div class="block-header">
                    <div class="left">
                      <h4 class="headline">合规安全</h4>
                    </div>
                    <div class="right">
                      <h4 class="headline--main__sub">参与行业标准制定</h4>
                    </div>
                  </div>
                  <div class="block-body">
                    <div class="item">
                      <div class="item--img">
                        <svg class="icon" aria-hidden="true">
                          <use xlink:href="#icon-ca"></use>
                        </svg>
                      </div>
                      <div class="comment">三大权威CA机构<br>合作支持</div>
                    </div>
                    <div class="item">
                      <div class="item--img">
                        <svg class="icon" aria-hidden="true">
                          <use xlink:href="#icon-jilu"></use>
                        </svg>
                      </div>
                      <div class="comment">全流程记录存证<br>全过程可出证</div>
                    </div>
                    <div class="item">
                      <div class="item--img">
                        <svg class="icon" aria-hidden="true">
                          <use xlink:href="#icon-sifa"></use>
                        </svg>
                      </div>
                      <div class="comment">对接司法流程<br>全系统司法鉴定</div>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12">
                <div class="block">
                  <div class="block-header">
                    <div class="left">
                      <h4 class="headline">运营安全</h4>
                    </div>
                    <div class="right">
                      <h4 class="headline--main__sub">&nbsp;&nbsp;&nbsp;可用性不低于99.99%，<br>支持每秒6000+次签署</h4>
                    </div>
                  </div>
                  <div class="block-body">
                    <div class="item">
                      <div class="item--img">
                        <svg class="icon" aria-hidden="true">
                          <use xlink:href="#icon-gaobingfa"></use>
                        </svg>
                      </div>
                      <div class="comment">高并发<br>每秒6000+次签署</div>
                    </div>
                    <div class="item">
                      <div class="item--img">
                        <svg class="icon" aria-hidden="true">
                          <use xlink:href="#icon-gaokeyong"></use>
                        </svg>
                      </div>
                      <div class="comment">高可用<br>7X24小时不间断运维<br>服务可用性99.99%</div>
                    </div>
                    <div class="item">
                      <div class="item--img">
                        <svg class="icon" aria-hidden="true">
                          <use xlink:href="#icon-gaokekao"></use>
                        </svg>
                      </div>
                      <div class="comment">高可靠<br>同城双活,异地容灾<br>无宕机天数1900天+</div>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12">
                <div class="block">
                  <div class="block-header">
                    <div class="left">
                      <h4 class="headline">使用安全</h4>
                    </div>
                    <div class="right">
                      <h4 class="headline--main__sub">&nbsp;&nbsp;金融级三层密钥加密，<br>合同数据一文一密加密</h4>
                    </div>
                  </div>
                  <div class="block-body">
                    <div class="item">
                      <div class="item--img">
                        <svg class="icon" aria-hidden="true">
                          <use xlink:href="#icon-shujujiami"></use>
                        </svg>
                      </div>
                      <div class="comment">数据加密<br>金融级三层密钥加密<br>实现数据高强度保护</div>
                    </div>
                    <div class="item">
                      <div class="item--img">
                        <svg class="icon" aria-hidden="true">
                          <use xlink:href="#icon-qukuailian"></use>
                        </svg>
                      </div>
                      <div class="comment">区块链技术<br>保证数据不可纂改<br>实时存证</div>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
            <button
              v-if="!isMobile"
              class="ssq-button-primary"
              @click="toDemo('hp_indexanquan')">
              免费试用
            </button>
          </div>
          <div v-if="isMobile" class="mobile-content">
            <img src="@/assets/images/index/mobile3.png" alt="">
          </div>
        </div>
      </div>
    </section>
    <section class="section section-4">
      <div class="container">
        <h2 class="section-headline headline--main">为你定制行业专属解决方案</h2>
        <div v-if="!isMobile" class="section-content">
          <div class="hover-icon-wrap">
            <div class="icon-left">
              <i
                class="el-icon-arrow-left"
                @click="handleIconSlide(1)"
              ></i>
            </div>
            <div class="hover-icon">
              <ul
                :class="{active: activeSlide === 1}"
                class="icon-list">
                <li
                  :class="{active: activeIcon === 9}"
                  @mouseover="handleActiveIcon(9)"
                  @click="handleActiveIcon(9)">
                  <svg v-if="activeIcon === 9" class="icon" aria-hidden="true">
                    <use xlink:href="#icon-renli-fill"></use>
                  </svg>
                  <svg v-else class="icon" aria-hidden="true">
                    <use xlink:href="#icon-renli"></use>
                  </svg>
                  <p>人力资源</p>
                </li>
                <li
                  :class="{active: activeIcon === 1}"
                  @mouseover="handleActiveIcon(1)"
                  @click="handleActiveIcon(1)">
                  <svg v-if="activeIcon === 1" class="icon" aria-hidden="true">
                    <use xlink:href="#icon-gongyinglian-fill"></use>
                  </svg>
                  <svg v-else class="icon" aria-hidden="true">
                    <use xlink:href="#icon-gongyinglian"></use>
                  </svg>
                  <p>B2B供应链</p>
                </li>
                <li
                  :class="{active: activeIcon === 8}"
                  @mouseover="handleActiveIcon(8)"
                  @click="handleActiveIcon(8)">
                  <svg v-if="activeIcon === 8" class="icon" aria-hidden="true">
                    <use xlink:href="#icon-lingshou-fill"></use>
                  </svg>
                  <svg v-else class="icon" aria-hidden="true">
                    <use xlink:href="#icon-lingshou"></use>
                  </svg>
                  <p>零售制造</p>
                </li>
                <li
                  :class="{active: activeIcon === 2}"
                  @mouseover="handleActiveIcon(2)"
                  @click="handleActiveIcon(2)">
                  <svg v-if="activeIcon === 2" class="icon" aria-hidden="true">
                    <use xlink:href="#icon-wuliu-fill"></use>
                  </svg>
                  <svg v-else class="icon" aria-hidden="true">
                    <use xlink:href="#icon-wuliu"></use>
                  </svg>
                  <p>智慧物流</p>
                </li>
                <li
                  :class="{active: activeIcon === 4}"
                  @mouseover="handleActiveIcon(4)"
                  @click="handleActiveIcon(4)">
                  <svg v-if="activeIcon === 4" class="icon" aria-hidden="true">
                    <use xlink:href="#icon-yinhang-fill"></use>
                  </svg>
                  <svg v-else class="icon" aria-hidden="true">
                    <use xlink:href="#icon-yinhang"></use>
                  </svg>
                  <p>银行保险</p>
                </li>
              </ul>
              <ul
                :class="{active: activeSlide === 2}"
                class="icon-list">
                <li
                  :class="{active: activeIcon === 7}"
                  @mouseover="handleActiveIcon(7)"
                  @click="handleActiveIcon(7)">
                  <svg v-if="activeIcon === 7" class="icon" aria-hidden="true">
                    <use xlink:href="#icon-qiche-fill"></use>
                  </svg>
                  <svg v-else class="icon" aria-hidden="true">
                    <use xlink:href="#icon-qiche"></use>
                  </svg>
                  <p>汽车金融</p>
                </li>
                <li
                  :class="{active: activeIcon === 3}"
                  @mouseover="handleActiveIcon(3)"
                  @click="handleActiveIcon(3)">
                  <svg v-if="activeIcon === 3" class="icon" aria-hidden="true">
                    <use xlink:href="#icon-hulianwang-fill"></use>
                  </svg>
                  <svg v-else class="icon" aria-hidden="true">
                    <use xlink:href="#icon-hulianwang"></use>
                  </svg>
                  <p>互联网平台</p>
                </li>
                <li
                  :class="{active: activeIcon === 5}"
                  @mouseover="handleActiveIcon(5)"
                  @click="handleActiveIcon(5)">
                  <svg v-if="activeIcon === 5" class="icon" aria-hidden="true">
                    <use xlink:href="#icon-jinrong-fill"></use>
                  </svg>
                  <svg v-else class="icon" aria-hidden="true">
                    <use xlink:href="#icon-jinrong"></use>
                  </svg>
                  <p>互联网金融</p>
                </li>
                <li
                  :class="{active: activeIcon === 6}"
                  @mouseover="handleActiveIcon(6)"
                  @click="handleActiveIcon(6)">
                  <svg v-if="activeIcon === 6" class="icon" aria-hidden="true">
                    <use xlink:href="#icon-zuling-fill"></use>
                  </svg>
                  <svg v-else class="icon" aria-hidden="true">
                    <use xlink:href="#icon-zuling"></use>
                  </svg>
                  <p>租赁平台</p>
                </li>
                <li
                  :class="{active: activeIcon === 10}"
                  @mouseover="handleActiveIcon(10)"
                  @click="handleActiveIcon(10)">
                  <svg v-if="activeIcon === 10" class="icon" aria-hidden="true">
                    <use xlink:href="#icon-jiaoyu-fill"></use>
                  </svg>
                  <svg v-else class="icon" aria-hidden="true">
                    <use xlink:href="#icon-jiaoyu"></use>
                  </svg>
                  <p>教育</p>
                </li>
              </ul>
            </div>
            <div class="icon-right">
              <i
                class="el-icon-arrow-right"
                @click="handleIconSlide(2)"
              ></i>
            </div>
          </div>
          <div class="hover-content" >
            <div class="text">
              <p class="text-title headline--main__sub">{{ iconContent[activeIcon].title }}</p>
              <p class="text-para">{{ iconContent[activeIcon].text }}</p>
              <button
                class="ssq-button-primary transparent"
                @click="handleSolution(iconContent[activeIcon].solutionId)">
                查看解决方案
              </button>
            </div>
            <div class="img">
              <img
                :src="iconContent[activeIcon].src"
                :alt="iconContent[activeIcon].alt">
            </div>
          </div>
        </div>
        <div v-if="isMobile" class="mobile-content">
          <el-row :gutter="10">
            <el-col :xs="12" :sm="12" v-for="content in mobileContent" :key="content.solutionId">
              <div class="block" @click="handleSolution(content.solutionId)">
                <img style="width: 100%" :src="content.src_m" alt="">
                <div class="title">{{ content.title }}</div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="12">
              <div class="block">
                <div class="mask">敬请期待</div>
                <img style="width: 100%" src="@/assets/images/index/other.jpg">
                <div class="title">其他</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </section>
  </article>
</template>

<script>
import IndexBanner from '@/components/Index/Banner.vue';
import IndexUsage from '@/components/Index/Usage.vue';

import iconImg1 from '~/assets/images/index/b2b.jpg';
import iconImg2 from '~/assets/images/index/wuliu.jpg';
import iconImg3 from '~/assets/images/index/hulianwang.jpg';
import iconImg4 from '~/assets/images/index/yinhang.jpg';
import iconImg5 from '~/assets/images/index/hujin.jpg';
import iconImg6 from '~/assets/images/index/zuling.jpg';
import iconImg7 from '~/assets/images/index/qiche.jpg';
import iconImg8 from '~/assets/images/index/zhizao.jpg';
import iconImg9 from '~/assets/images/index/renli.jpg';
import iconImg10 from '~/assets/images/index/jiaoyu.png';
import mobile1 from '~/assets/images/index/B2B供应链-min.jpg';
import mobile2 from '~/assets/images/index/物流-min.jpg';
import mobile3 from '~/assets/images/index/互联网平台-min.jpg';
import mobile4 from '~/assets/images/index/银行保险-min.jpg';
import mobile5 from '~/assets/images/index/互联网金融-min.jpg';
import mobile6 from '~/assets/images/index/租赁平台-min.jpg';
import mobile7 from '~/assets/images/index/汽车金融-min.jpg';
import mobile8 from '~/assets/images/index/零售与制造-min.jpg';
import mobile9 from '~/assets/images/index/人力资源-min.jpg';

export default {
  name: 'homepage',
  components: {
    IndexBanner,
    IndexUsage,
  },
  head() {
    return {
      title: '电子合同签约_电子签名_在线合同签约_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同,电子合同签署,电子签名,电子签章',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签是业内领先的在线电子合同签署，电子签名，电子签章的云服务平台，提供多种行业专属解决方案。',
        },
      ],
    };
  },
  data() {
    return {
      src:
        'https://static.bestsign.cn/64c61620131cd65271e8b38623ff3fd1c7f4e9cd.mp4',
      activeSlide: 1,
      activeIcon: 2,
      iconContent: {
        '1': {
          solutionId: 27,
          src: iconImg1,
          src_m: mobile1,
          title: 'B2B供应链',
          text:
            '上上签助B2B供应链平台解决跨地域交易的合同签署难题。提升业务时效性的同时，以严谨的法律合规性保障信用流转。',
          alt: '供应链解决方案',
        },
        '2': {
          solutionId: 21,
          src: iconImg2,
          src_m: mobile2,
          title: '智慧物流',
          text:
            '上上签显著提升物流平台效率，托运方与承运方可快速签署合同，线上业务动态实时掌握。加强合同追溯和管理，避免纠纷，提高平台公信力。',
          alt: '物流解决方案',
        },
        '3': {
          solutionId: 20,
          src: iconImg3,
          src_m: mobile3,
          title: '互联网平台',
          text:
            '上上签连接C端与B端用户，助互联网商业、直播、问答、教育等平台提升B端、C端用户资源获取效率，优化用户的使用体验。',
          alt: '互联网平台解决方案',
        },
        '4': {
          solutionId: 12,
          src: iconImg4,
          src_m: mobile4,
          title: '银行保险',
          text:
            '上上签拥有远超纸质合同的法律严谨性，确保签约真实有效、无法抵赖，不可篡改。同时严格保护客户数据的安全隐私，符合银行、保险客户的强监管要求。',
          alt: '银行保险解决方案',
        },
        '5': {
          solutionId: 19,
          src: iconImg5,
          src_m: mobile5,
          title: '互联网金融',
          text:
            '上上签第三方电子合同存管为互联网金融平台加强风控，拥抱监管，赢得用户信任；超高签章并发量保证签署顺畅，区块链存证确保合同数据安全。',
          alt: '互联网金融解决方案',
        },
        '6': {
          solutionId: 16,
          src: iconImg6,
          src_m: mobile6,
          title: '租赁平台',
          text:
            '上上签电子签约让租房平台与房东之间、房东与租客之间迅速签署合同。帮助租房平台快速拓展房源，优化租住体验，同时降本提效，避免合同纠纷。',
          alt: '租赁平台解决方案',
        },
        '7': {
          solutionId: 19,
          src: iconImg7,
          src_m: mobile7,
          title: '汽车金融',
          text:
            '上上签助汽车金融与电商平台提升签署效率，扩大业务量，优化用户购车体验，加强风控，符合监管要求。',
          alt: '汽车金融解决方案',
        },
        '8': {
          solutionId: 18,
          src: iconImg8,
          src_m: mobile8,
          title: '零售制造',
          text:
            '上上签助传统制造企业智能化升级，合同签署与管理降本提效，打造企业线上运营闭环。多部门之间协同作业，优化企业管理模式。',
          alt: '零售业解决方案',
        },
        '9': {
          solutionId: 15,
          src: iconImg9,
          src_m: mobile9,
          title: '人力资源',
          text:
            '上上签模板嵌套功能可一次生成海量合同并送达多人，助力HR减少重复劳动，提高工作效率。线上轻松管理合同，减少企业人力与物力投入。',
          alt: '人力资源解决方案',
        },
        '10': {
          solutionId: 28,
          src: iconImg10,
          src_m: mobile9,
          title: '教育行业',
          text:
            '上上签助力在线教育机构效率升级，课程报名、学费分期、招生反馈等协议在线签署，提升用户体验的同时降低合同管理成本，实现总部对分校的统一管理。',
          alt: '教育解决方案',
        },
      },
      banner: [],
      solutions: [],
      headBanner: {},
      swiperOptionM: {
        navigation: {
          nextEl: '.ssq-button-next',
          prevEl: '.ssq-button-prev',
        },
      },
    };
  },
  computed: {
    mobileContent() {
      return Object.keys(this.iconContent).map(o => this.iconContent[o]);
    },
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: '' });
  },
  methods: {
    handleIconSlide(index) {
      this.activeSlide = index;
    },
    handleActiveIcon(index) {
      this.activeIcon = index;
    },
    handleSolution(id) {
      const solutions = {
        28: 'education',
        27: 'supplyChain',
        25: 'B2B',
        21: 'logistics',
        20: 'internet',
        19: 'finance',
        18: 'industry',
        17: 'fintech',
        16: 'rent',
        15: 'HR',
        12: 'insurance',
      };
      this.$router.push(`/solution/${solutions[id]}`);
    },
    toDemo(str) {
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
        this.$router.push(`/demo/mobile?${str}`);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
        this.$router.push(`/demo?${str}`);
      }
    },
  },
};
</script>
<style scoped lang="scss">
.homepage {
  text-align: center;
  padding-top: 60px;
  .section-1 {
    position: relative;
    padding: 0;
    width: 100%;
    height: 35vw;
    background-size: 100% 115%;
    background-position-y: -5vw;
    background-repeat: no-repeat;
    &.carousel {
      position: relative;
      .container {
        height: 100%;
        display: flex;
        align-items: center;
        text-align: left;
        max-width: 1180px;
        &.auto {
          height: auto;
          background-color: rgba(0, 0, 0, 0.7);
        }
        .main-text {
          padding-left: 20px;
          h3 {
            font-size: 36px;
            font-weight: 400;
            margin-bottom: 20px;
          }
          h4 {
            font-size: 16px;
            line-height: 26px;
            color: #626262;
            max-width: 480px;
            font-weight: 300;
            margin-bottom: 45px;
          }
          .ssq-button-primary {
            background: #00aa64;
          }
          .tips {
            color: #00aa64;
            font-size: 18px;
            margin-top: 30px;
            font-weight: 400;
          }
        }
      }
    }
    .ssq-button-primary {
      img {
        margin-left: 5px;
        width: 20px;
        vertical-align: bottom;
      }
    }
  }
  .section-2 {
    .image-link {
      padding: 0 10px;
      max-width: 600px;
      img {
        width: 100%;
      }
      &:first-child {
        padding-left: 0;
      }
      &:last-child {
        padding-right: 0;
      }
    }
  }
  .section-3 {
    padding-top: 5rem;
    .container {
      width: 100%;
      max-width: 90rem;
    }
    .process {
      background: url(~assets/images/index/bg2.png) no-repeat;
      background-size: 100% 100%;
      height: 30rem;
      .block {
        height: 100%;
        text-align: center;
        padding: 0 32px;
        .headline {
          padding-top: 2.25rem;
        }
        .headline--main__sub {
          color: #00a664;
          padding: 1rem 0 0.5rem 0;
        }
        .comment {
          line-height: 18px;
          font-size: 13px;
        }
      }
    }
    .headline--num {
      color: #00a664;
      font-family: source-han-sans-simplified-c, sans-serif;
    }
    .section-headline {
      font-size: 2rem;
      padding-bottom: 0.75rem;
      font-weight: 300;
    }
    .headline--main {
      padding-bottom: 3.75rem;
    }
  }
  .section-4 {
    padding: 5rem 0 5rem 0;
    .section-headline {
      padding-bottom: 6.25rem;
    }
    .hover-icon-wrap {
      width: 980px;
      margin: 0 auto;
      display: flex;
      align-items: center;
      .icon-left,
      .icon-right {
        font-size: 36px;
        cursor: pointer;
        &:hover {
          color: #00a664;
        }
      }
    }
    .hover-icon {
      flex: 1;
      margin: 0 auto;
      overflow: hidden;
      position: relative;
      .icon-list {
        float: left;
        display: flex;
        justify-content: space-around;
        width: 100%;
        height: 100%;
        transition: all 0.5s ease;
        &:first-child {
          transform: translate3d(-100%, 0, 0);
        }
        &:first-child.active {
          transform: translate3d(0, 0, 0);
        }
        &:last-child {
          position: absolute;
          top: 0;
          transform: translate3d(100%, 0, 0);
        }
        &:last-child.active {
          transform: translate3d(0, 0, 0);
        }
        li {
          flex: 1;
          border-bottom: 1px solid #bfbfbf;
          position: relative;
          .icon {
            font-size: 42px;
          }
          &:hover,
          &.active {
            cursor: pointer;
            border-color: #00a664;
            color: #00a664;
            p {
              color: #00a664;
            }
          }
        }
        p {
          margin: 20px auto 40px;
          color: #323232;
          font-size: 16px;
        }
      }
    }
    .hover-content {
      display: flex;
      width: 980px;
      margin: 7rem auto 0;
      .img {
        position: relative;
        width: 552px;
        height: 279px;
        margin-left: 30px;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .text {
        flex: 1;
        text-align: left;
        .text-title {
          margin: 1.25rem 0;
        }
        .text-para {
          color: #3f3f3f;
          margin-bottom: 2.5rem;
          text-align: justify;
          line-height: 26px;
        }
        .button {
          padding-left: 0;
        }
      }
    }
  }
  .section-6 {
    padding: 5rem 0 3rem 0;
    background-color: #f6f7f8;
    .headline--num {
      color: #00a664;
    }
    .section-headline {
      padding-bottom: 6.25rem;
    }
    .content {
      width: 1160px;
      margin: 0 auto;
      .block {
        height: 288px;
        margin-left: 20px;
        margin-right: 20px;
        margin-bottom: 50px;
        border: 1px solid #00a664;
        .block-header {
          display: flex;
          border-bottom: 1px solid #00a664;
          .left {
            position: relative;
            width: 210px;
            height: 73px;
            line-height: 73px;
            color: #fff;
            z-index: 2;
            &:after {
              position: absolute;
              top: 0;
              left: 0;
              content: '';
              width: 210px;
              border-top: 73px solid #00a664;
              border-right: 30px solid transparent;
              z-index: 1;
            }
            .headline {
              z-index: 3;
              position: absolute;
              left: 50%;
              transform: translateX(-50%);
              width: 100%;
            }
          }
          .right {
            flex: 1;
            justify-content: center;
            display: flex;
            align-items: center;
            h4 {
              width: 100%;
              position: relative;
              left: -3px;
            }
          }
        }
        .block-body {
          width: 100%;
          height: 215px;
          display: flex;
          justify-content: space-around;
          .item {
            margin-top: 60px;
            &--img {
              width: 65px;
              height: 50px;
              margin: 0 auto;
              display: flex;
              align-items: center;
              justify-content: center;
              padding-bottom: 0.75rem;
              img {
                width: 100%;
              }
            }
            .icon {
              font-size: 46px;
            }
            .comment {
              line-height: 18px;
            }
          }
        }
      }
    }
    .mobile-content {
      padding: 0 10px;
      img {
        width: 100%;
      }
    }
  }
}
@media (max-width: 1024px) {
  .homepage {
    .section-4 {
      .mobile-content {
        padding: 0 10px;
        .block {
          position: relative;
          margin-bottom: 15px;
          border-radius: 4px;
          .mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2);
            color: #fff;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          img {
            width: 100%;
            height: 150px;
          }
          .title {
            background-color: #eee;
            height: 35px;
            line-height: 35px;
            text-align: center;
            font-size: 14px;
          }
        }
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .homepage {
    padding-top: 56px;
    .headline--main {
      font-size: 1.75rem;
    }
    .section-2 {
      padding-top: 10px;
      .image-link {
        flex: 1;
        width: 33.3%;
        padding: 0 5px;
      }
    }
    .section-3 {
      .container {
        width: 100%;
        max-width: 100%;
      }
      .mobile-content {
        img {
          width: 100%;
        }
      }
    }
    .section-4 {
      .mobile-content {
        padding: 0 10px;
        .block {
          position: relative;
          margin-bottom: 15px;
          border-radius: 4px;
          .mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2);
            color: #fff;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          img {
            width: 100%;
            height: 100px;
          }
          .title {
            background-color: #eee;
            height: 35px;
            line-height: 35px;
            text-align: center;
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>
