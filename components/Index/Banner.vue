<template>
<div v-if="headBanner.bannerMainTitle">
    <div class="index-banner" :style="{backgroundImage: `url(${isEN?(isLandMobile ? headBanner.mobileImgUrl : headBanner.pcImgUrl):(isLandMobile ? headBanner.mobileImgUrl : headBanner.pcImgUrl)}`}">
    <div class="container">
      <div v-if="!isLandMobile" class="main-text">
        <div class="main-title" :class="{ 'en-main-title':isEN}">
          <h3>{{ headBanner.bannerMainTitle }}</h3>
         <h3>{{ headBanner.bannerMainTitle1 }}</h3>
        </div>
        
        <h4>{{headBanner.bannerSecondTitle1}}</h4>
        <h4>{{headBanner.bannerSecondTitle2}}</h4>
        <h4>{{headBanner.bannerThirdTitle}}</h4>
        <button  v-if="headBanner.bannerMainTitle &&!isLandMobile " class="ssq-button-primary" :class="{'ssq-button-primary-en':isEN}" @click="toReg">{{headBanner.bannerBtnTitle}}</button>
         
      </div>
      <div v-else class="main-text1">
        <div class="main-title" v-if="!isEN">
          <h3>{{ headBanner.bannerMainTitle }}</h3>
         <h3>{{ headBanner.bannerMainTitle1 }}</h3>
        </div>
        <div class="main-title" v-if="isEN" :class="{ 'en-main-title':isEN}">
          <h3>{{ headBanner.bannerMainTitle }}{{ headBanner.bannerMainTitle1 }}</h3>
        </div>
        <h4>{{headBanner.bannerSecondTitle1}}</h4>
        <h4>{{headBanner.bannerSecondTitle2}}</h4>
        <h4>{{headBanner.bannerThirdTitle}}</h4>
        <button  v-if="headBanner.bannerMainTitle" class="ssq-button-primary" :class="{'ssq-button-primary-en':isEN}" @click="toReg">{{headBanner.bannerBtnTitle}}</button>
      </div>
      <!-- <div class="littleBanner">
          <little-banner></little-banner>
        </div>  -->
    </div>
   
  </div>
</div>
<div v-else class="index-banner">
  <a class="banner-bkg" :href="headBanner.bannerLink" target="_blank">
    <img :src="headBanner.mobileImgUrl"  width="100%" v-if="isLandMobile">
    <img :src="headBanner.pcImgUrl"  width="100%" v-else/>
    
  </a>
</div>



</template>

<script>
import LittleBanner from '@/components/Index/LittleBanner.vue';
import Qs from 'qs';
import { jumpToEntRegister } from '@/assets/utils/toRegister';
export default {
  components: {
    LittleBanner,
  },
  data: () => ({
    headBanner: {
      //由于banner图片太大，加载慢，然后做了默认数据，此图片很小
      mobileImgUrl:
        'https://static.bestsign.cn:443/2b3f642e16104298c29e712b907b8f3c9250dc99.jpg',
      pcImgUrl:
        'https://static.bestsign.cn:443/3891d55079eb606f1ee2b707cff645abcc61a29.jpg',
    },
    // bannerButton: {},
    isEN: false,
  }),
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  methods: {
    async handleFetch() {
      // const headBanner = await this.$axios.get(
      //   '/www/api/web/new/getHeadBanner'
      // );
      const headBanner = {
        alt: '',
        bannerBtnTitle: this.isEN
          ? this.$t('header.material')
          : this.$t('header.use'),
        bannerLink:
          'https://app.ma.scrmtech.com/resources/ResourcePc/ResourcePcInfo?pf_uid=7161_1266&id=10414&pf_type',
        bannerMainTitle: this.$t('homeBanner.bigTitle1'),
        bannerMainTitle1: this.$t('homeBanner.bigTitle2'),
        bannerSecondTitle1: this.$tc('homeBanner.littleTitle1'),
        bannerSecondTitle2: this.$tc('homeBanner.littleTitle2'),
        // bannerThirdTitle: this.$tc('homeBanner.littleTitle', 2),
        mobileImgUrl: this.isEN
          ? 'https://static.bestsign.cn:443/b5780d8a7b877a1263f923718571162810489f11.jpg'
          : 'https://static.bestsign.cn:443/524ce013345b250cee2a3f055af4876f1f97e70e.jpg',
        pcImgUrl: this.isEN
          ? 'https://static.bestsign.cn:443/4bad6af8c5e23350578af22d9850a6327b5a02be.jpg'
          : 'https://static.bestsign.cn:443/44521bbbf506b8e1ffab65271d4385f54ae06f03.jpg',
      };

      this.headBanner = headBanner || {};
    },
    toDemo(str) {
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
        this.$router.push(`/demo?${str}`);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
        this.$router.push(`/demo?${str}`);
      }
    },
    toReg() {
      if (this.isEN) {
        this.$router.push({
          path: `/${this.language}/material`,
        });
      } else {
        jumpToEntRegister();
      }
    },
  },
  mounted() {
    this.handleFetch();
  },
  created() {
    console.log(this.isEN);
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
};
</script>

<style scoped lang="scss">
.banner-bkg {
  width: 100%;
}
.index-banner {
  background-color: #f2f2f4;
  //   background: url('./img/PC-banner.jpg') no-repeat;
  position: relative;
  padding: 0;
  width: 100%;
  //   height: 53.5rem;
  height: 44vw;
  background-size: cover;
  .feature {
    margin-bottom: 20px;
    &-item {
      display: block;
      line-height: 1.5;
      color: #fff;
      i {
        color: #00aa64;
        margin-right: 8px;
      }
    }
  }
  .container {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    text-align: left;
    // max-width: 1480px;
    .littleBanner {
      position: absolute;
      bottom: 10px;
      // margin: 0 5rem;
      width: 100%;
    }

    &.auto {
      height: auto;
      background-color: rgba(0, 0, 0, 0.5);
    }
    .main-text {
      // position: relative;
      /*top: 5px;*/
      /*left: 50px;*/
      //   margin-top: 150px;
      //   padding-left: 8%;
      width: 70%;
      .main-title {
        font-size: 2.875rem;
        font-weight: 500;
        margin-bottom: 1.25rem;
        color: #fff;
        padding-top: 0;
        letter-spacing: 3px;
        h3 {
          margin-bottom: 15px;
        }
      }
      .en-main-title {
        letter-spacing: 0px;
      }
      h4 {
        font-size: 1.2rem;
        line-height: 28px;
        // max-width: 680px;
        font-weight: 400;
        // margin-bottom: 1.5rem;
        // display: inline-block;
        padding: 10px 0;
        color: #fff;
      }
      .ssq-button-primary {
        margin: 20px 0px;
        background: $-color-main;
        width: 170px;
        height: 40px;
        line-height: 40px;
        display: block;
        padding: 0 15px;
        font-weight: 600;
      }
      .ssq-button-primary-en {
        font-size: 18px;
      }
      .main-text-img {
        margin-top: 8rem;
        width: 75%;
      }
      .main-text-img-mp {
        margin-top: 2rem;
        width: 75%;
      }
      .banner-text {
        width: 26vw;
      }
      .tips {
        color: #00aa64;
        font-size: 18px;
        margin-top: 30px;
        font-weight: 400;
      }
    }
  }
  .container1 {
    width: 100%;
    display: flex;
    justify-content: space-evenly;
    margin-top: 16px;
  }
  .ssq-button-primary {
    img {
      margin-left: 5px;
      width: 20px;
      vertical-align: bottom;
    }
  }
  .banner-wrapper {
    // position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    background-color: transparent;
    color: #fff;
    .container {
      padding: 12px 0;
      display: flex;
    }
    .link-wrapper {
      flex: 1;
      max-width: 25%;
      border-left: 1px solid #fff;
      border-radius: 4px;
      background-color: #ffffff;
      box-shadow: 0 4px 16px 0 rgba(47, 67, 82, 0.1);
      display: inline-block;
      position: relative;
      margin-left: 5px;
      margin-right: 5px;
      padding-left: 10px;
      &:hover {
        -webkit-box-shadow: #ccc 10px 10px 10px;
        box-shadow: #ccc -0px 10px 10px;
      }
      &:last-child {
        border-right: 1px solid #fff;
      }
    }
    .image-link {
      display: inline-block;
      color: rgb(17, 15, 15);
      padding: 30px 24px;
      line-height: 23px;
      text-align: left;
      vertical-align: middle;
      height: 130px;
      width: 80%;
      h3 {
        margin-bottom: 12px;
        font-size: 16px;
        color: #515151;
        &:hover {
          color: #00aa64;
        }
      }
      span {
        font-size: 14px;
        color: #65697f;
        &:hover {
          color: #00aa64;
        }
      }
    }
    .bannerImg {
      width: 60px;
    }
  }
}
@media screen and (max-width: 767px) {
  .index-banner {
    background-size: 100% 100%;
    height: 170vw;
    .feature {
      margin-bottom: 30px;
      &-item {
        line-height: 24px;
        &:last-child {
          padding-left: 14px;
        }
      }
    }
    .container1 {
      margin-top: 18px;
      flex-wrap: wrap;
    }
    .container {
      display: block;
      position: relative;
      height: 100%;
      align-items: center;
      text-align: center;
      .littleBanner {
        position: relative;
        top: 12rem;
        left: auto;
        width: 100%;
        margin: 0;
      }
    }
    .main-text1 {
      text-align: center;
      padding-left: 0 !important;
      position: relative;
      .main-title {
        padding-top: 70px;
        color: #fff;
        font-size: 1.8rem;
        font-weight: 500;
        h3 {
          margin-bottom: 30px;
        }
      }
      .en-main-title {
        padding-top: 100px;
      }
      h4 {
        color: #fff;
        font-size: 1.2rem;
        line-height: 1.5;
        margin-top: 15px;
        font-weight: 500;
      }
      .ssq-button-primary {
        font-size: 16px;
        position: absolute;
        left: 50%;
        top: 90vw;
        transform: translateX(-50%);
        height: 40px;
        width: 150px;
        line-height: 1px;
      }
      .ssq-button-primary-en {
        width: 170px;
        top: 110vw;
        font-weight: 600;
        font-size: 18px;
      }
    }
    .banner-wrapper {
      //   bottom: -92px;
      position: relative;
      .container {
        padding: 16px 0;
        display: flex;
      }
      .link-wrapper {
        flex: none;
        width: calc(50% - 10px);
        max-width: none;
        margin-bottom: 10px;
        &:first-child {
          border-left: none;
        }
        &:last-child {
          border-right: none;
        }
      }
    }
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .index-banner {
    .container {
      .main-text {
        width: 100%;
      }
    }
    position: relative;
    height: 65vw;
    .feature {
      margin-bottom: 30px;
      &-item {
        line-height: 24px;
        &:last-child {
          padding-left: 14px;
        }
      }
    }
    .container {
      justify-content: center;
      text-align: center;
      align-items: flex-start;
      .ssq-button-primary {
        margin: 0 auto;
      }
    }
  }
}
@media only screen and (min-width: 992px) and (max-width: 1400px) {
  .index-banner {
    .container {
      .main-text {
      }
    }
  }
}
</style>
