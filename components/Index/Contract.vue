<template>
  <section class="index-contract section">
    <div class="container">
      <h2 class="section-headline headline--main">合同全生命周期管理，更高效安全</h2>
      <div class="content">
        <div class="block_left_container" v-if="!isMobile">
          <div class="block_left" v-if="number==1">
         <h1>智能档案</h1>
         <p>预先收集所有与合同所需的签署信息（如学历证书、职业证书等）。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
        </div>
        <div class="block_left" v-if="number==2">
         <h1>智能模板</h1>
         <p>将变量字段导入合同模板，一键生成多份合同。合同模板智能匹配签署人信息生成相应合同。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
        </div>
        <div class="block_left" v-if="number==3">
         <h1>智能起草</h1>
         <p>多人、多方共同协作编辑合同内容。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
        </div>
        <div class="block_left" v-if="number==4">
         <h1>智能收发</h1>
         <p>多份文件一次发送一次签署、多方签署一次发送等功能，一次搞定合同发送。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
        </div>
        <div class="block_left" v-if="number==5">
         <h1>智能签署</h1>
         <p>批量签署、自动签署、授权签署等多种方式。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
        </div>
        <div class="block_left" v-if="number==6">
         <h1>智能审批</h1>
         <p>建立合同智能审批流程，添加多个审批人，审批智能提醒，审批完成后自能发起签署。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
        </div>
        <div class="block_left" v-if="number==7">
         <h1>智能管理</h1>
         <p>合同状态管理、签署到期提醒、履约提醒、归档管理、导出下载、统计报表等功能，合同数据管理可视化。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
        </div>
        <div class="block_left" v-if="number==8">
         <h1>合规管理</h1>
         <p>在实名认证、意愿验证、时间戳防篡改等基础之上，通过签署人身份强一致性校验、刷脸校验、设定手写签名、强制阅读合同内容等功能，加强签署流程合规。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
        </div>
        <div class="block_left" v-if="number==9">
         <h1>权限管理</h1>
         <p>模板权限管理、角色管理、印章管理等功能，建立合同分级授权管理体系。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
        </div>
        <div class="block_left" v-if="number==10">
         <h1>灵活接口</h1>
         <p>全面兼容企业各种内部系统（ERP、CRM、OA、EHR等），与业务数据全面打通。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
        </div>
        <div class="block_left" v-if="number==11">
         <h1>行业包配置</h1>
         <p>不同行业不同行业包配置，更适合具体业务场景。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
        </div>
        </div>
        <div class="contract_dsc" v-if="isMobile">
          涵盖智能档案、智能模板、智能起草、智能收发、智能签署、智能审批、智能管理、合规管理、权限管理等合同管理的所有环节，让企业管理如臂使指。
        </div>
        <div class="block_right" v-if="!isMobile">
          <div class="icon_container">
            <div class="icon_1" :class="{ newStyle:1===number}" @mouseenter="check(1)">
            <span class="iconfont icon-xingzhuang44"></span>
          </div>
          <div class="title">智能档案</div>
          </div>
          <div class="icon_container">
            <div class="icon_1" :class="{ newStyle:2===number}" @mouseenter="check(2)">
              <span class="iconfont icon-xingzhuang32"></span>
            </div>
            <div class="title">智能模板</div>
          </div>
          <div class="icon_container">
            <div class="icon_1" :class="{ newStyle:3===number}" @mouseenter="check(3)">
              <span class="iconfont icon-xingzhuang45"></span>
            </div>
            <div class="title">智能起草</div>
          </div>
          <div class="icon_container">
            <div class="icon_1" :class="{ newStyle:4===number}" @mouseenter="check(4)">
              <span class="iconfont icon-xingzhuang33"></span>
            </div>
            <div class="title">智能收发</div>
          </div>
          <div class="icon_container">
            <div class="icon_1" :class="{ newStyle:5===number}" @mouseenter="check(5)">
              <span class="iconfont icon-xingzhuang36"></span>
            </div>
            <div class="title">智能签署</div>
          </div>
          <div class="icon_container">
            <div class="icon_1" :class="{ newStyle:6===number}" @mouseenter="check(6)">
              <span class="iconfont icon-xingzhuang34"></span>
            </div>
            <div class="title">智能审批</div>
          </div>
          <div class="icon_container">
            <div class="icon_1" :class="{ newStyle:7===number}" @mouseenter="check(7)">
              <span class="iconfont icon-xingzhuang47"></span>
            </div>
            <div class="title">智能管理</div>
          </div>
          <div class="icon_container">
            <div class="icon_1" :class="{ newStyle:8===number}" @mouseenter="check(8)">
              <span class="iconfont icon-xingzhuang39"></span>
            </div>
            <div class="title">合规管理</div>
          </div>
          <div class="icon_container">
            <div class="icon_1" :class="{ newStyle:9===number}" @mouseenter="check(9)">
              <span class="iconfont icon-xingzhuang48"></span>
            </div>
            <div class="title">权限管理</div>
          </div>
          <div class="icon_container">
            <div class="icon_1" :class="{ newStyle:10===number}" @mouseenter="check(10)">
              <span class="iconfont icon-xingzhuang49"></span>
            </div>
            <div class="title">灵活接口</div>
          </div>
          <div class="icon_container">
            <div class="icon_1" :class="{ newStyle:11===number}" @mouseenter="check(11)">
              <span class="iconfont icon-xingzhuang37"></span>
            </div>
            <div class="title">行业包配置</div>
          </div>
        </div>
        <div class="block_right" v-else>
          <div class="icon_container">
            <div class="icon_1">
            <span class="iconfont icon-xingzhuang44"></span>
          </div>
          <div class="title">智能档案</div>
          </div>
          <div class="icon_container">
            <div class="icon_1">
              <span class="iconfont icon-xingzhuang32"></span>
            </div>
            <div class="title">智能模板</div>
          </div>
          <div class="icon_container">
            <div class="icon_1">
              <span class="iconfont icon-xingzhuang45"></span>
            </div>
            <div class="title">智能起草</div>
          </div>
          <div class="icon_container">
            <div class="icon_1">
              <span class="iconfont icon-xingzhuang33"></span>
            </div>
            <div class="title">智能收发</div>
          </div>
          <div class="icon_container">
            <div class="icon_1">
              <span class="iconfont icon-xingzhuang36"></span>
            </div>
            <div class="title">智能签署</div>
          </div>
          <div class="icon_container">
            <div class="icon_1">
              <span class="iconfont icon-xingzhuang34"></span>
            </div>
            <div class="title">智能审批</div>
          </div>
          <div class="icon_container">
            <div class="icon_1">
              <span class="iconfont icon-xingzhuang47"></span>
            </div>
            <div class="title">智能管理</div>
          </div>
          <div class="icon_container">
            <div class="icon_1">
              <span class="iconfont icon-xingzhuang39"></span>
            </div>
            <div class="title">合规管理</div>
          </div>
          <div class="icon_container">
            <div class="icon_1">
              <span class="iconfont icon-xingzhuang48"></span>
            </div>
            <div class="title">权限管理</div>
          </div>
          <div class="icon_container">
            <div class="icon_1">
              <span class="iconfont icon-xingzhuang49"></span>
            </div>
            <div class="title">灵活接口</div>
          </div>
          <div class="icon_container">
            <div class="icon_1">
              <span class="iconfont icon-xingzhuang37"></span>
            </div>
            <div class="title">行业包配置</div>
          </div>
		  <i></i>
		  <i></i>
		  <i></i>
		  <i></i>
		  <i></i>
		  <i></i>
		  <i></i>
		  <i></i>
		  <i></i>
		  <i></i>
		  <i></i>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import Qs from 'qs';
export default {
  name: 'index-contract',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      number: 1,
    };
  },
  methods: {
    toReg() {
      const query = sessionStorage.getItem('query');
      const href = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push([
            '_trackEvent',
            'click_register_mobile',
            'click',
            href,
          ]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_register_pc', 'click', href]);
      }
      const url =
        window.location.host.indexOf('cn') > -1
          ? 'https://ent.bestsign.cn/register'
          : 'https://ent.bestsign.info/register';
      window.open(url + '?' + 'index_saas&' + query);
    },
    toDemo() {
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/demo',
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
          zhinengguanli: '',
        },
      });
    },
    check(num) {
      this.number = num;
    },
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
};
</script>

<style scoped lang="scss">
.index-contract {
  .section-headline {
    color: #090909;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
  }
  //   padding-bottom: 5rem;
  background-color: #fff;
  .content {
    display: flex;
    // flex-wrap: wrap;
    justify-content: center;
    .block_left_container {
      // flex: 1;
      margin: 0 2% 0;
      padding: 30px 0;
      width: 400px;
      img {
        width: 90px;
        height: 85px;
      }
      h1 {
        text-align: left;
        color: #1d1d1f;
        font-size: 1.6rem;
        font-weight: 400;
      }
      p {
        max-width: 80%;
        // margin: 0 auto;
        line-height: 1.5;
        color: #86868b;
        text-align: left;
      }
      .ssq-button-primary {
        // display: inline-block;
        margin: 2.5rem 0;
      }
      .tips {
        font-size: 14px;
        color: #00aa64;
      }
    }
    .block_right {
      margin: 0 10px 0;
      padding: 30px 0;
      display: flex;
      flex-wrap: wrap;
      width: 585px;
      .icon_container {
        margin: 1.5rem 4% 0;
        i {
          width: 6rem;
          height: 6rem;
          margin-right: 6rem;
        }
        .icon_1 {
          // display: flex;
          background-color: #fff;
          /* text-align: center; */
          /* vertical-align: middle; */
          display: flex;
          justify-content: center;
          align-items: center;
          border: 2px solid #f8f8f8;
          border-radius: 20px;
          padding: 1rem;
          // margin: 4%;
          width: 6rem;
          height: 6rem;
          cursor: pointer;
          .iconfont {
            font-size: 2.5rem;
            color: #3f3f3f;
          }
        }
        .title {
          font-size: 1.2rem;
          margin: 1rem 0;
          text-align: center;
        }
        .newStyle {
          // display: flex;
          background-color: #3f3f3f;
          //   background-color: #fff;
          /* text-align: center; */
          /* vertical-align: middle; */
          /* vertical-align: middle; */
          display: flex;
          justify-content: center;
          align-items: center;
          border: 2px solid #f2f2f2;
          border-radius: 20px;
          padding: 1rem;
          margin: 3%;
          width: 6rem;
          height: 6rem;
          .iconfont {
            font-size: 2.5rem;
            color: #fff;
          }
        }
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .index-contract {
    padding-top: 12rem;
    .section-headline {
      color: #090909;
      font-size: 1.9rem;
      line-height: 2.5rem;
      font-weight: 400;
    }
    .content {
      flex-wrap: wrap;
      .block_left_container {
        img {
          width: 50px;
          height: 45px;
        }
        h3 {
          font-size: 18px;
        }
        p {
          max-width: 75%;
          height: 30%;
        }
        .tips {
          max-width: 100%;
          font-size: 12px;
        }
      }
      .contract_dsc {
        font-size: 14px;
        color: #86868b;
        line-height: 2rem;
      }

      .block_right {
        margin: 0 10px 30px;
        padding: 30px 0;
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        justify-content: space-between;
        .icon_container {
          margin: 1.5rem 4.5% 0;
          .icon_1 {
            // display: flex;
            background-color: #fff;
            /* text-align: center; */
            /* vertical-align: middle; */
            border: 1px solid #cfcfcf;
            border-radius: 7px;
            padding: 1rem;
            margin: 1.8%;
            width: 5rem;
            height: 5rem;
            .iconfont {
              font-size: 2.5rem;
            }
          }
          .title {
            font-size: 1.4rem;
            margin: 1rem 0;
            text-align: center;
          }
        }
      }
    }
  }
}
@media screen and (min-width: 1088px) {
  .container {
    max-width: 100%;
    width: 100%;
  }
}
</style>
