<template>
  <section class="index-news section" :style="{background: isWhiteBg ? '#fff' : '#fff'}">
    <div class="container">
      <h2 class="section-headline headline--main">最新动态</h2>
	  <div class="news-list"  v-if="!isMobile">
		  <template v-for="(item,index) in newsList" >
        <!-- <nuxt-link class="news-content" :to="`/news/${item.dynamicType == 'company'?'list-company':'list-industries'}`" :key="index">
				<div @click="goDetail(item)">
					<div class="news-left">
						<img :src="item.imgUrl" alt="">
					</div>
					<div class="news-right">
						<p class="title">{{item.dynamicTitle}}</p>
						<p class="desc">{{item.tdkD}}</p>
            <div class="foot">
              <p class="type">{{categoryName(item.dynamicType)}}</p>
						<p class="date">{{formatDate(item.releaseTime)}}</p>
            </div>
					</div>
				</div>
        </nuxt-link> -->
        <!-- <nuxt-link class="news-content" :to="`/news/${item.dynamicType == 'company'?'list-company':'list-industries'}`" :key="index"> -->
        <div class="news-content" :key="index">
          <div @click="goDetail(item)" >
            <nuxt-link :to="`/news/detail/${item.id}`">
            <div class="news-left">
						<img :src="item.imgUrl" alt="">
					</div>
            </nuxt-link>
					<div class="news-right">
						<a :href="`/news/detail/${item.id}`" class="title">{{item.dynamicTitle}}</a>
						<a :href="`/news/detail/${item.id}`" class="desc">{{item.tdkD}}</a>
            <div class="foot">
              <a :href="`/news/list-${item.dynamicType}`" class="type">{{categoryName(item.dynamicType)}}</a>
						<a :href="`/news/detail/${item.id}`" class="date">{{formatDate(item.releaseTime)}}</a>
            </div>
					</div>
				</div>
        </div>
				
        <!-- </nuxt-link> -->
			
		</template>
	  </div>
	  <div class="news-list" v-if="isMobile">
			<template v-for="(item,index) in newsList" >
        <!-- <nuxt-link :to="`/news/detail/${item.id}`" :key="index"> -->
				<div class="news-content" :key="index" @click="goDetail(item)">
					<p class="title">{{item.dynamicTitle}}</p>
					<span class="el-icon-arrow-right"></span>
				</div>
        <!-- </nuxt-link> -->
			</template>
	  </div>
    </div>
  </section>
</template>

<script>
import moment from 'moment';
export default {
  name: 'Indexnews',
  props: {
    isWhiteBg: {
      type: Boolean,
      default: false,
    },
    demo: {
      type: Boolean,
      default: false,
    },
    // newsList: {
    //   type: Array,
    //   default: () => [],
    // },
    // typeList: {
    //   type: Array,
    //   default: () => [],
    // },
  },
  // async asyncData({ app }) {
  //   const res1 = await app.$axios.get(
  //     `/www/api/web/getDynamic?pageNum=1&pageSize=4`
  //   );
  //   const res0 = await app.$axios.get('/www/api/web/category/dynamic');
  //   const filterResult = res1.data.filter(
  //     o => moment() > moment(o.releaseTime)
  //   );
  //   return {
  //     newsList: filterResult,
  //     typeList: res0,
  //   };
  // },
  data() {
    return {
      activeName: '18',
      newsList: [],
      typeList: [],
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {
    goDetail(item) {
      this.$router.push(`/news/detail/${item.id}`);
    },
    formatDate(date) {
      // let time = new Date(date.replace(new RegExp(/-/gm), '/'));
      // return (
      //   time.getFullYear() +
      //   '-' +
      //   ('0' + (time.getMonth() + 1)).substr(-2) +
      //   '-' +
      //   time.getDate()
      // );
      return moment(date).format('YYYY-MM-DD');
    },
    categoryName(id) {
      const item = this.typeList.find(o => o.code === id);
      return item && item.codeValue;
    },
    getNews() {
      this.$axios({
        // url: '/www/api/web/college2?column=friendLink&pageNum=1&pageSize=10',
        url: '/www/api/web/getDynamic?pageNum=1&pageSize=4',
        method: 'get',
      }).then(res => {
        const link = res;
        this.newsList = link.data.filter(o => moment() > moment(o.releaseTime));
        console.log(res);
      });
    },
    getType() {
      this.$axios({
        // url: '/www/api/web/college2?column=friendLink&pageNum=1&pageSize=10',
        url: '/www/api/web/category/dynamic',
        method: 'get',
      }).then(res => {
        const link = res;
        this.typeList = link;
        console.log(res);
      });
    },
  },
  mounted() {
    this.getNews();
    this.getType();
  },
};
</script>

<style lang="scss" scoped>
.landscape .paragraph {
  .section-headline {
    color: #333;
    font-weight: 400;
  }
}
.section-news-bg {
  width: 84rem;
  height: 32rem;
  margin: 0 auto;
  background: linear-gradient(to right, #06df96, #12c8b4);
}
.index-news {
  .news-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    margin: 0 auto;
    padding: 0 40px;
    .news-content {
      //   width: 45%;
      margin-bottom: 2rem;
      //   display: flex;
      cursor: pointer;
      .news-left {
        // flex: 1;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 210px;
        float: left;
        img {
          width: 100%;
        }
      }
      .news-right {
        text-align: left;
        padding-left: 1rem;
        // flex: 1.5;
        float: left;
        width: 300px;
        .title {
          font-weight: 600;
          font-size: 16px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: normal;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          max-width: 460px;
          line-height: 20px;
          //   min-height: 40px;
          color: #515151;
        }
        .desc {
          margin-top: 24px;
          font-size: 14px;
          letter-spacing: 0;
          line-height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: normal;
          /* autoprefixer: off */
          -webkit-box-orient: vertical;
          display: -webkit-box;
          /* autoprefixer: on */
          -webkit-line-clamp: 2;
          max-width: 460px;
          color: #86868b;
          height: 40px;
        }
        .foot {
          display: flex;
          justify-content: space-between;
          .type {
            color: #c0bfbf;
            margin-top: 10px;
          }
          .date {
            color: #c0bfbf;
            margin-top: 10px;
          }
        }
      }
    }
  }

  .el-tabs__nav {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  .el-tabs__active-bar {
    display: none;
  }
  .el-tabs__item {
    font-size: 16px;
    padding: 0;
    text-align: center;
    font-weight: 400;
    color: #fff;
    opacity: 0.8;
    &.is-active {
      color: #fff;
      border-bottom: 2px solid #fff;
      opacity: 1;
    }
    &:hover {
      color: #fff;
      opacity: 1;
    }
  }
  .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: #fff;
    opacity: 0.8;
  }
}
@media screen and (max-width: 768px) {
  .index-news {
    .el-tabs__nav-wrap::after {
      bottom: 2px;
    }
    .container {
      padding: 0;
    }
    .news-list {
      flex-wrap: wrap;
      .news-right {
        width: 170px;
        margin-top: 30px;
      }
    }
    .el-tabs__item {
      transform: scale(0.9);
      font-size: 12px;
      color: #fff;
    }
  }
}
</style>
<style scoped lang="scss">
.index-news {
  text-align: center;
  //   padding: 5rem 0;
  background-color: #f7f8fa;
  .container {
    width: 100%;
    max-width: 1200px;
  }
  .ssq-button-more {
    a {
      color: #00a664;
    }
  }
  .section-headline {
    color: #333;
    line-height: 2rem;
    .logo-text {
      width: 7.25rem;
      height: 2.25rem;
      vertical-align: -0.2em;
      position: relative;
      left: -3px;
    }
  }
  .news-content {
    .el-tabs__item {
      color: #fff;
    }
  }
  .mobile-imgs {
    width: 100%;
    padding: 0 10px;
    img {
      width: 100%;
      padding-bottom: 10px;
    }
  }
}
.ssq-button-primary.is-text {
  display: inline-block;
}
.ssq-button-primary.is-text .el-icon-arrow-right {
  margin-left: 5px;
  font-size: 20px;
  vertical-align: bottom;
}
@media screen and (max-width: 1024px) {
}
@media screen and (max-width: 768px) {
  .container {
    margin: 0 auto;
  }

  .index-news {
    // padding: 50px 2px 0 2px;
    .section-headline {
      padding-bottom: 3rem;
      .logo-text {
        width: 6.125rem;
        height: 2.75rem;
        vertical-align: -0.35em;
        position: relative;
        left: -3px;
      }
      .img-text {
        vertical-align: top;
      }
    }
    .news-list {
      padding: 1rem;
      .news-content {
        border-bottom: 1px solid #ebeef5;
        padding: 15px 5px;
        margin: 0;
        width: 100%;
        display: flex;
        justify-content: space-between;
        p {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 14px;
          color: #86868b;
        }
      }
      .news-content:active {
        background: #eeeeee;
      }
      .news-content:first-child {
        border-top: 1px solid #ebeef5;
      }
    }
    .mobile-imgs {
      width: 100%;
      padding: 0 10px;
      img {
        width: 100%;
        padding-bottom: 10px;
      }
    }
    .ssq-button-primary {
      width: 130px;
      height: 44px;
      line-height: 44px;
      margin-top: 2rem;
    }
  }
}
</style>
