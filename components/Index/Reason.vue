<template>
  <section class="index-reason section">
    <div class="container">
      <h2 class="section-headline headline--main" v-if="reasonTitle" :class="{'section-headline-en':isEN}">{{$t('reason.title')}}</h2>
      <div class="content" :class="{'content-en':isEN}" v-if="!isLandMobile">
        <div class="block bor-r-b">
			<i class="iconfont icon-a-xingzhuang582"></i>
			 <h3>{{$tc('reason.verification')}}</h3>
          <p>{{$tc('reason.verification1')}}</p>
          <p>{{$tc('reason.verification2')}}</p>
          
        </div>
        <div class="block bor-r-b">
         <i class="iconfont icon-a-xingzhuang580"></i>
          <h3>{{$tc('reason.send')}}</h3>
          <p>{{$tc('reason.send1')}}</p>
          <p>{{$tc('reason.send2')}}</p>
          <p>{{$tc('reason.sendItem1')}}</p>
          <p>{{$tc('reason.sendItem2')}}</p>
          <p>{{$tc('reason.sendItem3')}}</p>
          <p>{{$tc('reason.sendItem4')}}</p>
        </div>
        <div class="block bor-b">
          <i class="iconfont icon-a-xingzhuang573"></i>
          <h3>{{$tc('reason.sign')}}</h3>
          <p>{{$tc('reason.sign1')}}</p>
          <p>{{$tc('reason.sign2')}}</p>
        </div>
        <div class="block bor-r">
          <i class="iconfont icon-a-xingzhuang583"></i>
          <h3>{{$tc('reason.seal')}}</h3>
          <p>{{$tc('reason.seal1')}}</p>
          <p>{{$tc('reason.seal2')}}</p>
        </div>
        <div class="block bor-r">
         <i class="iconfont icon-a-xingzhuang557"></i>
          <h3>{{$tc('reason.contract')}}</h3>
          <p>{{$tc('reason.contract1')}}</p>
          <p>{{$tc('reason.contract2')}}</p>
        </div>
        <div class="block">
			<i class="iconfont icon-a-xingzhuang558"></i>
          <h3>{{$tc('reason.contractSave')}}</h3>
          <p>{{$tc('reason.contractSave1')}}</p>
          <p>{{$tc('reason.contractSave2')}}</p>
          <p>{{$tc('reason.contractSave3')}}</p>
        </div>
      </div>
      <!-- <div class="content_wrap" v-else>
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#icon-xingzhuang51"></use>
        </svg>
      </div> -->
      <!-- <div class="ssq-button-primary" @click="toDemo" v-if="reasonUse">免费试用</div> -->
      <div class="container" v-else>
      <div class="content_wrap">
        <div class="swiper-container" v-swiper:mySwiper="swiperOptionMobile">
          <div class="swiper-wrapper">
				<div class="swiper-slide" v-for="(item, index) in data" :key="item.name">
					<div class="item">
						   <div class="top">
								<!-- <div :class="['default-avatar', activeIndex === index? 'active-avatar':'']" :style="{backgroundImage: `url(${item.logo})`}"> -->
								<div :class="['default-avatar', activeIndex === index? 'active-avatar':'']">
                  <!-- <div> -->
									<!-- <img :src="item.logo" alt="" width="100%" height="100%"> -->
                  <div class="main_icon">
                    <i :class="[activeClass, item.icon]"></i>
                  </div>
                  <div class="main-content" :class="{'main-content-en':isEN}">{{ item.title }}</div>
                  <div class="name">
                    <p class="label">{{ item.content1 }}</p>
                    <p class="label">{{ item.content2 }}</p>
                  </div>
                   <!-- <div class="ssq-button-more transparent" @click="toCase">了解详情我们的合作伙伴 ></div> -->
                   <!-- <div class="ssq-button-more transparent" @click="advantage(index)">了解详情 <i class="iconfont icon-xiangyoujiantou"></i></div> -->
								</div>
						   </div>
					</div>
				</div>
          </div>
        </div>
      </div>
      
	    <div class="swiper-pagination swiper-pagination__reason" slot="pagination"></div>
      </div>
    </div>
  </section>
</template>

<script>
import Qs from 'qs';
export default {
  name: 'index-reason',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
    reasonUse: {
      type: Boolean,
      default: true,
    },
    reasonTitle: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isEN: false,
      activeIndex: 0,
      swiperOptionMobile: {
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        pagination: {
          el: '.swiper-pagination.swiper-pagination__reason',
        },
      },
      partnerInfo: {
        content: '',
        name: '',
        desc: '',
      },
      activeClass: 'iconfont',
      data: [
        {
          icon: 'icon-a-xingzhuang582',

          title: this.$tc('reason.verification', 1),
          content1: this.$tc('reason.verification1'),
          content2: this.$tc('reason.verification2'),
        },
        {
          icon: 'icon-a-xingzhuang580',
          title: this.$tc('reason.send'),
          content1: this.$tc('reason.send1'),
          content2: this.$tc('reason.send2'),
        },
        {
          icon: 'icon-a-xingzhuang573',
          title: this.$tc('reason.sign'),
          content1: this.$tc('reason.sign1'),
          content2: this.$tc('reason.sign2'),
        },
        {
          icon: 'icon-a-xingzhuang583',
          title: this.$tc('reason.seal'),
          content1: this.$tc('reason.seal1'),
          content2: this.$tc('reason.seal2'),
        },
        {
          icon: 'icon-a-xingzhuang557',
          title: this.$tc('reason.contract'),
          content1: this.$tc('reason.contract1'),
          content2: this.$tc('reason.contract2'),
        },
        {
          icon: 'icon-a-xingzhuang558',
          title: this.$tc('reason.contractSave', 1),
          content1: this.$tc('reason.contractSave1'),
          content2: this.$tc('reason.contractSave2'),
        },
      ],
    };
  },
  computed: {
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  methods: {
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          index_youshi: '',
        },
      });
    },
    handleChange(cur) {
      const activeIndex = cur.realIndex;
      this.activeIndex = activeIndex;
    },
    advantage(page) {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/advantage/' + (page + 1),
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
        },
      });
    },
    // openAdvantage(item) {
    //   const query = sessionStorage.getItem('query');
    //   this.$router.push({
    //     path: `/advantage/${item}`,
    //     query: {
    //       id: Qs.parse(query).id,
    //       utm_source: Qs.parse(query).utm_source,
    //       hp_productnav: '',
    //     },
    //   });
    // },
  },
  mounted() {
    const _this = this;
    if (this.isLandMobile) {
      this.mySwiper.on('slideChange', function() {
        _this.handleChange(this);
      });
    }
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
};
</script>

<style scoped lang="scss">
// .section {
//   padding: 1.5rem 1.5rem 7rem;
// }
.index-reason {
  //   padding-top: 5rem;
  //   padding-bottom: 5rem;
  // background-color: #f2f2f2;
  padding: 1.5rem 1.5rem 7rem;
  // .section-headline {
  //   color: #090909;
  //   font-size: 2rem;
  //   line-height: 2.5rem;
  //   font-weight: 400;
  // }
  .content {
    display: flex;
    // margin-top: 3.5rem;
    flex-wrap: wrap;
    padding: 0 2rem;
    box-sizing: border-box;
    .el-button--text {
      color: $-color-main;
    }
    .icon {
      font-size: 3rem;
    }
    .block {
      padding: 50px;
      width: 32.66%;
      text-align: left;
      // cursor: pointer;
      transition: all 0.3s ease;
      background: #f5f5f7;
      border-radius: 10px;
      &.bor-r-b {
        margin-right: 1%;
        margin-bottom: 1%;
      }
      &.bor-b {
        margin-bottom: 1%;
      }
      &.bor-r {
        margin-right: 1%;
      }
      > .iconfont {
        color: $-color-main;
        font-size: 3rem;
        padding-bottom: 2rem;
        display: block;
        text-align: center;
      }
      img {
        width: 60px;
      }
      h3 {
        font-size: 20px;
        line-height: 1.5;
        margin: 0.75rem auto 1.75rem;
        color: #1d1d1f;
        font-weight: 400;
      }
      p {
        line-height: 1.5;
        color: #86868b;
        // text-align: left;
        margin: 10px 0;
      }
    }
  }
  .content-en {
    .block {
      padding: 50px;
      width: 32.66%;
      text-align: left;
      // cursor: pointer;
      transition: all 0.3s ease;
      background: #f5f5f7;
      border-radius: 10px;
      &.bor-r-b {
        margin-right: 1%;
        margin-bottom: 1%;
      }
      &.bor-b {
        margin-bottom: 1%;
      }
      &.bor-r {
        margin-right: 1%;
      }
      > .iconfont {
        color: $-color-main;
        font-size: 3rem;
        padding-bottom: 2rem;
        display: block;
        text-align: center;
      }
      img {
        width: 60px;
      }
      h3 {
        font-size: 20px;
        line-height: 1.5;
        margin: 0.75rem auto 1.75rem;
        color: #1d1d1f;
        font-weight: 600;
      }
      p {
        line-height: 1.5;
        color: #86868b;
        // text-align: left;
        margin: 10px 0;
        font-size: 1rem;
      }
    }
  }
  .ssq-button-primary {
    margin: 3rem auto;
  }
}
@media screen and (max-width: 767px) {
  .index-reason {
    padding: 1.5rem 0 80px;
    background-color: #fff;
    .section-headline-en {
      color: #090909;
      font-size: 1.9rem;
      line-height: 2.5rem;
      font-weight: 600;
    }
    .content_wrap {
      text-align: center;
      .main_icon {
        margin: 0 0 3rem;
      }
      .iconfont {
        font-size: 4rem;
        text-align: center;
        margin-top: 5rem;
        color: $-color-main;
      }
      .main-content {
        color: #090909;
        font-size: 1.4rem;
        line-height: 2.5rem;
        font-weight: 400;
      }
      .main-content-en {
        font-weight: 600;
      }
      .name {
        margin-top: 2rem;
        line-height: 1.5;
        color: #1d1d1f;
        font-size: 1.3rem;
        p {
          font-size: 14px;
          color: #86868b;
          line-height: 24px;
          // text-align: left;
          margin: 10px 0;
        }
      }
      .ssq-button-more {
        margin-top: 2rem;
        .iconfont {
          font-size: 14px;
        }
      }
    }
    .swiper-pagination {
      width: 100%;
      left: 50%;
      transform: translateX(-50%);
      margin-top: 20px;
      /deep/ .swiper-pagination-bullet {
        margin: 0 10px;
      }
      /deep/ .swiper-pagination-bullet-active {
        background: #62686f;
      }
    }
    .ssq-button-primary {
      width: 130px;
      height: 44px;
      line-height: 44px;
    }
  }
}
</style>
