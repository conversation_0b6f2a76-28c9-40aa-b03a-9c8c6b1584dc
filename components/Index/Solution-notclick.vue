<template>
  <div class="index-solution section">
    <div class="container">
      <h2 class="section-headline headline--main">九大行业解决方案</h2>
      <div class="content" >
        <div class="swiper-container">
          <div class="swiper-wrapper">
            <!-- <div class="swiper-slide" v-for="(item, index) in data" :key="item.name" @click="slideTo(index)" :style="{backgroundImage: `url(${item.imgSrc})`}"> -->
            <div class="swiper-slide" v-for="(item, index) in data" :key="item.name">
				<img :src="item.imgSrc" alt="">
				<div :class="['slide-box',activeIndex === index?'slide-active':'']">
					<a :class="{'active': activeIndex === index}" v-if="item.name == 'more'" @click="toDemoQJ">{{ item.label }}
						<i  class="iconfont icon-x<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></i>
					</a>
					<a :class="{'active': activeIndex === index}" v-else >{{ item.label }}</a>
				</div>
        <!-- <a :href="`/solution/${item.name}`" v-show="false">{{ item.label }}</a> -->
            </div>
			<i></i>
			<i></i>
			<i></i>
			<i></i>
			<i></i>
          </div>
        </div>
      </div>

	  <!-- <div class="content" v-if="isMobile">
		  <div class="solution-list">
            <div class="solution-item" v-for="item in data" :key="item.name">
				<img :src="item.imgSrc" alt="">
				<p>{{item.label}}</p>
            </div>
          </div>
	  </div> -->

    </div>
  </div>
</template>

<script>
import Qs from 'qs';
const data = [
  {
    name: 'finance',
    label: '金融',
    icon: 'jinrong',
    imgSrc: require('@/assets/images/index/financial-slide.jpg'),
    features: {
      tong: [
        '风控要求高',
        '场景复杂，产品易用性要求高',
        '业务量大，并发要求高',
      ],
      case: [
        '电子签名+法律科技提升风控等级',
        '应有场景灵活，业务流转效率更高',
        '高稳定系统，服务以及响应快速',
      ],
      type: [
        '《投资协议》',
        '《理财协议》',
        '《买卖合同》',
        '《保理协议》',
        '《信用卡申请》',
        '《投保单》',
      ],
    },
  },
  {
    name: 'retail',
    label: '零售制造',
    icon: 'lingshou',
    imgSrc: require('@/assets/images/index/retail-slide.jpg'),
    alt: '数十万家企业使用上上签电子合同',
    features: {
      tong: ['高成本', '低安全', '难管理', '低效率'],
      case: [
        '随时随地合同线上签署，更安全、更省时',
        '合同智能管理，更节约',
        '关联企业现有信息系统，业务全线上管理更高效',
      ],
      type: [
        '《经销商框架协议》',
        '《销售合同》',
        '《采购合同》',
        '《供货对账单》',
      ],
    },
  },
  {
    name: 'logistics',
    label: '物流',
    icon: 'wuliu',
    alt: '上上签电子合同租赁行业案例',
    imgSrc: require('@/assets/images/index/logistics-slide.jpg'),
    features: {
      tong: ['信用机制待完善', '线下签署周期长', '单据管理复杂'],
      case: [
        '电子签约作为运输业务真实性凭证，更安全',
        '实现随时随地的单据签署，更省时',
        '一站式单据存管，更节约更高效',
      ],
      type: [
        '《货物承运协议》',
        '《物流电子回单》',
        '《网点/司机加盟协议》',
        '《仓储服务合同》',
      ],
    },
  },
  {
    name: 'hr',
    label: '人力资源',
    icon: 'renli',
    imgSrc: require('@/assets/images/index/human-slide.jpg'),
    features: {
      tong: [
        '纸质劳动合同签署，管理效率低',
        '劳动合同文件管理繁琐，成本高',
        '劳动法规严格，纸质劳动合同有很多潜在风险',
      ],
      case: [
        '合同在线批量签署，更高效',
        '合同智能管理，更省成本',
        '电子签名科技，杜绝法律风险',
      ],
      type: [
        '《员工劳动合同》',
        '《员工竞业协议》',
        '《个人背调授权书》',
        '《劳务派遣合同》',
      ],
    },
  },
  {
    name: 'it',
    label: 'IT互联网',
    icon: 'hulianwang',
    imgSrc: require('@/assets/images/index/IT-slide.jpg'),
    features: {
      tong: ['成本高', '管理难', '风险大', '效率低'],
      case: [
        '无缝对接平台管理系统，业务流转效率更高',
        '合同在线签署、智能管理，更省成本',
        '电子签名技术提升风控等级，更安全',
      ],
      type: [
        '《商家入驻协议》',
        '《主播入驻协议》',
        '《文章授权使用协议》',
        '《互联网家装服务协议》',
      ],
    },
  },
  {
    name: 'business',
    label: '电商',
    icon: 'gongyinglian',
    imgSrc: require('@/assets/images/index/e-commerce-slide.jpg'),
    features: {
      tong: ['效率与效力并重', '管理复杂', '存证与举证'],
      case: [
        '无缝对接平台管理系统，业务流转效率更高',
        '合同在线签署、智能管理，更省成本',
        '电子签名技术提升风控等级，更安全',
      ],
      type: [
        '《平台服务协议》',
        '《担保函》',
        '《竞业协议》',
        '《会员服务协议》',
      ],
    },
  },
  {
    name: 'fang',
    label: '房地产',
    icon: 'zuling',
    imgSrc: require('@/assets/images/index/estate-slide.jpg'),
    features: {
      tong: ['签约成本高', '合同管理乱', '印章管控难'],
      case: [
        '合同在线签署，更省成本',
        '合同智能管理，更高效',
        '集团印章统一管理，更可控',
      ],
      type: [
        '《施工承包合同》',
        '《房东房屋委托书》',
        '《监理委托合同》',
        '《施工协议》',
      ],
    },
  },
  {
    name: 'car',
    label: '汽车',
    icon: 'qiche',
    imgSrc: require('@/assets/images/index/car-slide.jpg'),
    features: {
      tong: ['合规门槛高', '效率低', '流程长'],
      case: [
        '电子签名技术满足合规要求，更安全',
        '合同智能管理，运维效率提升',
        '签署流、审批流，业务流程更短',
      ],
      type: [
        '《采购协议》',
        '《试驾协议》',
        '《购车协议》',
        '《委托扣款授权书》',
      ],
    },
  },
  {
    name: 'other',
    label: '综合',
    icon: 'jiaoyu',
    imgSrc: require('@/assets/images/index/complex-slide.jpg'),
    features: {
      tong: [
        '交易信用不可控',
        '业务牵涉多方，时效性差',
        'B2B交易不同地域，纸质合同传递困难',
      ],
      case: [
        '电子签名+法律科技提升风控等级',
        '优化签署流程，业务流转效率更高',
        '合同在线签署，更高效',
      ],
      type: ['《经销商协议》', '《合作协议》'],
    },
  },
  {
    name: 'more',
    label: '免费试用',
    icon: 'iconfont icon-xiangyoujiantou',
    imgSrc: '',
    features: {
      tong: [
        '交易信用不可控',
        '业务牵涉多方，时效性差',
        'B2B交易不同地域，纸质合同传递困难',
      ],
      case: [
        '电子签名+法律科技提升风控等级',
        '优化签署流程，业务流转效率更高',
        '合同在线签署，更高效',
      ],
      type: ['《经销商协议》', '《合作协议》'],
    },
  },
];
export default {
  name: 'index-solution',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeName: 'HR',
      activeIndex: 2,
      data,
      swiperOption: {
        initialSlide: 2,
        centeredSlides: true,
        speed: 700,
        slidesPerView: 5,
        navigation: {
          nextEl: '.ssq-button-next',
          prevEl: '.ssq-button-prev',
        },
      },
    };
  },
  computed: {
    feature() {
      const item = this.data[this.activeIndex];
      return item && item.features;
    },
    isMobile() {
      return this.$store.state.isMobile;
    },
    gutter() {
      return this.isMobile ? 10 : 20;
    },
  },
  methods: {
    handleChange(cur) {
      const activeIndex = cur.activeIndex;
      this.activeIndex = activeIndex;
    },
    slideTo(index) {
      // this.mySwiper.slideTo(index);
      const url = `/solution/${this.data[index].name}`;
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: url,
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
          hp_topnav: '',
        },
      });
    },
    toSolution(item) {
      if (item.name != 'more') {
        this.$router.push(`/solution/${item.name}`);
      } else {
        const query = sessionStorage.getItem('query');
        this.$router.push({
          path: '/demo',
          query: {
            id: Qs.parse(query).id,
            utm_source: Qs.parse(query).utm_source,
            hp_solution: '',
          },
        });
      }
    },
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const query = sessionStorage.getItem('query');
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          ...Qs.parse(query),
        },
      });
    },
    toDemoQJ() {
      this.$scrollTo('body');
    },
  },
  mounted() {
    const _this = this;
    // this.mySwiper.on('slideChange', function() {
    //   _this.handleChange(this);
    // });
  },
};
</script>

<style scoped lang="scss">
.index-solution {
  text-align: center;
  //   padding-top: 5rem;
  padding-bottom: 0rem;
  .content {
    position: relative;
    // margin-top: 3rem;
    // margin-bottom: 3rem;
    min-height: 19rem;
    .icon {
      font-size: 46px;
    }
    .swiper-container {
      overflow: initial;
      max-width: 1113px;
      .swiper-wrapper {
        // background-image: url('~assets/images/index/solutionbg.jpg');
        // background-size: 100% 100%;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        i {
          width: 19%;
          margin: 6px 5px;
        }
      }
    }
    .swiper-slide {
      height: 11rem;
      background-color: rgba(0, 0, 0, 0.05);
      background-size: 100% 100%;
      cursor: pointer;
      width: 19%;
      margin: 6px 5px;
      border-radius: 5px;
      text-align: center;
      overflow: hidden;
      a {
        position: absolute;
        left: 50%;
        top: 50%;
        color: #fff;
        font-size: 20px;
        width: 100%;
        transform: translate(-50%, -50%);
        text-align: center;
        &.active {
          color: #fff;
        }
      }
      &:hover img {
        transform: scale(1.3);
      }
      img {
        height: 100%;
        position: absolute;
        right: 0;
        transform: scale(1);
        transition: all 0.8s ease 0s;
      }
      & img:hover {
        transform: scale(1.3);
        transition: all 0.8s ease 0s;
      }
    }
    .swiper-slide:hover::before {
      background-color: rgba(110, 110, 110, 0.2);
      transition: all 0.4s;
    }
    .swiper-slide:hover {
      background-color: rgba(0, 0, 0, 0);
      transition: all 0.4s cubic-bezier(0, 0, 0, 0.1);
    }
    .swiper-slide:nth-child(10):hover {
      background-color: rgba(0, 0, 0, 0.05);
      transition: all 0.4s cubic-bezier(0, 0, 0, 0.05);
    }
    .swiper-slide:nth-child(10) {
      a {
        color: #00aa64;
      }
    }
  }
  .feature {
    position: relative;
    padding: 15px 18px;
    text-align: left;
    height: 230px;
    margin-bottom: 30px;
    box-shadow: 0 0px 20px 0 rgba(27, 33, 31, 0.2);
    .title {
      font-size: 18px;
      margin-bottom: 30px;
    }
    .desc {
      font-size: 14px;
      line-height: 1.8;
      color: #888;
    }
    i {
      font-size: 46px;
      position: absolute;
      right: 22px;
      bottom: 20px;
    }
  }
  .ssq-button-prev,
  .ssq-button-next {
    position: absolute;
    top: 50%;
    transform: translate3d(0, -50%, 0);
    background: transparent;
    cursor: pointer;
    > i {
      font-size: 36px;
      color: rgba(35, 24, 21, 0.5);
      outline: none;
      &:hover {
        color: rgba(35, 24, 21, 1);
      }
      &:focus {
        outline: none;
      }
    }
    &:focus {
      outline: none;
    }
  }
  .ssq-button-prev {
    left: -22px;
  }
  .ssq-button-next {
    right: -22px;
  }
  .ssq-button-primary {
    margin-top: 3rem;
    display: inline-block;
  }
}
.solution-list {
  display: flex;
  flex-wrap: wrap;
  .solution-item {
    width: 33%;
    margin-bottom: 20px;
    img {
      border-radius: 50%;
      width: 70px;
      height: 70px;
      margin: 10px 0 20px 0;
    }
    p {
      font-size: 16px;
      color: #333;
      font-weight: 600;
    }
  }
}
@media screen and (max-width: 767px) {
  .index-solution {
    background-color: #fff;
    padding-bottom: 5rem;
    .container {
      padding: 0;
    }
    .content {
      position: relative;
      padding: 0px;
      .swiper-container {
        .swiper-wrapper {
          justify-content: space-between;
          .icon {
            font-size: 28px;
          }
          .swiper-slide {
            cursor: pointer;
            width: 32%;
            margin: 5px 0;
            height: 9rem;
            a {
              margin: 0 auto;
              font-size: 18px;
            }
          }
          .swiper-slide:nth-child(10) {
            display: none;
          }
        }
      }
    }
    .feature {
      position: relative;
      padding: 15px 6px;
      text-align: left;
      height: 260px;
      .title {
        font-size: 14px;
        margin-bottom: 16px;
      }
      .desc {
        font-size: 12px;
        line-height: 1.8;
      }
    }
    .ssq-button-prev,
    .ssq-button-next {
      > i {
        font-size: 22px;
      }
    }
    .ssq-button-prev {
      left: -8px;
    }
    .ssq-button-next {
      right: -8px;
    }
    .ssq-button-primary {
      width: 130px;
      height: 44px;
      line-height: 44px;
    }
  }
}
</style>
