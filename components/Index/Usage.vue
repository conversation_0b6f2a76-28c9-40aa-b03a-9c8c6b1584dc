<template>
  <section class="index-usage" :style="{background: isWhiteBg ? '#fff' : '#fff'}">
	<div class="section-usage-bg"></div>
    <div class="container">
      <h2 class="section-headline headline--main">1273万+标杆企业选择上上签</h2>
      	<!-- <el-tabs v-model="activeName" class="usage-content" v-if="!isMobile" @tab-click="getSolution">
			<el-tab-pane v-for="item in data" :label="item.solutionName" :name="item.id" :key="item.id">
				
				<img :src="item.pc" :alt="`上上签电子合同${item.solutionName}行业案例`">
			</el-tab-pane>
		</el-tabs> -->
    <div v-if="!isMobile" class="usage-content">
      <div class="btn_switch_container">
        <div class="btn_switch" :class="{ newStyle:item.id===imgItem.id}" v-for="item in data" :key="item.id" @mouseenter="change(item.id)">
                    {{item.solutionName}}
        </div>
      </div>
      <div class="user-content">
                  <img :src="imgItem.pc" :alt="`上上签电子合同${imgItem.solutionName}行业案例`">
      </div>
		</div>
		<div v-if="isMobile" class="usage-content">
      <img v-for="img in imgs" :src="img" :key="img" alt="">
		</div>
      <div class="section-content">
        <div v-if="!demo&&!isMobile" class="ssq-button-more transparent">
          <nuxt-link :to="`/case/list-${solutionPage}`">
            更多案例 <i class="iconfont icon-xiangyoujiantou"></i>
          </nuxt-link>
        </div>
        <button v-if="demo" class="ssq-button-primary" @click="toDemo">
          免费试用
        </button>
      </div>
    </div>
  </section>
</template>

<script>
import find from 'lodash/find';
const getJson = () => import('@/static/_new.json').then(m => m.default || m);
const imgs = [
  'https://static.bestsign.cn:443/98b4ba9486f7ffc7f1420cda560e2cadae899c9a.jpg',
  // 'https://static.bestsign.cn/84dc4147aa38f6da60aead381a9067db2fabdd8c.jpg',
  // 'https://static.bestsign.cn/be2dae3da606bebb830448cfa2d88d1467960360.jpg',
  // 'https://static.bestsign.cn/b754ad6e9eff20d8d764dd9ba7c50e4ab44a290b.jpg',
  // 'https://static.bestsign.cn/5d4d44aa226b3bfabaf5a3e8e3aefbcb95021c3f.jpg',
  // 'https://static.bestsign.cn/1f712180f8c8f059e7777de4fff66f8edba4f4c3.jpg',
  // 'https://static.bestsign.cn/42b04fda0a9e715e21fd35f31ab74b5b54ba1bfb.jpg',
  // 'https://static.bestsign.cn/fd67fa159cfcaffbb303d59d494fc9ef91e40db6.jpg',
  // 'https://static.bestsign.cn/1fb937668479e26086bc8ba4ed076328eee60ebf.jpg',
];
export default {
  name: 'IndexUsage',
  props: {
    isWhiteBg: {
      type: Boolean,
      default: false,
    },
    demo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeName: '18',
      data: [],
      imgs,
      solutionPage: 'finance',
      imgItem: {
        id: '18',
        solutionName: '金融',
        pc:
          'https://static.bestsign.cn:443/b24715881bc70bcd1e4c687a3275b85bc8e44e24.jpg',
      },
    };
  },
  computed: {
    feature() {
      const item = find(this.data, o => o.name === this.activeName);
      return item && item.features;
    },
    caseName() {
      const solutions = {
        31: 'other',
        25: 'car',
        30: 'business',
        22: 'logistics',
        26: 'it',
        23: 'retail',
        21: 'fang',
        19: 'hr',
        18: 'finance',
      };
      return solutions[this.activeName];
    },
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {
    change(num) {
      // console.log(num);
      this.imgItem = this.data.filter((item, index) => item.id === num)[0];
      // console.log(this.imgItem);
      this.getSolution(num);
    },
    getSolution(item) {
      switch (item) {
        case '22':
          this.solutionPage = 'logistics';
          break;
        case '31':
          this.solutionPage = 'other';
          break;
        case '25':
          this.solutionPage = 'car';
          break;
        case '30':
          this.solutionPage = 'business';
          break;
        case '26':
          this.solutionPage = 'it';
          break;
        case '23':
          this.solutionPage = 'retail';
          break;
        case '21':
          this.solutionPage = 'fang';
          break;
        case '19':
          this.solutionPage = 'hr';
          break;
        case '18':
          this.solutionPage = 'finance';
          break;
        default:
          this.solutionPage = 'finance';
      }
    },
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const query = sessionStorage.getItem('query');
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          ...Qs.parse(query),
        },
      });
    },
  },
  async mounted() {
    const response = await getJson();
    this.data = response.map(o => ({
      id: o.id.toString(),
      solutionName: o.solutionName,
      pc: o.usage && o.usage.pc,
    }));
  },
};
</script>

<style lang="scss" scoped>
.landscape .paragraph {
  .section-headline {
    color: #fff;
    font-weight: 400;
  }
}
.section-usage-bg {
  width: 84rem;
  height: 32rem;
  margin: 0 auto;
  //   background: linear-gradient(to right, #06df96, #12c8b4);
  background-image: url('~assets/images/index/usagebg.jpg');
  background-size: 100% 100%;
  position: relative;
}
.index-usage .section-usage-bg:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.index-usage {
  .el-tabs__nav {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  .el-tabs__active-bar {
    display: none;
  }
  .el-tabs__item {
    font-size: 16px;
    padding: 0;
    text-align: center;
    font-weight: 400;
    color: #fff;
    opacity: 0.8;
    &.is-active {
      color: #fff;
      border-bottom: 2px solid #fff;
      opacity: 1;
    }
    &:hover {
      color: #fff;
      opacity: 1;
    }
  }
  /deep/ .el-tabs__header {
    padding: 0 1.5rem;
  }
  /deep/ .el-tabs__active-bar {
    background: #fff;
  }
  /deep/ .el-tabs__item.is-active,
  /deep/ .el-tabs__item:hover {
    color: #fff;
  }
  /deep/.el-tabs__item {
    color: #cfc4b9;
    width: 18%;
    padding: 0 18px;
  }
  /deep/ .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: #fff;
    opacity: 0.8;
  }
}
@media screen and (max-width: 768px) {
  .index-usage {
    .el-tabs__nav-wrap::after {
      bottom: 2px;
    }
    .section-usage-bg {
      width: 100%;
      height: 22rem;
      margin: 0 auto;
      background-image: url('~assets/images/index/usage.png');
    }
    .index-usage .container {
      margin-top: -80px;
    }
    .el-tabs__item {
      transform: scale(0.9);
      font-size: 12px;
      color: #fff;
    }
  }
}
</style>
<style scoped lang="scss">
.index-usage {
  text-align: center;
  padding: 0 0 5rem 0;
  background-color: #f7f8fa;
  .container {
    width: 100%;
    max-width: 1080px;
    margin-top: -18rem;
  }
  .ssq-button-more {
    font-size: 14px;
    .iconfont {
      font-size: 14px;
    }
    a {
      color: #00a664;
    }
  }
  .section-headline {
    color: #fff;
    line-height: 2rem;
    padding-top: 0;
    .logo-text {
      width: 7.25rem;
      height: 2.25rem;
      vertical-align: -0.2em;
      position: relative;
      left: -3px;
    }
    padding-bottom: 5rem;
  }
  .usage-content img {
    width: 100%;
    // padding-top: 2rem;
    // padding-bottom: 4rem;
    padding: 1.5rem;
  }
  .usage-content {
    .el-tabs__item {
      color: #fff;
    }
    .btn_switch_container {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #fff;
      margin: 1.5rem;
      font-size: 14px;
      color: #cfc4b9;
      .btn_switch {
        width: 10%;
        cursor: pointer;
      }
      .newStyle {
        font-weight: 800;
        color: #fff;
        border-bottom: 1px solid #fff;
        cursor: pointer;
        padding-bottom: 10px;
      }
    }
  }
  .mobile-imgs {
    width: 100%;
    padding: 0 10px;
    img {
      width: 100%;
      padding-bottom: 10px;
    }
  }
}
.ssq-button-primary.is-text {
  display: inline-block;
}
.ssq-button-primary.is-text .el-icon-arrow-right {
  margin-left: 5px;
  font-size: 20px;
  vertical-align: bottom;
}
@media screen and (max-width: 767px) {
  .index-usage {
    .section-usage-bg {
      background-size: cover;
    }
  }
  .container {
    margin: 0 auto;
    padding: 0;
  }
  .index-usage {
    padding: 50px 2px;
    .section-headline {
      padding-bottom: 3rem;
      .logo-text {
        width: 6.125rem;
        height: 2.75rem;
        vertical-align: -0.35em;
        position: relative;
        left: -3px;
      }
      .img-text {
        vertical-align: top;
      }
    }
    .mobile-imgs {
      width: 100%;
      padding: 0 10px 30px 0;
      img {
        width: 100%;
        padding-bottom: 10px;
      }
    }
    .ssq-button-primary {
      width: 130px;
      height: 44px;
      line-height: 44px;
      margin-top: 2rem;
    }
  }
}
</style>
