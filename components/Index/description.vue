<!--  -->
<template>
  <article class="desc-con">
    <section class="section section-1">
      <div class="container">
        <div class="package-type">
          <div v-for="(item,index) in packageType" :key="index" class="package-con">
            <div class="icon-container">
              <i :class="['iconfont ',item.iconfont]"></i>
            </div>
            <div class="type">{{item.title}}</div>
            <div class="desc">{{item.desc}}</div>
          </div>
        </div>
      </div>
    </section> 
  </article>
</template>

<script>
//import x from ''
export default {
  components: {},
  data() {
    return {
      packageType: [
        {
          iconfont: 'icon-a-xingzhuang643',
          title: this.$t('homeBanner.1'),
          desc: this.$t('homeBanner.2'),
        },
        {
          iconfont: 'icon-ruanjianjishu',
          title: this.$t('homeBanner.5'),
          desc: this.$t('homeBanner.6'),
        },
        {
          iconfont: 'icon-wuliupeisong',
          title: this.$t('homeBanner.3'),
          desc: this.$t('homeBanner.4'),
        },
      ],
    };
  },
  computed: {},
  methods: {},
  mounted() {},
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>

<style lang='scss' scoped>
//@import url()
.desc-con {
  .section-1 {
    background: #f5f5f7;
    padding: 9rem 0 5rem;
    .container {
      max-width: 65%;
      width: 65%;
      .package-type {
        display: flex;
        justify-content: space-around;
        .package-con {
          margin: 0 1%;
          width: 34%;
          cursor: pointer;
          box-shadow: 0 8px 15px 0 rgba(82, 94, 102, 0.15);
          background: #fff;
          padding: 50px 30px;
          border-radius: 10px;
          .icon-container {
            text-align: center;
            margin-bottom: 2rem;
            .iconfont {
              color: #f3c51e;
              font-size: 2.5rem;
            }
          }
          .type {
            font-size: 1.2rem;
            margin: 0;
            font-weight: 500;
            color: #1d1d1f;
            line-height: 1.5;
            text-align: center;
          }
          .desc {
            font-size: 0.9rem;
            color: #86868b;
            line-height: 1.5;
            margin: 1rem 0;
            text-align: left;
          }
        }
      }
    }
  }
}
@media (max-width: 768px) {
  .desc-con {
    .section-1 {
      padding: 2rem 0 0;
      .container {
        max-width: 100%;
        width: 100%;
        .package-type {
          display: block;
          .package-con {
            margin: 20px 0 0;
            width: 100%;
            .type {
              font-size: 1.5rem;
            }
            .desc {
              font-size: 1.2rem;
            }
          }
        }
      }
    }
  }
}
</style>
