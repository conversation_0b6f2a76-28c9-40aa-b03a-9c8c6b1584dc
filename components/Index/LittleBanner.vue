<template>
<div class="banner-wrapper">
  <div class="container1">
    <template v-for="item in banner">
      <div class="link-wrapper" :key="item.id">

        
        <div class="banner-right">
          <!-- <a class="image-link" :href="tolink(item.bannerLink)"> -->
            <a class="image-link" :href="item.bannerLink">
            <div class="mainTitle" v-if="!isMobile">
              <div class="little-logo" v-if="item.imgUrl"> 
                <img :src="item.imgUrl" width=100%;> 
              </div>
              {{item.bannerTitle }}
			</div>
      <div class="mainTitle" v-if="isMobile">
        <div class="mainTitleCon">
          <div class="little-logo" v-if="item.imgUrl"> 
                <img :src="item.imgUrl" width=30px;> 
              </div>
              <div class="mainTitleDsc">
                {{item.bannerTitle }}
              </div>
        </div>
				<i class="iconfont icon-xiangyoujiantou"></i>
			</div>
            <div class="minTitle" v-if="!isLandMobile">{{ item.bannerDescription }}
              <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-xiangyoujiantou"></use>
              </svg>
            </div>
          </a>
        </div>
        <!-- <div class="banner-left" v-if="!isLandMobile">
          <img class="bannerImg"  :src="item.imgUrl" width="60">
        </div> -->
        
      </div>
    </template>
  </div>
</div>

</template>

<script>
export default {
  data: () => ({
    banner: [],
  }),
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    async handleFetch() {
      const banner = await this.$axios.get(
        // '/www/api/web/college2?column=banner&pageNum=1&pageSize=10&status=0'
        '/www/api/web/getTextBanner'
      );

      this.banner = banner.sort((a, b) => a.sortIndex - b.sortIndex);
    },
    toDemo(str) {
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
        this.$router.push(`/demo?${str}`);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
        this.$router.push(`/demo?${str}`);
      }
    },
    // tolink(route) {
    //   var query = Object.entries(this.$route.query)
    //     .map(([key, value]) => {
    //       return `${key}=${value}`;
    //     })
    //     .join('&');
    //   // debugger;
    //   if (route.indexOf('?') > -1) {
    //     route = route + '&' + query;
    //   } else {
    //     route = route + '?' + query;
    //   }
    //   return route;
    // },
  },
  mounted() {
    this.handleFetch();
  },
};
</script>

<style scoped lang="scss">
.feature {
  margin-bottom: 20px;
  &-item {
    display: block;
    line-height: 1.5;
    color: #626262;
    i {
      color: #00aa64;
      margin-right: 8px;
    }
  }
}
.container {
  height: 100%;
  display: flex;
  align-items: center;
  text-align: left;
  // max-width: 1480px;
  &.auto {
    height: auto;
    background-color: rgba(0, 0, 0, 0.5);
  }
  .main-text {
    position: relative;
    /*top: 5px;*/
    /*left: 50px;*/
    //   margin-top: 150px;
    //   padding-left: 8%;
    h3 {
      font-size: 40px;
      // font-weight: 500;
      margin-bottom: 20px;
      color: #fff;
    }
    h4 {
      font-size: 20px;
      line-height: 36px;
      max-width: 480px;
      // font-weight: 400;
      margin-bottom: 1.5rem;
      // border: 1px solid #fff;
      // border-radius: 8px;
      display: inline-block;
      padding: 6px 10px;
      color: #fff;
    }
    .ssq-button-primary {
      margin-top: 20px;
      background: #00aa64;
      width: 180px;
      height: 48px;
      line-height: 48px;
      display: block;
    }
    .main-text-img {
      margin-top: 8rem;
      width: 75%;
    }
    .main-text-img-mp {
      margin-top: 2rem;
      width: 75%;
    }
    .banner-text {
      width: 26vw;
    }
    .tips {
      color: #00aa64;
      font-size: 18px;
      margin-top: 30px;
      font-weight: 400;
    }
  }
}
.container1 {
  width: 100%;
  display: flex;
  justify-content: space-evenly;
  margin-top: 16px;
}
.ssq-button-primary {
  img {
    margin-left: 5px;
    width: 20px;
    vertical-align: bottom;
  }
}
.banner-wrapper {
  // position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  color: #fff;
  .container {
    padding: 12px 0;
    display: flex;
  }
  .link-wrapper {
    flex: 1;
    max-width: 25%;
    border-left: 1px solid #fff;
    border-radius: 2px;
    background-color: #ffffff;
    box-shadow: 0 4px 16px 0 rgba(47, 67, 82, 0.1);
    margin-left: 5px;
    margin-right: 5px;
    padding-left: 10px;
    height: 125px;
    overflow: hidden;
    &:hover {
      -webkit-box-shadow: #ccc 0px 0px 10px;
      box-shadow: #ccc -0px 0px 10px;
    }
    &:last-child {
      border-right: 1px solid #fff;
    }
  }
  .banner-right {
    float: left;
    width: 100%;
  }
  .image-link {
    display: inline-block;
    color: rgb(17, 15, 15);
    padding: 20px 20px;
    line-height: 23px;
    text-align: left;
    vertical-align: middle;
    width: 100%;
    .mainTitle {
      margin-bottom: 12px;
      font-size: 15px;
      color: #515151;
      height: 50px;
      overflow: hidden;
      .little-logo {
        display: inline-block;
        width: 30px;
        margin: 0px 5px;
        height: 28px;
        vertical-align: middle;
        img {
          height: 15px;
        }
      }
      .iconfont {
        font-size: 14px;
      }
      &:hover {
        color: #00aa64;
      }
    }
    .minTitle {
      font-size: 14px;
      color: #65697f;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #00aa64;
      // &:hover {
      //   color: #00aa64;
      // }
    }
  }
  .banner-left {
    float: left;
    width: 60px;
    margin-left: -100%;
  }
  .bannerImg {
    margin-top: 30px;
  }
}

@media screen and (max-width: 767px) {
  .feature {
    margin-bottom: 30px;
    &-item {
      line-height: 24px;
      &:last-child {
        padding-left: 14px;
      }
    }
  }

  .index-banner {
    .container {
      align-items: flex-start !important;
      justify-content: center;

      .container1 {
        margin-top: 18px;
        display: block;
      }
    }
  }
  .main-text1 {
    text-align: center;
    padding-left: 0 !important;
    margin-top: 30px;
    position: relative;
    h3 {
      color: #fff;
      font-size: 28px;
      // margin-top: 45px;
      // margin: 0 10px;
    }
    h4 {
      color: #fff;
      font-size: 16px;
      line-height: 24px;
      margin-top: 25px;
    }
    .ssq-button-primary {
      font-size: 16px;
      position: absolute;
      left: 50%;
      top: 40vw;
      transform: translateX(-50%);
      height: 44px;
      width: 180px;
      line-height: 1px;
    }
  }

  .banner-wrapper {
    //   bottom: -92px;
    position: relative;
    .container {
      padding: 16px 0;
      display: flex;
    }
    .link-wrapper {
      flex: none;
      width: 100%;
      max-width: none;
      margin-bottom: 0px;
      border-bottom: 1px solid #e5e5e5;
      height: 50px;
      border-radius: 0;
      margin-left: 0px;
      margin-right: 0px;
      padding-left: 0px;
      .mainTitle {
        margin-bottom: 0;
        font-size: 15px;
        color: #515151;
        height: auto;
        overflow: hidden;
        line-height: 50px;
        display: flex;
        justify-content: space-between;
        .mainTitleCon {
          display: flex;
        }
      }

      &:first-child {
        border-left: none;
      }
      &:last-child {
        border-right: none;
      }
    }
    .image-link {
      padding: 0 8px;
      width: 100%;
      margin-left: 0px;
      height: auto;
      box-sizing: border-box;
      //   .mainTitle {
      //     margin-bottom: 0;
      //     line-height: 50px;
      //     height: auto;
      //   }
      h3 {
        margin: 8px 0px;
        font-size: 14px;
        color: #515151;
        vertical-align: middle;
        line-height: 20px;
      }
      span {
        font-size: 12px;
      }
    }
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .feature {
    margin-bottom: 30px;
    &-item {
      line-height: 24px;
      &:last-child {
        padding-left: 14px;
      }
    }
  }
  .container {
    justify-content: center;
    text-align: center;
    align-items: flex-start;
    .main-text {
      margin-top: 10vw;
    }
    .ssq-button-primary {
      margin: 0 auto;
    }
  }
}
</style>
