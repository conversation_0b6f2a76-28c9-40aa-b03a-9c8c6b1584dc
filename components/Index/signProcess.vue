<template>
  <section class="index-signProcess section">
    <div class="container" v-if="!isLandMobile">
      <h2 class="section-headline headline--main">{{$t('signProcess.title')}}</h2>
      <div class="signProcess-content">
        <div class="signProcess-content-img">
          <div class="center"><img src="@/assets/images/yuan.png"></div>
        </div>
        <div class="space"></div>
        <div class="signProcess-content-img">
          <div class="center"><img src="@/assets/images/yuan.png"></div>
        </div>
        <div class="space"></div>
        <div class="signProcess-content-img">
          <div class="center"><img src="@/assets/images/yuan.png"></div>
        </div>
      </div>
      <div class="signProcess-content">
        <!-- <div class="space"></div> -->
        <div class="signProcess-content-block">
          <div class="big-step">{{$t('signProcess.step1')}}</div>
          <div class="big-dsc">{{$tc('signProcess.step1Desc',1)}}</div>
          <div class="small-dsc">{{$tc('signProcess.step1Desc',2)}}</div>
          <div></div>
        </div>
        <div class="signProcess-content-block">
          <div class="big-step">{{$t('signProcess.step2')}}</div>
          <div class="big-dsc">{{$tc('signProcess.step2Desc',1)}}</div>
          <div class="small-dsc">{{$tc('signProcess.step2Desc',2)}}</div>
        </div>
        <div class="signProcess-content-block">
          <div class="big-step">{{$t('signProcess.step3')}}</div>
          <div class="big-dsc">{{$tc('signProcess.step3Desc',1)}}</div>
          <div class="small-dsc">{{$tc('signProcess.step3Desc',2)}}</div>
        </div>
      </div>
    </div>
    <div class="container" v-else>
      <div class="signProcess-title">
        <h2 class="section-headline headline--main" v-if="reasonTitle">{{$t('signProcess.title')}}</h2>
      </div>
      <div class="signProcess-content-wap">
        <div class="signProcess-content-block">
          <div class="big-step">{{$t('signProcess.step1')}}</div>
          <div class="big-dsc">{{$tc('signProcess.step1Desc',1)}}</div>
          <div class="small-dsc">{{$tc('signProcess.step1Desc',2)}}</div>
        </div>
        <div class="signProcess-content-block">
          <div class="big-step">{{$t('signProcess.step2')}}</div>
          <div class="big-dsc">{{$tc('signProcess.step2Desc',1)}}</div>
          <div class="small-dsc">{{$tc('signProcess.step2Desc',2)}}</div>
        </div>
        <div class="signProcess-content-block">
          <div class="big-step">{{$t('signProcess.step3')}}</div>
          <div class="big-dsc">{{$tc('signProcess.step3Desc',1)}}</div>
          <div class="small-dsc">{{$tc('signProcess.step3Desc',2)}}</div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import Qs from 'qs';
export default {
  name: 'signProcess',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
    reasonUse: {
      type: Boolean,
      default: true,
    },
    reasonTitle: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      activeIndex: 0,
      activeClass: 'iconfont',
    };
  },
  computed: {
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          index_youshi: '',
        },
      });
    },
    advantage(page) {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/advantage/' + (page + 1),
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
        },
      });
    },
    openAdvantage(item) {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: `/advantage/${item}`,
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
          hp_productnav: '',
        },
      });
    },
  },
  mounted() {},
};
</script>

<style scoped lang="scss">
.index-signProcess {
  //   padding-top: 5rem;
  //   padding-bottom: 5rem;
  background: #f9f9f9;
  padding: 5rem 0 !important;
  .section-headline {
    padding: 2rem 0 2rem 0;
    text-align: center;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
    color: #090909;
  }
  .signProcess-content {
    display: flex;
    justify-content: center;
    padding: 20px 0 0;
    .signProcess-content-img {
      img {
        width: 10px;
      }
    }
    .space {
      // position: absolute;
      width: 13%;
      height: 2px;
      background-color: #3f3f3f;
      // bottom: 192px;
      margin: 7px 8%;
    }
    .signProcess-content-block {
      text-align: center;
      z-index: 1;
      width: 25%;
      margin: 0 2.5%;
      .top {
        img {
          height: 60px;
        }
      }
      .center {
        margin: 40px 0;
        img {
          width: 15px;
        }
      }
      .big-step {
        padding-bottom: 50px;
        color: #3f3f3f;
        font-weight: 800;
      }
      .big-dsc {
        font-size: 20px;
        font-weight: 400;
        color: #1d1d1f;
        padding-bottom: 10px;
        height: 60px;
        line-height: 1.3;
      }
      .small-dsc {
        color: #86868b;
        line-height: 1.7;
        // margin: 10px 15%;
      }
    }
  }
  .min-title {
    text-align: center;
    padding-bottom: 2.5rem;
  }
  .content {
    text-align: center;
    font-size: 1rem;
    line-height: 2rem;
  }
  .ssq-button-primary {
    margin: 3rem auto;
  }
}
@media screen and (max-width: 1280px) {
  .index-signProcess {
    .content {
    }
  }
}
@media screen and (max-width: 767px) {
  .index-signProcess {
    padding: 0 !important;
    background-color: #fff;
    .section-headline {
      color: #090909;
      font-size: 1.9rem;
      line-height: 2.5rem;
      font-weight: 400;
      text-align: center;
    }
    .signProcess-content-wap {
      display: block;
      padding: 0;
      .signProcess-content-block {
        width: 100%;
        margin: 30px 0;
        .top {
          img {
            height: 40px;
          }
        }
        .big-step {
          color: #00aa64;
          font-size: 18px;
          padding: 10px;
        }

        .big-dsc {
          color: #090909;
          font-size: 18px;
          font-weight: 500;
          margin: 30px 0 10px;
        }
        .small-dsc {
          color: #888;
          font-size: 14px;
        }
      }
    }
    .content-wap {
      text-align: left;
      margin: 0 20px;
      font-size: 14px;
      line-height: 25px;
    }
    .ssq-button-primary {
      width: 130px;
      height: 44px;
      line-height: 44px;
    }
  }
}
</style>
