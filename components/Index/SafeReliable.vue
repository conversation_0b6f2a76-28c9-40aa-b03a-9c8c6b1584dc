<!-- 组件说明 -->
<template>
  <section class="section">
      <div class="container">
        <h1 class="section-headline">{{$t('safety.title')}}</h1>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="8">
            <el-card
              :body-style="{ padding: '0px' }"
              shadow="hover"
              class="card-1">
			  <div class="card-icon">
				 <i class="iconfont icon icon-jiamitongxun"></i>
			  </div>
              <div class="card-title">{{$tc('safety.communication',1)}} </div>
              <div class="card-content">{{$tc('safety.communication',2)}}</div>
			</el-card>
          </el-col>
          <el-col :xs="24" :sm="8">
            <el-card
              :body-style="{ padding: '0px' }"
              shadow="hover"
              class="card-2">
			  <div class="card-icon">
				  <i class="iconfont icon icon-shujujiami1"></i>
			  </div>
              <div class="card-title">{{$tc('safety.data',1)}} </div>
              <div class="card-content">{{$tc('safety.data',2)}}</div>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="8">
            <el-card
              :body-style="{ padding: '0px' }"
              shadow="hover"
              class="card-3">
			    <div class="card-icon">
				  <i class="iconfont icon icon-shujucunchu"></i>
			  </div>
              <div class="card-title">{{$tc('safety.save',1)}} </div>
              <div class="card-content">{{$tc('safety.save',2)}}</div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </section>
</template>

<script>
//import x from ''
export default {
  name: 'SafeReliable',
  components: {},
  data() {
    return {};
  },
  computed: {},
  methods: {},
};
</script>

<style lang='scss' scoped>
//@import url()
.section {
  text-align: center;
  .article-headline {
    color: #090909;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
  }
  .article-subtitle {
    margin: 28px auto 80px;
  }
  .el-row {
    //   max-width: 1220px;
    margin: 0 auto;
  }

  .card-icon {
    font-size: 60px;
    margin-top: 2rem;
    .iconfont {
      font-size: 4rem;
      cursor: pointer;
      // color: #00a664;
    }
  }
  .el-card {
    background: #f9f9f9;
    border: 0;
  }
  .el-card__body:hover {
    //   .iconfont {
    //     color: #00a664;
    //   }
  }
  .card-title {
    margin: 50px auto 30px;
    font-size: 20px;
    font-weight: 400;
    color: #1d1d1f;
  }
  .card-content {
    height: 180px;
    padding: 0 32px 30px 32px;
    color: #888;
    text-align: justify;
    line-height: 24px;
    color: #86868b;
  }
}
@media only screen and (max-width: 767px) {
  .el-card {
    margin-bottom: 20px;
  }
}
</style>
