
<template>
  <section class=" function-solution section">
    <div class="container">
      <h2 class="section-headline headline--main" :class="{'section-headline-en':isEN}">{{$t('service.feature.title')}}</h2>
      <div class="function-solution-container">
        <div class="function-solution-item" v-for="(item,index) in solutionList" :key="index">
          <div class="solution-item-icon">
            <!-- <i :class="['iconfont',item.iconfont]"></i> -->
            <img :src="item.imgUrl" alt="">
          </div>
          <div class="solution-item-content" :class="{'solution-item-content-en':isEN}">
            <div class="solution-item-content-title">{{item.title}}</div>
            <div class="solution-item-content-content">{{item.content}}</div>
            <div class="solution-item-content-advantage-con">
              <div class="solution-item-content-advantage" v-for="i in item.advantage" :key="i.title">
                <div class="title-con">
                  <div class="tip">-</div>
                  <div class="title">{{i.title}}: <span>{{i.desc}}</span></div>
                </div>
              
              <!-- <div class="desc">{{i.desc}}</div> -->
            </div>
            </div>
             <!-- <div class="btn-con" @click="gotoDetails(item.type)">查看详情 ></div> -->
          </div>
          <!-- <div class="btn-con" @click="gotoDetails(item.type)">查看详情 ></div> -->
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import Qs from 'qs';
export default {
  name: 'solutions',
  props: {
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isEN: false,
      solutionList: [
        {
          imgUrl:
            'https://static.bestsign.cn:443/d3bc59bfbbd08109f6deed117986d578feeda806.jpg',
          title: this.$tc('service.feature.f1.1'),
          content: this.$tc('service.feature.f1.2'),
          advantage: [
            {
              title: this.$tc('service.feature.f1.3'),
              desc: this.$tc('service.feature.f1.4'),
            },
            {
              title: this.$tc('service.feature.f1.5'),
              desc: this.$tc('service.feature.f1.6'),
            },
            {
              title: this.$tc('service.feature.f1.7'),
              desc: this.$tc('service.feature.f1.8'),
            },
            {
              title: this.$tc('service.feature.f1.9'),
              desc: this.$tc('service.feature.f1.10'),
            },
          ],
          type: '/product-sign',
        },
        {
          imgUrl:
            'https://static.bestsign.cn:443/15e9dfdc15e87da9e24edc81b2ee9c4684ad3ff1.jpg',
          title: this.$tc('service.feature.f2.1'),
          content: this.$tc('service.feature.f2.2'),
          advantage: [
            {
              title: this.$tc('service.feature.f2.3'),
              desc: this.$tc('service.feature.f2.4'),
            },
            {
              title: this.$tc('service.feature.f2.5'),
              desc: this.$tc('service.feature.f2.6'),
            },
            {
              title: this.$tc('service.feature.f2.7'),
              desc: this.$tc('service.feature.f2.8'),
            },
          ],
          type: '/product-cycle',
        },
        {
          imgUrl:
            'https://static.bestsign.cn:443/602ad554840e7f975d0b26e109f7c9ae9c0980f1.jpg',
          title: this.$tc('service.feature.f3.1'),
          content: this.$tc('service.feature.f3.2'),
          advantage: [
            {
              title: this.$tc('service.feature.f3.3'),
              desc: this.$tc('service.feature.f3.4'),
            },
            {
              title: this.$tc('service.feature.f3.5'),
              desc: this.$tc('service.feature.f3.6'),
            },
          ],
          type: '/product-link',
        },
      ],
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  methods: {
    gotoDetails(type) {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: type,
        query: {
          ...Qs.parse(query),
          utm_source: Qs.parse(query).utm_source || '',
        },
      });
    },
  },
  mounted() {
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
};
</script>

<style scoped lang="scss">
.function-solution {
  background-color: #f5f5f7;
  padding-bottom: 2rem;
  .function-solution-container {
    display: flex;
    .function-solution-item {
      position: relative;
      width: 32%;
      background: #fff;
      border-radius: 10px;
      margin: 0px;
      &:nth-child(2) {
        margin: 0 2%;
      }
      .solution-item-icon {
        border-radius: 10px 10px 0 0;
        img {
          width: 100%;
          border-radius: 10px 10px 0 0;
        }
      }
      .solution-item-content {
        text-align: left;
        padding: 2rem 2rem 1rem;
        .solution-item-content-title {
          font-size: 1.5rem;
          font-weight: 500;
          color: #000;
          margin: 1rem 0;
        }
        .solution-item-content-content {
          line-height: 1.5;
          font-size: 1rem;
        }
        .solution-item-content-advantage-con {
          margin: 2rem 0;
          font-size: 0.8rem;
          line-height: 2;
          .solution-item-content-advantage {
            .title-con {
              display: flex;
              .tip {
                margin-right: 5px;
              }
              .title {
                color: #000;
                span {
                  color: #666;
                }
              }
            }
          }
        }
        // .btn-con {
        //   color: #f3c51e;
        //   font-size: 0.8rem;
        //   cursor: pointer;
        //   text-align: center;
        // }
      }
      .solution-item-content-en {
        .solution-item-content-content {
          font-weight: 500;
        }
        .solution-item-content-title {
          font-weight: 600;
        }
        .solution-item-content-advantage-con {
          font-size: 1.1rem;
          line-height: 1.75;
          .solution-item-content-advantage {
            .title-con {
              .title {
                color: #000;
                font-weight: 600;
                span {
                  color: #666;
                  font-weight: 400;
                }
              }
            }
          }
        }
      }
      .btn-con {
        color: #f3c51e;
        font-size: 0.8rem;
        cursor: pointer;
        position: absolute;
        bottom: 2.5rem;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .function-solution {
    .function-solution-container {
      display: block;
      .function-solution-item {
        width: 100%;
        &:nth-child(2) {
          margin: 20px 0;
        }
        .solution-item-icon {
          .iconfont {
            font-size: 2.5rem;
            color: #f3c51e;
            font-weight: 800;
          }
        }
        .solution-item-content {
          padding: 2rem 2rem 5rem;
          .solution-item-content-title {
            font-size: 1.5rem;
            font-weight: 500;
            color: #000;
            margin: 25px 0;
          }
          .solution-item-content-content {
            line-height: 1.5;
            font-size: 1.3rem;
          }
          .solution-item-content-advantage-con {
            .solution-item-content-advantage {
              .title-con {
                display: flex;
                font-size: 1.3rem;
                .tip {
                }
                .title {
                  color: #000;
                  span {
                    color: #666;
                  }
                }
              }
            }
          }
        }
        .btn-con {
          font-size: 1.2rem;
          bottom: 3rem;
        }
        .solution-item-content-en {
          .solution-item-content-content {
            font-weight: 600;
          }
          .solution-item-content-title {
            font-weight: 600;
          }
        }
      }
    }
  }
}
</style>
