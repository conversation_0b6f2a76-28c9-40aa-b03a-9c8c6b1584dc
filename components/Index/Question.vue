<!-- 组件说明 -->
<template>
  <section class="index-question section">
    <div class="container">
      <h2 class="section-headline headline--main">{{$t('question.title')}}</h2>
      <div class="content">
        <div class="block">
			 <div class="question-dsc"> {{$tc('question.answer1',1)}}</div>
        <div class="answer">{{$tc('question.answer1',2)}}</div>
        </div>
        <div class="block">
			 <div class="question-dsc"> {{$tc('question.answer2',1)}}</div>
        <div class="answer">{{$tc('question.answer2',2)}}</div>
        </div>
        <div class="block">
			 <div class="question-dsc"> {{$tc('question.answer3',1)}}</div>
        <div class="answer">{{$tc('question.answer3',2)}}</div>
        </div>
        <div class="block">
			 <div class="question-dsc"> {{$tc('question.answer4',1)}}</div>
        <div class="answer">{{$tc('question.answer4',2)}}</div>
        </div>
        <div class="block">
			 <div class="question-dsc"> {{$tc('question.answer5',1)}}</div>
        <div class="answer">{{$tc('question.answer5',2)}}</div>
        </div>
        <div class="block">
			 <div class="question-dsc"> {{$tc('question.answer6',1)}}</div>
        <div class="answer">{{$tc('question.answer6',2)}}</div>
        </div>
        
      </div>
    </div>
  </section>
</template>

<script>
//import x from ''
export default {
  name: 'question',
  components: {},
  data() {
    return {};
  },
  computed: {},
  methods: {},
};
</script>

<style lang='scss' scoped>
//@import url()
.index-question {
  .container {
    .content {
      display: flex;
      justify-content: space-around;
      flex-wrap: wrap;
      .block {
        border: 1px solid #ebeef5;
        width: 48%;
        margin: 10px 1%;
        padding: 30px;
        text-align: left;
        border-radius: 5px;
        .question-dsc {
          font-weight: normal;
          font-size: 16px;
          color: #333;
          margin-bottom: 30px;
          line-height: 1.6;
        }
        .answer {
          font-size: 13px;
          color: #999;
          line-height: 1.7;
          margin: 0 0 7.5px;
        }
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .index-question {
    .container {
      .content {
        .block {
          width: 100%;
        }
      }
    }
  }
}
</style>
