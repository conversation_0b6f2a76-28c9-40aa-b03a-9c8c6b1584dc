
<template>
  <div class="index-scene">
    <div class="container-title">
      <h2 class="section-headline" :class="{'section-headline-en':isEN}">{{$t('case.evaluate')}}</h2>
    </div>
    <div class="container-logo">
      <div class="logoImg-con" >
        <img v-if="!isMobile" src="https://static.bestsign.cn:443/a752b879ad1e425a7953d7e93d5c848668af57a2.png" alt="">
        <img v-if="isMobile" src="https://static.bestsign.cn:443/3de6835d2a00808567abd0f255a7e23472fa57c0.png" alt="">
      </div>
    </div>
    <div class="container">
      
      <div class="content">
        <div class="swiper-container" v-swiper:mySwiper="isMobile?swiperOptionMobile:swiperOption" v-if="newEvaluate.length>1">
          <div class="swiper-wrapper">
				<div class="swiper-slide" v-for="(item, index) in newEvaluate" :key="index">
					<div class="item">
						  <div class="top">
								<!-- <div :class="['default-avatar', activeIndex === index? 'active-avatar':'']" :style="{backgroundImage: `url(${item.logo})`}"> -->
								<div :class="['default-avatar', activeIndex === index? 'active-avatar':'']">
                <div class="card" :class="{ 'en-card':isEN}">
                  <div class="img-footer">
                    <img
                      :key="item.newImgUrl"
                      class="card-image"
                      :src="item.newImgUrl">
				          </div>
                  <div class="card-content" :class="{'card-content-en':isEN}">
                    <h4 class="header">{{ item.customerName }}</h4>
                    <p class="body">{{item.evaluate}}</p>
                  </div>
                  
                </div>
                  
								</div>
						  </div>
					</div>
				</div>
          </div>
        </div>
        <div class="arrow" v-if="!isMobile">
          <div class="ssq-button-prev-a">
            <i class="el-icon-arrow-left"></i>
          </div>
          <div class="ssq-button-next-a">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
	    <div v-if="isMobile" class="swiper-pagination swiper-pagination__evaluate" slot="pagination"></div>
        <div class="ssq-button-primary is-white" @click="toMoreCase">{{$t('case.more')}}<i class="iconfont icon-jiantou"></i></div>
    </div>
  </div>
</template>

<script>
import Qs from 'qs';
const getJsonEn = () =>
  import('@/static/_caseEN.json').then(m => m.default || m);
const getJson = () => import('@/static/_case.json').then(m => m.default || m);
const getJsonJP = () =>
  import('@/static/_caseJP.json').then(m => m.default || m);

export default {
  name: 'index-scene',
  props: {},
  data() {
    return {
      newEvaluate: [],
      activeIndex: 0,
      swiperOption: {
        loop: true,
        initialSlide: 0,
        centeredSlides: true,
        slideToClickedSlide: true,
        speed: 700,
        slidesPerView: 3,
        navigation: {
          nextEl: '.ssq-button-next-a',
          prevEl: '.ssq-button-prev-a',
        },
      },
      swiperOptionMobile: {
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        pagination: {
          el: '.swiper-pagination.swiper-pagination__evaluate',
        },
      },
      isEN: false,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  methods: {
    toMoreCase() {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: `/${this.language}/case`,
        query: {
          ...Qs.parse(query),
        },
      });
    },
    handleChange(cur) {
      const activeIndex = cur.realIndex;
      this.activeIndex = activeIndex;
      this.getInfo();
    },
    getInfo() {
      this.$nextTick(() => {
        this.partnerInfo = this.newEvaluate.filter(
          (item, index) => index == this.activeIndex
        )[0];
      });
    },
  },
  mounted() {
    if (this.newEvaluate.length > 1) {
      const _this = this;
      this.mySwiper.on('slideChange', function() {
        _this.handleChange(this);
      });
      this.getInfo();
    }
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
  async created() {
    if (this.$store.state.locale.indexOf('en') != -1) {
      var caseResponse = await getJsonEn();
    } else if (this.$store.state.locale === 'ja') {
      var caseResponse = await getJsonJP();
    } else {
      var caseResponse = await getJson();
    }
    var shownListFilter = caseResponse.filter(item => item.pageNum === 1);
    // console.log(shownListFilter);
    const evaluateList = shownListFilter[0].data.filter(
      item => Number(item.id) <= 6
    );
    this.newEvaluate = evaluateList;
    // console.log(evaluateList);
  },
};
</script>

<style scoped lang="scss">
.index-scene {
  padding: 2rem 0 7rem;
  background-color: #f5f5f7;
  text-align: center;
  .container {
    margin-top: 5rem;
  }
  .section-headline {
    background: $-color-main;
    color: #fff;
    padding: 6rem 10% 4.5rem;
    width: 100%;
  }
  .container-logo {
    background: $-color-main;
    .logoImg-con {
      width: 65%;
      margin: 0 auto;
      padding: 0 0 7rem;
      img {
        width: 100%;
      }
    }
  }
  .content {
    width: 90%;
    position: relative;
    margin: 0 auto 3rem;
    .item {
      width: 100%;
      background-color: #f5f5f7;
      padding: 20px 10px;
      .top {
        display: flex;
        align-items: center;
        justify-content: center;
        // img {
        //   width: 90px;
        //   //   margin-right: 30px;
        //   border-radius: 50%;
        //   border: 1px solid #eee;
        // }
        .default-avatar {
          // margin: 0 auto;
          padding: 0;
          border-radius: 10px;
          width: 100%;
          height: auto;
          .iconfont-container {
            margin-bottom: 20px;
            .iconfont {
              color: #00aa64;
              font-size: 1.5rem;
            }
          }

          .title {
            color: #333;
            font-weight: 500;
            font-size: 1em;
          }
          .little-item-con {
            margin: 20px 0;
            .little-item {
              font-size: 0.8rem;
              color: #666;
              line-height: 1.5;
            }
          }
          .card {
            margin: 0;
            height: 40rem;
            width: 100%;
            .card-content {
              height: auto;
              padding: 1rem 2rem 2rem;
              .header {
                margin: 20px 0;
                font-size: 1.2rem;
                font-weight: 500;
              }
              .body {
                line-height: 1.5;
                font-size: 0.9rem;
              }
            }
            .card-content-en {
              .header {
                font-weight: 600;
              }
              .body {
                font-size: 1rem;
              }
            }
            .card-image {
              width: 100%;
              height: auto;
            }
          }
          .en-card {
            height: 47rem;
            .card-content {
              .body {
                text-align: left;
              }
            }
          }
        }

        .name {
          text-align: left;
          font-size: 20px;
          line-height: 1.3;
        }
      }
    }
    // .swiper-slide-active .default-avatar img {
    //   -ms-transform: scale(1.4); /* IE 9 */
    //   -webkit-transform: scale(1.4); /* Safari */
    //   transform: scale(1.4); /* 标准语法 */
    //   transition: transfrom 0.4s;
    // }
    .swiper-slide-prev {
      .item {
        .default-avatar {
          margin-left: 0;
        }
      }
    }
    .swiper-slide-next {
      .item {
        transition: all 0.5s;
        .default-avatar {
          margin-right: 0;
          transition: all 0.5s;
        }
      }
    }
  }
  .new-content {
    display: flex;
    justify-content: center;
    width: 80%;
    margin: 0 auto;
    .no-swiper-slide {
      padding: 0 10px;
      width: 16rem;
      .new-avatar {
        border-radius: 10px;
        width: 100%;
        .iconfont-container {
          margin-bottom: 20px;
          .iconfont {
            color: #00aa64;
            font-size: 1.5rem;
          }
        }

        .title {
          color: #333;
          font-weight: 500;
          font-size: 1em;
        }
        .little-item-con {
          margin: 20px 0;
          .little-item {
            font-size: 0.8rem;
            color: #666;
            line-height: 1.5;
          }
        }
      }
    }
  }
  .main-content {
    max-width: 40rem;
    text-align: center;
    text-align: justify;
    line-height: 1.75;
    margin: 0 auto;
    color: #86868b;
  }
  .name {
    margin-top: 2.5rem;
    .title {
      font-size: 20px;
      font-weight: 600;
    }
    .label {
      font-size: 18px;
      margin: 1rem 0 2rem 0;
    }
  }
  .ssq-button-more {
    color: #00aa64;
    font-size: 14px;
    .iconfont {
      font-size: 14px;
    }
  }
  .ssq-button-prev-a,
  .ssq-button-next-a {
    position: absolute;
    top: 50%;
    transform: translate3d(0, -50%, 0);
    background: transparent;
    cursor: pointer;
    > i {
      font-size: 24px;
      color: rgba(35, 24, 21, 0.5);
      outline: none;
      background: #eee;
      border-radius: 50%;
      padding: 4px;
      &:hover {
        color: rgba(35, 24, 21, 1);
      }
      &:focus {
        outline: none;
      }
    }
    &:focus {
      outline: none;
    }
  }
  .ssq-button-prev-a {
    left: -60px;
  }
  .ssq-button-next-a {
    right: -60px;
  }
  .ssq-button-primary {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    width: 160px;
    height: 50px;
    box-shadow: 0 0 0 0;
    font-weight: 600;
    .iconfont {
      font-size: 12px;
      margin-left: 20px;
    }
  }
  .is-white {
    &:hover {
      background: #fff;
      color: $-color-main;
    }
  }
}
.swiper-pagination {
  width: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 20px;
  /deep/ .swiper-pagination-bullet {
    margin: 0 10px;
  }
  /deep/ .swiper-pagination-bullet-active {
    background: #62686f;
  }
}
@media screen and (max-width: 767px) {
  .index-scene {
    padding: 40px 0px 80px;
    .section-headline {
      padding: 5rem 0 4.5rem;
    }
    .container-logo {
      .logoImg-con {
        width: 89%;
      }
    }
    .content {
      position: relative;
      width: 100%;
      padding: 0;
      margin: 0;
      .main-content {
        font-size: 14px;
        color: #86868b;
      }
      .iconfont {
        font-size: 14px;
      }
      .swiper-slide-active .default-avatar img {
        -ms-transform: scale(1); /* IE 9 */
        -webkit-transform: scale(1); /* Safari */
        transform: scale(1); /* 标准语法 */
        transition: transfrom 0.4s;
      }

      .item {
        width: 100%;
        padding: 0 5px;
        .top {
          .default-avatar {
            padding: 0;
            .title {
              font-size: 1.3em;
            }
            .little-item-con {
              .little-item {
                font-size: 1.2rem;
              }
            }
            .card {
              height: 52rem;
              .card-content {
                height: auto;
                padding: 0 2rem;
                .header {
                  margin: 20px 0;
                  font-size: 1.5rem;
                  font-weight: 500;
                }
                .body {
                  line-height: 1.5;
                  font-size: 1.3rem;
                }
              }
              .card-image {
                width: 100%;
                height: auto;
              }
            }
            .en-card {
              height: 55rem;
              .card-content {
                .body {
                  text-align: left;
                }
              }
              .card-content-en {
                .header {
                  font-weight: 600;
                }
              }
            }
          }
          .name {
            text-align: left;
            font-size: 16px;
            line-height: 1.3;
          }
        }
        .main-content {
          text-align: justify;
          line-height: 1.75;
          margin-top: 1.5rem;
          color: #717171;
        }
      }
    }
    .ssq-button-primary {
      margin: 60px auto 0;
    }

    .ssq-button-prev-a,
    .ssq-button-next-a {
      > i {
        font-size: 22px;
      }
    }
    .ssq-button-prev-a {
      left: -12px;
    }
    .ssq-button-next-a {
      right: -12px;
    }
  }
}
</style>
