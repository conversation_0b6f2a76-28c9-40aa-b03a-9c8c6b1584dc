<template>
  <div class="demo-header">
    <div class="container">
      <div class="brand">
        <a
          @click="handleIndex"
          class="nav-brand">
          <img width="87px" src="@/assets/images/logo.svg" alt="上上签logo，电子合同">
        </a>
        <span class="brand-text border-left">免费试用</span>
      </div>
      <div class="operation">
        <a @click="handleIndex" class="brand-text"><i class="el-icon-arrow-left" style="font-size: 18px;"></i><span v-if="!isMobile">返回官网</span></a>
        <!--<span class="right">-->
          <!--<a @click="loginEnt" class="brand-text " :class="{'border-left': !isMobile}">登录</a>-->
          <!--<a @click="toReg" class="brand-text" :class="{'border-left': isMobile}">注册</a>-->
        <!--</span>-->
      </div>
    </div>
  </div>
</template>

<script>
import Qs from 'qs';
import isEmpty from 'lodash/isEmpty';
export default {
  name: 'DemoHeader',
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {
    loginEnt() {
      const url =
        window.location.host.indexOf('cn') > -1
          ? 'https://ent.bestsign.cn'
          : 'https://ent.bestsign.info';
      window.open(url);
    },
    toReg() {
      const query = sessionStorage.getItem('query');
      let queryString;
      if (!isEmpty(query)) {
        queryString = Qs.stringify(JSON.parse(query));
      }
      const url =
        window.location.host.indexOf('cn') > -1
          ? 'https://ent.bestsign.cn/register'
          : 'https://ent.bestsign.info/register';
      window.open(url + '?' + queryString);
    },
    handleIndex() {
      if (this.isMobile) {
        this.$router.go(-1);
      } else {
        const query = sessionStorage.getItem('query');
        this.$router.push({
          path: '/',
          query: Qs.parse(query),
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.demo-header {
  width: 100%;
  height: 66px;
  line-height: 66px;
  background-color: #fff;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.25);
  .container {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
  }
  .nav-brand {
    width: 87px;
    height: 37px;
    cursor: pointer;
    img {
      width: 87px;
    }
  }
  .brand-text {
    cursor: pointer;
    line-height: 20px;
    font-size: 16px;
    padding: 0 12px;
    margin-left: 12px;
    color: #00aa64;
    &.border-left {
      border-left: 1px solid #00aa64;
    }
  }
  .brand {
    display: inline-block;
  }
  .operation {
    float: right;
    .brand-text {
      margin-left: 0;
    }
  }
}
</style>
