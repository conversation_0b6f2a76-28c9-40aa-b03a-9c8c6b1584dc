<template>
  <div class="slide-demo">
    <div ref="dragDiv" class="drag drag-demo" v-show="getTraceStatus" :class="{ dragMobile:isMobile}">
      <div class="drag_bg" :style="{width:drag_with +'px'}"></div>
      <div class="drag_text" :style="{color: textColor, backgroundColor:textBdg, border:textBorder}">
        {{ loadingStatus ? loadingTips: successStatus ? successTips : tips}}
        <i class="el-icon-loading" v-show="loadingStatus"></i>
      </div>
      <div ref="moveDiv" class="handler handler_bg" :class="[successStatus ? 'handler_ok_bg':'']"
           :style="{left:handlerLeft + 'px'}"
           @mousedown="mousedownFn" @touchstart="touchdownFn"></div>
    </div>
    <div v-if="!getTraceStatus" class="textErrCss" :class="{ textErrCssMobile:isMobile}">
      <i class="el-icon-warning"></i>
      <span class="span-text" style="font-size:10px;">
        出错了，点击<span class="span-btn" @click="getNewTraceStatus">刷新</span>再来一次
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SliderVerify',
  props: {
    tips: {
      type: String,
      default: '请按住滑块，拖动到最后边',
    },
    successTips: {
      type: String,
      default: '验证通过',
    },
    loadingTips: {
      type: String,
      default: '加载中',
    },
    status: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      successStatus: false,
      beginClientX: 0 /* 距离屏幕左端距离 */,
      mouseMoveStatus: false /* 触发拖动状态  判断 */,
      maxWidth: '' /* 拖动最大宽度，依据滑块宽度算出来的 */,
      init: {} /* 鼠标的初始位置 */,
      trace: [] /* 存储鼠标点的轨迹 */,
      pxScale: 1,
      // 拖拽的样式
      textColor: '#222427',
      textBdg: '',
      textBorder: '',
      handlerLeft: 0,
      dragWidth: 0,
      // 后端校验，判定不是人为划动
      getTraceStatus: true,
      // timer: null,
      checkStatus: false,
      loadingStatus: false,
    };
  },
  computed: {
    __status: {
      get() {
        return this.status;
      },
    },
    drag_with: {
      get() {
        return this.dragWidth;
      },
    },
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  mounted() {
    this.maxWidth =
      this.$refs.dragDiv.clientWidth - this.$refs.moveDiv.clientWidth;
    // pc端鼠标移动
    document
      .getElementsByTagName('html')[0]
      .addEventListener('mousemove', this.mouseMoveFn);
    document
      .getElementsByTagName('html')[0]
      .addEventListener('mouseup', this.moseUpFn);
    // 手机端手动滑动
    document
      .getElementsByTagName('html')[0]
      .addEventListener('touchmove', this.touchMoveFn);
    document
      .getElementsByTagName('html')[0]
      .addEventListener('touchend', this.touchUpFn);
    // this.sideInit()
  },
  /*  离开页面就销毁计时器 */
  beforeDestroy() {
    clearInterval(this.timer);
  },
  methods: {
    // 初始化数据
    initData() {
      this.init = {};
      this.trace = [];
      this.textColor = '#222427';
      this.textBdg = '';
      this.textBorder = '';
      this.handlerLeft = 0;
      this.dragWidth = 0;
    },
    initEventListener() {
      // pc端鼠标移动
      document
        .getElementsByTagName('html')[0]
        .addEventListener('mousemove', this.mouseMoveFn);
      document
        .getElementsByTagName('html')[0]
        .addEventListener('mouseup', this.moseUpFn);
      // 手机端手动滑动
      document
        .getElementsByTagName('html')[0]
        .addEventListener('touchmove', this.touchMoveFn);
      document
        .getElementsByTagName('html')[0]
        .addEventListener('touchend', this.touchUpFn);
    },
    // 重新显示滑动页面
    getNewTraceStatus() {
      this.getTraceStatus = true;
      // 重新初始化数据
      this.initData();
      // 初始化监听
      this.initEventListener();
      // this.sideInit()
      console.log(this.init);
      console.log(this.trace);
    },
    // pc端鼠标滑动
    mousedownFn(e) {
      if (this.successStatus) return;
      e.preventDefault && e.preventDefault(); // 阻止文字选中等 浏览器默认事件
      this.mouseMoveStatus = true;
      this.beginClientX = e.clientX;
      this.init = { x: e.clientX, y: e.clientY };
      this.trace = [];
    },
    // 手机端手动滑动
    touchdownFn(e) {
      if (this.successStatus) return;
      e.preventDefault && e.preventDefault(); // 阻止文字选中等 浏览器默认事件
      this.mouseMoveStatus = true;
      this.beginClientX = e.changedTouches[0].clientX;
      this.init = {
        x: e.changedTouches[0].clientX,
        y: e.changedTouches[0].clientY,
      };
      this.trace = [];
    },
    successFunction() {
      this.mouseMoveStatus = false;
      // this.successCss = true
      // pc 端鼠标滑动
      document
        .getElementsByTagName('html')[0]
        .removeEventListener('mousemove', this.mouseMoveFn);
      document
        .getElementsByTagName('html')[0]
        .removeEventListener('mouseup', this.moseUpFn);
      // 手机端手动滑动
      document
        .getElementsByTagName('html')[0]
        .removeEventListener('touchmove', this.touchMoveFn);
      document
        .getElementsByTagName('html')[0]
        .removeEventListener('touchend', this.touchUpFn);
      // document.getElementsByClassName('drag_text')[0].style.color = '#ffff'
      // document.getElementsByClassName('drag_text')[0].style.backgroundColor = '#00AA64'
      // document.getElementsByClassName('drag_text')[0].style.border = '0.5px solid  #00AA64'
      // document.getElementsByClassName('handler')[0].style.left = this.maxWidth + 'px'
      // document.getElementsByClassName('drag_bg')[0].style.width = this.maxWidth * this.pxScale  + 'px'
      this.textColor = '#fff';
      this.textBdg = '#00AA64';
      this.textBorder = '1px solid  #00AA64';
      this.handlerLeft = this.maxWidth;
      this.dragWidth = this.maxWidth * this.pxScale;
      this.loadingStatus = true;
      // this.$emit('success', this.trace)
      //  this.getTraceStatus = false
      //  this.loadingStatus = false
      this.getPostMethod();
    },
    //  pc端鼠标滑动方法
    mouseMoveFn(e) {
      if (!this.mouseMoveStatus) return;
      let ctX = e.clientX - this.init.x;
      let ctY = e.clientX - this.init.y;
      this.trace.push({
        x: ctX,
        y: ctY,
        z: new Date().getTime(),
      });
      // this.trace.push(e)
      const width = e.clientX - this.beginClientX;
      if (width && width <= this.maxWidth) {
        if (width < 0) {
          this.$el.getElementsByClassName('handler')[0].style.left = 0 + 'px';
        } else {
          this.$el.getElementsByClassName('handler')[0].style.left =
            width + 'px';
          this.$el.getElementsByClassName('drag_bg')[0].style.width =
            width + 'px';
          this.$el.getElementsByClassName('drag_bg')[0].style.backgroundColor =
            '#00AA64';
          this.$el.getElementsByClassName('drag_bg')[0].style.border =
            '1px solid #00AA64';
          this.$el.getElementsByClassName('drag_bg')[0].style.borderRadius =
            '2px';
          this.dragWidth = width;
        }
      } else if (width > this.maxWidth * this.pxScale) {
        this.successFunction();
      }
    },
    moseUpFn(e) {
      this.mouseMoveStatus = false;
      const width = e.clientX - this.beginClientX;
      if (width < this.maxWidth * this.pxScale) {
        this.$el.getElementsByClassName('handler')[0].style.left = 0 + 'px';
        this.$el.getElementsByClassName('drag_bg')[0].style.width = 0 + 'px';
      }
    },
    //  手机端手动滑动方法
    touchMoveFn(e) {
      if (!this.mouseMoveStatus) return;
      let ctX = e.changedTouches[0].clientX - this.init.x;
      let ctY = e.changedTouches[0].clientX - this.init.y;
      this.trace.push({
        x: ctX,
        y: ctY,
        z: new Date().getTime(),
      });

      // this.trace.push(e)
      const phoneWidth = e.changedTouches[0].clientX - this.beginClientX;
      if (phoneWidth && phoneWidth <= this.maxWidth * this.pxScale) {
        if (phoneWidth < 0) {
          this.$el.getElementsByClassName('handler')[0].style.left = 0 + 'px';
        } else {
          this.$el.getElementsByClassName('handler')[0].style.left =
            phoneWidth + 'px';
          this.$el.getElementsByClassName('drag_bg')[0].style.width =
            phoneWidth + 'px';
          this.$el.getElementsByClassName('drag_bg')[0].style.backgroundColor =
            '#00AA64';
          this.$el.getElementsByClassName('drag_bg')[0].style.border =
            '1px solid #00AA64';
          this.$el.getElementsByClassName('drag_bg')[0].style.borderRadius =
            '2px';
          this.dragWidth = phoneWidth;
        }
      } else if (phoneWidth > this.maxWidth * this.pxScale) {
        this.successFunction();
      }
    },
    touchUpFn(e) {
      this.mouseMoveStatus = false;
      const phoneWidth = e.changedTouches[0].clientX - this.beginClientX;
      if (phoneWidth < this.maxWidth * this.pxScale) {
        this.$el.getElementsByClassName('handler')[0].style.left = 0 + 'px';
        this.$el.getElementsByClassName('drag_bg')[0].style.width = 0 + 'px';
      }
    },
    getPostMethod() {
      this.$axios
        .post('/www/api/demo/slideverify', {
          trailPoints: this.trace,
        })
        .then(res => {
          if (res.code === '150001') {
            this.successStatus = true;
            this.checkStatus = true;
            this.loadingStatus = false;
            //  clearInterval(this.timer)
          } else {
            // this.getTraceStatus = res.data.message
            this.getTraceStatus = false;
            this.loadingStatus = false;
          }
          this.$emit('success', this.checkStatus);
        })
        .catch(err => {
          this.getTraceStatus = false;
          this.loadingStatus = false;
          // this.$message(err.message)
        });
    },
  },
};
</script>

<style lang="scss" scoped>
$scale: 1;
@mixin common {
  .drag {
    position: relative;
    background: #f4f4f4;
    width: 300px * $scale;
    height: 36px * $scale;
    line-height: 34px * $scale;
    text-align: center;
    border-radius: 2px * $scale;
  }
  .dragMobile {
    position: relative;
    background: #f4f4f4;
    width: 200px * $scale;
    height: 36px * $scale;
    line-height: 34px * $scale;
    text-align: center;
    border-radius: 2px * $scale;
  }
  .handler {
    position: absolute;
    top: 0;
    left: 0;
    width: 46px * $scale;
    height: 36px * $scale;
    border: 1px solid #dcdfe6;
    border-radius: 2px * $scale;
    cursor: move;
  }

  .handler_bg {
    background: #fff
      url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA3hpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDIxIDc5LjE1NTc3MiwgMjAxNC8wMS8xMy0xOTo0NDowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo0ZDhlNWY5My05NmI0LTRlNWQtOGFjYi03ZTY4OGYyMTU2ZTYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NTEyNTVEMURGMkVFMTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NTEyNTVEMUNGMkVFMTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTQgKE1hY2ludG9zaCkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo2MTc5NzNmZS02OTQxLTQyOTYtYTIwNi02NDI2YTNkOWU5YmUiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NGQ4ZTVmOTMtOTZiNC00ZTVkLThhY2ItN2U2ODhmMjE1NmU2Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+YiRG4AAAALFJREFUeNpi/P//PwMlgImBQkA9A+bOnfsIiBOxKcInh+yCaCDuByoswaIOpxwjciACFegBqZ1AvBSIS5OTk/8TkmNEjwWgQiUgtQuIjwAxUF3yX3xyGIEIFLwHpKyAWB+I1xGSwxULIGf9A7mQkBwTlhBXAFLHgPgqEAcTkmNCU6AL9d8WII4HOvk3ITkWJAXWUMlOoGQHmsE45ViQ2KuBuASoYC4Wf+OUYxz6mQkgwAAN9mIrUReCXgAAAABJRU5ErkJggg==')
      no-repeat center;
  }

  /*.handler_ok_bg {*/
  /*background: #fff url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA3hpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDIxIDc5LjE1NTc3MiwgMjAxNC8wMS8xMy0xOTo0NDowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo0ZDhlNWY5My05NmI0LTRlNWQtOGFjYi03ZTY4OGYyMTU2ZTYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NDlBRDI3NjVGMkQ2MTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NDlBRDI3NjRGMkQ2MTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTQgKE1hY2ludG9zaCkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDphNWEzMWNhMC1hYmViLTQxNWEtYTEwZS04Y2U5NzRlN2Q4YTEiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NGQ4ZTVmOTMtOTZiNC00ZTVkLThhY2ItN2U2ODhmMjE1NmU2Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+k+sHwwAAASZJREFUeNpi/P//PwMyKD8uZw+kUoDYEYgloMIvgHg/EM/ptHx0EFk9I8wAoEZ+IDUPiIMY8IN1QJwENOgj3ACo5gNAbMBAHLgAxA4gQ5igAnNJ0MwAVTsX7IKyY7L2UNuJAf+AmAmJ78AEDTBiwGYg5gbifCSxFCZoaBMCy4A4GOjnH0D6DpK4IxNSVIHAfSDOAeLraJrjgJp/AwPbHMhejiQnwYRmUzNQ4VQgDQqXK0ia/0I17wJiPmQNTNBEAgMlQIWiQA2vgWw7QppBekGxsAjIiEUSBNnsBDWEAY9mEFgMMgBk00E0iZtA7AHEctDQ58MRuA6wlLgGFMoMpIG1QFeGwAIxGZo8GUhIysmwQGSAZgwHaEZhICIzOaBkJkqyM0CAAQDGx279Jf50AAAAAABJRU5ErkJggg==") no-repeat center;*/
  /*}*/
  .handler_ok_bg {
    background: #fff url('../assets/images/icon/ic_yanzheng_wancheng.svg')
      no-repeat center;
  }

  .drag_bg {
    background-color: #e2f3d9;
    border: 1px solid #d4edc6;
    border-right: 0;
    height: 36px * $scale;
    width: 0;
  }
  .drag_text {
    height: 36px;
    line-height: 34px;
    // border: 0px solid #DCDFE6;
    border: 1px solid #d8dadd;
    border-radius: 2px;
    position: absolute;
    top: 0;
    width: 100%;
    font-size: 10px * $scale;
    text-align: center;
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
    -o-user-select: none;
    -ms-user-select: none;
    padding-left: 20px;
    .icon {
      background: transparent;
      z-index: 3;
      color: #ffffff;
    }
  }
  .textErrCss {
    width: 300px * $scale;
    height: 36px * $scale;
    line-height: 20px;
    // text-indent: 3px;
    border: #faf1d5 1px solid;
    background-image: none;
    font-size: 14px;
    padding: 7px 5px 8px 10px;
    color: #ef9f06;
    background: #ffffff;
    .el-icon-warning {
      cursor: default;
      color: #ff3f08;
      font-size: 18px;
      float: left;
      background: transparent;
      z-index: 3;
    }
    .span-text {
      display: inline-block;
      margin-left: 10px;
      float: left;
      .span-btn {
        //  display: inline-block;
        color: #00aa64;
        cursor: pointer;
      }
    }
  }
  .textErrCssMobile {
    width: 200px * $scale;
    height: 36px * $scale;
    line-height: 20px;
    // text-indent: 3px;
    border: #faf1d5 1px solid;
    background-image: none;
    font-size: 14px;
    padding: 7px 5px 8px 10px;
    color: #ef9f06;
    background: #ffffff;
    .el-icon-warning {
      cursor: default;
      color: #ff3f08;
      font-size: 18px;
      float: left;
      background: transparent;
      z-index: 3;
    }
    .span-text {
      display: inline-block;
      margin-left: 10px;
      float: left;
      .span-btn {
        //  display: inline-block;
        color: #00aa64;
        cursor: pointer;
      }
    }
  }
}
.slide-demo {
  @include common;
}
.slide-demo[data-demo-pc] {
  @include common;
}
.demo-mobile .slide-demo {
  .drag {
    position: relative;
    background: #f4f4f4;
    width: 337px;
    height: 42px;
    line-height: 40px;
    text-align: center;
    border-radius: 2px;
  }

  .handler {
    position: absolute;
    top: 0;
    left: 0;
    width: 46px;
    height: 42px;
    border: 1px solid #dcdfe6;
    border-radius: 2px;
    cursor: move;
  }

  .handler_bg {
    background: #fff
      url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA3hpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDIxIDc5LjE1NTc3MiwgMjAxNC8wMS8xMy0xOTo0NDowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo0ZDhlNWY5My05NmI0LTRlNWQtOGFjYi03ZTY4OGYyMTU2ZTYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NTEyNTVEMURGMkVFMTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NTEyNTVEMUNGMkVFMTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTQgKE1hY2ludG9zaCkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo2MTc5NzNmZS02OTQxLTQyOTYtYTIwNi02NDI2YTNkOWU5YmUiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NGQ4ZTVmOTMtOTZiNC00ZTVkLThhY2ItN2U2ODhmMjE1NmU2Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+YiRG4AAAALFJREFUeNpi/P//PwMlgImBQkA9A+bOnfsIiBOxKcInh+yCaCDuByoswaIOpxwjciACFegBqZ1AvBSIS5OTk/8TkmNEjwWgQiUgtQuIjwAxUF3yX3xyGIEIFLwHpKyAWB+I1xGSwxULIGf9A7mQkBwTlhBXAFLHgPgqEAcTkmNCU6AL9d8WII4HOvk3ITkWJAXWUMlOoGQHmsE45ViQ2KuBuASoYC4Wf+OUYxz6mQkgwAAN9mIrUReCXgAAAABJRU5ErkJggg==')
      no-repeat center;
  }

  /*.handler_ok_bg {*/
  /*background: #fff url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA3hpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDIxIDc5LjE1NTc3MiwgMjAxNC8wMS8xMy0xOTo0NDowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo0ZDhlNWY5My05NmI0LTRlNWQtOGFjYi03ZTY4OGYyMTU2ZTYiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NDlBRDI3NjVGMkQ2MTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NDlBRDI3NjRGMkQ2MTFFNEI5NDBCMjQ2M0ExMDQ1OUYiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTQgKE1hY2ludG9zaCkiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDphNWEzMWNhMC1hYmViLTQxNWEtYTEwZS04Y2U5NzRlN2Q4YTEiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NGQ4ZTVmOTMtOTZiNC00ZTVkLThhY2ItN2U2ODhmMjE1NmU2Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+k+sHwwAAASZJREFUeNpi/P//PwMyKD8uZw+kUoDYEYgloMIvgHg/EM/ptHx0EFk9I8wAoEZ+IDUPiIMY8IN1QJwENOgj3ACo5gNAbMBAHLgAxA4gQ5igAnNJ0MwAVTsX7IKyY7L2UNuJAf+AmAmJ78AEDTBiwGYg5gbifCSxFCZoaBMCy4A4GOjnH0D6DpK4IxNSVIHAfSDOAeLraJrjgJp/AwPbHMhejiQnwYRmUzNQ4VQgDQqXK0ia/0I17wJiPmQNTNBEAgMlQIWiQA2vgWw7QppBekGxsAjIiEUSBNnsBDWEAY9mEFgMMgBk00E0iZtA7AHEctDQ58MRuA6wlLgGFMoMpIG1QFeGwAIxGZo8GUhIysmwQGSAZgwHaEZhICIzOaBkJkqyM0CAAQDGx279Jf50AAAAAABJRU5ErkJggg==") no-repeat center;*/
  /*}*/
  .handler_ok_bg {
    background: #fff url('../assets/images/icon/ic_yanzheng_wancheng.svg')
      no-repeat center;
  }
  .drag_bg {
    background-color: #e2f3d9;
    border: 1px solid #d4edc6;
    border-right: 0;
    height: 42px;
    width: 0;
  }
  .drag_text {
    /*border: 1px solid #DCDFE6;*/
    border: 0px solid #dcdfe6;
    border-radius: 2px;
    position: absolute;
    top: 0;
    height: 42px;
    line-height: 42px;
    width: 100%;
    font-size: 10px;
    text-align: center;
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
    -o-user-select: none;
    -ms-user-select: none;
    padding-left: 20px;
    .icon {
      background: transparent;
      z-index: 3;
      color: #ffffff;
    }
  }
  .textErrCss {
    width: 337px;
    height: 46px;
    line-height: 20px;
    // text-indent: 3px;
    border: #faf1d5 1px solid;
    background-image: none;
    font-size: 16px;
    padding: 12px 5px 14px 10px;
    color: #ef9f06;
    background: #ffffff;
    .el-icon-warning {
      cursor: default;
      color: #ff3f08;
      font-size: 18px;
      float: left;
      background: transparent;
      z-index: 3;
      vertical-align: middle;
      margin-top: 0.5vw;
    }
    .span-text {
      display: inline-block;
      margin-left: 10px;
      float: left;
      .span-btn {
        //  display: inline-block;
        color: #00aa64;
        cursor: pointer;
      }
    }
  }
}
</style>
