<template>
  <div class="energy-saving">
    <div class="container">
      <h3>使用上上签电子签约，不仅降本增效，而且更环保。</h3>
      <div class="data">
        <div class="item">
          <div class="img"><img src="@/assets/images/energy-saving/icon-1.png" alt=""></div>
          <div class="tips">
            <div class="text">上上签已保护树木</div>
            <div class="number"><span class="num">
              <ICountUp
                :delay="delay"
                :endVal="endValTree"
                :options="optionsTree"
              />
            </span><span class="unit">棵</span></div>
          </div>
        </div>
        <div class="item">
          <div class="img"><img src="@/assets/images/energy-saving/icon-2.png" alt=""></div>
          <div class="tips">
            <div class="text">已为企业减少碳排放</div>
            <div class="number"><span class="num">
              <ICountUp
                :delay="delay"
                :endVal="endValCarton"
                :options="optionsCarton"
              />
            </span><span class="unit">kg</span></div>
          </div>
        </div>
      </div>
      <div class="data no-flex">
        <div class="item">
          <div class="tips">
            <div class="text">共节省成本</div>
            <div class="number"><span class="num">
              <ICountUp
                :delay="delay"
                :endVal="endValTotal"
                :options="optionsTotal"
              />
            </span><span class="unit">元</span></div>
          </div>
        </div>
      </div>
      <div class="ssq-button-primary" @click="visible = !visible">成本计算器</div>
    </div>
    <el-dialog
      :visible.sync="visible"
      class="modal"
      width="600px"
      top="10vh"
    >
      <div class="rule-wrapper">
        <div class="header">
          <div class="title">算算你的企业使用纸质合同每年需要花费多少成本</div>
          <el-input class="item" type="number" v-model="papers" placeholder="请输入每年使用的纸质合同量"></el-input>
          <p class="total">各项成本汇总 <span class="num">{{ papers * 42 }}元</span></p>
          <div class="result">
            <span><img src="./icon3.png" alt="">打印成本</span>
            <span class="price">{{ papers * 1 }}元</span>
          </div>
          <div class="result">
            <span><img src="./icon2.png" alt="">快递成本</span>
            <span class="price">{{ papers * 26 }}元</span>
          </div>
          <div class="result">
            <span><img src="./icon1.png" alt="">人工成本</span>
            <span class="price">{{ papers * 15 }}元</span>
          </div>
          <div class="ssq-button-primary" style="margin-bottom: 24px" @click="toReg">立即节省成本</div>
        </div>
        <div class="bottom">
          <div class="title top">节省成本计算规则：</div>
          <div>一份合同总成本=打印成本+快递成本+人工成本</div>
          <div><span class="bold">打印成本：</span>每份纸质合同大概5页，一式两份</div>
          <div><span class="bold">快递成本：</span>每份纸质合同需要来回快递两次</div>
          <div><span class="bold">人工成本：</span>包括打印、盖章、封装、邮寄、核对、扫描、归档等</div>
          <div>节省总成本=截至目前上上签已发送的电子合同总数*一份合同总成本</div>
          <div class="title">保护树木成本计算规则：</div>
          <div>一棵树可以制造30000张A4纸（数据来源：知乎）</div>
          <div>保护树木总数=截至目前上上签已发送的电子合同总数*（合同页数）/一棵树可造纸张数</div>
          <div class="title">减少碳排放量计算规则：</div>
          <div>一棵树一年可减少17.9kg碳排放（数据根据北京环境交易所科学测算）</div>
          <div>减少碳排放量=保护树木总数*17.9kg</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Message } from 'element-ui';
import isEmpty from 'lodash/isEmpty';
import Qs from 'qs';
import axios from 'axios';
import moment from 'moment';

export default {
  name: 'EnergySaving',
  components: {},
  data() {
    return {
      visible: false,
      delay: 1000,
      endValTotal: 0,
      endValTree: 0,
      endValCarton: 0,
      optionsTotal: {
        startVal: 0,
        useEasing: true,
        useGrouping: true,
      },
      optionsTree: {
        startVal: 0,
        useEasing: true,
        useGrouping: true,
      },
      optionsCarton: {
        startVal: 0,
        useEasing: true,
        useGrouping: true,
      },
      count: 0,
      papers: null,
    };
  },
  mounted() {
    window.addEventListener('scroll', this.handleScroll);
  },
  methods: {
    toReg() {
      const query = sessionStorage.getItem('query');
      let queryString;
      if (!isEmpty(query)) {
        queryString = Qs.stringify(JSON.parse(query));
      }
      const href = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push([
            '_trackEvent',
            'click_register_mobile',
            'click',
            href,
          ]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_register_pc', 'click', href]);
      }
      const url =
        window.location.host.indexOf('cn') > -1
          ? 'https://ent.bestsign.cn/register'
          : 'https://ent.bestsign.info/register';
      window.open(url + '?' + 'hp_savecost&' + queryString);
    },
    async handleFetch() {
      try {
        const day = moment()
          .subtract(1, 'days')
          .format('YYYY-MM-DD');
        if (this.count === 0) {
          const store = this.handleGetStore(day);
          let res;
          if (store) {
            res = store;
          } else {
            const { data } = await axios.get('/www-api/saving-data');
            res = data;
          }
          if (res.code === 0) {
            this.optionsCarton.startVal = res.data.totalCartonBefore;
            this.endValCarton = res.data.totalCarton;
            this.optionsTree.startVal = res.data.totalTreeBefore;
            this.endValTree = res.data.totalTree;
            this.optionsTotal.startVal = res.data.totalBefore;
            this.endValTotal = res.data.total;
            this.handleStore(day, res);
          } else {
            Message.error(res.msg);
          }
        }
      } catch (e) {
        Message.error(e);
      } finally {
        this.count++;
      }
    },
    handleReset() {
      this.count = 0;
      this.optionsCarton.startVal = 0;
      this.endValCarton = 0;
      this.optionsTree.startVal = 0;
      this.endValTree = 0;
      this.optionsTotal.startVal = 0;
      this.endValTotal = 0;
    },
    handleScroll() {
      const scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      const offsetTop = document.querySelector('.energy-saving').offsetTop;
      if (scrollTop > offsetTop - 300 && scrollTop < offsetTop + 600) {
        this.handleFetch();
      } else if (scrollTop > offsetTop + 800) {
        this.handleReset();
      }
    },
    handleStore(day, data) {
      localStorage.setItem(day, JSON.stringify(data));
    },
    handleGetStore(day) {
      const data = localStorage.getItem(day);
      return data && JSON.parse(data);
    },
  },
};
</script>
<style lang="scss">
.energy-saving {
  .el-dialog__body {
    color: #282828;
  }
}
</style>
<style scoped lang="scss">
.energy-saving {
  width: 100%;
  height: 600px;
  background: url(~assets/images/energy-saving/bg_tree.jpg) no-repeat;
  color: #fff;
  padding-top: 70px;
  h3 {
    font-size: 28px;
    font-weight: 400;
  }
  .data {
    width: 750px;
    margin: 80px auto 0;
    display: flex;
    justify-content: space-between;
    &.no-flex {
      display: block;
      margin-top: 48px;
      .item {
        align-items: center;
        justify-content: center;
        .tips {
          text-align: center;
          margin-left: 0;
        }
        .number .num {
          font-size: 52px;
          letter-spacing: 6px;
          font-weight: bold;
        }
      }
    }
    .item {
      display: flex;
      .tips {
        margin-left: 24px;
        text-align: left;
      }
      .text {
        height: 30px;
        line-height: 30px;
        font-size: 18px;
      }
      .number {
        margin-top: 5px;
        .num {
          font-size: 38px;
          letter-spacing: 2px;
          font-family: 'Roboto', sans-serif;
        }
        .unit {
          font-size: 18px;
        }
      }
    }
  }
  .ssq-button-primary {
    width: 170px;
    margin: 32px auto 0;
    background: #00a664;
    font-size: 17px;
  }
  .btn-text {
    margin-top: 40px;
    display: inline-block;
    font-size: 14px;
    line-height: 26px;
    border-bottom: 1px solid #fff;
    cursor: pointer;
  }
}
.modal {
  text-align: left;
  color: #282828;
  font-size: 15px;
  line-height: 24px;
  .title {
    padding-top: 24px;
    font-size: 17px;
    font-weight: 600;
  }
  .bold {
    font-weight: 600;
  }
  .header {
    border-bottom: 1px solid #eee;
    text-align: center;
    .title {
      padding-top: 0;
    }
    .el-input,
    .result {
      width: 270px;
      margin: 0 auto;
    }
    .num {
      color: #ff6432;
    }
    .total {
      margin: 16px auto;
      font-size: 17px;
      font-weight: 600;
    }
    .result {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 40px;
      background-color: #f4f4f4;
      border: 1px solid #c2c2c2;
      color: #5e5e5e;
      border-radius: 4px;
      padding: 0 16px;
      margin-bottom: 16px;
      img {
        padding-right: 8px;
      }
      .price {
        color: #ff6432;
      }
    }
    .item {
      margin-top: 16px;
    }
  }
}
</style>
