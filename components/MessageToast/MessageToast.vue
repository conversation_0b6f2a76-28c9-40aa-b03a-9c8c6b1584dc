<!--
  this.$MessageToast('设置成功').then();
  this.$MessageToast.(success || info || error || warning)().then();
  或者
  this.$MessageToast({
    message: '设置成功',
    type: 'success',
    iconClass: 'el-icon-ssq-qianyuewancheng'
  }).then();
  this.$MessageToast({
    message: '设置失败',
    type: 'error',
    iconClass: 'el-icon-ssq-warm-filling'
  }).then();
  this.$MessageToast.(success || info || error || warning)({
    message,
    iconClass
  }).then();

  type: 'success' || info || 'error' || 'warning' 为默认类型，有默认图标
  显示的图标以 iconClass 为准，可以覆盖默认图标
 -->
<template>
  <transition
    enter-active-class="message-toast-animated fadeIn"
    leave-active-class="message-toast-animated fadeOut">
    <div
      v-if="visible"
      class="message-toast">
      <!-- <i
        v-if="iconClass !== ''"
        :class="iconClass"
      > -->
      <i
        v-if="iconVisible"
        :class="computedClass"
       >
       </i>
      <p>{{ message }}</p>
    </div>
  </transition>
</template>

<script type="text/babel">
export default {
  name: 'MessageToast',
  props: {},
  data() {
    return {
      message: '',
      iconClass: '',
      type: null,
      visible: true,
      duration: 2500,
      closed: false,
      timer: null,
      dispatch: null,
    };
  },

  computed: {
    iconVisible() {
      return this.iconClass !== '' || this.type !== null;
    },
    computedClass() {
      let iconClassName = '';

      if (this.type !== null) {
        switch (this.type) {
          case 'success':
            iconClassName = 'el-icon-success';
            break;
          case 'info':
            iconClassName = 'el-icon-info';
            break;
          case 'warning':
            iconClassName = 'el-icon-warning';
            break;
          case 'error':
            iconClassName = 'el-icon-error';
            break;
        }
      }

      return this.iconClass !== '' ? this.iconClass : iconClassName;
    },
  },

  watch: {
    closed(newVal) {
      if (newVal) {
        this.visible = false;
        this.$el.addEventListener('transitionend', this.destroyElement);
        this.dispatch('resolve');
      }
    },
  },

  methods: {
    destroyElement() {
      this.$el.removeEventListener('transitionend', this.destroyElement);
      this.$destroy(true);
      this.$el.parentNode.removeChild(this.$el);
    },

    clearTimer() {
      clearTimeout(this.timer);
    },

    startTimer() {
      if (this.duration > 0) {
        this.timer = setTimeout(() => {
          if (!this.closed) {
            this.closed = true;
          }
        }, this.duration);
      }
    },
  },

  mounted() {
    this.startTimer();
  },
};
</script>
<style lang="scss" scoped>
.message-toast-animated {
  animation-duration: 0.3s;
  animation-fill-mode: both;
}
.message-toast {
  box-sizing: border-box;
  z-index: 20000;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-65%);
  background: rgba(0, 0, 0, 0.7);
  min-width: 156px;
  padding: 20px 36px;
  border-radius: 4px;
  color: #fff;
  text-align: center;
  i {
    margin-bottom: 15px;
    font-size: 33px;
  }
  p {
    font-size: 14px;
  }
}
</style>
