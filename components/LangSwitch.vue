<template>
    <div class="lang-switch-wrapper" :class="{'footer': isFooter}">
      <div class="lang-select-wrapper" :class="{'select-active': showSelect}" @click="handleClick">
        <i class="iconfont icon-diqiu"></i>
        <span>{{ selectedLangLabel }}</span>
      </div>
      <transition name="fade">
      <ul class="language-list" :class="{'language-list-footer': isFooter}" v-if="showSelect">
        <li
            v-for="item in langOptions"
            :key="item.value"
            @click="switchLang(item.value)"
            class="list-item"
            :class="{'active': selectedLang === item.value}"
        >
            <span class="item-text">{{ item.label }}</span>
        </li>
    </ul>
    </transition>
    </div>
  </template>
  
  <script>
export default {
  props: {
    isFooter: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showSelect: false,
      langOptions: [
        {
          value: 'en',
          label: 'English',
        },
        {
          value: 'ja',
          label: '日本語',
        },
      ],
    };
  },
  computed: {
    selectedLangLabel() {
      const lang = this.langOptions.find(
        item => item.value === this.selectedLang
      );
      return lang ? lang.label : '';
    },
    selectedLang() {
      return this.$store.state.locale || 'en';
    },
  },
  methods: {
    switchLang(lang) {
      this.$i18n.locale = lang; // 切换语言
      this.showSelect = false;
      this.$store.commit('SET_LANGUAGE', lang);
      // 保持当前路由，仅替换语言前缀
      const newPath = this.$route.path.replace(/^\/[^\/]+/, `/${lang}`);
      this.$router.push(newPath);
    },
    handleClick() {
      this.showSelect = !this.showSelect;
    },
  },
};
</script>
<style lang="scss">
.lang-switch-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  right: 10px;
  width: 100px;
}
.lang-select-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
  width: 100%;
  border: 1px solid rgba(255, 255, 255, 1);
}
.select-active {
  border: 1px solid rgba(116, 127, 148, 1);
  border-radius: 5px;
}
.language-list {
  transition: all 1s ease;
  width: 100px;
  box-shadow: 0px 2px 20px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: rgba(255, 255, 255, 1);
  cursor: pointer;
  position: absolute;
  left: 0;
  top: 64px;
  li {
    height: 30px;
    line-height: 30px;
    color: rgba(116, 127, 148, 1);
    margin: 5px;
    padding: 0 3px;
    box-sizing: border-box;
  }
  .active {
    background: rgba(245, 245, 245, 1);
    color: rgba(17, 36, 62, 1);
    border-radius: 5px;
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease-in;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.language-list-footer {
  position: absolute;
  top: -82px;
}
.icon-diqiu {
  font-size: 20px !important;
  cursor: pointer;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.footer {
  color: rgba(245, 245, 245, 1);
  .lang-select-wrapper {
    border: 1px solid #181f28;
  }
  .select-active {
    border: 1px solid rgba(116, 127, 148, 1);
  }
}
</style>
