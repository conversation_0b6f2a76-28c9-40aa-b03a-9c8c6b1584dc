<template>
  <div class="landscape-form" :class="{mobile: isMobile}">
    <div class="tips">1分钟了解{{ keywords }}全流程</div>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="ruleForm">
      <el-form-item prop="customerName">
        <el-input v-model.trim="ruleForm.customerName" placeholder="姓名"></el-input>
      </el-form-item>
      <el-form-item prop="companyName">
        <el-input v-model.trim="ruleForm.companyName"  placeholder="公司名"></el-input>
      </el-form-item>
      <el-form-item prop="mail">
        <el-input v-model.trim="ruleForm.mail"  placeholder="邮箱"></el-input>
      </el-form-item>
      <el-form-item prop="contact">
        <el-input v-model.trim="ruleForm.contact"  placeholder="手机号"></el-input>
      </el-form-item>
      <el-form-item prop="verifyCode">
        <el-col :span="16"><el-input v-model.trim="ruleForm.verifyCode" placeholder="短信验证码"></el-input></el-col>
        <el-col :span="7" :offset="1"><count-down class="countDown code-sent" :clickedFn="send" :disabled="countDownDisabled" ref="btn" :second="60" initial="验证码"></count-down></el-col>
      </el-form-item>
      <el-form-item v-if="showPictureVerCon" class="pictureVer-item" prop="imageCode">
        <el-input
          class="pictureVer"
          placeholder="图形验证码"
          :maxlength="4"
          v-model.trim="ruleForm.imageCode"
        >
          <template slot="append">
            <PictureVerify
              class="form-pictureVerify"
              ref="pictureVerify"
              :imageKey="ruleForm.imageKey"
              @change-imageKey="changeImageKey"
            />
          </template>
        </el-input>
      </el-form-item>
      <div style="text-align: center">
        <el-button :loading="loading" type="primary" class="submit" @click="toRegister">体验Demo</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import CountDown from '../CountDown.vue';
import PictureVerify from '../PictureVerify.vue';
import { isPhoneOrMail } from '@/assets/utils/reg.js';
import aes from '@/assets/utils/aes.js';
import resRules from '@/assets/utils/regs.js';
export default {
  name: 'LandscapeForm',
  components: {
    CountDown,
    PictureVerify,
  },
  props: {
    keywords: {
      type: String,
      default: '电子签约',
    },
  },
  data() {
    return {
      loading: false,
      showPictureVerCon: false,
      countDownDisabled: false,
      codeDisabled: true,
      visible: false,
      showForm: true,
      ruleForm: {
        customerName: '',
        // 职务
        addInfo: '',
        companyName: '',
        contact: '',
        imageCode: '',
        verifyCode: '',
        verifyKey: '',
        imageKey: '',
        // 邮箱
        mail: '',
      },
      colums: [],
      rules: {
        customerName: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z]{2,10}$/,
            message: '请输入正确的姓名',
            trigger: 'blur',
          },
        ],
        contact: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur',
          },
          {
            pattern: resRules.userPhone,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
        verifyCode: [
          {
            required: true,
            message: '请输入验证码',
            trigger: 'blur',
          },
          {
            pattern: /^\d{6}.*$/,
            message: '请输入正确的验证码',
            trigger: 'blur',
          },
        ],
        imageCode: [
          {
            required: true,
            message: '请输入图形验证码',
            trigger: 'blur',
          },
          {
            pattern: /^\d{4}.*$/,
            message: '请输入正确的图形验证码',
            trigger: 'blur',
          },
        ],
        companyName: [
          {
            required: true,
            message: '请输入公司名称',
            trigger: 'blur',
          },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]{1,128}$/,
            message: '请输入正确的公司名称',
            trigger: 'blur',
          },
        ],
        addInfo: [
          {
            required: true,
            message: '请选择公司职务',
            trigger: 'blur',
          },
        ],
        mail: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          {
            pattern: resRules.userEmail,
            message: '请输入正确的邮箱',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  computed: {
    hasMail() {
      const {
        query: { email },
      } = this.$route;
      return email === 'yes';
    },
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {
    // 更改图形验证码
    changeImageKey(value) {
      this.codeDisabled = false;
      this.ruleForm.imageKey = value;
    },
    // 发送验证码
    send() {
      const { contact, imageCode, imageKey } = this.ruleForm;
      this.$refs['ruleForm'].validateField(['contact'], error => {
        if (error) {
          return false;
        }
        this.countDownDisabled = true;
        let headersObj = {};
        if (imageCode !== '' && imageKey !== '') {
          headersObj = {
            // 'Content-Type': 'application/json; charset=utf-8',
            additionalImgVerCode: JSON.stringify({
              imageCode,
              imageKey,
            }),
            'request-source': 'WEB',
          };
        }
        // let host = 'https://ent.bestsign.info';
        // if (process.env.baseUrl.indexOf('cn') > -1) {
        //   host = 'https://ent.bestsign.cn';
        // }
        let host = '';
        if (process.env.NODE_ENV !== 'development') {
          host =
            process.env.baseUrl.indexOf('cn') > -1
              ? 'https://ent.bestsign.cn'
              : 'https://ent.bestsign.info';
        }
        this.$axios({
          url: `${host}/users/ignore/captcha/notice?encryptToken=${aes(
            'B001',
            contact
          )}`,
          method: 'post',
          headers: headersObj,
          data: {
            code: 'B001',
            sendType: isPhoneOrMail(contact) === 'phone' ? 'S' : 'E',
            target: contact,
            imageCode,
            imageKey,
          },
        })
          .then(res => {
            if (res) {
              this.$MessageToast.success('发送成功！');
              setTimeout(this.sended, 0);
              this.ruleForm.verifyKey = res.value;
              this.codeDisabled = false;
            }
          })
          .catch(err => {
            const res = err.response.data;
            if (res.code === '902' || res.code === '100006') {
              if (this.showPictureVerCon) {
                setTimeout(() => {
                  this.$refs.pictureVerify.changeImg();
                }, 20);
              } else {
                this.showPictureVerCon = true;
              }
              if (res.message !== '图片验证码不能为空') {
                this.$MessageToast.error(res.message);
              } else {
                this.$MessageToast.error('请先填写图形验证码');
              }
            } else {
              this.$MessageToast.error(res.message);
            }
            this.$refs.btn.reset();
          });
      });
    },

    sended() {
      this.$refs.btn.run();
      this.countDownDisabled = false;
    },

    // 申请试用
    toRegister() {
      this.loading = true;
      const {
        companyName,
        verifyKey,
        verifyCode,
        contact,
        customerName,
        addInfo,
        mail,
      } = this.ruleForm;
      // 百度统计当用户点击“申请免费试用”成功提交信息之后，需要将当前页面URL地址，客户地区记录
      window._hmt &&
        window._hmt.push([
          '_trackEvent',
          'register_url_pc',
          'submit',
          location.href,
        ]);
      const params = {
        contact,
        companyName,
        verifyCode,
        verifyKey,
        customerName,
        addInfo,
        mail,
        applyUrl: location.href,
        type: 4,
      };
      this.$refs['ruleForm'].validate(valid => {
        if (!valid) {
          this.loading = false;
          return false;
        }
        this.$axios
          .post('/www/api/web/tryout/register', {
            ...params,
          })
          .then(res => {
            if (res && res.value === 'OK') {
              const url = `https://demo.bestsign.${
                window.location.host.indexOf('cn') > -1 ? 'cn' : 'info'
              }?phone=${params.contact}&companyName=${params.companyName}`;
              window.location.href = url;
            }
            this.loading = false;
          })
          .catch(err => {
            this.loading = false;
            const res = err.response.data;
            if (res.code === '903') {
              this.$MessageToast.error('提交失败请稍后再试');
            } else {
              this.$MessageToast.error(res.message);
            }
          });
      });
    },
  },
};
</script>
<style lang="scss">
.landscape-form {
  .el-form-item {
    margin-bottom: 18px;
  }
  .el-input__inner {
    border-radius: 0;
    border-color: #c5c6c6;
  }
  .el-button {
    width: 100%;
    border-radius: 0;
  }
  .el-button--primary {
    background-color: #00aa64;
    border-color: #00aa64;
  }
}
</style>

<style scoped lang="scss">
.landscape-form {
  width: 100%;
  max-width: 350px;
  margin: 0 auto;
  padding: 20px 24px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  box-shadow: 7px 7px 5px 0 rgba(0, 0, 0, 0.15);
  &.mobile {
    .tips {
      font-size: 16px;
    }
  }
  .tips {
    font-size: 18px;
    color: #595757;
    text-align: center;
    margin-bottom: 24px;
  }
  .count-down {
    display: inline-block;
    width: 100%;
    height: 40px;
    color: #00aa64;
    font-size: 14px;
  }
}
</style>
