<template>
  <section
    class="section cooperation"
  >
    <div class="container">
      <h3 class="section-headline">战略合作伙伴</h3>
      <p class="section-subtitle">我们与Apple、甲骨文、微软、WPS、蚂蚁区块链等行业巨头建立了战略合作伙伴关系，构建了强大的电子签约生态版图。</p>
      <div class="img-wrap">
        <img :src="bgSrc">
      </div>
    </div>
  </section>
</template>

<script>
import img from '@/assets/images/cooperation/cooperation.png';
import img_w from '@/assets/images/cooperation/<EMAIL>';
import img_m from '@/assets/images/cooperation/<EMAIL>';
export default {
  name: 'Cooperation',
  props: {
    isWhiteBg: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    bgSrc() {
      if (this.isMobile) {
        return img_m;
      }
      return img;
    },
  },
};
</script>

<style scoped lang="scss">
.cooperation {
  .section-subtitle {
    margin: 75px auto 100px;
  }
  .img-wrap,
  img {
    width: 100%;
  }
}
@media screen and (max-width: 767px) {
  .cooperation {
    .section-subtitle {
      margin: 30px auto;
    }
    .img-wrap,
    img {
      width: 100%;
    }
  }
}
</style>
