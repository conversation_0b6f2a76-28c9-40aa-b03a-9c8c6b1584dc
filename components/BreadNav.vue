<template>
  <div class="bread-nav">
    <div class="container">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">{{$t('beeadNum.home')}}</el-breadcrumb-item>
        <template v-for="(item, index) in menu">
          <el-breadcrumb-item :key="index">
            <nuxt-link v-if="item.to" :to="item.to" >{{ item.name }}</nuxt-link>
            <span v-else style="color:#000;font-weight:500">{{ item.name }}</span>
          </el-breadcrumb-item>
        </template>
      </el-breadcrumb>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BreadNav',
  props: {
    menu: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="scss">
.bread-nav {
  width: 100%;
  background-color: #fafbfc;
  padding: 0 0 10px;
  .container {
    width: 100%;
    max-width: 100%;
    // border-top: 1px solid #d0d4db;
    border-bottom: 1px solid #d0d4db;
    padding: 20px 5%;
    .el-breadcrumb__inner a,
    .el-breadcrumb__inner.is-link {
      font-weight: normal;
      color: #606266;
      &:hover {
        color: $-color-main;
      }
    }
    .el-breadcrumb {
      line-height: 1;
    }
  }
}
@media screen and (max-width: 767px) {
  .bread-nav {
    .container {
      .el-breadcrumb {
        line-height: 2;
      }
    }
  }
}
</style>
