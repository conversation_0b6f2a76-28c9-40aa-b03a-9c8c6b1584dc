<template>
  <div class="demo">
    <demo-header></demo-header>
    <div class="demo-card">
      <div class="card-header">免费试用</div>
      <div class="card-body">
        <div class="block">
          <div class="tips">立即体验上上签电子签约，1分钟了解电子签约全流程。</div>
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="ruleForm" label-width="80px">
            <el-form-item prop="customerName" label="姓名">
              <el-input v-model.trim="ruleForm.customerName" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item prop="contact" label="手机号">
              <el-input v-model.trim="ruleForm.contact"  placeholder="请输入11位有效号码"></el-input>
            </el-form-item>
            <el-form-item prop="verifyCode" label="验证码">
              <el-col :span="16"><el-input v-model.trim="ruleForm.verifyCode" placeholder="输入验证码"></el-input></el-col>
              <el-col :span="7" :offset="1"><count-down class="countDown code-sent" :clickedFn="send" :disabled="countDownDisabled" ref="btn" :second="60"></count-down></el-col>
            </el-form-item>
            <el-form-item v-if="showPictureVerCon" label="图形验证码" class="pictureVer-item" prop="imageCode">
              <el-input
                class="pictureVer"
                placeholder="请填写4位验证码"
                :maxlength="4"
                v-model.trim="ruleForm.imageCode"
              >
                <template slot="append">
                  <PictureVerify
                    class="form-pictureVerify"
                    ref="pictureVerify"
                    :imageKey="ruleForm.imageKey"
                    @change-imageKey="changeImageKey"
                  />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item prop="mail" label="邮箱" v-if="hasMail">
              <el-input v-model.trim="ruleForm.mail"  placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item prop="companyName" label="公司名称">
              <el-input v-model.trim="ruleForm.companyName"  placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item prop="addInfo" label="公司职务">
              <el-select style="width: 100%" v-model="ruleForm.addInfo" placeholder="请选择">
                <el-option label="创始人/CEO" value="创始人/CEO"></el-option>
                <el-option label="高层管理者" value="高层管理者"></el-option>
                <el-option label="中层管理者" value="中层管理者"></el-option>
                <el-option label="普通员工" value="普通员工"></el-option>
              </el-select>
            </el-form-item>
            <div style="text-align: center">
              <el-button :loading="loading" type="primary" class="submit" @click="toRegister">免费试用</el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <float-help></float-help>
  </div>
</template>

<script>
import resRules from '@/assets/utils/regs.js';
import CountDown from '@/components/CountDown.vue';
import PictureVerify from '@/components/PictureVerify.vue';
import DemoHeader from '@/components/DemoHeader.vue';
import FloatHelp from '@/components/FloatHelp.vue';
import { isPhoneOrMail } from '@/assets/utils/reg.js';
import aes from '@/assets/utils/aes.js';
import { encode, demo } from '@/assets/utils/aes.js';
import Qs from 'qs';

const CryptoJs = require('crypto-js');

export default {
  name: 'demo',
  layout: 'blank',
  components: {
    CountDown,
    PictureVerify,
    DemoHeader,
    FloatHelp,
  },
  data() {
    return {
      loading: false,
      showPictureVerCon: false,
      countDownDisabled: false,
      codeDisabled: true,
      visible: false,
      showForm: true,
      ruleForm: {
        customerName: '',
        // 职务
        addInfo: '',
        companyName: '',
        contact: '',
        imageCode: '',
        verifyCode: '',
        verifyKey: '',
        imageKey: '',
        mail: '',
      },
      colums: [],
      rules: {
        customerName: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z]{2,10}$/,
            message: '请输入正确的姓名',
            trigger: 'blur',
          },
        ],
        contact: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          {
            pattern: resRules.userPhone,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
        verifyCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          {
            pattern: /^\d{6}.*$/,
            message: '请输入正确的验证码',
            trigger: 'blur',
          },
        ],
        imageCode: [
          { required: true, message: '请输入图形验证码', trigger: 'blur' },
          {
            pattern: /^\d{4}.*$/,
            message: '请输入正确的图形验证码',
            trigger: 'blur',
          },
        ],
        companyName: [
          { required: true, message: '请输入公司名称', trigger: 'blur' },
          {
            attern: /^[\u4e00-\u9fa5a-zA-Z0-9（）]{1,128}$/,
            message: '请输入正确的公司名称',
            trigger: 'blur',
          },
        ],
        addInfo: [
          { required: true, message: '请选择公司职务', trigger: 'blur' },
        ],
        mail: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          {
            pattern: resRules.userEmail,
            message: '请输入正确的邮箱',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  computed: {
    hasMail() {
      const {
        query: { email },
      } = this.$route;
      return email === 'yes';
    },
  },
  methods: {
    // 更改图形验证码
    changeImageKey(value) {
      this.codeDisabled = false;
      this.ruleForm.imageKey = value;
    },
    // 发送验证码
    send() {
      const { contact, imageCode, imageKey } = this.ruleForm;
      this.$refs['ruleForm'].validateField(['contact'], error => {
        if (error) {
          return false;
        }
        let headersObj = {};
        if (imageCode !== '' && imageKey !== '') {
          headersObj = {
            // 'Content-Type': 'application/json; charset=utf-8',
            additionalImgVerCode: JSON.stringify({
              imageCode,
              imageKey,
            }),
            'request-source': 'WEB',
          };
        }
        // let host = 'https://ent.bestsign.info';
        // if (process.env.baseUrl.indexOf('cn') > -1) {
        //   host = 'https://ent.bestsign.cn';
        // }
        let host = '';
        if (process.env.NODE_ENV !== 'development') {
          host =
            process.env.baseUrl.indexOf('cn') > -1
              ? 'https://ent.bestsign.cn'
              : 'https://ent.bestsign.info';
        }
        this.$axios({
          url: `${host}/users/ignore/captcha/notice?encryptToken=${aes(
            'B001',
            contact
          )}`,
          method: 'post',
          headers: headersObj,
          data: {
            code: 'B001',
            sendType: isPhoneOrMail(contact) === 'phone' ? 'S' : 'E',
            target: contact,
            imageCode,
            imageKey,
          },
        })
          .then(res => {
            if (res) {
              this.$MessageToast.success('发送成功！');
              this.countDownDisabled = true;
              setTimeout(this.sended, 0);
              this.ruleForm.verifyKey = res.value;
              this.codeDisabled = false;
            }
          })
          .catch(err => {
            const res = err.response.data;
            if (res.code === '902' || res.code === '100006') {
              if (this.showPictureVerCon) {
                setTimeout(() => {
                  this.$refs.pictureVerify.changeImg();
                }, 20);
              } else {
                this.showPictureVerCon = true;
              }
              if (res.message !== '图片验证码不能为空') {
                this.$MessageToast.error(res.message);
              } else {
                this.$MessageToast.error('请先填写图形验证码');
              }
            } else {
              this.$MessageToast.error(res.message);
            }
            this.$refs.btn.reset();
          });
      });
    },

    sended() {
      this.$refs.btn.run();
      this.countDownDisabled = false;
    },

    // 申请试用
    toRegister() {
      this.loading = true;
      const {
        companyName,
        verifyKey,
        verifyCode,
        contact,
        customerName,
        addInfo,
        mail,
      } = this.ruleForm;
      // 百度统计当用户点击“申请免费试用”成功提交信息之后，需要将当前页面URL地址，客户地区记录
      const query = sessionStorage.getItem('query');
      const queryString = Qs.stringify({
        ...Qs.parse(query),
        ...this.$route.query,
      });
      const url = `${location.origin}/demo?${queryString}`;
      window._hmt &&
        window._hmt.push(['_trackEvent', 'register_url_pc', 'submit', url]);

      const params = {
        contact,
        companyName,
        verifyCode,
        verifyKey,
        customerName,
        addInfo,
        mail,
        applyUrl: url,
        type: 4,
      };
      this.$refs['ruleForm'].validate(valid => {
        if (!valid) {
          this.loading = false;
          return false;
        }
        this.$axios
          .post('/www/api/web/tryout/register', {
            ...params,
          })
          .then(res => {
            if (res && res.value === 'OK') {
              const query = encode({
                phone: params.contact,
                companyName: params.companyName,
              });
              const url = `https://demo.bestsign.${
                window.location.host.indexOf('cn') > -1 ? 'cn' : 'info'
              }/service?token=${query}`;
              window.location.href = url;
            }
            this.loading = false;
          })
          .catch(err => {
            this.loading = false;
            const res = err.response.data;
            if (res.code === '903') {
              this.$MessageToast.error('提交失败请稍后再试');
            } else {
              this.$MessageToast.error(res.message);
            }
          });
      });
    },
  },
};
</script>
<style lang="scss">
.demo {
  .el-form-item {
    margin-bottom: 24px;
  }
  .el-input.is-active .el-input__inner,
  .el-input__inner:focus {
    border-color: #f3c51e;
  }
  .el-select .el-input.is-focus .el-input__inner {
    border-color: #f3c51e;
  }
  .el-button--primary {
    background-color: #f3c51e;
    border-color: #f3c51e;
    width: 200px;
  }
  .el-button--primary.is-active,
  .el-button--primary:active {
    background: #f3c51e;
  }
}
.el-select-dropdown__item {
  text-align: left;
}
.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: #e7f5f1;
}
.el-select-dropdown__item.selected {
  color: #f3c51e;
}
</style>
<style scoped lang="scss">
.countDown {
  background-color: #fff;
  font-size: 14px;
  color: #333;
  width: 100%;
  height: 40px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}
.demo {
  width: 100%;
  height: 100vh;
  position: relative;
  background: url(~assets/images/demo/<EMAIL>) transparent no-repeat;
  background-size: 85%;
  .banner-wrapper {
    position: absolute;
    width: 100%;
    height: 100%;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .demo-card {
    margin: 6rem 12rem 6rem auto;
    width: 500px;
    background-color: #fff;
    box-shadow: 0 0 8px 0 #ddd;
    .card-header {
      height: 52px;
      line-height: 52px;
      padding: 0 32px;
      color: #f3c51e;
      background-color: #e7f5f1;
      text-align: center;
    }
    .card-body {
      color: #333;
      padding-bottom: 32px;
      .block {
        width: 440px;
        margin: 0 auto;
        .tips {
          margin: 24px 0;
          font-size: 14px;
        }
      }
    }
  }
  .form-pictureVerify {
    width: 80px;
    height: 36px;
  }
}
</style>
