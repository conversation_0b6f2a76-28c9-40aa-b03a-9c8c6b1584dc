<template>
  <div class="demo-mobile">
    <demo-header></demo-header>
    <div class="mobile-card">
      <div class="card-header">免费试用</div>
      <div class="card-body">
        <div class="tips">立即体验上上签电子签约，1分钟了解电子签约全流程。</div>
        <van-cell-group>
          <van-field
            v-model="ruleForm.customerName"
            required
            clearable
            label="姓名"
            placeholder="请输入"
            :error-message="errors.customerName"
            @blur="validate('customerName')"
          ></van-field>
          <van-field
            v-model="ruleForm.contact"
            required
            clearable
            label="手机号"
            placeholder="请输入"
            :error-message="errors.contact"
            @blur="validate('contact')"
          ></van-field>
          <van-field
            v-model="ruleForm.verifyCode"
            required
            clearable
            label="验证码"
            placeholder="请输入"
            :error-message="errors.verifyCode"
            @blur="validate('verifyCode')"
          >
            <count-down slot="button" class="countDown code-sent" :clickedFn="send" :disabled="countDownDisabled" ref="btn" :second="60"></count-down>
          </van-field>
          <van-field
            v-if="showPictureVerCon"
            v-model="ruleForm.imageCode"
            required
            clearable
            label="图形验证码"
            placeholder="请输入"
            :error-message="errors.imageCode"
            @blur="validate('imageCode')"
          >
            <PictureVerify
              slot="button"
              class="form-pictureVerify"
              ref="pictureVerify"
              :imageKey="ruleForm.imageKey"
              @change-imageKey="changeImageKey"
            />
          </van-field>
          <van-field
            v-if="hasMail"
            v-model="ruleForm.mail"
            required
            clearable
            label="邮箱"
            placeholder="请输入"
            :error-message="errors.mail"
            @blur="validate('email')"
          ></van-field>
          <van-field
            v-model="ruleForm.companyName"
            required
            clearable
            label="公司名称"
            placeholder="请输入"
            :error-message="errors.companyName"
            @blur="validate('companyName')"
          ></van-field>
          <van-field
            v-model="ruleForm.addInfo"
            label="公司职务"
            placeholder="请选择"
            required
            right-icon="arrow"
            disabled
            @click-right-icon="showPop = true"
            :error-message="errors.addInfo"
          ></van-field>
          <van-popup v-model="showPop" position="bottom">
            <van-picker show-toolbar :columns="columns" @change="validate('addInfo')" @confirm="handlePickerOk" @cancel="showPop = false" />
          </van-popup>
        </van-cell-group>
        <van-button size="large" type="primary" :loading="loading" class="apply" @click="toRegister">体验Demo</van-button>
      </div>
    </div>
  </div>
</template>

<script>
import DemoHeader from '@/components/DemoHeader.vue';
import CountDown from '@/components/CountDown.vue';
import PictureVerify from '@/components/PictureVerify.vue';
import resRules from '@/assets/utils/regs.js';
import { isPhoneOrMail } from '@/assets/utils/reg.js';
import aes from '@/assets/utils/aes.js';
import { encode } from '@/assets/utils/aes.js';
import without from 'lodash/without';
import every from 'lodash/every';
import Qs from 'qs';
export default {
  name: 'demo-mobile',
  layout: 'blank',
  components: {
    DemoHeader,
    CountDown,
    PictureVerify,
  },
  data() {
    return {
      loading: false,
      showPictureVerCon: false,
      countDownDisabled: false,
      codeDisabled: true,
      showPop: false,
      columns: ['创始人/CEO', '高层管理者', '中层管理者', '普通员工'],
      ruleForm: {
        customerName: '',
        addInfo: '',
        companyName: '',
        contact: '',
        imageCode: '',
        verifyCode: '',
        verifyKey: '',
        imageKey: '',
        mail: '',
      },
      errors: {
        customerName: '',
        contact: '',
        verifyCode: '',
        imageCode: '',
        companyName: '',
        addInfo: '',
        mail: '',
      },
    };
  },
  computed: {
    hasMail() {
      const {
        query: { email },
      } = this.$route;
      return email === 'yes';
    },
    rules() {
      return {
        customerName: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z]{2,10}$/,
            message: '请输入正确的姓名',
            trigger: 'blur',
          },
        ],
        contact: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          {
            pattern: resRules.userPhone,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
        verifyCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          {
            pattern: /^\d{6}.*$/,
            message: '请输入正确的验证码',
            trigger: 'blur',
          },
        ],
        imageCode: [
          {
            required: this.showPictureVerCon,
            message: '请输入图形验证码',
            trigger: 'blur',
          },
          {
            pattern: /^\d{4}.*$/,
            message: '请输入正确的图形验证码',
            trigger: 'blur',
          },
        ],
        companyName: [
          { required: true, message: '请输入公司名称', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9（）]{1,128}$/,
            message: '请输入正确的公司名称',
            trigger: 'blur',
          },
        ],
        addInfo: [
          { required: true, message: '请选择公司职务', trigger: 'blur' },
        ],
        mail: [
          { required: this.hasMail, message: '请输入邮箱', trigger: 'blur' },
          {
            pattern: resRules.userEmail,
            message: '请输入正确的邮箱',
            trigger: 'blur',
          },
        ],
      };
    },
  },
  methods: {
    handlePickerOk(value) {
      this.ruleForm.addInfo = value;
      this.showPop = false;
      this.validate('addInfo');
    },
    // 更改图形验证码
    changeImageKey(value) {
      this.codeDisabled = false;
      this.ruleForm.imageKey = value;
    },
    // 发送验证码
    send() {
      const { contact, imageCode, imageKey } = this.ruleForm;
      const valid = this.validate('contact');
      if (!valid) return false;
      let headersObj = {};
      if (imageCode !== '' && imageKey !== '') {
        headersObj = {
          // 'Content-Type': 'application/json; charset=utf-8',
          additionalImgVerCode: JSON.stringify({
            imageCode,
            imageKey,
          }),
          'request-source': 'WEB',
        };
      }
      // let host = 'https://ent.bestsign.info';
      // if (process.env.baseUrl.indexOf('cn') > -1) {
      //   host = 'https://ent.bestsign.cn';
      // }
      let host = '';
      if (process.env.NODE_ENV !== 'development') {
        host =
          process.env.baseUrl.indexOf('cn') > -1
            ? 'https://ent.bestsign.cn'
            : 'https://ent.bestsign.info';
      }
      this.$axios({
        url: `${host}/users/ignore/captcha/notice?encryptToken=${aes(
          'B001',
          contact
        )}`,
        method: 'post',
        headers: headersObj,
        data: {
          code: 'B001',
          sendType: isPhoneOrMail(contact) === 'phone' ? 'S' : 'E',
          target: contact,
          imageCode,
          imageKey,
        },
      })
        .then(res => {
          if (res) {
            this.$MessageToast.success('发送成功！');
            this.countDownDisabled = true;
            setTimeout(this.sended, 0);
            this.ruleForm.verifyKey = res.value;
            this.codeDisabled = false;
          }
        })
        .catch(err => {
          const res = err.response.data;
          if (res.code === '902' || res.code === '100006') {
            if (this.showPictureVerCon) {
              setTimeout(() => {
                this.$refs.pictureVerify.changeImg();
              }, 20);
            } else {
              this.showPictureVerCon = true;
            }
            if (res.message !== '图片验证码不能为空') {
              this.$MessageToast.error(res.message);
            } else {
              this.$MessageToast.error('请先填写图形验证码');
            }
          } else {
            this.$MessageToast.error(res.message);
          }
          this.$refs.btn.reset();
        });
    },

    sended() {
      this.$refs.btn.run();
      this.countDownDisabled = false;
    },
    validate(field) {
      const value = this.ruleForm[field];
      const rules = this.rules[field];
      let flag;
      if (rules) {
        for (let rule of rules) {
          if (rule.required && value === '') {
            this.errors[field] = rule.message;
            flag = false;
            break;
          }
          if (rule.pattern && !rule.pattern.test(value)) {
            this.errors[field] = rule.message;
            flag = false;
            break;
          }
          this.errors[field] = '';
          flag = true;
        }
      }
      return flag;
    },

    // 申请试用
    toRegister() {
      const {
        companyName,
        verifyKey,
        verifyCode,
        contact,
        customerName,
        addInfo,
        mail,
      } = this.ruleForm;
      // 百度统计当用户点击“申请免费试用”成功提交信息之后，需要将当前页面URL地址，客户地区记录
      const query = sessionStorage.getItem('query');
      const queryString = Qs.stringify({
        ...Qs.parse(query),
        ...this.$route.query,
      });
      const url = `${location.origin}/demo/mobile?${queryString}`;
      window._hmt &&
        window._hmt.push(['_trackEvent', 'register_url_mobile', 'submit', url]);

      const params = {
        contact,
        companyName,
        verifyCode,
        verifyKey,
        customerName,
        addInfo,
        mail,
        applyUrl: url,
        type: 4,
      };
      this.loading = true;
      let fields = without(Object.keys(this.ruleForm), 'verifyKey', 'imageKey');
      if (!this.showPictureVerCon) {
        fields = without(fields, 'imageCode');
      }
      if (!this.hasMail) {
        fields = without(fields, 'mail');
      }
      for (let field of fields) {
        this.validate(field);
      }
      const valid = every(this.errors, o => o === '');
      if (!valid) {
        this.loading = false;
        return false;
      }
      this.$axios
        .post('/www/api/web/tryout/register', {
          ...params,
        })
        .then(res => {
          if (res && res.value === 'OK') {
            const query = encode({
              phone: params.contact,
              companyName: params.companyName,
            });
            const url = `https://demo.bestsign.${
              window.location.host.indexOf('cn') > -1 ? 'cn' : 'info'
            }?token=${query}`;
            window.location.href = url;
          }
          this.loading = false;
        })
        .catch(err => {
          this.loading = false;
          const res = err.response.data;
          if (res.code === '903') {
            this.$MessageToast.error('提交失败请稍后再试');
          } else {
            this.$MessageToast.error(res.message);
          }
        });
    },
  },
};
</script>
<style lang="scss">
.demo-mobile {
  .mobile-card {
    .card-body {
      .verifyCode {
        border: none;
      }
    }
  }
  .van-field__control {
    height: 100%;
  }
  .van-field__control:disabled {
    color: #323233 !important;
  }
}
</style>
<style scoped lang="scss">
.demo-mobile {
  .countDown {
    background-color: #fff;
    font-size: 14px;
    color: #00aa64;
    width: 100%;
    height: 30px;
    border: none;
    border-left: 1px solid #ddd;
  }
  .mobile-card {
    padding: 0 16px;
    .card-header {
      font-size: 17px;
      color: #323232;
      padding: 16px 0;
    }
    .card-body {
      .tips {
        line-height: 20px;
        font-size: 14px;
        padding: 0 0 12px;
      }
      .form-pictureVerify {
        height: 30px;
      }
      .apply {
        width: 100%;
        margin-top: 30px;
        background-color: #00aa64;
        border: 1px solid #00aa64;
      }
    }
  }
}
</style>
