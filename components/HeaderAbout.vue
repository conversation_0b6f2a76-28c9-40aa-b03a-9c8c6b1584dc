<template>
  <header class="site-header">
    <nav class="nav-main navbar">
      <div class="nav-header">
        <a
          @click="handleIndex"
          class="nav-brand">
          <img src="@/assets/images/logo-bestsign.svg" alt="">
        </a>
      </div>
      <ul class="nav-section">
        <li class="solution">
          <nuxt-link
            :class="{'is-active': activeNav === 'about-us'}"
            to="/about/about-us">公司简介</nuxt-link>
        </li>
        <!--<li class="solution">-->
          <!--<nuxt-link-->
            <!--:class="{'is-active': activeNav === 'news'}"-->
            <!--to="/news">新闻动态</nuxt-link>-->
        <!--</li>-->
        <li class="solution">
          <nuxt-link
            :class="{'is-active': activeNav === 'join'}"
            to="/about/join-us">加入我们</nuxt-link>
        </li>
        <li class="solution">
          <nuxt-link
            :class="{'is-active': activeNav === 'contract'}"
            to="/about/contact-us">联系我们</nuxt-link>
        </li>
      </ul>
      <ul class="nav-footer">
        <li style="display: flex;align-items: center;">
          <span style="font-size: 16px;color: #00aa64;width: 120px;">400-993-6665</span>
        </li>
        <li class="btn login">
          <a class="btn btn-gradient-green-link" @click="loginEnt" style="width: 75px">登录</a>
        </li>
<!--        <li>-->
<!--          <a-->
<!--            href="javascript: void (0)"-->
<!--            class="btn btn-gradient-green-link"-->
<!--            style="width: 75px;margin-right: 20px"-->
<!--            @click="toReg"-->
<!--          >-->
<!--            <span>注册</span>-->
<!--          </a>-->
<!--        </li>-->
        <li>
          <a class="btn btn-gradient-green-link demo" @click="handleDemo">免费试用</a>
        </li>
      </ul>
      <div class="navbar-toggle-wrap">
        <a class="navbar-toggle" @click="menuVisible = !menuVisible">
          <i class="iconfont icon-menu"></i>
        </a>
      </div>
    </nav>
    <div class="navbar-collapse collapse" :class="{'in': menuVisible}">
      <ul class="items-wrap collapse-list">
        <li><nuxt-link to="/about/about-us">公司简介</nuxt-link></li>
        <!--<li><nuxt-link to="/news">新闻动态</nuxt-link></li>-->
        <li><nuxt-link to="/about/join-us">加入我们</nuxt-link></li>
        <li><nuxt-link to="/about/contract-us">联系我们</nuxt-link></li>
        <li>
          <a class="btn btn-gradient-green-link demo" @click="handleDemo">免费试用</a>
        </li>
      </ul>
    </div>
  </header>
</template>

<script>
import Qs from 'qs';
export default {
  name: 'HeaderAbout',
  data() {
    return {
      menuVisible: false,
    };
  },
  computed: {
    activeNav() {
      return this.$store.state.path;
    },
  },
  watch: {
    $route() {
      this.menuVisible = false;
    },
  },
  methods: {
    loginEnt() {
      const query = sessionStorage.getItem('query');
      const url =
        window.location.host.indexOf('cn') > -1
          ? 'https://ent.bestsign.cn'
          : 'https://ent.bestsign.info';
      window.open(url + '?' + query);
    },
    handleIndex() {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/',
        query: Qs.parse(query),
      });
    },
    toReg() {
      const query = sessionStorage.getItem('query');
      const href = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push([
            '_trackEvent',
            'click_register_mobile',
            'click',
            href,
          ]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_register_pc', 'click', href]);
      }
      const url =
        window.location.host.indexOf('cn') > -1
          ? 'https://ent.bestsign.cn/register'
          : 'https://ent.bestsign.info/register';
      window.open(url + '?' + 'hp_nav&' + query);
    },
    handleDemo() {
      const query = sessionStorage.getItem('query');
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          ...Qs.parse(query),
          hp_topnav: '',
        },
      });
    },
  },
};
</script>
