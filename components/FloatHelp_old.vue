<template>
  <div class="help-wrapper">
    <div class="pc float-help" v-if="!isMobile">
      <el-popover
        trigger="hover"
        placement="right"
      >
        <div class="link">
          <p>在线咨询</p>
        </div>
        <div
          slot="reference"
          class="item"
          :style="{ backgroundColor: bgColor }"
          @click="handleService">
          <div class="wrapper">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-kefu"></use>
            </svg>
            <span>在线咨询</span>
          </div>
        </div>
      </el-popover>
      <el-popover
        trigger="hover"
        placement="right"
      >
        <div class="link">
          <p>全国服务热线</p>
          <a href="tel:4009936665">************</a>
        </div>
        <div
          slot="reference"
          class="item"
          :style="{ backgroundColor: bgColor }">
          <div class="wrapper">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-phone"></use>
            </svg>
            <span>电话咨询</span>
          </div>
        </div>
      </el-popover>
      <el-popover
        trigger="hover"
        placement="right"
      >
        <div class="download">
          <img
            src="@/assets/images/qr-wechat.jpeg"
            width="80"
            height="80">
          <p>扫码添加客服<br>获取更多福利资讯</p>
        </div>
        <div
          slot="reference"
          class="item"
          :style="{ backgroundColor: bgColor }">
          <div class="wrapper">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-wechat"></use>
            </svg>
            <span>关注微信</span>
          </div>
        </div>
      </el-popover>
      <div
        class="item"
        :style="{ backgroundColor: bgColor }"
        @click="handleScroll">
        <div class="wrapper" style="border: none">
          <i class="el-icon-arrow-up"></i>
          <span>置顶</span>
        </div>
      </div>
    </div>
    <div class="mobile" v-else>
      <div class="left" @click="handleService" :style="{ color: bgColor, borderColor: bgColor }">
        <i class="iconfont icon-kefu"></i>在线咨询</div>
      <a class="right" :style="{ backgroundColor: bgColor }" href="tel:4009936665">
        <i class="iconfont icon-phone"></i>免费服务热线</a>
    </div>
    <div class="iframe" v-if="serviceVisible" :class="{open: serviceVisible}">
      <div class="close" @click="closeFeedback">
        <i class="el-icon-close"></i>
      </div>
      <iframe src="//bestsign.udesk.cn/im_client/?web_plugin_id=23490" frameborder="0"></iframe>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FloatHelp',
  props: {
    bgColor: {
      type: String,
      default: '#00aa64',
    },
  },
  computed: {
    serviceVisible() {
      return this.$store.state.serviceVisible;
    },
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  mounted() {
    this.udesk();
  },
  methods: {
    handleScroll() {
      this.$scrollTo('body');
    },
    handleHelp() {
      this.$router.push('/help');
    },
    handleService() {
      this.$store.commit('setServiceVisible', true);
    },
    closeFeedback() {
      this.$store.commit('setServiceVisible', false);
    },
    udesk() {
      (function(a, h, c, b, f, g) {
        a['UdeskApiObject'] = f;
        a[f] =
          a[f] ||
          function() {
            (a[f].d = a[f].d || []).push(arguments);
          };
        g = h.createElement(c);
        g.async = 1;
        g.charset = 'utf-8';
        g.src = b;
        c = h.getElementsByTagName(c)[0];
        c.parentNode.insertBefore(g, c);
      })(
        window,
        document,
        'script',
        '//assets-cli.udesk.cn/im_client/js/udeskApi.js',
        'ud'
      );
      ud({
        // code: '1aifdai3',
        // /* "targetSelector":"#btn_udesk_im",
        //  "selector":"#btn_udesk_im",*/
        // mode: 'inner',
        // color: '#70b544',
        // pos_flag: 'vrm',
        // css: {
        //   bottom: '80px',
        //   right: '50px',
        //   width: '36px',
        //   height: '36px',
        //   border: 'none',
        //   background: 'url("/page/static/<EMAIL>") no-repeat',
        //   backgroundColor: 'rgba(0, 0, 0, 0.2)'
        // },
        link: '//bestsign.udesk.cn/im_client/?web_plugin_id=23490',
      });
    },
  },
};
</script>
<style lang="scss">
.el-popover {
  text-align: center;
  .download {
    p {
      margin-top: 15px;
    }
  }
  .link {
    p {
      padding: 7px 0;
    }
  }
}
</style>
<style scoped lang="scss">
.help-wrapper {
  .iframe {
    position: fixed;
    right: 260px;
    bottom: 0;
    width: 320px;
    height: 480px;
    background-color: #f8fbff;
    box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 20px 0px;
    transition: all 0.5s ease;
    transform: translateY(100%);
    z-index: 100000;
    iframe {
      width: 100%;
      height: 100%;
    }
    .close {
      position: absolute;
      right: -9px;
      top: -12px;
      font-size: 16px;
      cursor: pointer;
      padding: 10px;
      color: #fff;
    }
    &.open {
      transform: translateY(0);
    }
    &.service {
      .close {
        color: #fff;
        right: 34px;
        top: 9px;
      }
    }
  }
  .mobile {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 50px;
    display: flex;
    font-size: 18px;
    text-align: center;
    z-index: 999;
    .iconfont {
      font-size: 20px;
      margin-right: 0.75rem;
      vertical-align: middle;
    }
    .left {
      display: inline-block;
      width: 45%;
      height: 100%;
      line-height: 50px;
      color: #00aa64;
      background-color: #fff;
      border-top: 1px solid #00aa64;
    }
    .right {
      flex: 1;
      height: 100%;
      line-height: 50px;
      background-color: #00aa64;
      color: #fff;
    }
  }
}
.float-help {
  position: fixed;
  bottom: 30%;
  right: 25px;
  text-align: center;
  z-index: 999;
  background-color: #00aa64;
  .item {
    position: relative;
    width: 70px;
    padding: 0 8px;
    background-color: #00aa64;
    cursor: pointer;
    .wrapper {
      padding: 16px 0;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      border-bottom: 1px solid #fff;
      font-size: 13px;
      color: #fff;
      span {
        margin-top: 10px;
        font-size: 13px;
        font-weight: 500;
      }
    }
    .el-icon-arrow-up {
      font-size: 22px;
      color: #fff;
    }
    .icon {
      font-size: 20px;
    }
    &:hover {
      background-color: #019558;
    }
  }
}
@media screen and (max-width: 767px) {
  .help-wrapper {
    .iframe {
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
