<template>
  <section class="section section-partner">
    <div class="container">
      <h1 class="section-headline">战略合作伙伴</h1>
      <p>我们与Apple、甲骨文、微软、WPS、蚂蚁区块链等行业巨头建立了战略合作伙伴关系，构建了强大的电子签约生态版图。</p>
      <!-- <div class="content flex bc">
        <div class="left">
          <p>上上签电子签约，中国电子签约云平台领跑者，为企业提供「合同起草」、「实名认证」、「在线审批」、「签约管理」、「合同管理」、「履约提醒」、「法律支持」、「证据链保存」等合同全生命周期的智能管理服务。</p>
          <p>自2014年成立以来，以SaaS极简方案持续领跑电子签约市场。截止2020年12月底，上上签平台累计合同签署量达<span style="font-weight: bold;">127</span>亿次，服务超过<span style="font-weight: bold;">1273</span>万家企业。权威智库艾媒咨询《2018-2019中国电子签名市场专题报告》数据显示，上上签2018年以44.3%的市场份额强势领跑。</p>
        </div>
        <div class="right">
          <div class="video-wrapper">
            <video
              src="https://static.bestsign.cn/c276d7eaf30a1ce4a080627f47f0b15f9fb35b19.mp4"
              controls="controls"
              poster="https://static.bestsign.cn/19f77a726fbb4099b265f592d601a1a202a37c5a.jpg"
            ></video>
          </div>
        </div>
      </div> -->
      <div class="logo-container">
        <div v-for="(item,index) in data" :key="index" class="logo-each">
          <img :src="item.logo" :key="item.logo" alt="">
          <div class="title">{{item.name}}</div>
        </div>
		<i></i>
		<i></i>
		<i></i>
		<i></i>
		<i></i>
		<i></i>
		<i></i>
		<i></i>
      </div>
    </div>
  </section>
</template>

<script>
const data = [
  {
    logo:
      'https://static.bestsign.cn/fb003b516b6ee341d2e451a409038d5e5aa3b91c.png',
    name: 'Apple',
  },
  {
    logo:
      'https://static.bestsign.cn/36354d5222bc90bdd93c5af459609aa741d1277b.png',
    name: 'AWS',
  },
  {
    logo:
      'https://static.bestsign.cn/88d89eb86e79bfe1ed7f20103a033191d607ee74.png',
    name: 'Oracle',
  },
  {
    logo:
      'https://static.bestsign.cn/878f445aa4de50076dd6f5b5b8dc4f6fb061ba62.png',
    name: 'WPS',
  },
  {
    logo:
      'https://static.bestsign.cn/ffad16cb42ae33347b5290edb5902829e049447.png',
    name: 'Microsoft',
  },
  {
    logo:
      'https://static.bestsign.cn/50498dcb1733c72d74141ede88b6da9e1f09cde.png',
    name: 'intel',
  },
  {
    logo:
      'https://static.bestsign.cn/dea60259231834fb1c39a5daadc18df0c2b52223.png',
    name: '第五届世界互联网大会',
  },
  {
    logo:
      'https://static.bestsign.cn/9af6f76e1030ea5368287330214c9e29e9346a5f.png',
    name: '华为',
  },
  {
    logo:
      'https://static.bestsign.cn/fb3829f5604ffd32e185f98d6689fbb87fd0246a.png',
    name: '蚂蚁区块链',
  },
  {
    logo:
      'https://static.bestsign.cn/af6859f22f5a9a924d4999714733db61d8b991.png',
    name: '中智',
  },
  {
    logo:
      'https://static.bestsign.cn/181a72751af6cc610401375dc63fbc5642b68bc.png',
    name: '北森',
  },
  {
    logo:
      'https://static.bestsign.cn/25a98d2698e3a56f81e402d1b491c787b5a975c6.png',
    name: '盖雅工场',
  },
  {
    logo:
      'https://static.bestsign.cn/c17b7d037d1de333dc8491232aeca337db0d0b18.png',
    name: '销售易',
  },
  {
    logo:
      'https://static.bestsign.cn/5f7f9b44dd91b37e304de066dac3b45d1a85b8c7.png',
    name: '中国金融认证',
  },
  {
    logo:
      'https://static.bestsign.cn/288bd2d8a06355bf9ad29905fe64d1be9d885144.png',
    name: '北京数字认证',
  },
  {
    logo:
      'https://static.bestsign.cn/fdb0e9ade6bf42683701226456f96dd63bf1fb12.png',
    name: '福建省企业两化融合促进会',
  },
  {
    logo:
      'https://static.bestsign.cn/a4c5f0f968d76d95a17aef7f893ab8daad2dc10e.png',
    name: '衢州仲裁委员会',
  },
  {
    logo:
      'https://static.bestsign.cn/b9a0c840ee4b508f542bb41e38973549661aba96.png',
    name: '珠海仲裁委员会',
  },
  {
    logo:
      'https://static.bestsign.cn/6f19867976d22b40fa67fefe783dbd73f07b7ad4.png',
    name: '杭州互联网法院诉讼平台',
  },
  {
    logo:
      'https://static.bestsign.cn/76745b8a97b1969165f6b344305f5f7bfe47ef0.png',
    name: '上海市东方公证处',
  },
  {
    logo:
      'https://static.bestsign.cn/3ea1f16dceb501e306e0c6070f59c4ec89087177.png',
    name: '浙江省杭州市钱塘公证处',
  },
];
export default {
  name: 'partner',
  data() {
    return {
      data,
    };
  },
};
</script>

<style scoped lang="scss">
.section-partner {
  background-color: #fff;
  p {
    color: #888;
  }
  h1 {
    margin: 0;
  }
  .logo-container {
    margin: 4%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    // padding: 0 5rem;
    .logo-each {
      margin: 2rem 2rem;
      width: 7rem;
      img {
        width: 4rem;
      }
      .title {
        margin-top: 10px;
        font-size: 0.8rem;
        line-height: 1rem;
      }
    }
    i {
      width: 7rem;
      margin-right: 62px;
    }
  }
}
@media only screen and (max-width: 767px) {
  .section-partner {
    p {
      line-height: 2rem;
      margin: 0 2%;
    }
    .logo-container {
      margin: 4%;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      .logo-each {
        margin: 2rem 1.9%;
        width: 5rem;
        img {
          width: 5rem;
        }
        .title {
          margin-top: 10px;
          font-size: 12px;
          line-height: 1.3rem;
        }
      }
    }
  }
}
$base-color: #00aa64;
@function pxToVw($fz) {
  @return ($fz / 1920) * 100vw;
}
.about-us {
  .section {
    padding: 0 6rem 6rem;
  }
  .container {
    max-width: 1180px;
    width: 100%;
  }
  .section-headline {
    // margin-bottom: 50px;
    font-weight: 400;
    margin: 0;
  }
  .flex {
    display: flex;
    &.cc {
      align-items: center;
      justify-content: center;
    }
    &.bc {
      justify-content: space-between;
      align-items: center;
    }
  }
}

// @media only screen and (max-width: 767px) {
//   .about-us .section {
//     padding: 36px 12px;
//     .section-headline {
//       margin-bottom: 24px;
//     }
//   }
//   .section-6 {
//     .mobile-content {
//       .line {
//         margin-bottom: 2.75rem;
//         .title {
//           font-size: 15px;
//         }
//         .desc {
//           color: #888;
//           padding: 8px 0;
//         }
//       }
//       .el-col-16 {
//         padding-right: 3rem;
//         text-align: left;
//       }
//     }
//   }
// }

// @media only screen and (min-width: 992px) {
//   .section-headline {
//     font-size: 36px;
//   }
// }
</style>
