<template>
    <div id="hand-painted">

        <el-row class="drawSign" v-if="orientation=='portrait'">
            <el-col :span="4" class="footer-box col-draw">
                <div class="sign-footer">
                    <el-row class="rotate_footer">
                        <el-col :span="12" class="draw_footer_left">
                            <el-button @click="repaint" class="reButton" type="text">重写</el-button>

                            <button :class="['colorBtn','blackBtn',actNum == 1? 'activeBtn' : '']"
                                    @click="selectColor(1)"></button>
                            <button :class="['colorBtn','blueBtn',actNum == 2? 'activeBtn' : '']"
                                    @click="selectColor(2)"></button>
                            <button :class="['colorBtn','redBtn',actNum == 3? 'activeBtn' : '']"
                                    @click="selectColor(3)"></button>
                        </el-col>
                        <el-col :span="12" class="draw_footer_right">
                            <el-button @click="closeDraw('click')">不写了</el-button>
                            <el-button type="primary" @click="submitSign" class="useBtn"
                                    v-loading.fullscreen.lock="fullscreenLoading">使用</el-button>
                        </el-col>
                    </el-row>

                </div>
            </el-col>
            <el-col :span="17" class="col-draw">
                <div class="canvas_box" id="j_canvas_box">

                    <canvas id="drawBoard" :class="drawBoardBg" :width="devicePixelRatio?canvasH*devicePixelRatio:canvasH" :height="devicePixelRatio?canvasW*devicePixelRatio:canvasW" :style="drawStyle">
                       你的浏览器不支持画布手绘签名，请升级浏览器。
                    </canvas>
                </div>
            </el-col>
            <el-col :span="3" class="col-draw">
                <div class="sign-title">
                    <div class="rotate_title">
                        <p class="title" ref="title">手写签名</p>
                    </div>
                </div>
            </el-col>
        </el-row>
        <div class="el-row-sign" v-else>
            <!-- <vHeader class="header" :isReturn="true">
               <div slot="operate" class="title">手写签名</div>
             </vHeader>-->
            <div class="landscape_box" id="j_landscape_box">

                <canvas id="drawBoard" :class="drawBoardBg" :width="devicePixelRatio?canvasW*devicePixelRatio:canvasW" :height="devicePixelRatio?canvasH*devicePixelRatio:canvasH" :style="drawStyle">
                    你的浏览器不支持画布手绘签名，请升级浏览器。
                </canvas>
                <el-row class="draw_footer">
                    <el-col :span="12" class="draw_footer_left">
                        <el-button @click="repaint" class="reButton" type="text">重写</el-button>

                        <button :class="['colorBtn','blackBtn',actNum == 1? 'activeBtn' : '']"
                                @click="selectColor(1)"></button>
                        <button :class="['colorBtn','blueBtn',actNum == 2? 'activeBtn' : '']"
                                @click="selectColor(2)"></button>
                        <button :class="['colorBtn','redBtn',actNum == 3? 'activeBtn' : '']"
                                @click="selectColor(3)"></button>
                    </el-col>
                    <el-col :span="12" class="draw_footer_right">
                        <el-button @click="closeDraw('click')">不写了</el-button>
                        <el-button type="primary" @click="submitSign" class="useBtn"
                                   v-loading.fullscreen.lock="fullscreenLoading">使用
                        </el-button>
                    </el-col>

                </el-row>
            </div>

        </div>

    </div>
</template>
<script>
import SignaturePad from './signature_pad.js';

// 是否是华为手机
const isHUAWEI = function() {
  //   if (process.browser) {
  //     navigator = window.navigator;
  //   }
  let ua = navigator.userAgent;
  return ua.includes('HUAWEI') || ua.includes('HONOR') || ua.includes('Honor');
};

export default {
  name: 'HandPainted',
  components: {},
  /**
   * business表示所属业务，对应调用的接口不同
   * usercenter: 用户中心修改签名
   * sign: web签署页和移动端签署页
   * mobileSign: 扫码签名
   */
  props: {
    initialBusiness: {},
    parentGift: {},
    canvasWidth: {
      default: '',
    },
    canvasHeight: {
      default: '',
    },
  },
  data() {
    return {
      fullscreenLoading: false,
      submitUrl: '',
      business: this.$route.query.business
        ? this.$route.query.business
        : this.initialBusiness,
      isNewLabel: this.$route.query.isNewLabel
        ? this.$route.query.isNewLabel
        : 0, //标识是不是wap自定义签署位置进入的
      canvasElement: null,
      signaturePad: null,
      lastTouchEnd: 0,
      actNum: 1,
      orientation: '',
      lastOrientation: '',
      canvasW: 0,
      canvasH: 0,
      canvas64: '',
      penColor: '#333',
      isIosWX: false,
      devicePixelRatio: 1 /*获取屏幕是几倍屏，做画板的高清显示用*/,
      isEn: false,
    };
  },
  computed: {
    drawBoardBg() {
      return this.isEn ? 'drawBoard_en' : 'drawBoard';
    },
    isWap() {
      return this.$route.query.from === 'wap';
    },
    drawStyle: function() {
      let style = {};
      let self = this;
      if (this.orientation == 'portrait') {
        /* style =  {

                        "background": `#fff url('static/drawBkg1.png') no-repeat  center `,
                        "background-size":"51px 219px",

                    }*/
        if (this.devicePixelRatio) {
          style.width = `${self.canvasH}px`;
          style.height = `${self.canvasW}px`;
        }
      } else {
        /*style =  {
                        "background": `#fff url('static/drawBkg2.png') no-repeat  center `,
                        "background-size":"219px 51px",

                    }*/
        if (this.devicePixelRatio) {
          style.width = `${self.canvasW}px`;
          style.height = `${self.canvasH}px`;
        }
      }

      return style;
    },
    // 提交手写签名
    putHandWriteUrl() {
      let url = '';
      switch (this.business) {
        case 'usercenter':
          /*这个感觉废掉了，用户中心的是直接连接到这里来的，就是默认的提交方式*/
          url = `/users/ignore/signatures/handWrite`;
          break;
        case 'sign-single':
          /*pc版已经实名过的直接签署*/
          url = `${signPath}/contracts/${this.parentGift.contractId}/labels/${
            this.parentGift.labelId
          }/handWriteSignature`;
          break;
        case 'sign-all':
          /*手机短信的链接直接手机打开的时候签署,或者pc版新注册未实名的用户进来的时候签署*/
          url = `${signPath}/contracts/${
            this.parentGift.contractId
          }/labels/handWriteSignature`;
          break;
        case 'addSignature':
          // 签署人自定义签署位置 添加手绘签名时保存为我的签名
          url = `/users/signatures/qrcode`;
          break;
        case 'mobileSign':
          /*手机扫码签署的时候提交的接口*/
          url = `${signPath}/ignore/contracts/handWriteSignature/${
            this.$route.query.token
          }`;
          break;
        case 'batchSign':
          /*批量签署的时候，如果没有实名的话，会走这个逻辑*/
          url = `/users/signatures/default/hand`;
          break;
        case 'sign-demo':
          url = '';
          break;
        default:
          /*用户中心的设置签名的提交接口*/
          url = `/users/ignore/signatures/handWrite`;
      }
      return url;
    },
  },
  mounted() {
    let _this = this;
    this.devicePixelRatio = this.initDevicePixelRatio();

    document.body.style.backgroundColor = '#F6FAFD';
    if (!!this.canvasWidth && !!this.canvasHeight) {
      _this.canvasW = _this.canvasWidth;
      _this.canvasH = _this.canvasHeight;
      this.$nextTick(function() {
        this.initSignaturePad();
      });
    } else {
      _this.updateOrientation();
      window.addEventListener('resize', _this.updateOrientation);
    }
    //初始化的时候判断好，后面旋转就不用重复判断了
    this.isIosWX = this.isIosWXFn();
    //_this.doLayout();
    if (this.isWap) {
      if (window.history && window.history.pushState) {
        window.addEventListener('popstate', this.goBack, false);
      }
    }
    console.log(this.devicePixelRatio);
  },
  destroyed() {
    if (this.isWap) {
      window.removeEventListener('popstate', this.goBack, false);
    }
  },

  methods: {
    /*
             * @method
             *  @param
             * @returns
             * @desc 根据手机设备像素比来判断要几倍图 目前发现华为手机自带浏览器的上传图片太大的话，在网速较慢的情况下会传不上去，所以这里尽量取<3的值
             */
    initDevicePixelRatio() {
      if (isHUAWEI()) {
        return 1;
      }
      if (window.devicePixelRatio % 2 == 0) {
        return 2;
      } else if (window.devicePixelRatio % 3 == 0) {
        return 3;
      } else {
        console.log('ffff3');
        return window.devicePixelRatio;
      }
    },
    goBack() {
      //以下代码是为了，在wap上点击手写进入签名之后，点击返回或者不写了之后回到签署页
      // let host = 'https://ent.bestsign.info';
      // if (process.env.baseUrl.indexOf('cn') > -1) {
      //   host = 'https://ent.bestsign.cn';
      // }
      let host = '';
      if (process.env.NODE_ENV !== 'development') {
        host =
          process.env.baseUrl.indexOf('cn') > -1
            ? 'https://ent.bestsign.cn'
            : 'https://ent.bestsign.info';
      }
      if (this.isNewLabel === '1') {
        this.$http.delete(
          `${host}/contract-api/contracts/${
            this.parentGift.contractId
          }/labels/${this.parentGift.labelId}`
        );
      }
    },
    // 初始化 canvas
    initSignaturePad() {
      let canvasElement = document.querySelector('#drawBoard');
      let self = this;
      this.canvasElement = canvasElement;
      this.signaturePad = new SignaturePad(canvasElement, {
        throttle: 0,
        velocityFilterWeight: 1,
        minWidth: 0.5,
        maxWidth: 2.5,
        penColor: this.penColor,
        onBegin: function() {
          /*手写的时候把背景字去掉*/
          document.getElementById('drawBoard').className = '';
        },
        /*dotSize: 2,
                     minWidth: 1.5,
                     maxWidth: 5.5,
                     throttle: 0,
                     minDistance: 0,
                     velocityFilterWeight: 0*/
      });
    },
    /*
             * @method
             *  @param
             * @returns
             * @desc 手机情况下，根据屏幕旋转动态计算画板的宽高
             */
    updateOrientation() {
      const canvas = document.querySelector('#drawBoard');
      if (!canvas) {
        return false;
      }

      this.canvas64 = canvas.toDataURL('image/png');
      //console.log(this.canvas64);
      this.orientation =
        document.documentElement.clientWidth >
        document.documentElement.clientHeight
          ? 'landscape'
          : 'portrait';
      if (this.lastOrientation == this.orientation) {
        /*每次转屏幕会进入resize两次，为了避免重复出现问题，这里控制只进一次*/
        return false;
      }

      let oldH = this.canvasH;
      let oldW = this.canvasW;

      this.$nextTick(function() {
        let self = this;
        if (self.orientation == 'portrait') {
          //为了和之前的画布保持一致，横屏的高度就是竖屏的宽度，旋转不会重新计算。但是ios微信需要重新计算下。
          self.canvasW =
            oldW > 0 && !self.isIosWX
              ? oldW
              : document.getElementById('j_canvas_box').offsetHeight;
          self.canvasH =
            oldH > 0 && !self.isIosWX
              ? oldH
              : document.getElementById('j_canvas_box').offsetWidth;
          /*原来的画要跟着旋转*/
          self
            .rotateImage(
              this.canvas64,
              90,
              document.getElementById('drawBoard')
            )
            .then(function(data) {
              self.canvas64 = data;
            });
        } else {
          /*如果是ios微信，因为每次旋转都会重新计算，所以这里把这个地方改大点*/
          let newHeight = self.isIosWX
            ? Math.round(window.innerHeight * 0.8)
            : Math.round(window.innerHeight * 0.70833 * 0.95);
          let newWidth = document.getElementById('j_landscape_box').offsetWidth;
          //为了和之前的画布保持一致，横屏的高度就是竖屏的宽度，旋转不会重新计算。但是ios微信需要重新计算下。
          self.canvasH = oldH > 0 && !self.isIosWX ? oldH : newHeight;
          self.canvasW = oldW > 0 && !self.isIosWX ? oldW : newWidth;
          /*原来的画要跟着旋转*/
          self
            .rotateImage(
              this.canvas64,
              -90,
              document.getElementById('drawBoard')
            )
            .then(function(data) {
              self.canvas64 = data;
            });
        }
        if (!!this.lastOrientation) {
          /*存在则代表旋转过一次了，则需要把画板背景去掉*/
          this.$nextTick(function() {
            document.getElementById('drawBoard').className = '';
          });
        }
        self.lastOrientation = self.orientation;
        this.$nextTick(function() {
          /*一定要等新的canvas渲染完了才能初始化*/
          this.initSignaturePad();
        });
      });
    },
    isWeiXin() {
      var ua = window.navigator.userAgent.toLowerCase();
      if (ua.match(/MicroMessenger/i) == 'micromessenger') {
        return true;
      } else {
        return false;
      }
    },
    /*
             * @method ios的新版微信下面多了一个bar，旋转的时候需要重新计算画布的宽高
             * @param
             * @returns 判断是否是ios的微信
             */
    isIosWXFn() {
      var u = navigator.userAgent;
      var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
      return isIOS && this.isWeiXin();
    },
    /**
             *
             * @param {参数类型} 参数名 参数说明
             * @return {返回值类型} 返回值说明
             * @desc 浏览器关闭画板

             */
    CloseWebPage() {
      if (navigator.userAgent.indexOf('MSIE') > 0) {
        //close IE
        if (navigator.userAgent.indexOf('MSIE 6.0') > 0) {
          window.opener = null;
          window.close();
        } else {
          window.open('', '_top');
          window.top.close();
        }
      } else if (navigator.userAgent.indexOf('Firefox') > 0) {
        //close firefox
        window.location.href = 'about:blank ';
      } else {
        //close chrome;It is effective when it is only one
        window.opener = null;
        window.open('', '_self', '');
        window.close();
      }
      window.history.back(); //部分手机
    },
    // 返回笔画数、笔画最长的点数
    formatStroke() {
      let stroke = this.signaturePad.toData();

      stroke.sort((a, b) => {
        return a.points.length < b.points.length;
      });

      return {
        length: stroke.length,
        maxLength: stroke.length ? stroke[0].points.length : 0,
      };
    },
    // 将 data 转为 [x, y, x, y] 的格式
    formatPositionCount() {
      let arr = [];
      let paths = [];
      let stroke = this.signaturePad.toData();

      if (stroke.length) {
        stroke.forEach(path => {
          paths.push(...path.points);
        });

        paths.forEach(point => {
          arr.push(point.x, point.y);
        });
      }

      return arr;
    },
    /*
             * @method
             * @param {canvas64} canvas画布画好的base64图片
             * @returns
             * @desc 发请求提交手写签名到服务端，不存在putHandWriteUrl return给组件处理
             */
    uploadDrawImage(canvas64) {
      this.$store.commit('setSignImageBase64', canvas64);
      if (!this.putHandWriteUrl) {
        this.$emit('write-done', canvas64);
        return;
      }
      let _this = this;
      let submitData = _this.getSubmitData(canvas64);

      this.fullscreenLoading = true;
      this.$http
        .post(this.putHandWriteUrl, submitData)
        .then(res => {
          // 自定义签署位置 手绘签名后增加到我的签名列表
          if (this.business == 'addSignature') {
            this.$emit('save-signature');
          } else {
            _this.$MessageToast({
              message: this.$t('handwrite.submitTip'),
              type: 'success',
            });
          }
          this.$emit('write-done', {
            imageData: submitData.file,
            resData: res.data,
          });
          this.$emit('write-done', {
            imageData: submitData.file,
            resData: res.data,
          });
          _this.fullscreenLoading = false;

          this.closeDraw();
        })
        .catch(err => {
          _this.fullscreenLoading = false;
        });
    },
    /**
             *
             * @param {参数类型} 参数名 参数说明
             * @return {返回值类型} 返回值说明
             * @desc 点击按钮"不写了"的时候执行这个，如果是扫码进来的话则是关闭，其他的如果是在弹窗里面的话则发消息给父组件关闭弹窗

             */
    closeDraw(method) {
      // 只有wap端需要跳转, wap端this.business恒为'sign-all'
      if (this.isWap) {
        /*为了兼容有些手机$router.go(-1)不起作用，所以这样。。。*/
        if (
          window.localStorage &&
          window.localStorage.getItem('transitionHref')
        ) {
          let transitionHref = window.localStorage.getItem('transitionHref');
          this.$router.push(transitionHref);
          //以下代码是为了，在wap上点击手写进入签名之后，点击返回或者不写了之后回到签署页 删除默认加入的签名标签
          if (method === 'click' && this.isNewLabel === '1') {
            // let host = 'https://ent.bestsign.info';
            // if (location.host.indexOf('cn') > -1) {
            //   host = 'https://ent.bestsign.cn';
            // }
            let host = '';
            if (process.env.NODE_ENV !== 'development') {
              host =
                process.env.baseUrl.indexOf('cn') > -1
                  ? 'https://ent.bestsign.cn'
                  : 'https://ent.bestsign.info';
            }
            this.$http.delete(
              `${host}/contract-api/contracts/${
                this.parentGift.contractId
              }/labels/${this.parentGift.labelId}`
            );
          }
        } else {
          /*因为如果是手机签署页过来的时候，刷新的时候并没有走一次router的next渲染，所以这只需要go-1就ok*/
          this.$router.go(-1);
        }
        return;
      } else if (
        this.business.match(/sign-all|sign-single|batchSign|addSignature/i)
      ) {
        this.$emit('close-handwrite');
        return;
      } else if (this.business.match(/sign-demo/i)) {
        return this.$emit('close-handwrite');
      }
      if (this.isWeiXin()) {
        WeixinJSBridge.call('closeWindow');
      } else {
        this.CloseWebPage();
      }
    },
    /*画布重绘*/
    repaint() {
      this.signaturePad.clear();
      // const canvas = document.querySelector('#drawBoard');
      // const context = canvas.getContext('2d');
      // context.clearRect(0, 0, canvas.width, canvas.height);
      // Draw._positionCount = [];//重绘时统计数组清零
    },
    /**
             *
             * @param {canvas} canvas的dom节点
             * @return {bool} 是否是空的canvas
             * @desc 判断是否是空的canvas

             */
    isCanvasBlank(canvas) {
      var blank = document.createElement('canvas');
      blank.width = canvas.width;
      blank.height = canvas.height;

      return canvas.toDataURL() == blank.toDataURL();
    },
    /*
             * @method
             * @param {canvas64} canvas画布画好的base64图片
             * @returns {imageData} 请求保存签名的时候发送给服务端的数据
             * @desc 获取保存签名的时候发送给服务端的数据
             */
    getSubmitData(canvas64) {
      const imageData = canvas64.split(',')[1];
      const token = this.$route.query.token || '';

      let submitData;
      switch (this.business) {
        case 'batchSign':
          submitData = {
            file: imageData,
          };
          break;
        case 'sign-single':
        case 'mobileSign':
          submitData = {
            file: imageData, //  签名数据  string
            token: token, //  二维码中获取的token  string
          };
          break;
        case 'sign-all':
          submitData = {
            labelId: this.parentGift.labelId,
            file: imageData, //  签名数据  string
            token: token, //  二维码中获取的token  string
          };
          break;
        case 'addSignature':
          submitData = {
            file: imageData, //  签名数据  string
            sigId: '',
          };
          break;
        default:
          submitData = {
            data: this.formatPositionCount(),
            file: imageData, //  签名数据  string
            token: token, //  二维码中获取的token  string
          };
      }
      return submitData;
    },
    /*
             * @method 确认使用按钮的响应事件

             */
    submitSign() {
      const canvas = document.querySelector('#drawBoard');
      if (!!canvas && this.isCanvasBlank(canvas)) {
        this.$MessageToast.error(this.$t('handwrite.needWrite'));
        return false;
      }
      //因为涉及到旋转的时候的重绘，不能再用这个方法判断是否是空没填了
      /* let clearedWriting = this.formatStroke();

                 /!* 判断不为空笔画数小于2，并且记录的最长的路径点小于 7 则提示 *!/
                 if (clearedWriting.length < 2 && clearedWriting.maxLength < 7) {
                 this.$MessageToast.error('请手绘正确的姓名！');
                 return false;
                 }*/

      this.canvas64 = canvas.toDataURL('image/png');
      let self = this;
      if (self.orientation == 'portrait') {
        /*如果是竖屏，则需要将图片90°旋转之后再提交*/
        self.rotateImage(this.canvas64, -90).then(function(data) {
          self.uploadDrawImage(data);
        });
      } else {
        self.uploadDrawImage(self.canvas64);
      }
    },
    /*
             * @method
             * @param {url,degrees,canvasEl}
             * @returns Promise对象 异步处理图片旋转
             * @desc 根据图片和需要的旋转角度进行旋转
             */
    rotateImage(url, degrees, canvasEl = null) {
      let self = this;
      return new Promise(function(resolve, reject) {
        const image = new Image();

        image.onload = function() {
          let data = self.rotate(image, degrees, canvasEl);
          resolve(data);
        };

        /*image.onerror = function() {
                     reject();
                     };*/

        image.src = url;
      });
    },
    /*
             * @method
             * @param {image,degrees,canvasEl}
             * @returns 图片旋转后的base64数据
             * @desc 根据图片和需要的旋转角度进行旋转
             */
    rotate(image, degrees, canvasEl) {
      var _this = this;
      var w = image.naturalWidth;
      var h = image.naturalHeight;
      var canvasWidth = Math.max(w, h);
      var cvs = _this._getCanvas(canvasWidth, canvasWidth);
      var ctx = cvs.getContext('2d');
      ctx.translate(canvasWidth / 2, canvasWidth / 2);
      ctx.rotate(degrees * (Math.PI / 180));
      var x = -canvasWidth / 2;
      var y = -canvasWidth / 2;
      degrees = degrees % 360;
      if (degrees === 0) {
        return callback(src, w, h);
      }
      var sx = 0;
      var sy = 0;
      if (degrees % 180 !== 0) {
        if (degrees === -90 || degrees === 270) {
          x = -w + canvasWidth / 2;
        } else {
          y = canvasWidth / 2 - h;
        }
        const c = w;
        w = h;
        h = c;
      } else {
        x = canvasWidth / 2 - w;
        y = canvasWidth / 2 - h;
      }
      ctx.drawImage(image, x, y);
      //var cvs2 = _this._getCanvas(w, h);
      var cvs2 = !canvasEl ? _this._getCanvas(w, h) : canvasEl;
      var ctx2 = cvs2.getContext('2d');
      if (canvasEl) {
        /*如果是提交前旋转的话是画板是真实图大小的1/放大倍率*/
        ctx2.drawImage(
          cvs,
          0,
          0,
          w,
          h,
          0,
          0,
          w / _this.devicePixelRatio,
          h / _this.devicePixelRatio
        );
      } else {
        /*如果是要提交了，这里的画布的高宽和实际图片是同等高宽的*/
        ctx2.drawImage(cvs, 0, 0, w, h, 0, 0, w, h);
      }

      /*var mimeType = _this._getImageType(image.src);
                 var data = cvs2.toDataURL(mimeType, 1);*/
      let data = cvs2.toDataURL('image/png');
      return data;
    },
    /**
             *
             * @param {参数类型} 参数名 参数说明
             * @return {返回值类型} 返回值说明
             * @desc 根据高宽得到画板dom

             */
    _getCanvas(width, height) {
      var canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      return canvas;
    },
    /*根据颜色设置画板画笔颜色*/
    selectColor(actNum) {
      this.actNum = actNum;
      if (this.actNum == 3) {
        this.penColor = '#cb0404';
      } else if (this.actNum == 2) {
        this.penColor = '#127fd2';
      } else {
        this.penColor = '#333';
      }
      this.signaturePad.setPenColor(this.penColor);
    },
  },
};
</script>
<style lang="scss" scoped>
@import './index.scss';
</style>
