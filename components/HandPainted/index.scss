@function pxTorem($px) {
    @return $px / 75 * 1rem;
  }
  html, body {
      width: 100%;
      height: 100%;
      background-color: #F6FAFD;
  }
  #hand-painted{
      background-color: #F6FAFD;
      .col-draw{
          height:100%;
      }
      .title{
          text-align: center;
          color: #333;
          font-size: 15px;
          height: 50px;
          line-height: 50px;
      }
  
      .drawSign{
          height: 100vh;
          width: 100%;
          overflow: hidden;
  
      }
      .row-sign{
          min-height: 100vh;
          width: 100%;
          overflow: auto;
          background-color: #f6f6f6;
      }
      .sign-title{
          transform: rotate(90deg);
          -ms-transform:rotate(90deg); /* Internet Explorer */
          -moz-transform:rotate(90deg); /* Firefox */
          -webkit-transform:rotate(90deg); /* Safari 和 Chrome */
          -o-transform:rotate(90deg); /* Opera */
          width: 100vh;
          height: 100%;
          position: relative;
          background-color: #fff;
      }
      .rotate_title{
          width: 100%;
          position: absolute;
          left: 0;
          bottom: 0;
      }
      .footer-box{
          overflow: hidden;
      }
      .sign-footer{
          transform: rotate(90deg);
          -ms-transform:rotate(90deg); /* Internet Explorer */
          -moz-transform:rotate(90deg); /* Firefox */
          -webkit-transform:rotate(90deg); /* Safari 和 Chrome */
          -o-transform:rotate(90deg); /* Opera */
          width: 100vh;
          height: 100%;
          position: relative;
      }
      .rotate_footer{
          position: absolute;
          left: 0;
          bottom: -4px;
          width: 100%;
          height: 60px;
          padding: 0 15px;
      }
      .canvas_box{
          width: 95%;
          height: 96%;
          background-color: #f6f6f6;
          border-radius: 2px;
          margin-top: 5%;
          margin-left: 5px;
  
          .drawBoard{
              background: #fff url('../../assets/images/handpaint/drawBkg1.png') no-repeat  center;
              background-size: 51px 219px;
          }
          .drawBoard_en {
              background: #fff url('../../assets/images/handpaint/draw-bg-en-2.png') no-repeat  center;
              background-size: 32px 219px;
          }
      }
      .draw_footer{
          margin-top: 10px;
      }
      .draw_footer_left{
          text-align: left;
      }
      .draw_footer_right{
          text-align: right;
          overflow: hidden;
  
      }
      @media screen and (max-width: 321px) {
          .draw_footer_right{
  
              padding-right: 30px;
          }
      }
      @media screen and (min-width: 321px) {
          .draw_footer_right{
  
              padding-right: 65px;
          }
      }
      /*判断是iphonex*/
      @media only screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio:3) {
          .draw_footer_right{
              padding-right: 110px;
          }
      }
      .landscape_box{
          width: 96%;
          /* height: 90%;*/
          margin: 0 auto;
  
          .drawBoard{
              background: #fff url('../../assets/images/handpaint/drawBkg2.png') no-repeat  center;
              background-size:219px 51px;
          }
          .drawBoard_en {
              background: #fff url('../../assets/images/handpaint/draw-bg-en.png') no-repeat  center;
              background-size:219px 32px;
          }
      }
      #drawBoard{
          background-color: #fff;
          margin: 8px auto;
          display: block;
  
      }
      .drawBoard{
          margin: 8px auto 4px;
      }
      .finish{
          text-align: center;
          margin-top: 100px;
          color: red;
          font-size: 14px;
      }
      .reButton{
          background-color: #F6FAFD;
          border-radius: 0;
          border-right: 1px solid #b6b6b6;
          padding-right: 20px;
      }
      .useBtn{
          padding: 10px 30px;
      }
      .colorBtn{
          width: 26px;
          height: 26px;
          border-radius: 13px;
          margin-left: 15px;
          padding: 7px 0;
          outline: 0 none;
          display: inline-block;
      }
      .blackBtn{
          background: #333;
      }
      .blueBtn{
          background: #127fd2;
      }
      .redBtn{
          background: #cb0404;
      }
      .reButton:hover, .reButton:focus, .reButton:active{
          background-color: #F6FAFD;
          border-color: #f6f6f6;
          border-right: 1px solid #b6b6b6;
      }
      .activeBtn{
          border: 3px solid #cfcfcf;
          width: 30px;
          height: 30px;
          border-radius: 15px;
      }
  }
  /*#hand-painted {
         background-color: #f2f2f2;
          position: relative;
          overflow: hidden;
          // font-size: pxTorem(20);
          font-size: 13px;
          .title {
              width: 100%;
              // height: pxTorem(60);
              height: 50px;
              background-color: #002b45;
              // line-height: pxTorem(60);
              line-height: 50px;
              text-align: center;
              color: #fff;
          }
          #drawBoard {
              background-color: #ffffff;
          }
          .canvas-con {
              position: relative;
              user-select: none;
          }
          .sign-info {
              background-image: url(../../images/sign_info.png);
              background-size: 65%;
              background-repeat: no-repeat;
              background-position: center
          }
          .bottom {
              !*position: absolute;
              left: 0;*!
              width: 100%;
              // height: pxTorem(70);
              // line-height: pxTorem(70);
              height: 60px;
              line-height: 60px;
              background-color: #f2f2f2;
              border-top: 1px solid #ddd;
              i {
                  // margin-left: pxTorem(30);
                  // margin-right: pxTorem(15);
                  margin-left: 13px;
                  margin-right: 5px;
                  color: #127fd2;
              }
              span {
                  color: #666;
              }
              .info, .btnCon, .btn {
                  display: inline-block;
              }
              .btnCon {
                  position: absolute;
                  // right: pxTorem(30);
                  right: 20px;
              }
              .btn {
                  // width: pxTorem(150);
                  // height: pxTorem(40);
                  // line-height: pxTorem(40);
                  width: 70px;
                  height: 30px;
                  line-height: 30px;
              }
              .btn-type-repainted {
                  cursor: pointer;
                  text-align: center;
                  color: #666;
              }
          }
      }*/
  #hand-painted.fullScreen {
      .bottom {
          bottom: 0;
      }
  }
  #hand-painted.noFullScreen {
      background-color: #f2f2f2;
      .blank-section {
          width: 100%;
          height: 25px;
          background-color: #f2f2f2;
          border-bottom: 1px solid #ddd;
      }
      .bottom {
          margin-top: -2px;
          border-bottom: 1px solid #ddd;
      }
  }
  #hand-painted.vertical {
      position: absolute;
      .title, .bottom {
          position: absolute;
      }
      
      /*去掉overflow 微信显示正常，但是浏览器有问题，竖屏时强制横屏缩小*/
      overflow: hidden;
      .title {
          -webkit-transform: rotate(90deg);
          -ms-transform: rotate(90deg);
          transform: rotate(90deg);
          -ms-transform-origin: left top;
          transform-origin: left top;
          -webkit-transform-origin: left top;
      }
      .bottom {
          -webkit-transform: rotate(90deg);
          -ms-transform: rotate(90deg);
          transform: rotate(90deg);
          -ms-transform-origin: left bottom;
          transform-origin: left bottom;
          -webkit-transform-origin: left bottom;
      }
  }
          
  