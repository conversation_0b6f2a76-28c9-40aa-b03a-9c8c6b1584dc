<!-- 获取验证码倒计时 -->
<template>
  <button
    :disabled="disabled || time > 0"
    :class="{active: time > 0}"
    class="count-down"
    @click.prevent="clickedFn"
  >
    {{ text }}
  </button>
</template>
<script>
export default {
  name: 'CountDown',
  data() {
    return {
      time: 0,
    };
  },
  props: {
    isWap: {
      type: Boolean,
      default: false,
    },
    second: {
      type: Number,
      default: 60,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    clickedFn: {
      type: Function,
    },
    runFlag: {
      type: Boolean,
      default: false,
    },
    initial: {
      type: String,
      default: '获取验证码',
    },
  },
  methods: {
    reset() {
      this.time = 0;
    },
    run: function() {
      this.time = this.second;
      this.timer();
    },
    timer: function() {
      let inTimer = setInterval(() => {
        if (this.time > 0) {
          this.time--;
        } else {
          clearInterval(inTimer);
        }
      }, 1000);
    },
  },
  computed: {
    text: function() {
      if (this.isWap) {
        return this.time > 0 ? `重新获取(${this.time}s)` : this.initial;
      } else {
        return this.time > 0 ? `${this.time}s` : this.initial;
      }
    },
  },
  watch: {
    runFlag: function(val, oldVal) {
      if (val) {
        this.run();
      }
    },
  },
};
</script>
<style lang="scss">
.count-down {
  background-color: #f6f6f6;
  color: #127fd2;
  border: 1px solid #ccc;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
  border-radius: 1px;
  &:hover {
    background-color: #fff;
  }
  &.active {
    background-color: #fff;
    color: #999;
  }
}
</style>
