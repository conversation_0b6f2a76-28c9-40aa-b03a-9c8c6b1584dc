<template>
  <div class="help-wrapper">
    <div class="pc float-help" v-if="!isMobile">
      <!-- <div
          slot="reference"
          id="notShowUdesk"
          class="item"
          :style="{ backgroundColor: bgColor }">
            <i class="iconfont icon-icon-test"></i>
            <a>在线咨询</a>
        </div>
        <div class="space"></div>
      <el-popover
        trigger="hover"
        placement="right"
      >
        <div class="link">
          <p>全国服务热线</p>
          <a href="tel:4009936665">************</a>
        </div>
        <div
          slot="reference"
          class="item"
          :style="{ backgroundColor: bgColor }">
            <i class="iconfont icon-dianhua1"></i>
            <span>电话咨询</span>
        </div>
      </el-popover>
      <div class="space"></div> -->
      <div
        class="item"
        @click="handleDemo">
        <i class="iconfont icon-a-xingzhuang619" style="font-size:22px;"></i>
            <span>{{$t('header.use')}}</span>
      </div>
      <div
        class="item"
        @click="handleScroll">
        <i class="iconfont icon-a-xingzhuang616" style="font-size:14px;"></i>
      </div>
     </div>
    <!-- <div class="mobile" v-else>
        <div class="left" :style="{ color: bgColor, borderColor: bgColor }" id="notShowUdesk">
        <i class="iconfont icon-icon-test"></i><a @click="toServiceWap">在线咨询</a></div>
      <a class="right" :style="{ backgroundColor: bgColor }" href="tel:4009936665">
        <i class="iconfont icon-dianhua1"></i>免费服务热线</a>
    </div> -->
  </div>
</template>

<script>
import Qs from 'qs';
export default {
  name: 'FloatHelp',
  props: {
    bgColor: {
      type: String,
      default: '#f3c51e',
    },
  },
  computed: {
    serviceVisible() {
      return this.$store.state.serviceVisible;
    },
    isMobile() {
      return this.$store.state.isMobile;
    },
    serviceSrc() {
      return 'https://bestsign.udesk.cn/im_client/?web_plugin_id=23490';
    },
  },
  mounted() {
    // this.udesk();
  },
  methods: {
    handleScroll() {
      this.$scrollTo('body');
    },
    handleHelp() {
      this.$router.push('/help');
    },
    handleService() {
      this.$store.commit('setServiceVisible', true);
    },
    closeFeedback() {
      this.$store.commit('setServiceVisible', false);
    },
    handleDemo() {
      const query = sessionStorage.getItem('query');
      const url = 'https://ent.bestsign.com/register';
      window.open(url + '?' + query);
    },
    tolink(route) {
      var query = Object.entries(this.$route.query)
        .map(([key, value]) => {
          return `${key}=${value}`;
        })
        .join('&');
      if (route.indexOf('?') > -1) {
        route = route + '&' + query;
      } else {
        route = route + '?' + query;
      }
      return route;
    },
  },
};
</script>
<style lang="scss">
.el-popover {
  text-align: center;
  .download {
    p {
      margin-top: 15px;
    }
  }
  .link {
    p {
      padding: 7px 0;
    }
    a {
      color: $-color-main;
    }
  }
}
</style>
<style scoped lang="scss">
.help-wrapper {
  .iframe {
    position: fixed;
    right: 60px;
    bottom: 0;
    width: 360px;
    height: 480px;
    background-color: #f8fbff;
    box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 20px 0px;
    transition: all 0.5s ease;
    transform: translateY(100%);
    z-index: 100000;
    iframe {
      width: 100%;
      height: 100%;
    }
    .close,
    .enlarge {
      position: absolute;
      right: 12px;
      top: 0;
      font-size: 16px;
      cursor: pointer;
      padding: 10px;
      color: #fff;
      span {
        font-size: 12px;
        font-weight: 800;
      }
      .iconfont {
        font-size: 12px;
      }
    }
    &.open,
    &.enlarge {
      transform: translateY(0);
    }
    &.service {
      .close,
      .enlarge {
        color: #fff;
        right: 125px;
        top: 9px;
      }
      .enlarge {
        right: 76px;
        i {
          vertical-align: text-bottom;
          margin: 0 0 0 2px;
          font-weight: bold;
        }
      }
    }
  }
  .is-enlarge {
    width: 500px;
    height: 572px;
  }
  .mobile {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 50px;
    display: flex;
    font-size: 18px;
    text-align: center;
    z-index: 999;
    .iconfont {
      font-size: 20px;
      margin-right: 0.75rem;
      vertical-align: middle;
    }
    .left {
      display: inline-block;
      width: 45%;
      height: 100%;
      line-height: 50px;
      color: $-color-main;
      background-color: #fff;
      // border-top: 1px solid $-color-main;
    }
    .right {
      flex: 1;
      height: 100%;
      line-height: 50px;
      // background-color: $-color-main;
      color: #fff;
    }
  }
}
.float-help {
  position: fixed;
  bottom: 20%;
  right: 20px;
  text-align: center;
  z-index: 999;
  background: #fff;
  border-radius: 30px;
  box-shadow: 0 0px 30px 0 rgba(0, 0, 0, 0.1);
  .item {
    &:hover {
      span {
        color: $-color-main;
      }
      .iconfont {
        color: $-color-main;
      }
    }
    .iconfont {
      color: #767676;
      font-size: 18px;
      // &:hover {
      //   color: $-color-main;
      // }
    }
    // padding: 16px 0;
    display: flex;
    width: 60px;
    cursor: pointer;
    position: relative;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    // border-top: 5px solid #fff;
    // border: 2.5px solid #fff;
    font-size: 12px;
    color: #fff;
    height: 60px;
    // background-color: $-color-main;
    border-radius: 2px 2px 2px 2px;
    margin: 10px 0;
    span {
      margin-top: 7px;
      font-size: 12px;
      font-weight: 500;
      color: #767676;
      // &:hover {
      //   color: $-color-main;
      // }
      a {
        color: #767676;
      }
    }
    a {
      margin-top: 7px;
      a {
        color: #fff;
      }
    }
  }
  #notShowUdesk {
    // padding: 16px 0;
    display: flex !important;
    width: 60px;
    cursor: pointer;
    position: relative;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    // border-top: 5px solid #fff;
    // border: 2.5px solid #fff;
    font-size: 12px;
    color: #fff;
    height: 60px;
    // background-color: red;
    border-radius: 2px 2px 2px 2px;
    margin-top: 10px;
    &:hover {
      .iconfont {
        color: $-color-main;
      }
      span {
        color: $-color-main;
      }
    }
    span {
      margin-top: 7px;
      font-size: 12px;
      color: #767676;
      a {
        color: #fff;
      }
    }
    a {
      margin-top: 7px;
      font-size: 14px;
      font-weight: 500;
      a {
        color: #fff;
      }
    }
  }
  .el-icon-arrow-up {
    font-size: 22px;
    color: #fff;
  }
  .icon {
    font-size: 20px;
  }
  // .space {
  //   background-color: transparent;
  //   height: 10px;
  //   width: 100%;
  // }
}
.sapce {
  // border-top: 2.5px solid #fff;
  // border-bottom: 2.5px solid #fff;
  background-color: #fff;
  height: 5px !important;
  width: 100%;
}
@media screen and (max-width: 767px) {
  .help-wrapper {
    .iframe {
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
