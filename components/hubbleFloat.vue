<template>
  <div class="help-wrapper">
    <div class="pc hubble-float-help" v-if="!isMobile">
        <div
          slot="reference"
          id="notShowUdesk"
          class="item"
          @click="toServicePC">
            <i class="iconfont icon-zaixianzixun4"></i>
            <span>在线咨询</span>
        </div>
        <!-- <div class="space"></div> -->
        <!-- <el-popover
        trigger="hover"
        placement="right"
      >
        <div class="link">
          <p>在线咨询</p>
        </div>
        <div
          slot="reference"
          id="notShowUdesk"
          class="item">
          <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-dianhua1"></use>
            </svg>
            <i class="iconfont icon-zaixianzixun4"></i>
            <span>电话咨询</span>
        </div>
      </el-popover> -->
      <el-popover
        trigger="hover"
        placement="right"
      >
        <div class="link">
          <p>全国服务热线</p>
          <a href="tel:4009936665">************</a>
        </div>
        <div
          slot="reference"
          class="item">
          <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-dianhua1"></use>
            </svg> -->
            <i class="iconfont icon-a-xingzhuang613"></i>
            <span>电话咨询</span>
        </div>
      </el-popover>
      <!-- <div class="space"></div> -->
      <!-- <el-popover
        trigger="hover"
        placement="right"
      >
        <div class="link">
          <p>免费试用</p>
        </div>
        <a href="javascript:void(0);"
          slot="reference"
          class="item"
          @click="handleDemo">
            <i class="iconfont icon-a-xingzhuang619" style="font-size:22px;"></i>
        </a>
      </el-popover> -->
      <div
        class="item"
        @click="gotoHubble('right')">
        <i class="iconfont icon-a-xingzhuang619" style="font-size:22px;"></i>
            <span>免费试用</span>
      </div>
        
      <!-- <div
        class="item"
        :style="{ backgroundColor: bgColor }"
        @click="handleScroll">
        <i class="iconfont icon-xiangshangjiantouarrowup"></i>
            <span>返回顶部</span>
      </div> -->
      <div
        class="item"
        @click="handleScroll">
        <i class="iconfont icon-a-xingzhuang616" style="font-size:14px;"></i>
            <!-- <span>返回顶部</span> -->
      </div>
      <div class="iframe service" v-if="serviceVisible" :class="{open: serviceVisible, 'is-enlarge': isEnlarge}">
            <div class="close"  @click="closeFeedback">
                <span>最小化<i class="iconfont icon-zuixiaohua"></i></span>
                
            </div>
            <div class="enlarge" @click="handleEnlarge">
                <span>{{ isEnlarge ? '缩小' : '放大' }}<i class="iconfont icon-fangda"></i></span>
                
                
            </div>
            <iframe :src="serviceSrc" frameborder="0"></iframe>
        </div>
    </div>
    <div class="mobile" v-else>
        <div
          slot="reference"
          id="notShowUdesk"
          class="item"
          @click="toServiceWap">
            <!-- <i class="iconfont icon-zaixianzixun4"></i> -->
            <i class="iconfont icon-qm"></i>
            <!-- <div class="text">在线咨询</div> -->
        </div>
    </div>
  </div>
</template>

<script>
import Qs from 'qs';
// const Random = require('random-js');
// import { Random } from 'random-js';
// import sha1 from 'js-sha1';
export default {
  name: 'FloatHelp',
  props: {
    bgColor: {
      type: String,
      default: '#0C85E9',
    },
  },
  data() {
    return {
      isEnlarge: false,
    };
  },
  computed: {
    serviceVisible() {
      return this.$store.state.serviceVisible;
    },
    isMobile() {
      return this.$store.state.isMobile;
    },
    serviceSrc() {
      // var userkey = 'f25b205a263df814b042c0ddaf72b11e';
      // var random = new Random();
      // var nonce = random.string(16);
      // var timestamp = Date.parse(new Date());
      // var web_token = this.getUserAccount;
      // var sign_str =
      //   'nonce=' +
      //   nonce +
      //   '&timestamp=' +
      //   timestamp +
      //   '&web_token=' +
      //   web_token +
      //   '&' +
      //   userkey;
      // var signature = sha1(sign_str).toUpperCase();
      // var type = this.getUserType === 'Person' ? '个人' : '企业';
      // var mainbody = '';
      // if (this.getUserType === 'Person') {
      //   mainbody = this.getUserFullName
      //     ? this.getUserFullName
      //     : this.getUserAccount;
      // } else {
      //   mainbody = this.console_getEntName
      //     ? this.console_getEntName
      //     : this.getUserAccount;
      // }
      // var query = Qs.stringify({
      //   nonce: nonce,
      //   timestamp: timestamp,
      //   web_token: web_token,
      //   signature: signature,
      //   c_cf_用户登录账号: web_token,
      //   c_cf_用户类型: type,
      //   c_cf_认证主体名: mainbody,
      //   c_cf_用户咨询来源: 'PC',
      // });
      return 'https://bestsign.udesk.cn/im_client/?web_plugin_id=23490';
    },
  },
  mounted() {
    // this.udesk();
  },
  methods: {
    handleScroll() {
      this.$scrollTo('body');
    },
    handleHelp() {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/help',
        query: {
          ...Qs.parse(query),
        },
      });
    },
    closeFeedback() {
      this.$store.commit('setServiceVisible', false);
    },
    // 放大/缩小
    handleEnlarge() {
      this.isEnlarge = !this.isEnlarge;
    },
    udesk() {
      (function(a, h, c, b, f, g) {
        a['UdeskApiObject'] = f;
        a[f] =
          a[f] ||
          function() {
            (a[f].d = a[f].d || []).push(arguments);
          };
        g = h.createElement(c);
        g.async = 1;
        g.charset = 'utf-8';
        g.src = b;
        c = h.getElementsByTagName(c)[0];
        c.parentNode.insertBefore(g, c);
      })(
        window,
        document,
        'script',
        'https://assets-cli.udesk.cn/im_client/js/udeskApi.js',
        'ud'
      );
      ud({
        code: '1aifdai3',
        targetSelector: '#notShowUdesk',
        //  "selector":"#btn_udesk_im",*/
        // mode: 'inner',
        // color: '#70b544',
        // pos_flag: 'vrm',
        // css: {
        //   bottom: '80px',
        //   right: '50px',
        //   width: '36px',
        //   height: '36px',
        //   border: 'none',
        //   background: 'url("/page/static/<EMAIL>") no-repeat',
        //   backgroundColor: 'rgba(0, 0, 0, 0.2)'
        // },
        link: 'https://bestsign.udesk.cn/im_client/?web_plugin_id=23490',
      });
    },
    gotoHubble(cta) {
      this.$emit('gotoEntHubble', cta);
    },
    tolink(route) {
      var query = Object.entries(this.$route.query)
        .map(([key, value]) => {
          return `${key}=${value}`;
        })
        .join('&');
      // debugger;
      if (route.indexOf('?') > -1) {
        route = route + '&' + query;
      } else {
        route = route + '?' + query;
      }
      return route;
    },
    toServiceWap() {
      const url = this.tolink(
        'http://bestsign.udesk.cn/im_client/?web_plugin_id=23490'
      );
      window.open(url);
      // console.log(url);
    },
    toServicePC() {
      this.$store.commit('setServiceVisible', true);
    },
  },
};
</script>
<style lang="scss">
.el-popover {
  text-align: center;
  .download {
    p {
      margin-top: 15px;
    }
  }
  .link {
    p {
      padding: 7px 0;
    }
    a {
      color: #0c85e9;
    }
  }
}
</style>
<style scoped lang="scss">
.help-wrapper {
  // .iframe {
  //   position: fixed;
  //   right: 60px;
  //   bottom: 0;
  //   width: 320px;
  //   height: 480px;
  //   background-color: #f8fbff;
  //   box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 20px 0px;
  //   transition: all 0.5s ease;
  //   transform: translateY(100%);
  //   z-index: 100000;
  //   iframe {
  //     width: 100%;
  //     height: 100%;
  //   }
  //   .close {
  //     position: absolute;
  //     right: -9px;
  //     top: -12px;
  //     font-size: 16px;
  //     cursor: pointer;
  //     padding: 10px;
  //     color: #fff;
  //     span {
  //       font-size: 14px;
  //     }
  //   }
  //   &.open {
  //     transform: translateY(0);
  //   }
  //   .enlarge {
  //     position: absolute;
  //     right: 12px;
  //     top: 0;
  //     font-size: 16px;
  //     cursor: pointer;
  //     padding: 10px;
  //     color: #fff;
  //     span {
  //       font-size: 14px;
  //     }
  //   }
  //   &.service {
  //     .close {
  //       color: #fff;
  //       right: 34px;
  //       top: 9px;
  //     }
  //   }
  // }

  .iframe {
    position: fixed;
    right: 60px;
    bottom: 0;
    width: 360px;
    height: 480px;
    background-color: #f8fbff;
    box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 20px 0px;
    transition: all 0.5s ease;
    transform: translateY(100%);
    z-index: 100000;
    iframe {
      width: 100%;
      height: 100%;
    }
    .close,
    .enlarge {
      position: absolute;
      right: 12px;
      top: 0;
      font-size: 16px;
      cursor: pointer;
      padding: 10px;
      color: #fff;
      span {
        font-size: 12px;
        font-weight: 800;
      }
      .iconfont {
        font-size: 12px;
      }
    }
    &.open,
    &.enlarge {
      transform: translateY(0);
    }
    &.service {
      .close,
      .enlarge {
        color: #fff;
        right: 125px;
        top: 9px;
      }
      .enlarge {
        right: 76px;
        i {
          vertical-align: text-bottom;
          margin: 0 0 0 2px;
          font-weight: bold;
        }
      }
    }
  }
  .is-enlarge {
    width: 500px;
    height: 572px;
  }
  .mobile {
    width: 50px;
    display: flex;
    cursor: pointer;
    position: relative;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    font-size: 12px;
    // color: #fff;
    color: #fff;
    height: 50px;
    border-radius: 2px 2px 2px 2px;
    margin-top: 10px;
    position: fixed;
    right: 10px;
    bottom: 11%;
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1), 0 6px 10px rgba(0, 0, 0, 0.06),
      0 1px 16px rgba(0, 0, 0, 0.06);
    text-align: center;
    // background: #0c85e9;
    background: #0c85e9;
    border-radius: 50%;
    .text {
      margin-top: 5px;
    }
    .item {
      .iconfont {
        font-size: 26px;
      }
    }
  }
}
.hubble-float-help {
  position: fixed;
  bottom: 20%;
  right: 20px;
  text-align: center;
  z-index: 999;
  background: #fff;
  border-radius: 30px;
  box-shadow: 0 0px 30px 0 rgba(0, 0, 0, 0.1);
  .item {
    &:hover {
      span {
        color: #0c85e9;
      }
      .iconfont {
        color: #0c85e9;
      }
    }
    .iconfont {
      color: #767676;
      &:hover {
        color: #0c85e9;
      }
    }
    // padding: 16px 0;
    display: flex;
    width: 60px;
    cursor: pointer;
    position: relative;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    // border-top: 5px solid #fff;
    // border: 2.5px solid #fff;
    font-size: 12px;
    color: #fff;
    height: 60px;
    // background-color: #0C85E9;
    border-radius: 2px 2px 2px 2px;
    span {
      margin-top: 7px;
      font-size: 12px;
      font-weight: 500;
      color: #767676;
      &:hover {
        color: #0c85e9;
      }
      a {
        color: #767676;
      }
    }
    a {
      margin-top: 7px;
      a {
        color: #fff;
      }
    }
  }
  #notShowUdesk {
    // padding: 16px 0;
    display: flex !important;
    width: 60px;
    cursor: pointer;
    position: relative;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    // border-top: 5px solid #fff;
    // border: 2.5px solid #fff;
    font-size: 12px;
    color: #fff;
    height: 60px;
    // background-color: red;
    border-radius: 2px 2px 2px 2px;
    margin-top: 10px;
    &:hover {
      .iconfont {
        color: #0c85e9;
      }
      span {
        color: #0c85e9;
      }
    }
    span {
      margin-top: 7px;
      font-size: 12px;
      color: #767676;
      a {
        color: #fff;
      }
    }
    a {
      margin-top: 7px;
      font-size: 14px;
      font-weight: 500;
      a {
        color: #fff;
      }
    }
  }
  .el-icon-arrow-up {
    font-size: 22px;
    color: #fff;
  }
  .icon {
    font-size: 20px;
  }
  // .space {
  //   background-color: transparent;
  //   height: 10px;
  //   width: 100%;
  // }
}
.sapce {
  // border-top: 2.5px solid #fff;
  // border-bottom: 2.5px solid #fff;
  background-color: #fff;
  height: 5px !important;
  width: 100%;
}
@media screen and (max-width: 767px) {
  .help-wrapper {
    .iframe {
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
