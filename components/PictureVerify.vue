<!-- 图形验证码 -->
<template>
	<div class="picture-verify" @click='changeImg'>
		<img :src="imgSrc">
	</div>
</template>
<script>
import axios from 'axios';
export default {
  name: 'PictureVerify',
  props: ['imageKey'],
  data() {
    return {
      imgSrc: '',
    };
  },
  methods: {
    getImageVerCode() {
      // let host = 'https://ent.bestsign.info';
      // if (process.env.baseUrl.indexOf('cn') > -1) {
      //   host = 'https://ent.bestsign.cn';
      // }
      let host = '';
      if (process.env.NODE_ENV !== 'development') {
        host =
          process.env.baseUrl.indexOf('cn') > -1
            ? 'https://ent.bestsign.cn'
            : 'https://ent.bestsign.info';
      }
      this.$axios
        .get(`${host}/users/ignore/captcha/base64-image`, {
          params: { imageKey: this.imageKey },
        })
        .then(res => {
          this.$emit('change-imageKey', res.imageKey);
          this.imgSrc = `data:image/jpeg;base64,${res.image}`;
        });
    },
    changeImg() {
      this.getImageVerCode(this.imageKey);
    },
  },
  created() {
    this.getImageVerCode(this.imageKey);
  },
};
</script>
<style lang="scss" scoped>
.picture-verify {
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
  img {
    width: 100%;
    height: 100%;
  }
}
</style>
