<!-- 组件说明 -->
<template>
  <div class='hubbleFooter'>
    <div class="copyRight" v-if="!isMobile">
      <div class="text">
        If you have any other concerns, please email us <a href="mailto:<EMAIL>" target="_blank" class="mail"><EMAIL></a>
      </div>
      <div class="text" >Copyright © 2025 Hubble Inc. All rights reserved.  
        <a href="/privacyPolicy.html" target="_blank">Policy</a> |
        <a href="/terms.html" target="_blank">Terms</a>
        
        </div>
    </div>
    <div class="copyRight" v-if="isMobile">
      <div class="text">
        If you have any other concerns, please email us <a href="mailto:<EMAIL>" target="_blank" class="mail"><EMAIL></a>
      </div>
      <div class="text">
        <div class="num">Copyright © 2025 Hubble Inc. All rights reserved. </div>
        <!-- <div class="num">浙公网安备 33010602006436</div> -->
        <div class="num">
          <a href="/privacyPolicy.html" target="_blank">Policy</a> |
          <a href="/terms.html" target="_blank">Terms</a>
        </div>
      </div>
    </div>
    <!-- <div class="copyContent" v-if="isMobile">
      电脑端输入<span class="content">https://www.bestsign.cn/hubble</span>了解更多详情
    </div> -->
  </div>
</template>

<script>
//import x from ''
import { BeiAn, handleICPEnv } from '@/assets/utils/consts.js';
export default {
  components: {},
  data() {
    return {
      BeiAn: BeiAn,
      handleICPEnv: handleICPEnv,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  watch: {},
  beforeUpdate() {},
  created() {},
  methods: {},
  mounted() {},
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>

<style lang='scss' scoped>
//@import url()
.hubbleFooter {
  position: relative;
  .copyRight {
    // background: #313134;
    // background: linear-gradient(#0c0b0f 50%, #313134 50%);
    // background: linear-gradient(to right, #0c0b0f, #313134) no-repeat -1px;
    padding: 0;
    text-align: center;
    position: absolute;
    left: 50%;
    bottom: 20px;
    transform: translateX(-50%);
    .text {
      color: #fff;
      // font-size: 0.8rem;
      margin: 20px 0;
      line-height: 1.5;
      a {
        color: #fff;
        margin: 0 10px;
        &:hover {
          color: #888;
        }
      }
      .mail {
        border-bottom: 1px solid;
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .hubbleFooter {
    .copyRight {
      width: 100%;
      // margin-bottom: 89px;
      bottom: 0px;
      padding: 10px;
      .text {
        margin: 10px 0;
        .num {
          padding: 3px 0;
          text-align: center;
        }
      }
    }
    .copyContent {
      font-size: 1.2rem;
      position: fixed;
      bottom: 0;
      padding: 20px 2%;
      text-align: left;
      background: #fff;
      width: 100%;
      line-height: 1.6;
      .content {
        color: #0c85e9;
        padding: 2px 5px;
        border: 1px solid #0c85e9;
        border-radius: 20px;
        margin: 0 5px;
      }
    }
  }
}
</style>
