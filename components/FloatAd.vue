<!-- 右侧悬浮广告为-->
<template>
  <div class="float-ad" v-if="!isMobile&&online">
    <div class="img-con" @click="toAd">
      <!-- <img src="https://static.bestsign.cn:443/55111247e44710f5ef26ed8ffbd6b9ef5a9af923.gif" alt="上上签电子签约"> -->
      <!-- <img :src="rightAd.pcImgUrl" :alt="rightAd.alt"> -->
    </div>
  </div>
</template>

<script>
//import x from ''
import Qs from 'qs';
export default {
  components: {},
  data() {
    return {
      online: false,
      rightAd: {
        pcImgUrl: '',
        bannerLink: '',
        alt: '',
      },
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {
    toAd() {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: this.rightAd.bannerLink,
        query: {
          ...Qs.parse(query),
        },
      });
    },
    // getAd() {
    //   this.$axios({
    //     url: '/www/api/web/right-ad',
    //     method: 'get',
    //   }).then(res => {
    //     this.rightAd = res;
    //     this.online = res.pcImgUrl === null ? false : true;
    //   });
    // },
  },
  mounted() {},
  created() {
    // this.getAd();
  },
};
</script>

<style lang='scss' scoped>
//@import url()
.float-ad {
  position: fixed;
  bottom: calc(20% + 270px);
  right: 20px;
  text-align: center;
  z-index: 999;
  .img-con {
    width: 60px;
    &:hover {
      cursor: pointer;
    }
    img {
      width: 86%;
    }
  }
}
</style>
