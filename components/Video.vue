<template>
  <div class="video-wrapper">
    <el-dialog
      :width="width"
      :visible.sync="dialogVisible"
      @open="handleVideoOpen"
      @close="handleVideoClose"
    >
      <video
        :src="videoUrl"
        id="video"
        autoplay
        controls
      ></video>
      <!--  :id="videoId"-->
    </el-dialog>
  </div>
</template>

<script>
import icon from '@/assets/images/icon/<EMAIL>';
import icon_w from '@/assets/images/icon/icon@play_w.png';
export default {
  name: 'Video',
  props: {
    videoUrl: {
      type: String,
      required: true,
      default: '',
    },
    visible: {
      type: Boolean,
      default: false,
    },
    // videoId: {
    //   type: String,
    //   default: 'video',
    // },
  },
  data() {
    return {
      dialogVisible: this.visible,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    width() {
      return this.isMobile ? '100%' : '50%';
    },
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
  },
  methods: {
    handleVideoOpen() {
      // const id = `#${this.videoId}`;
      // const video = document.querySelector(id);
      const video = document.querySelector('#video');
      video && video.play();
      // console.log(video);
    },
    handleVideoClose() {
      //   const id = `#${this.videoId}`;
      //   const video = document.querySelector(id);
      const video = document.querySelector('#video');
      video && video.pause();
      this.$emit('close');
    },
  },
};
</script>

<style lang="scss">
.video-wrapper {
  video {
    width: 100%;
    vertical-align: middle;
  }
  .el-dialog__header,
  .el-dialog__body {
    padding: 0;
  }
  .el-dialog__headerbtn {
    color: #fff;
    z-index: 28;
  }
  .el-dialog__close {
    background-color: #fff;
    border-radius: 50%;
  }
}
@media (max-width: 768px) {
  .el-dialog {
    width: 100%;
  }
}
</style>
