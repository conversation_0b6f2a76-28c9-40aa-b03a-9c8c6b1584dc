<!-- 组件说明 -->
<template>
  <div class='typedText' :class="{'typedTextNew': isStar}">
    {{typedText}}
  </div>
</template>

<script>
//import x from ''
export default {
  name: 'typed',
  props: {
    typedList: {
      type: Array,
      default: () => [],
    },
    loop: {
      type: Boolean,
      default: true,
    },
    speed: {
      type: Number,
      default: 100,
    },
  },
  components: {},
  data() {
    return {
      typedText: '',
      index: 0,
      isStar: false,
    };
  },
  computed: {},
  methods: {
    start() {
      let j = 0;
      if (this.typedText.length < 0) {
        console.log('没有文字');
        return;
      }
      let arr = this.typedList[this.index].split('');
      // let timer = setInterval(() => {
      //   if (j < arr.length) {
      //     this.typedText += arr[j++];
      //   } else {
      //     clearInterval(timer);
      //     this.isStar = true;
      //     if (this.loop) {
      //       setTimeout(() => {
      //         this.isStar = false;
      //         this.typedText = '';
      //         if (this.index === 3) {
      //           this.index = 0;
      //         } else {
      //           this.index = this.index + 1;
      //         }
      //         this.start();
      //       }, 3000);
      //     }
      //   }
      // }, this.speed);
      let timer = setInterval(() => {
        if (j < arr.length) {
          this.typedText += arr[j++];
        } else {
          clearInterval(timer);
          this.isStar = true;
        }
      }, this.speed);
    },
  },
  mounted() {
    this.start();
  },
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>

<style lang='scss' scoped>
.typedText {
  position: relative;
  font-size: 3rem;
  font-weight: 500;
  color: #000;
  line-height: 1.6;
}
.typedText:after {
  position: absolute;
  content: '|';
  color: #000;
  font-size: 3rem;
}
.typedTextNew:after {
  position: absolute;
  content: '|';
  animation: 0.9s steps(1, start) 0s normal none infinite running blink;
  color: #000;
}
@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
@media only screen and (max-width: 767px) {
  .typedText {
    position: relative;
    font-size: 2rem;
    // height: 2.5rem;
    font-weight: 500;
    color: #000;
    line-height: 1.6;
  }
  .typedText:after {
    position: absolute;
    content: '|';
    color: #000;
    font-size: 2rem;
    // top: -0.2rem;
  }
  .typedTextNew:after {
    position: absolute;
    content: '|';
    font-size: 2rem;
    animation: 0.9s steps(1, start) 0s normal none infinite running blink;
    color: #000;
    // top: -0.2rem;
  }
}
</style>
