<template>
  <div class="index-contractType section">
    <div class="container">
      <h2 class="section-headline headline--main">{{$t('solutionDescriptiopn.typeTitle')}}</h2>
      <div class="content" >
        <div class="swiper-container">
          <div class="swiper-wrapper">
              <div class="swiper-slide" v-for="(item, index) in contractType" :key="item.label">
				<img :src="item.imgSrc" alt="">
				<div :class="['slide-box',activeIndex === index?'slide-active':'']">
          <a href="javascript:void(0);" :class="{'active': activeIndex === index}">{{ item.label }}</a>
				</div>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  name: 'contract-type',
  props: {
    contractType: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      activeName: 'HR',
      activeIndex: 2,
      swiperOption: {
        initialSlide: 2,
        centeredSlides: true,
        speed: 700,
        slidesPerView: 5,
        navigation: {
          nextEl: '.ssq-button-next',
          prevEl: '.ssq-button-prev',
        },
      },
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    gutter() {
      return this.isMobile ? 10 : 20;
    },
  },
  methods: {},
  mounted() {},
};
</script>

<style scoped lang="scss">
.index-contractType {
  text-align: center;
  //   padding-top: 5rem;
  // padding-bottom: 0rem;
  .little-headline {
    padding: 0 20px 20px;
    line-height: 1.5;
  }
  .content {
    position: relative;
    // margin-top: 3rem;
    // margin-bottom: 3rem;
    min-height: 19rem;
    .icon {
      font-size: 46px;
    }
    .swiper-container {
      overflow: initial;
      max-width: 1113px;
      .swiper-wrapper {
        // background-image: url('~assets/images/index/solutionbg.jpg');
        // background-size: 100% 100%;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        // img {
        //   opacity: 0.8;
        //   filter: alpha(opacity=60);
        // }
        i {
          width: 19%;
          margin: 6px 5px;
        }
      }
    }
    .swiper-slide {
      height: 11rem;
      background-color: rgba(0, 0, 0, 0.05);
      background-size: 100% 100%;
      cursor: pointer;
      width: 19%;
      margin: 6px 5px;
      border-radius: 5px;
      text-align: center;
      overflow: hidden;
      a {
        position: absolute;
        left: 50%;
        top: 50%;
        color: #fff;
        font-size: 20px;
        width: 100%;
        transform: translate(-50%, -50%);
        text-align: center;
        &.active {
          color: #fff;
        }
      }
      &:hover img {
        transform: scale(1.3);
      }
      img {
        height: 100%;
        position: absolute;
        right: 0;
        transform: scale(1);
        transition: all 0.8s ease 0s;
      }
      & img:hover {
        transform: scale(1.3);
        transition: all 0.8s ease 0s;
      }
    }
    .swiper-slide:hover::before {
      background-color: rgba(110, 110, 110, 0.2);
      transition: all 0.4s;
    }
    .swiper-slide:hover {
      background-color: rgba(0, 0, 0, 0);
      transition: all 0.4s cubic-bezier(0, 0, 0, 0.1);
    }
    .swiper-slide:nth-child(10):hover {
      background-color: rgba(0, 0, 0, 0.05);
      transition: all 0.4s cubic-bezier(0, 0, 0, 0.05);
    }
  }
  .feature {
    position: relative;
    padding: 15px 18px;
    text-align: left;
    height: 230px;
    margin-bottom: 30px;
    box-shadow: 0 0px 20px 0 rgba(27, 33, 31, 0.2);
    .title {
      font-size: 18px;
      margin-bottom: 30px;
    }
    .desc {
      font-size: 14px;
      line-height: 1.8;
      color: #888;
    }
    i {
      font-size: 46px;
      position: absolute;
      right: 22px;
      bottom: 20px;
    }
  }
  .ssq-button-prev,
  .ssq-button-next {
    position: absolute;
    top: 50%;
    transform: translate3d(0, -50%, 0);
    background: transparent;
    cursor: pointer;
    > i {
      font-size: 36px;
      color: rgba(35, 24, 21, 0.5);
      outline: none;
      &:hover {
        color: rgba(35, 24, 21, 1);
      }
      &:focus {
        outline: none;
      }
    }
    &:focus {
      outline: none;
    }
  }
  .ssq-button-prev {
    left: -22px;
  }
  .ssq-button-next {
    right: -22px;
  }
  .ssq-button-primary {
    margin-top: 3rem;
    display: inline-block;
  }
}
@media screen and (max-width: 767px) {
  .index-contractType {
    background-color: #f2f2f2;
    padding-bottom: 5rem;
    .container {
      padding: 0;
    }
    .content {
      position: relative;
      padding: 0px;
      .swiper-container {
        .swiper-wrapper {
          justify-content: space-around;
          .icon {
            font-size: 28px;
          }
          .swiper-slide {
            cursor: pointer;
            width: 32%;
            margin: 5px 0;
            height: 9rem;
            a {
              margin: 0 auto;
              font-size: 18px;
            }
          }
          .swiper-slide:nth-child(10) {
            display: none;
          }
        }
      }
    }
    .feature {
      position: relative;
      padding: 15px 6px;
      text-align: left;
      height: 260px;
      .title {
        font-size: 14px;
        margin-bottom: 16px;
      }
      .desc {
        font-size: 12px;
        line-height: 1.8;
      }
    }
    .ssq-button-prev,
    .ssq-button-next {
      > i {
        font-size: 22px;
      }
    }
    .ssq-button-prev {
      left: -8px;
    }
    .ssq-button-next {
      right: -8px;
    }
    .ssq-button-primary {
      width: 130px;
      height: 44px;
      line-height: 44px;
    }
  }
}
</style>
