<template>
  <section class="solution-case">
	  <div class="container">
		<h2 class="main-title">{{$t('solutionDescriptiopn.useTitle')}}</h2>
		<el-carousel :interval="4000" :autoplay="true" type="card" height="550px" arrow="never" indicator-position="none" v-if="!isMobile" @change=getCurrent> 
			<el-carousel-item class="solution-carouse" v-for="(item, index) in solution" :key="index" :style="{backgroundImage:`url(${item.imgUrl})`}">
				<div class="wrap-icon">
					<i :class="['iconfont', item.iconfont]"></i>
				</div>
				<div class="title">{{ item.title }}</div>
				<div class="paragraph">{{activeIndex == index? item.desc :"" }}</div>
			</el-carousel-item>
		</el-carousel>
		<!-- <el-carousel :interval="4000" :autoplay="true" height="450px"  indicator-position="outside" v-if="isMobile" arrow="always"> 
			<el-carousel-item class="solution-carouse" v-for="(item, index) in solution" :key="index" :style="{backgroundImage:`url(${item.imgUrl})`}">
				<div class="wrap-icon">
					<i :class="['iconfont', item.iconfont]"></i>
				</div>
				<div class="title">{{ item.title }}</div>
				<div class="paragraph">{{item.desc}}</div>
			</el-carousel-item>
		</el-carousel> -->
		 <div class="swiper-container" v-swiper:mySwiper="swiperOptionMobile" v-if="isMobile">
			<div class="swiper-wrapper">
					<div class="swiper-slide" v-for="(item, index) in solution" :key="index" :style="{backgroundImage:`url(${item.imgUrl})`}">
						<div class="wrap-icon">
						<i :class="['iconfont', item.iconfont]"></i>
					</div>
					<div class="title">{{ item.title }}</div>
					<div class="paragraph">{{item.desc}}</div>
					</div>
			</div>
			<div class="ssq-button-prev-a" v-if="isMobile">
				<i class="el-icon-arrow-left"></i>
			</div>
			<div class="ssq-button-next-a" v-if="isMobile">
				<i class="el-icon-arrow-right"></i>
			</div>
		 </div>
		  <div class="swiper-pagination swiper-pagination__reason" slot="pagination"></div>
	</div>
  </section>
</template>

<script>
export default {
  name: 'solution-case',
  props: {
    solution: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      activeIndex: 0,
      swiperOptionMobile: {
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        navigation: {
          nextEl: '.ssq-button-next-a',
          prevEl: '.ssq-button-prev-a',
        },
        pagination: {
          el: '.swiper-pagination.swiper-pagination__reason',
        },
      },
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toDemo() {
      const { params } = this.$route;
      this.$router.push(`/demo?${params.id}`);
    },
    getCurrent(index) {
      this.activeIndex = index;
    },
  },
};
</script>

<style scoped lang="scss">
.solution-case {
  padding-bottom: 0;
  //   .section {
  //     padding: 0 0 4rem;
  //   }
  .container {
    max-width: 1100px;
    .main-title {
      padding: 3rem 0;
      color: #090909;
      font-size: 2rem;
      line-height: 2.5rem;
      font-weight: 400;
    }
    .solution-carouse {
      background-repeat: no-repeat;
      background-position: center;
      background-size: 100% 100%;
      padding: 2rem 3rem;
      text-align: center;
      box-sizing: border-box;
      color: #fff;
      .wrap-icon {
        width: 90px;
        height: 90px;
        margin: 6.25rem auto 2.5rem;
        .iconfont {
          font-size: 60px;
        }
        img {
          width: 100%;
        }
      }
      .title {
        margin: 0 auto 30px;
        font-size: 20px;
        line-height: 1.5;
        color: #fff;
        font-weight: 400;
      }
      .paragraph {
        padding: 0 60px;
        line-height: 1.5;
        color: #fff;
        text-align: center;
        font-size: 16px;
      }
      /deep/ .el-carousel__container {
        position: relative;
      }
    }
    /deep/ .el-carousel__item {
      background-size: cover;
    }
    /deep/ .el-carousel {
      border-bottom: 1px solid #eee;
      padding-bottom: 5rem;
    }
    /deep/ .el-carousel__container .is-active {
      width: 35%;
      transform: translateX(350px) scale(1) !important;
    }
    /deep/ .el-carousel__mask {
      display: none;
    }
    .el-carousel__item::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      background-color: rgba(0, 0, 0, 0.4);
    }
    .el-carousel__item.is-active::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      background-color: rgba(0, 0, 0, 0.1);
    }
  }
  .ssq-button-primary {
    text-align: center;
  }
  .swiper-container {
    height: 400px;
    .swiper-wrapper {
      position: absolute;
      z-index: 0;
    }
    .swiper-slide {
      background-size: cover;
      padding: 2rem 3rem;
      text-align: center;
      position: relative;
      .wrap-icon {
        margin: 2.25rem auto 2.5rem;
        .iconfont {
          font-size: 60px;
          color: #fff;
        }
      }
      .title {
        margin: 0 auto 30px;
        font-size: 18px;
        line-height: 1.5;
        color: #fff;
        font-weight: 400;
        text-align: center;
      }
      .paragraph {
        padding: 0 30px;
        line-height: 24px;
        color: #fff;
        text-align: center;
        font-size: 14px;
      }
      //   &::before {
      //     content: '';
      //     position: absolute;
      //     width: 100%;
      //     height: 100%;
      //     left: 0;
      //     top: 0;
      //     background-color: rgba(0, 0, 0, 0.4);
      //   }
    }
    .ssq-button-prev-a {
      position: absolute;
      font-size: 25px;
      top: 50%;
      transform: translateY(-50%);
      color: #fff;
      left: 10px;
      background: rgba(88, 85, 85, 0.3);
      border-radius: 50%;
      width: 34px;
      height: 34px;
      display: flex;
      align-items: center;
      justify-content: center;
      &:focus {
        outline: none;
      }
    }
    .ssq-button-next-a {
      position: absolute;
      font-size: 25px;
      top: 50%;
      transform: translateY(-50%);
      color: #fff;
      right: 10px;
      background: rgba(88, 85, 85, 0.3);
      border-radius: 50%;
      width: 34px;
      height: 34px;
      display: flex;
      align-items: center;
      justify-content: center;
      &:focus {
        outline: none;
      }
    }
  }
  .swiper-pagination {
    width: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 20px;
    /deep/ .swiper-pagination-bullet {
      margin: 0 10px;
    }
    /deep/ .swiper-pagination-bullet-active {
      background: #62686f;
    }
  }
  .block {
    padding: 3rem 0;
    border-top: 1px solid #eaeaea;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &:first-child {
      margin-top: 3rem;
      border-top: none;
    }
    .title {
      font-size: 22px;
      margin-bottom: 3rem;
    }
    .paragraph {
      color: #888;
      line-height: 2;
    }
    .ssq-button-primary {
      margin-top: 2rem;
    }
    img {
      width: 100%;
      max-width: 578px;
    }
    .left {
      margin-right: 4rem;
    }
    .text-wrap {
      max-width: 450px;
    }
  }
}
@media (max-width: 767px) {
  .solution-case {
    padding: 0 1rem;
    .container {
      padding: 0 2%;
      /deep/.el-carousel {
        border-bottom: 1px solid #eee;
        padding-bottom: 3rem;
      }
      /deep/ .el-carousel__container .is-active {
        width: 100%;
        transform: none !important;
      }
      /deep/ .el-carousel__mask {
        display: none;
      }

      .solution-carouse {
        .title {
          font-size: 18px;
        }
        .paragraph {
          padding: 0 5px;
          font-size: 14px;
          color: #fff;
          line-height: 24px;
        }
        .wrap-icon {
          margin: 2.25rem auto 2.5rem;
        }
      }
    }

    .block {
      display: block;
      padding-top: 0;
      padding-bottom: 2.5rem;
      margin-top: 2.5rem;
      border: none;
      .title {
        font-size: 18px;
        margin-bottom: 2rem;
      }
      .left,
      .right {
        margin-right: 0;
        text-align: center;
      }
      .left {
        margin-top: 2rem;
      }
      .right {
        margin-top: 2rem;
        img {
          width: 80%;
        }
      }
      .ssq-button-primary {
        margin-left: auto;
        margin-right: auto;
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .solution-case {
    /deep/ .el-carousel__arrow {
      i {
        font-size: 20px;
      }
    }
  }
}
@media screen and (min-width: 767px) and (max-width: 1024px) {
  .solution-case {
    .block {
      padding-left: 32px;
      padding-right: 32px;
      .title {
        font-size: 20px;
        margin-bottom: 2rem;
      }
      .ssq-button-primary {
        margin-top: 1rem;
      }
    }
  }
}
</style>
