<template>
  <div class="solution-content section" v-if="content.title">
    <div class="container-wrapper">
      <div class="headline" v-if="content.title">{{ content.title }}</div>
      <div class="subtitle">{{ content.introduction }}</div>
      <div v-if="content.problems" class="problems">
        <template v-for="(problem, index) in content.problems">
          <div class="problem" :key="`${content.title}-problems${index}`">
            <div class="media-box">
              <svg class="icon" aria-hidden="true">
                <use :xlink:href="`#${problem.icon}`"></use>
              </svg>
            </div>
            <p class="text" v-html="problem.text"></p>
          </div>
        </template>
      </div>
      <div v-if="content.features" class="features">
        <template v-for="(feature, index) in content.features">
          <div class="feature" :key="`${content.title}-feature${index}`" :style="{width: `${100 / (content.column ? content.column : 4)}%`}" :class="{overflow: content.features.length > 4}">
            <div class="left">
              <img :src="feature.icon" alt="">
            </div>
            <div class="right">{{ feature.text }}</div>
          </div>
        </template>
      </div>
      <div class="icon-wrapper">
        <template v-for="(icon, index) in content.icons">
          <div class="icon-item" :key="`${icon.title}-${index}`">
            <div class="media-box">
              <img class="image" :src="icon.icon" alt="">
            </div>
            <div class="order">
              <span>{{ index + 1 }}</span>
            </div>
            <p class="text" v-html="icon.text"></p>
          </div>
        </template>
      </div>
      <button
        class="ssq-button-primary"
        @click="toDemo()">
        免费试用
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'solution-content',
  props: {
    content: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    maxWidth() {
      return `${100 / this.content.column}%`;
    },
  },
  methods: {
    toDemo() {
      const { params } = this.$route;
      this.$router.push(`/demo?${params.id}`);
    },
  },
};
</script>

<style scoped lang="scss">
@import './index';
.features {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 4rem;
  .feature {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    .left {
      width: 35%;
      text-align: right;
      img {
        width: 60px;
      }
    }
    .right {
      flex: 1;
      margin-left: 15px;
      font-size: 16px;
      text-align: left;
      color: #888;
    }
    &.overflow {
      flex: initial;
      margin-bottom: 2rem;
    }
  }
}
.icon-wrapper,
.problems {
  display: flex;
  width: 100%;
  .icon-item,
  .problem {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    .media-box {
      width: 100%;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .icon {
      font-size: 56px;
    }
    .order {
      margin: 1rem auto 2rem;
      width: 100%;
      color: #00aa64;
      border-bottom: 1px solid #eee;
      span {
        position: relative;
        top: 50%;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        border-radius: 50%;
        font-size: 16px;
        display: inline-block;
        border: 1px solid #00aa64;
        background-color: #fff;
      }
    }
    .text {
      line-height: 24px;
      margin-top: 2.5rem;
      font-size: 16px;
      color: #888;
      max-width: 60%;
    }
  }
}
@media only screen and (max-width: 767px) {
  .problems {
    .problem {
      width: 100%;
      .icon {
        font-size: 36px;
      }
      .text {
        margin-top: 1.5rem;
        font-size: 12px;
        max-width: 100%;
      }
    }
  }
  .features {
    margin-bottom: 1rem;
    .feature {
      width: 25%;
      flex: initial;
      display: block;
      .left {
        width: 100%;
        text-align: center;
        img {
          width: 40px;
        }
      }
      .right {
        margin-left: 0;
        margin-top: 12px;
        margin-bottom: 24px;
        font-size: 12px;
        text-align: center;
      }
    }
  }
  .icon-wrapper {
    display: block;
    .icon-item {
      flex-direction: row;
      width: 100%;
      padding: 12px 0;
      .media-box {
        order: 1;
        margin-right: 16px;
        width: 50px;
        .image {
          transform: scale(0.8);
        }
      }
      .order {
        width: 20%;
        order: 0;
        display: inline-block;
        margin: 0 16px 0;
        border-bottom: none;
        span {
          font-size: 14px;
        }
      }
      .text {
        flex: 1;
        order: 2;
        margin-top: 0;
        font-size: 12px;
        text-align: left;
        line-height: 20px;
      }
    }
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .icon-wrapper,
  .problems {
    .icon-item,
    .problem {
      .image {
        transform: scale(0.9);
      }
      .icon {
        font-size: 46px;
      }
      .text {
        margin-top: 2rem;
        font-size: 16px;
      }
    }
  }
  .features {
    .feature {
      .left img {
        width: 30px;
      }
      .right {
        margin-left: 8px;
        font-size: 14px;
        text-align: left;
      }
    }
  }
}
</style>
