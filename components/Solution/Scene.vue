<template>
  <section class="scenes-section">
    <h2 class="main-title">使用场景</h2>
    <div class="container"  :style="{backgroundImage:`url(${isMobile?'':bgImg})`}">
      <template v-for="(item, index) in scene">
        <div :class="['scene', index==0&&defaultActive?'scene-active':'']" :key="index" @mouseenter="enter(index)" @mouseleave="leave(index)">
          <p class="scene-icon">
            <!-- <img :src="item.icon" alt=""> -->
			<i :class="['iconfont',item.iconfont]"></i>
          </p>
          <div class="scene-content">
            <p class="scene-title">{{ item.title }}</p>
			<div class="scene-text">
				 <p class="paragraph" v-for="(item,index) in  formatStr(item.desc)" :key="index">
					{{item}}
				</p>
			</div>
           
          </div>
        </div>
      </template>
    </div>
  </section>
</template>

<script>
export default {
  name: 'Scene',
  props: {
    scene: {
      type: Array,
      default: () => [],
    },
    bgImg: {
      type: String,
      default: '',
    },
  },
  computed: {
    isMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  data() {
    return {
      defaultActive: true,
    };
  },
  methods: {
    formatStr(str) {
      return str.split(/,|、/);
    },
    enter(index) {
      if (this.isMobile) {
        return;
      }
      if (!index) {
        this.defaultActive = true;
      } else {
        this.defaultActive = false;
      }
    },
    leave(index) {
      if (this.isMobile) {
        return;
      }
      if (!index) {
        this.defaultActive = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.scenes-section {
  background-color: #fafbfc;
  .container {
    display: flex;
    flex-wrap: wrap;
    margin-top: 3.5rem;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
  }
  .scene {
    width: 25%;
    padding: 2rem;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    height: 400px;
    flex: 1;
    text-align: center;
    position: relative;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    .main-title {
      padding: 3rem 0;
    }
    .scene-icon {
      //   visibility: hidden;
      //   display: none;
      opacity: 0;
      margin-top: 90px;
      position: relative;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      .iconfont {
        font-size: 40px;
        color: #fff;
      }
    }
    .scene-content {
      color: #fff;
      font-size: 20px;
      .scene-title {
        width: 100%;
        font-size: 20px;
        line-height: 1.25;
        margin-top: 180px;
        position: relative;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      }
      .scene-text {
        margin-top: 25px;
        display: none;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      }
      .paragraph {
        font-size: 16px;
        line-height: 1.5;
        position: relative;
        opacity: 0;
        margin-top: 10px;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
    &:hover .paragraph {
      opacity: 1;
      margin-top: 0;
    }
    &:hover .scene-title {
      margin-top: 50px;
    }
    &:hover .scene-icon {
      //   visibility: visible;
      //   display: block;
      opacity: 1;
      margin-top: 80px;
      transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }
    &:hover .scene-text {
      display: block;
      transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    }
    &:before {
      content: '';
      position: absolute;
      width: 100%;
      height: 20%;
      top: 80%;
      left: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.65);
      transition: all 0.6s;
    }
    &:hover::before {
      background-color: rgba(0, 0, 0, 0.65);
      height: 100%;
      left: 0;
      top: 0;
      bottom: 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    // &:hover {
    //   background-color: rgba(0, 0, 0, 0.6);
    //   transition: all 14s cubic-bezier(0.4, 0, 0.2, 1);
    // }

    i {
      width: 15%;
      text-align: center;
    }
  }
  .scene-active {
    .scene-icon {
      //   visibility: visible;
      //   display: block;
      opacity: 1;
      margin-top: 80px;
    }
    .scene-content {
      .paragraph {
        opacity: 1;
        margin-top: 0;
        font-size: 16px;
        margin: 0 auto;
        max-width: 75%;
        line-height: 1.5;
        color: #fff;
        text-align: center;
      }
      .scene-title {
        margin-top: 50px;
        text-align: center;
        font-size: 20px;
        line-height: 1.5;
        color: #fff;
        font-weight: 400;
      }

      .scene-text {
        display: block;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
    &::before {
      background-color: rgba(0, 0, 0, 0.7);
      height: 100%;
      left: 0;
      top: 0;
      bottom: 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
}
@media (max-width: 767px) {
  .scenes-section {
    .container {
      background-image: none;
      padding: 0 1rem;
    }
    .scene {
      width: 100%;
      border-bottom: 1px solid #eee;
      padding-left: 0;
      padding-right: 0;
      flex: auto;
      margin: 10px 0;
      height: auto;
      background-repeat: no-repeat;
      background-size: cover;
      //   background-color: #fff;

      //   box-shadow: 0 10px 16px 0 rgba(101, 105, 127, 0.1);
      &:last-child {
        border-bottom: none;
      }
      .scene-icon {
        // visibility: visible;
        display: block;
        margin-top: 0px;
        position: relative;
        opacity: 1;
        transition: margin-top 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        .iconfont {
          font-size: 40px;
          color: #00aa64;
        }
      }
      .scene-content {
        .scene-title {
          width: 100%;
          font-size: 18px;
          line-height: 1.25;
          margin-top: 50px;
          position: relative;
          color: #1d1d1f;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .scene-text {
          margin: 25px 0;
          display: block;
        }
        .paragraph {
          font-size: 14px;
          line-height: 1.5;
          position: relative;
          opacity: 1;
          margin-top: 10px;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          color: #86868b;
        }
      }
      &:hover .paragraph {
        opacity: 1;
        margin-top: 10px;
      }
      &:hover .scene-title {
        margin-top: 50px;
      }
      &:hover .scene-icon {
        visibility: visible;
        display: block;
        margin-top: 0px;
      }
      &:hover .scene-text {
        margin: 25px 0;
        display: block;
      }
      &:before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        bottom: 0;
        background-color: #fafafa;
        transition: all 0.4s;
      }
      &:hover::before {
        background-color: rgba(0, 0, 0, 0);
        height: 100%;
        left: 0;
        top: 0;
        bottom: 0;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }
}
</style>
