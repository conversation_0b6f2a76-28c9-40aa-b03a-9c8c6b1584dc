<template>
	<section class="banner" :style="{backgroundImage: `url(${isLandMobile ? banner.mobileImgUrl : banner.pcImgUrl}`}">
	<!-- <section class="banner"> -->
		<div class="container">
			<h2 class="main-title">{{banner.title}}{{$t('header.solution')}}</h2>
			<p class="text-gray paragraph">{{ banner.desc }}</p>
			<div class="ssq-button-primary" @click="toDemo">{{$t('header.use')}}</div>
			<!-- <div class="white-border" @click="dialogVisible = true">观看视频
				<i class="iconfont icon-bofang"></i>
			</div> -->
		</div>
		<v-video
		:visible="dialogVisible"
		:videoUrl="banner.videoUrl"
		@close="dialogVisible = false"></v-video>
	</section>
</template>

<script>
import Qs from 'qs';
import Video from '@/components/Video.vue';
import { jumpToEntRegister } from '@/assets/utils/toRegister';
export default {
  name: 'Banner',
  components: {
    'v-video': Video,
  },
  props: {
    banner: {
      type: Object,
      // default: () => {},
      default() {
        return {
          mobileImgUrl:
            'https://static.bestsign.cn/2f79fd63cf5eab505162e34457126fb0605e59ea.jpg',
          pcImgUrl:
            'https://static.bestsign.cn/581cab6b7ff7fd81d4104fd59a674f2e2ef5df16.jpg',
        };
      },
    },
  },
  data: () => ({
    dialogVisible: false,
  }),
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toDemo() {
      jumpToEntRegister();
    },
    scroll(e) {
      const { id } = e.target.dataset;
      this.$scrollTo(`#${id}`);
    },
  },
};
</script>

<style scoped lang="scss">
.banner {
  width: 100%;
  height: 35vw;
  //   background: url('~assets/images/solution/financebg.jpg') no-repeat;
  background-size: cover;
  background-position: center;
  color: #fff;
  display: flex;
  align-items: center;
  .container {
    padding: 0 2rem;
    text-align: center;
    .main-title {
      font-size: 2.875rem;
      font-weight: 500;
      margin-bottom: 20px;
      color: #fff;
    }
    .paragraph {
      width: 60%;
      margin: 2rem auto;
      line-height: 1.7;
      font-size: 1.2rem;
      font-weight: 400;
    }
    .ssq-button-primary {
      margin: 20px auto 0;
      background: #00aa64;
      width: 120px;
      height: 40px;
      line-height: 40px;
      display: block;
      padding: 0 15px;
      font-size: 15px;
    }
    .white-border {
      margin-top: 2rem;
      cursor: pointer;
      font-size: 1.2rem;
      font-weight: 400;
      // .el-icon-video-play {
      //   font-size: 1.3rem;
      //   vertical-align: middle;
      // }
      // .iconfont {
      //   font-size: 18px;
      //   margin-left: 3px;
      // }
    }
  }
}
@media (max-width: 767px) {
  .banner {
    height: 100vw;
    .container {
      .main-title {
        line-height: 1.4;
        text-align: center;
        font-size: 28px;
        font-weight: 500;
      }
      .paragraph {
        width: 90%;
        font-size: 16px;
      }
      .ssq-button-primary {
        font-size: 16px;
        width: 130px;
        height: 40px;
        line-height: 40px;
      }
    }
  }
}
</style>
