<template>
<div>
  <section class="section section-1">
    <div class="container">
      <div class="content flex bc">
       <div class="description">
		   <div class="main-content">
			<h1 class="main-title">{{$t('solutionDescriptiopn.title')}}</h1>
			<div class="description-desc">
				<p>{{dsc}}</p>
			</div>
    	</div>
	   </div>
      </div>
    </div>
  </section>

</div>
  
  
</template>

<script>
export default {
  name: 'description',
  props: {
    dsc: {
      type: String,
      default: '',
    },
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
};
</script>

<style scoped lang="scss">
.section-1 {
  .description {
    .main-title {
      padding: 2rem 0 2rem 0;
      text-align: center;
      font-size: 2rem;
      line-height: 2.5rem;
      font-weight: 400;
      color: #090909;
    }

    .description-desc {
      width: 70%;
      line-height: 1.7;
      text-align: center;
      font-size: 16px;
      margin: 0 auto;
    }
  }
}
@media only screen and (max-width: 767px) {
  .section-1 {
    padding-top: 0;
    .description {
      .main-title {
        color: #090909;
        font-size: 2rem;
        line-height: 2.5rem;
        font-weight: 400;
        padding: 4.375rem 0 2.5rem;
        margin: 0;
      }

      .description-desc {
        width: 100%;
        line-height: 1.7;
        text-align: center;
        font-size: 14px;
        margin: 0 auto;
        p {
          font-size: 14px;
          color: #86868b;
        }
      }
    }
  }
}
</style>
