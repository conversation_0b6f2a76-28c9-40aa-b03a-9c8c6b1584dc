<template>
  <section class="pains">
    <h2 class="main-title">{{title}}</h2>
    <div class="container">
      <template v-for="(item, index) in pain">
        <div class="pain" :key="index" :style="{width:isMobile? '100%': 1/(pain.length)*100+'%'}">
          <i :class="['iconfont ',item.iconfont]"></i>
          <div class="title">{{ item.title }}</div>
          <div class="paragraph">{{ item.desc }}</div>
        </div>
      </template>
    </div>
  </section>
</template>

<script>
export default {
  name: 'Pain',
  props: {
    pain: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: '',
    },
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
};
</script>

<style scoped lang="scss">
.pains {
  //   border-bottom: 1px solid #d3d3d3;
  .container {
    display: flex;
  }
  .pain {
    flex: 1;
    margin-top: 2.5rem;
    // margin-left: 1rem;
    // margin-right: 1rem;
    padding: 2rem 0;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    .main-title {
      padding: 3rem 0;
      color: #090909;
      font-size: 2rem;
      line-height: 2.5rem;
      font-weight: 400;
    }
    &:hover {
      transform: scale(1.05);
      //   box-shadow: 0 0 5px #06df96;
    }
    img {
      display: inline-block;
      width: 80px;
      height: 90px;
      border-radius: 50%;
    }
    .title {
      margin: 1.5rem auto;
      max-width: 80%;
      font-size: 20px;
      line-height: 1.5;
      color: #1d1d1f;
      font-weight: 400;
    }
    .paragraph {
      font-size: 16px;
      margin: 0 auto;
      max-width: 75%;
      line-height: 1.5;
      color: #86868b;
      text-align: center;
    }
    .iconfont {
      font-size: 4rem;
    }
  }
  .pain:nth-child(2) {
    border-right: 1px solid #eee;
    border-left: 1px solid #eee;
  }
  .pain:nth-child(4) {
    border-left: 1px solid #eee;
  }
}
@media (max-width: 767px) {
  .pains {
    border-bottom: none;
    .container {
      display: flex;
      flex-wrap: wrap;
      .pain {
        border: none;
        padding: 2rem 0;
        box-shadow: 0 10px 16px 0 rgba(101, 105, 127, 0.1);
        .title {
          font-size: 18px;
        }
        .paragraph {
          font-size: 14px;
          color: #86868b;
        }
      }
    }
  }
}
</style>
