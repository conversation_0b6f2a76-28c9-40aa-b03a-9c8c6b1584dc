<template>
  <section class="solution-client section">
    <div class="container">
      <div class="main-title">客户案例</div>
      <div class="image-wrapper">
		  <!-- <div class="img-bg"> -->
        <img :src="url" alt="">

		  <!-- </div> -->
      </div>
	<div class="ssq-button-more transparent" @click="toDemo">更多案例 <i class="iconfont icon-xiangyoujiantou"></i></div>
    </div>
  </section>
</template>

<script>
import findKey from 'lodash/findKey';
export default {
  name: 'solution-client',
  props: {
    usage: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    url() {
      const isMobile = this.$store.state.isLandMobile;
      return isMobile ? this.usage.mp : this.usage.pc;
    },
  },
  methods: {
    toDemo() {
      const solutions = {
        31: 'other',
        25: 'car',
        30: 'business',
        22: 'logistics',
        26: 'it',
        23: 'retail',
        21: 'fang',
        19: 'hr',
        18: 'finance',
      };
      const { params } = this.$route;
      const key = findKey(solutions, o => o.toString() === params.id);
      this.$router.push(`/case/list-${params.id}`);
    },
  },
};
</script>

<style scoped lang="scss">
@import './index';
.solution-client {
  background-color: #fafbfc;
  text-align: center;
  padding-top: 0;
  .main-title {
    padding: 3rem 0;
  }
  .image-wrapper {
    margin: 0 auto 3rem;
    width: 70rem;
    // img {
    //   border-right: 1px solid #dedede;
    // }
  }
  .img-bg {
    background-image: url(~assets/images/solution/casebg.png);
    background-size: 100% 100%;
    width: 58.375rem;
    height: 41.25rem;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .ssq-button-more {
    cursor: pointer;
    font-size: 14px;
    .iconfont {
      font-size: 14px;
    }
  }
}
@media (max-width: 767px) {
  .solution-client {
    .image-wrapper {
      margin: 0 auto 3rem;
      width: 100%;
      img {
        // border-left: 1px solid #dedede;
        border-right: none;
      }
    }
  }
}
</style>
