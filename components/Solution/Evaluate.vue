<template>
  <div class="index-evaluate section">
    <div class="container">
      <h2 class="section-headline headline--main">上上签客户的肯定</h2>
      <div class="content">
        <div class="swiper-container" v-swiper:mySwiper="isMobile?swiperOptionMobile:swiperOption">
          <div class="swiper-wrapper">
				<div class="swiper-slide" v-for="(item, index) in evaluate" :key="item.name">
					<div class="item">
						   <div class="top">
								<!-- <div :class="['default-avatar', activeIndex === index? 'active-avatar':'']" :style="{backgroundImage: `url(${item.logo})`}"> -->
								<div :class="['default-avatar', activeIndex === index? 'active-avatar':'']">
									<img :src="item.logo" alt="" width="100%" height="100%">
								</div>
						   </div>
					</div>
					<div v-if="isMobile">
						<div class="main-content">{{ partnerInfo.content }}</div>
						<div class="name">
							<p class="title">{{ partnerInfo.name }}</p>
							<p class="label">{{ partnerInfo.desc }}</p>
						</div>
					</div>
				</div>
          </div>
        </div>
        <div class="arrow" v-if="!isMobile">
          <div class="ssq-button-prev-a">
            <i class="el-icon-arrow-left"></i>
          </div>
          <div class="ssq-button-next-a">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
	 	<div class="main-content" v-if="!isMobile">{{ partnerInfo.content }}</div>
		<div class="name" v-if="!isMobile">
			<p class="title">{{ partnerInfo.name }}</p>
			<p class="label">{{ partnerInfo.desc }}</p>
		</div>
      <!-- <div v-if="!demo" class="ssq-button-more transparent" @click="toCase">进一步了解我们的合作伙伴 <i class="iconfont icon-xiangyoujiantou"></i></div> -->
      <div v-if="demo" class="ssq-button-more" style="color: #fff" @click="toDemo">免费试用 ></div>
	<div v-if="isMobile" class="swiper-pagination swiper-pagination__evaluate" slot="pagination"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'index-evaluate',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
    evaluate: {
      type: Array,
      default: () => {},
    },
  },
  data() {
    return {
      activeIndex: 0,
      swiperOption: {
        loop: true,
        initialSlide: 0,
        centeredSlides: true,
        slideToClickedSlide: true,
        speed: 700,
        slidesPerView: this.evaluate.length >= 5 ? 5 : 1,
        navigation: {
          nextEl: '.ssq-button-next-a',
          prevEl: '.ssq-button-prev-a',
        },
      },
      swiperOptionMobile: {
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        pagination: {
          el: '.swiper-pagination.swiper-pagination__evaluate',
        },
      },
      partnerInfo: {
        content: '',
        name: '',
        desc: '',
      },
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {
    toCase() {
      this.$router.push('/evaluate');
    },
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const query = sessionStorage.getItem('query');
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          ...Qs.parse(query),
        },
      });
    },
    handleChange(cur) {
      const activeIndex = cur.realIndex;
      this.activeIndex = activeIndex;
      this.getInfo();
    },
    getInfo() {
      this.$nextTick(() => {
        this.partnerInfo = this.evaluate.filter(
          (item, index) => index == this.activeIndex
        )[0];
      });
    },
  },
  mounted() {
    const _this = this;
    this.mySwiper.on('slideChange', function() {
      _this.handleChange(this);
    });
    this.getInfo();
  },
};
</script>

<style scoped lang="scss">
.index-evaluate {
  //   padding-top: 5rem;
  //   padding-bottom: 2rem;
  background-color: #fff;
  text-align: center;
  .content {
    width: 65%;
    position: relative;
    margin: 0 auto 3rem;
    .item {
      width: 100%;
      background-color: #fff;
      padding: 20px 10px;
      .top {
        display: flex;
        align-items: center;
        img {
          width: 90px;
          //   margin-right: 30px;
          border-radius: 50%;
          border: 1px solid #eee;
        }
        .default-avatar {
          margin: 0 auto;
          img {
            height: 90px;
            border-radius: 50%;
            width: 90px;
            cursor: pointer;
          }
        }

        .name {
          text-align: left;
          font-size: 20px;
          line-height: 1.3;
        }
      }
    }
    .swiper-slide-active .default-avatar img {
      -ms-transform: scale(1.4); /* IE 9 */
      -webkit-transform: scale(1.4); /* Safari */
      transform: scale(1.4); /* 标准语法 */
      transition: transfrom 0.4s;
    }
    .swiper-slide-prev {
      .item {
        .default-avatar {
          margin-left: 0;
        }
      }
    }
    .swiper-slide-next {
      .item {
        transition: all 0.5s;
        .default-avatar {
          margin-right: 0;
          transition: all 0.5s;
        }
      }
    }
  }
  .main-content {
    max-width: 40rem;
    text-align: center;
    text-align: justify;
    line-height: 1.75;
    margin: 0 auto;
    color: #86868b;
  }
  .name {
    margin-top: 2.5rem;
    .title {
      font-size: 20px;
      font-weight: 600;
    }
    .label {
      font-size: 18px;
      margin: 1rem 0 2rem 0;
    }
  }
  .ssq-button-more {
    color: #00aa64;
    font-size: 14px;
    .iconfont {
      font-size: 14px;
    }
  }
  .ssq-button-prev-a,
  .ssq-button-next-a {
    position: absolute;
    top: 50%;
    transform: translate3d(0, -50%, 0);
    background: transparent;
    cursor: pointer;
    > i {
      font-size: 24px;
      color: rgba(35, 24, 21, 0.5);
      outline: none;
      background: #eee;
      border-radius: 50%;
      padding: 4px;
      &:hover {
        color: rgba(35, 24, 21, 1);
      }
      &:focus {
        outline: none;
      }
    }
    &:focus {
      outline: none;
    }
  }
  .ssq-button-prev-a {
    left: -60px;
  }
  .ssq-button-next-a {
    right: -60px;
  }
  .ssq-button-primary {
    display: inline-block;
  }
}
.swiper-pagination {
  width: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 20px;
  /deep/ .swiper-pagination-bullet {
    margin: 0 10px;
  }
  /deep/ .swiper-pagination-bullet-active {
    background: #62686f;
  }
}
@media screen and (max-width: 767px) {
  .index-evaluate {
    padding: 40px 10px 80px;
    .content {
      position: relative;
      width: 100%;
      padding: 0 10px;
      margin: 0;
      .main-content {
        font-size: 14px;
        color: #86868b;
      }
      .iconfont {
        font-size: 14px;
      }
      .swiper-slide-active .default-avatar img {
        -ms-transform: scale(1); /* IE 9 */
        -webkit-transform: scale(1); /* Safari */
        transform: scale(1); /* 标准语法 */
        transition: transfrom 0.4s;
      }

      .item {
        width: 100%;
        padding: 0 15px 20px 15px;
        .top {
          img {
            width: 60px;
            margin-right: 20px;
            border-radius: 50%;
            border: 1px solid #eee;
          }
          .name {
            text-align: left;
            font-size: 16px;
            line-height: 1.3;
          }
        }
        .main-content {
          text-align: justify;
          line-height: 1.75;
          margin-top: 1.5rem;
          color: #717171;
        }
      }
    }

    .ssq-button-prev-a,
    .ssq-button-next-a {
      > i {
        font-size: 22px;
      }
    }
    .ssq-button-prev-a {
      left: -12px;
    }
    .ssq-button-next-a {
      right: -12px;
    }
  }
}
</style>
