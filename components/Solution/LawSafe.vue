<template>
  <div class="solution-lawSafe">
    <div class="section">
      <div class="container-wrapper">
        <div class="headline">如何保证合法合规</div>
        <div class="subtitle">2005年颁布的《中华人民共和国电子签名法》第十四条指出可靠的电子签名与手写签名或者盖章具有同等的法律效力</div>
        <el-row class="features">
          <el-col :xs="24" :sm="8">
            <div class="feature">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-common-icon4"></use>
              </svg>
              <div class="title">合同主体可信</div>
              <div class="desc">上上签对接公安部、工商局、银联数据库对每个签约企业和个人做实名认证，通过后由国家认可对权威CA机构颁发数字证书。</div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="8">
            <div class="feature">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-common-icon3"></use>
              </svg>
              <div class="title">签署行为真实有效</div>
              <div class="desc">合同签署前，上上签会以签约密码、短信验证码、活体识别等方式确认签署人本人在真实意愿下进行签署，确保签署行为真实有效。</div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="8">
            <div class="feature">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-common-icon6"></use>
              </svg>
              <div class="title">签署结果不可篡改</div>
              <div class="desc">签署时，合同上加盖签署人双方的电子签名和数字证书，同时加盖时间戳来标定签署时间，时间戳的时间源自中科院国家授时中心，然后加盖上上签的数字证书。一旦合同被篡改，证书会立刻失效从而被发现。</div>
            </div>
          </el-col>
        </el-row>
        <button
          class="ssq-button-primary"
          @click="toDemo()">
          免费试用
        </button>
      </div>
    </div>
    <div class="section">
      <div class="container-wrapper">
        <div class="headline">如何保证数据安全</div>
        <el-row class="features direction-row">
          <el-col :xs="24" :sm="12">
            <div class="feature">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-common-icon"></use>
              </svg>
              <div class="desc">上上签是业内率先拥有ISO27001、ISO27018、ISO 38505-1:2017、公安部信息安全三级等保、工信部可信云、云计算SaaS服务能力符合性评估三级六大资质认证等电子签约云平台，具备银行级别安全机制。</div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12">
            <div class="feature">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-common-icon5"></use>
              </svg>
              <div class="desc">作为业内率先拥有自主安全团队的企业，上上签平台用过模拟黑客攻击的白帽子安全众测。</div>
            </div>
          </el-col>
        </el-row>
        <el-row class="features direction-row">
          <el-col :xs="24" :sm="12">
            <div class="feature">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-common-icon2"></use>
              </svg>
              <div class="desc">数据存储采用业界超高强度AES256位加密，分布式存储集群，碎片化存储，保证数据的安全性。</div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12">
            <div class="feature">
              <svg class="icon" aria-hidden="true">
                <use xlink:href="#icon-common-icon1"></use>
              </svg>
              <div class="desc">同时从网络攻击防御、系统性能、业务连续性、高强度加密等多方面保证平台安全。</div>
            </div>
          </el-col>
        </el-row>
        <button
          class="ssq-button-primary"
          @click="toDemo()">
          免费试用
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'solution-lawSafe',
  methods: {
    toDemo() {
      const { params } = this.$route;
      this.$router.push(`/demo?${params.id}`);
    },
  },
};
</script>

<style scoped lang="scss">
@import './index';
.solution-lawSafe .section {
  &:first-child {
    background-color: #fafbfc;
  }
}
.features {
  .feature {
    margin-bottom: 2.5rem;
    .icon {
      font-size: 56px;
    }
    .title {
      font-size: 20px;
      margin: 2rem auto;
    }
    .desc {
      padding: 0 32px;
      text-align: justify;
      line-height: 28px;
      font-size: 16px;
      color: #888;
    }
  }
  &.direction-row {
    .feature {
      display: flex;
      justify-content: center;
      margin-bottom: 3.5rem;
      .desc {
        width: 75%;
        padding: 0 0 0 32px;
      }
    }
  }
}
@media only screen and (max-width: 767px) {
  .features {
    .feature {
      .icon {
        font-size: 36px;
      }
      .title {
        font-size: 16px;
        margin: 1.5rem auto;
      }
      .desc {
        font-size: 12px;
        padding: 0 16px;
        line-height: 20px;
      }
    }
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .features {
    .feature {
      .icon {
        font-size: 46px;
      }
      .title {
        font-size: 18px;
        margin: 2rem auto;
      }
      .desc {
        font-size: 14px;
        padding: 0 16px;
        line-height: 20px;
      }
    }
    &.direction-row {
      .feature {
        .desc {
          padding: 0 0 0 16px;
        }
      }
    }
  }
}
</style>
