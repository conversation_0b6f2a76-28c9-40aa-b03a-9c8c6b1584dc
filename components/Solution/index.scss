.section {
  padding: 5rem 10px;
}
.container-wrapper {
  max-width: 1180px;
  width: 100%;
  margin: 0 auto;
  .ssq-button-primary {
    margin-top: 50px;
  }
}
.headline {
  font-size: 32px;
  margin-bottom: 4rem;
}
.subtitle {
  margin-bottom: 4rem;
  margin-left: auto;
  margin-right: auto;
  max-width: 620px;
  font-size: 16px;
  line-height: 24px;
  color: #888;
  text-align: center;
}
.image-wrapper {
  width: 100%;
  img {
    width: 100%;
  }
}
/* mobile */
@media only screen and (max-width: 767px) {
  .headline {
    font-size: 20px;
    margin-bottom: 2.5rem;
  }
  .subtitle {
    margin-bottom: 2.5rem;
    font-size: 12px;
  }
  .container-wrapper {
    .ssq-button-primary {
      margin-top: 30px;
      width: 120px;
      height: 36px;
      padding: 0;
    }
  }
}

/* ipad */
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .headline {
    font-size: 26px;
    margin-bottom: 3rem;
  }
  .subtitle {
    margin-bottom: 3rem;
    font-size: 14px;
  }
}
