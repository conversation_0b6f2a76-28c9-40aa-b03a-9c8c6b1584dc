<template>
  <section
    :style="{backgroundColor: isWhiteBg ? '#fff' : '#f7f8fa'}"
    class="usage">
    <div class="container">
      <h2 class="section-headline headline--main">1273万+标杆企业选择
        <svg class="icon logo-text" aria-hidden="true">
          <use xlink:href="#icon-logo-text"></use>
        </svg>
      </h2>
      <el-tabs v-model="activeName" v-if="!isMobile" class="usage-content">
        <el-tab-pane label="人力资源" name="HR">
          <img src="@/assets/images/usage/renli.jpg" alt="上上签电子合同人力资源行业案例">
        </el-tab-pane>
        <el-tab-pane label="B2B供应链" name="B2B">
          <img src="@/assets/images/usage/gongying.jpg" alt="上上签电子合同B2B供应链行业案例">
        </el-tab-pane>
        <el-tab-pane label="零售制造" name="retail">
          <img src="@/assets/images/usage/lingshou.jpg" alt="数十万家企业使用上上签电子合同">
        </el-tab-pane>
        <el-tab-pane label="租赁" name="lease">
          <img src="@/assets/images/usage/zulin.jpg" alt="上上签电子合同租赁行业案例">
        </el-tab-pane>
        <el-tab-pane label="互联网平台" name="internet">
          <img src="@/assets/images/usage/hulianwang.jpg" alt="上上签电子合同互联网平台行业案例">
        </el-tab-pane>
        <el-tab-pane label="金融保险" name="finance">
          <img src="@/assets/images/usage/jinrong.jpg" alt="上上签电子合同金融保险行业案例">
        </el-tab-pane>
      </el-tabs>
      <div class="mobile-content" v-if="isMobile">
        <img src="@/assets/images/usage/usage-m.png" alt="">
      </div>
      <div class="section-content" v-if="!isMobile">
        <button class="ssq-button-primary transparent">
          <nuxt-link :to="`/case#${activeName}`">
            更多案例
          </nuxt-link>
        </button>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'Usage',
  props: {
    isWhiteBg: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeName: 'HR',
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
};
</script>

<style lang="scss">
.usage {
  .el-tabs__nav {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  .el-tabs__active-bar {
    display: none;
  }
  .el-tabs__item {
    font-size: 1.25rem;
    padding: 0;
    text-align: center;
    &.is-active {
      color: #00a664;
      border-bottom: 3px solid #00a664;
    }
    &:hover {
      color: #00a664;
    }
  }
  .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: #00a664;
  }
}
</style>
<style scoped lang="scss">
.usage {
  text-align: center;
  padding: 5rem 0;
  .container {
    width: 100%;
    max-width: 980px;
  }
  .section-headline {
    line-height: 2rem;
    .logo-text {
      width: 7.25rem;
      height: 2.25rem;
      vertical-align: -0.2em;
      position: relative;
      left: -3px;
    }
    padding-bottom: 5rem;
  }
  .usage-content img {
    width: 100%;
    padding-top: 2rem;
    padding-bottom: 4rem;
  }
}
.ssq-button-primary.is-text {
  display: inline-block;
}
.ssq-button-primary.is-text .el-icon-arrow-right {
  margin-left: 5px;
  font-size: 20px;
  vertical-align: bottom;
}
@media screen and (max-width: 767px) {
  .usage {
    padding: 50px 10px;
    .section-headline {
      .logo-text {
        width: 6.125rem;
        height: 2.75rem;
        vertical-align: -0.35em;
        position: relative;
        left: -3px;
      }
      .img-text {
        vertical-align: top;
      }
    }
    img {
      width: 100%;
      padding-bottom: 30px;
    }
  }
}
</style>
