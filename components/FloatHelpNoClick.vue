<template>
  <div class="help-wrapper">
    <div class="pc float-help" v-if="!isMobile">
        <!-- <div class="link">
          <p>在线咨询</p>
        </div> -->
        <!-- <div
          slot="reference"
          class="item"
          :style="{ backgroundColor: bgColor }"
          @click="handleService">
          <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-zaixiankefu"></use>
            </svg>
            <span>在线咨询</span>
        </div> -->
        <div
          slot="reference"
          id="notShowUdesk"
          :style="{ backgroundColor: bgColor }">
          <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-zaixiankefu"></use>
            </svg>
            <a>在线咨询</a>
        </div>
        <div class="space"></div>
      <el-popover
        trigger="hover"
        placement="right"
      >
        <div class="link">
          <p>全国服务热线</p>
          <a href="tel:4009936665">************</a>
        </div>
        <div
          slot="reference"
          class="item"
          :style="{ backgroundColor: bgColor }">
          <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-dianhua"></use>
            </svg>
            <span>电话咨询</span>
        </div>
      </el-popover>
      <div class="space"></div>
        <a href="javascript:void(0);"
          slot="reference"
          class="item"
          :style="{ backgroundColor: bgColor }"
          @click="handleDemo">
          <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-zhuce"></use>
            </svg>
            <span>免费试用</span>
        </a>
        <div class="space"></div>
      <div
        class="item"
        :style="{ backgroundColor: bgColor }"
        @click="handleScroll">
        <!-- <i class="icon-xiangshangjiantouarrowup"></i> -->
          <!-- <span>置顶</span> -->
          <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-xiangshangjiantouarrowup"></use>
            </svg>
      </div>
    </div>
    <div class="mobile" v-else>
      <!-- <div class="left" @click="handleService" :style="{ color: bgColor, borderColor: bgColor }">
        <i class="iconfont icon-kefu"></i>在线咨询</div> -->
        <!-- <div class="left" :style="{ color: bgColor, borderColor: bgColor }" id="notShowUdesk">
        <i class="iconfont icon-kefu"></i><a>在线咨询</a></div> -->
        <div class="left" :style="{ color: bgColor, borderColor: bgColor }" id="notShowUdesk">
        <i class="iconfont icon-kefu"></i><a @click="toService">在线咨询</a></div>
      <a class="right" :style="{ backgroundColor: bgColor }" href="tel:4009936665">
        <i class="iconfont icon-phone"></i>免费服务热线</a>
    </div>
    <!-- <div class="iframe" v-if="serviceVisible" :class="{open: serviceVisible}">
      <div class="close" @click="closeFeedback">
        <i class="el-icon-close"></i>
      </div>
      <iframe src="//bestsign.udesk.cn/im_client/?web_plugin_id=23490" frameborder="0"></iframe>
    </div> -->
  </div>
</template>

<script>
import Qs from 'qs';
export default {
  name: 'FloatHelp',
  props: {
    bgColor: {
      type: String,
      default: '#00aa64',
    },
  },
  computed: {
    serviceVisible() {
      return this.$store.state.serviceVisible;
    },
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  mounted() {
    this.udesk();
  },
  methods: {
    handleScroll() {
      this.$scrollTo('body');
    },
    handleHelp() {
      this.$router.push('/help');
    },
    handleService() {
      this.$store.commit('setServiceVisible', true);
    },
    closeFeedback() {
      this.$store.commit('setServiceVisible', false);
    },
    udesk() {
      (function(a, h, c, b, f, g) {
        a['UdeskApiObject'] = f;
        a[f] =
          a[f] ||
          function() {
            (a[f].d = a[f].d || []).push(arguments);
          };
        g = h.createElement(c);
        g.async = 1;
        g.charset = 'utf-8';
        g.src = b;
        c = h.getElementsByTagName(c)[0];
        c.parentNode.insertBefore(g, c);
      })(
        window,
        document,
        'script',
        'https://assets-cli.udesk.cn/im_client/js/udeskApi.js',
        'ud'
      );
      ud({
        code: '1aifdai3',
        targetSelector: '#notShowUdesk',
        //  "selector":"#btn_udesk_im",*/
        // mode: 'inner',
        // color: '#70b544',
        // pos_flag: 'vrm',
        // css: {
        //   bottom: '80px',
        //   right: '50px',
        //   width: '36px',
        //   height: '36px',
        //   border: 'none',
        //   background: 'url("/page/static/<EMAIL>") no-repeat',
        //   backgroundColor: 'rgba(0, 0, 0, 0.2)'
        // },
        link: 'https://bestsign.udesk.cn/im_client/?web_plugin_id=148681',
      });
    },
    handleDemo() {
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/demo',
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
          hp_right: '',
        },
      });
    },
    tolink(route) {
      var query = Object.entries(this.$route.query)
        .map(([key, value]) => {
          return `${key}=${value}`;
        })
        .join('&');
      // debugger;
      if (route.indexOf('?') > -1) {
        route = route + '&' + query;
      } else {
        route = route + '?' + query;
      }
      return route;
    },
    toService() {
      const url = this.tolink(
        'http://bestsign.udesk.cn/im_client/?web_plugin_id=23490'
      );
      window.open(url);
      // console.log(url);
    },
  },
};
</script>
<style lang="scss">
.el-popover {
  text-align: center;
  .download {
    p {
      margin-top: 15px;
    }
  }
  .link {
    p {
      padding: 7px 0;
    }
    a {
      color: #00aa64;
    }
  }
}
</style>
<style scoped lang="scss">
.help-wrapper {
  .iframe {
    position: fixed;
    right: 260px;
    bottom: 0;
    width: 320px;
    height: 480px;
    background-color: #f8fbff;
    box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 20px 0px;
    transition: all 0.5s ease;
    transform: translateY(100%);
    z-index: 100000;
    iframe {
      width: 100%;
      height: 100%;
    }
    .close {
      position: absolute;
      right: -9px;
      top: -12px;
      font-size: 16px;
      cursor: pointer;
      padding: 10px;
      color: #fff;
    }
    &.open {
      transform: translateY(0);
    }
    &.service {
      .close {
        color: #fff;
        right: 34px;
        top: 9px;
      }
    }
  }
  .mobile {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 50px;
    display: flex;
    font-size: 18px;
    text-align: center;
    z-index: 999;
    .iconfont {
      font-size: 20px;
      margin-right: 0.75rem;
      vertical-align: middle;
    }
    .left {
      display: inline-block;
      width: 45%;
      height: 100%;
      line-height: 50px;
      color: #00aa64;
      background-color: #fff;
      // border-top: 1px solid #00aa64;
    }
    .right {
      flex: 1;
      height: 100%;
      line-height: 50px;
      // background-color: #00aa64;
      color: #fff;
    }
  }
}
.float-help {
  position: fixed;
  bottom: 30%;
  right: 10px;
  text-align: center;
  z-index: 999;
  .item {
    // padding: 16px 0;
    display: flex;
    width: 70px;
    cursor: pointer;
    position: relative;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    // border-top: 5px solid #fff;
    // border: 2.5px solid #fff;
    font-size: 12px;
    color: #fff;
    height: 70px;
    background-color: #00aa64;
    border-radius: 2px 2px 2px 2px;
    span {
      margin-top: 7px;
      font-size: 13px;
      font-weight: 500;
      a {
        color: #fff;
      }
    }
    a {
      margin-top: 7px;
      a {
        color: #fff;
      }
    }
  }
  #notShowUdesk {
    // padding: 16px 0;
    display: flex !important;
    width: 70px;
    cursor: pointer;
    position: relative;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    // border-top: 5px solid #fff;
    // border: 2.5px solid #fff;
    font-size: 12px;
    color: #fff;
    height: 70px;
    background-color: #00aa64;
    border-radius: 2px 2px 2px 2px;
    span {
      margin-top: 7px;
      a {
        color: #fff;
      }
    }
    a {
      margin-top: 7px;
      font-size: 13px;
      font-weight: 500;
      a {
        color: #fff;
      }
    }
  }
  .el-icon-arrow-up {
    font-size: 22px;
    color: #fff;
  }
  .icon {
    font-size: 20px;
  }
  .space {
    // border-top: 2.5px solid #fff;
    // border-bottom: 2.5px solid #fff;
    background-color: transparent;
    height: 10px;
    width: 100%;
  }
}
.sapce {
  // border-top: 2.5px solid #fff;
  // border-bottom: 2.5px solid #fff;
  background-color: #fff;
  height: 5px !important;
  width: 100%;
}
@media screen and (max-width: 767px) {
  .help-wrapper {
    .iframe {
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
