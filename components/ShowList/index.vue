<!--  -->
<template>
	<section class="section section-list is-tiny">
		<div class="container">
        <el-row>
          <template v-for="(item, index) in list">
            <el-col
              :key="index"
              :md="8"
              :lg="6"
              :sm="8"
            >
              <nuxt-link class="card" :to="`/case/detail/${item.id}`">
			    <!-- <nuxt-link :to="{path: `/activity/${item.id}`, query: {}}"> -->
                  <!-- 在上一层页码刷新当下页面后，这里用nuxt-link会把那个页码带过来，所以用a便签实现 -->
                <div>
                  <div class="card-content">
                    <h4 class="header">{{ item.customerName }}</h4>
                    <p class="body">{{ item.caseName }}</p>
                  </div>
				   <img
                    :key="item.imgUrl"
                    v-lazy="formatUrl(item.imgUrl)"
                    class="card-image">
                </div>
              </nuxt-link>
            </el-col>
          </template>
        </el-row>
        <div class="button-wrap">
          <el-pagination
		  background
            :page-size="12"
            :current-page="pageNum"
            :total="totalNum"
            @current-change="handlePageChange"
            layout="prev, pager, next">
			</el-pagination>
        </div>
        <!-- seo优化，由于分页是客户端渲染，源码里无法拿到路由 -->
        <div style="display:none" v-for="item in pageNums" :key="item">
          <a v-if="item === 1" href="/case">{{item}}</a>
          <a v-else :href="`/case-p${item}`">{{item}}</a>
        </div>
      </div>
	</section>
</template>

<script>
export default {
  components: {},
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  methods: {
    formatUrl(url) {
      const arr = url && url.split('?');
      return url && arr[0];
    },
  },
  created() {},
  mounted() {},
};
</script>
<style lang='scss' scoped>
.section-list {
  text-align: center;
  background: #eee;
  a {
    color: #323232;
  }
  .card {
    height: 24rem;
  }
  .card-image {
    width: 50%;
    height: 5rem;
    padding-top: 20px;
    border-top: 1px solid #eee;
  }
  .card-content {
    text-align: center;
    height: auto;
    padding-bottom: 60px;
    .header {
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      font-size: 1.4rem;
      line-height: 2.25rem;
      margin: 4rem 0 1rem;
    }
    .body {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      text-align: justify;
      word-break: break-all;
      line-height: 1.25rem;
      padding: 0 30px;
    }
    .footer {
      align-self: flex-end;
      a {
        color: #00a664;
      }
    }
  }
}
</style>
