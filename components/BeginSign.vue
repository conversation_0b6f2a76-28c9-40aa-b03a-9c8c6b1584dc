<template>
  <section class="section begin-sign">
    <div class="container">
      <div class="sign-right">
        <h2 class="section-headline headline--main">{{$t('beginSign.title')}}</h2>
        <!-- <p class="subtitle headline--main__sub-foot">{{$t('beginSign.desc')}}</p> -->
        <div>
          <button
			type="button"
			class="ssq-button-primary"
			@click="beginSign"
			>
			{{isEN?$t('header.material'):$t('header.use')}}
			</button>
        </div>
        
      </div>
      <!-- <div class="icon-container" v-if="!isMobile">
        <div v-for="(item,index) in list" :key="index">
          <div class="icon-item">
            <div class="icon-icon">
              <i class="el-icon-success"></i>
            </div>
            <div class="icon-desc">
              <div class="icon-desc-item">
              {{item.content1}}
            </div>
            <div class="icon-desc-item">
              {{item.content2}}
            </div>
            </div>
            
          </div>
        </div>
      </div> -->
    </div>
  </section>
</template>

<script>
import Qs from 'qs';
import { jumpToEntRegister } from '@/assets/utils/toRegister';
export default {
  name: 'BeginSign',
  props: {
    scroll: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      beginSignDialogVisible: false,
      isEN: false,
      list: [
        {
          content1: this.$tc('header.desc1', 1),
          content2: this.$tc('header.desc1', 2),
        },
        {
          content1: this.$tc('header.desc2', 1),
          content2: this.$tc('header.desc2', 2),
        },
        {
          content1: this.$tc('header.desc3', 1),
          content2: this.$tc('header.desc3', 2),
        },
        {
          content1: this.$tc('header.desc4', 1),
          content2: this.$tc('header.desc4', 2),
        },
      ],
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  methods: {
    beginSign() {
      if (this.isEN) {
        this.$router.push({
          path: `/${this.language}/material`,
        });
      } else {
        jumpToEntRegister();
      }
    },
  },
  mounted() {
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
};
</script>

<style scoped lang="scss">
.section {
  width: 100%;
  padding: 0;
}
.begin-sign {
  background-color: #f5f5f7;
  .container {
    max-width: 100%;
    // background: url('~assets/images/footer/footerbg.jpg') no-repeat;
    // background-size: 100% 100%;
    background: $-color-main;
    padding: 3rem 0 8rem;
    text-align: center;
    color: #fff;
    margin: 0 auto;
    width: 100%;
    position: relative;
    display: flex;
    justify-content: space-around;
    position: relative;
    .sign-right {
      text-align: center;
      width: 100%;
      .section-headline {
        padding: 2rem 0;
        color: #fff;
        font-weight: 500;
      }
      .headline--main__sub-foot {
        font-size: 1.3125rem;
        font-weight: 400;
      }
      .subtitle {
        margin: 0rem 0 3rem;
      }
    }
    .icon-container {
      position: absolute;
      bottom: 0;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      bottom: 0;
      background: rgba(100, 100, 100, 0.1);
      width: 100%;
      padding: 0 10%;
      flex-wrap: nowrap;
      .icon-item {
        display: flex;
        .icon-icon {
          margin-right: 5px;
        }
        .icon-desc {
          text-align: left;
          .icon-desc-item {
            margin-bottom: 5px;
          }
        }
      }
    }
  }

  .ssq-button-primary {
    border-radius: 3px;
    width: 190px;
    height: 48px;
    color: $-color-main;
    background: #fff;
    border: none;
    font-size: 18px;
    font-weight: 600;
  }
}

@media (max-width: 768px) {
  .section {
    padding-top: 50px;
    padding-bottom: 50px;
    &.begin-sign {
      padding: 0;
      background-color: #fff;
      .container {
        // border-radius: 0;
        // padding: 3rem 0 !important;
        // width: 100%;
        // background: url('~assets/images/footer/footerbg.jpg') no-repeat;
        // background-size: cover;
        background: $-color-main;
        padding: 4rem 0;
        .sign-img {
          float: left;
          width: 80px;
          height: 132px;
          margin-left: -20px;
        }
        .ssq-button-primary {
          display: block;
          margin: 0 auto;
          width: 190px;
          margin-top: 10px;
          font-size: 16px;
          height: 40px;
        }
        .sign-right {
          text-align: center;
          .subtitle {
            // display: none;
            margin: 0rem 10% 3rem;
            line-height: 1.5;
          }
        }
      }

      .ssq-input {
        width: 150px;
        margin: 0 auto 15px;
      }
      .section-headline {
        font-size: 22px;
        color: #fff;
        font-weight: 500;
      }
      .ssq-button-primary {
        display: block;
        margin: 0 auto;
        width: 150px;
      }
    }
  }
  .title {
    font-size: 21px;
  }
  .subtitle {
    font-size: 14px;
    margin: 20px 0 40px;
  }
  .ant-input,
  .button {
    width: 157px;
    height: 50px;
    border-radius: 50px;
  }
  .ant-input {
    display: block;
    margin: 0 auto 10px;
    font-size: 14px;
    padding: 2px 15px;
    text-align: center;
  }
  .button {
    font-size: 16px;
  }
  .section-headline {
    color: #323232;
    font-size: 20px;
    font-weight: normal;
  }
  .ssq-input {
    width: 167px;
    display: block;
  }
  .ssq-input,
  .ssq-button-primary {
    padding: 0 10px;
    width: 167px;
    display: block;
    margin: 10px auto;
    text-align: center;
  }
}
// @media screen and (min-width: 1472px) {
// 	.begin-sign .container{
// 		max-width:90rem;
//     	width: 90rem;
// 	}

// }
</style>
