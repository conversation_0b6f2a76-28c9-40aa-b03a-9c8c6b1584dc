<template>
  <div class="">
    <header class="site-header">
      <nav class="nav-main navbar" v-cloak>
        <div class="nav-header">
          <div
            @click="handleIndex"
            class="nav-brand">
            <img src="@/assets/images/logo-bestsign.png" alt="">
          </div>
        </div>
        <ul class="nav-section" v-if="loadingEN&&!loadingJP">
          <li class="solution">
            <nuxt-link :to="`/${language}/product2/service`">{{$t('header.service')}}</nuxt-link>
          </li>
          <li class="solution">
            <nuxt-link :to="`/${language}/product2/function`">{{$t('header.proFunction')}}</nuxt-link>
          </li>
          <li class="solution">
            <nuxt-link :to="`/${language}/product2`">{{$t('header.signType')}}</nuxt-link>
          </li>
          <li class="solution">
            <nuxt-link :to="`/${language}/case`" :class="{'is-active': activeNav === 'case'}">
				      <span>{{$t('header.case')}}</span>
            </nuxt-link>
          </li>
          <li class="solution">
            <nuxt-link :to="`/${language}/about-us`">{{$t('header.about')}}</nuxt-link>
          </li>
        </ul>
        <ul class="nav-section" v-if="loadingJP&&!loadingEN">
          <li class="solution">
            <a
              :class="{'is-active': activeNav === 'product'}"
              href="javascript:void(0)">
			  <span>{{$t('header.production')}}</span>
			  <i class="iconfont icon icon-xiangxiajiantou"></i>
			</a>
            <div class="solution-menu-wrap">
              <ul class="solution-menu">
                <li>
                  <nuxt-link :to="`/${language}/product2/service`">{{$t('header.service')}}</nuxt-link>
                </li>
                <li>
                  <nuxt-link :to="`/${language}/product2/function`">{{$t('header.proFunction')}}</nuxt-link>
                </li>
                <li>
                  <nuxt-link :to="`/${language}/product2`">{{$t('header.signType')}}</nuxt-link>
                </li>
              </ul>
            </div>
          </li>
          <li class="solution">
            <nuxt-link :to="`/${language}/cost`">{{$t('header.cost')}}</nuxt-link>
          </li>
          <li class="solution">
            <nuxt-link :to="`/${language}/case`" :class="{'is-active': activeNav === 'case'}" >
				      <span>{{$t('header.case')}}</span>
            </nuxt-link>
          </li>
          <li class="solution">
            <nuxt-link :to="`/${language}/about-us`">{{$t('header.about')}}</nuxt-link>
          </li>
        </ul>
        <LangSwitch v-if="!isMobile"></LangSwitch>
        <ul class="nav-footer" v-if="loadingEN &&!loadingJP">
            <li>
              <a class="btn btn-gradient-green-link materialEn" @click="handleMaterial">{{$t('header.material')}}</a>
            </li>
        </ul>
        <ul class="nav-footer" v-if="loadingJP&&!loadingEN">
            <li class="btn login">
                <a class="btn btn-gradient-green-link login-btn" @click="loginEnt" style="width: 100px;padding:0 10px">{{$t('header.login')}}</a>
            </li>
            <li>
                <a class="btn btn-gradient-green-link demo" @click="handleDemo" >{{$t('header.use1')}}</a>
            </li>
            <li>
              <a class="btn btn-gradient-green-link material" @click="handleMaterial">{{$t('header.material')}}</a>
            </li>
        </ul>
        <div class="navbar-toggle-wrap">
          <span class="ssq-button-primary"  :class="{'ssq-button-primary-en':loadingEN}" @click="handleMaterial">{{$t('header.material')}}</span>
          <a class="navbar-toggle" @click="handleView">
            <i class="iconfont icon-menu"></i>
          </a>
        </div>
      </nav>
    </header>
    <div class="navbar-collapse collapse " :class="{'in': menuVisible}">
      <ul class="items-wrap collapse-list" :style="{'max-height':screenHeight +'px'}">
        <li>
          <el-collapse :accordion="true" class="menu-collapse" @change="closeOther" v-model="activeColl">
            <el-collapse-item  :title="$t('header.production')" name="1">
              <div class="item-content">
                <nuxt-link :to="`/${language}/product2/service`" @click="handleView">{{$t('header.service')}}</nuxt-link>
                <nuxt-link :to="`/${language}/product2/function`" @click="handleView">{{$t('header.proFunction')}}</nuxt-link>
                <nuxt-link :to="`/${language}/product2`" @click="handleView">{{$t('header.signType')}}</nuxt-link>
              </div>
            </el-collapse-item>
          </el-collapse>
        </li>
        <li>
          <nuxt-link class="border-item" :to="`/${language}/cost`" @click="handleView" v-if="!loadingEN">{{$t('header.cost')}}</nuxt-link>
        </li>
        <li>
          <nuxt-link class="border-item" :to="`/${language}/case`" @click="handleView">{{$t('header.case')}}</nuxt-link>
        </li>
        <li>
          <nuxt-link class="border-item" :to="`/${language}/about-us`">{{$t('header.about')}}</nuxt-link>
        </li>
        <li v-if="!loadingEN">
          <a class="split" >
            <span class="loginBtn" @click="loginEnt">{{$t('header.login')}}</span>
            <span class="loginBtn-demo" style="font-size: 14px;text-align: center;" @click="handleDemo">{{$t('header.use')}}</span>
          </a>
        </li>
        <li v-if="loadingEN">
          <a class="split" >
            <span class="loginBtn-demo" style="font-size: 14px;text-align: center;" @click="handleMaterial">{{$t('header.material')}}</span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import Qs from 'qs';
import { jumpToEntRegister } from '@/assets/utils/toRegister';
import LangSwitch from './LangSwitch.vue';

const getJson = () =>
  import('@/static/_jpSolution.json').then(m => m.default || m);

const type = {
  42: 'hr',
  40: 'law',
  41: 'houseRent',
};

export default {
  name: 'Header',
  components: {
    LangSwitch,
  },
  data: () => ({
    activeColl: '',
    screenHeight: '',
    type,
    advertising: true,
    getAdType: {
      pcImgUrl: '',
      bannerLink: '',
      alt: '',
    },
    // onlineAd: true,
    queryLink: '',
    loadingEN: true,
    loadingJP: true,
  }),
  computed: {
    menuVisible() {
      return this.$store.state.menuVisible;
    },
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
    activeNav() {
      return this.$store.state.path;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  watch: {
    $route() {
      this.$store.commit('setMenuVisible', false);
      this.goAnchor(window.location.hash);
    },
    language: {
      handler() {
        this.loadingEN = this.language.indexOf('en') != -1 ? true : false;
        this.loadingJP = this.language === 'ja' ? true : false;
      },
      immediate: true,
    },
  },
  methods: {
    goAnchor(selector) {
      // 最好加个定时器给页面缓冲时间,不等的话dom没渲染完，获取锚点元素拿不到
      if (window.location.hash) {
        setTimeout(() => {
          // 获取锚点元素
          // debugger;
          let anchor = document.querySelector(selector);
          anchor && anchor.scrollIntoView({ behavior: 'smooth' });
        }, 100);
      }
    },
    linkQuery() {
      this.queryLink = sessionStorage.getItem('query');
      console.log(this.queryLink);
    },
    adClose() {
      this.advertising = false;
      let adShow = this.advertising;
      this.$emit('closeAd', adShow);
    },
    // getAd() {
    //   this.$axios({
    //     // url: '/www/api/web/college2?column=friendLink&pageNum=1&pageSize=10',
    //     url: '/www/api/web/top-ad',
    //     method: 'get',
    //   }).then(res => {
    //     this.getAdType = res;
    //     // console.log(res);
    //     let onlineAd = res.pcImgUrl === null ? false : true;
    //     // let onlineAd = false;
    //     this.$emit('changeOnline', onlineAd);
    //     // console.log('pppp');
    //     // console.log(this.onlineAd);
    //   });
    // },
    closeOther(value) {
      this.activeColl = value;
    },
    loginEnt() {
      const query = sessionStorage.getItem('query');
      // const url = 'https://ent.bestsign.com/login';
      // const url = 'https://ent.bestsign.com/account-center/login';
      if (window.location.href.indexOf('.com') > -1) {
        var url = 'https://ent.bestsign.com/account-center/login';
      } else {
        var url = 'https://ent-jp.bestsign.tech/account-center/login';
      }
      if (query) {
        window.open(url + '?' + 'language=ja' + '&' + query);
      } else {
        window.open(url + '?' + 'language=ja');
      }
    },
    loginOpen() {
      const url =
        window.location.host.indexOf('cn') > -1
          ? 'https://openapi.bestsign.cn'
          : 'https://openapi.bestsign.info';
      window.open(url);
    },
    readPolicy() {
      const url = 'https://www.bestsign.cn/wiki/detail/463';
      window.open(url);
    },
    toReg() {
      const query = sessionStorage.getItem('query');
      const href = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push([
            '_trackEvent',
            'click_register_mobile',
            'click',
            href,
          ]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_register_pc', 'click', href]);
      }
      const url =
        window.location.host.indexOf('cn') > -1
          ? 'https://ent.bestsign.cn/register'
          : 'https://ent.bestsign.info/register';
      window.open(url + '?' + 'hp_nav&' + query);
    },
    handleView() {
      this.$store.commit('setMenuVisible', !this.menuVisible);
    },
    handleRouter(url) {
      this.$router.push(url);
    },
    handleDemo() {
      // const query = sessionStorage.getItem('query');
      // const url = 'https://ent.bestsign.com/register';
      // window.open(url + '?' + query);
      jumpToEntRegister();
    },
    handleMaterial() {
      this.$router.push({
        path: `/${this.language}/material`,
      });
    },
    handleDemoNav() {
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/demo',
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
          hp_productnav: '',
        },
      });
    },
    toWebDemo() {
      const url =
        window.location.host.indexOf('cn') > -1
          ? 'https://demo.bestsign.cn'
          : 'https://demo.bestsign.info';
      window.open(url);
    },
    handleIndex() {
      this.$router.push({
        path: `/${this.language}/`,
      });
    },
  },
  async mounted() {
    // debugger;
    if (window.location.hash) {
      await this.goAnchor(window.location.hash);
    }
    await this.linkQuery();
    this.screenHeight = window.screen.height * 0.7;
    const response = await getJson();
    this.solutions = response.map(o => ({
      id: o.id,
      solutionName: o.solutionName,
    }));
    // await this.getAd();
    //日文的官网目前看不需要广告，由于官网广告位影响到下面的页面布局，所以采用进来就关闭广告的情况
    await this.adClose();
  },
};
</script>


<style lang="scss">
[v-cloak] {
  display: none;
}
@import '@/assets/css/index.scss';
@keyframes bigTosmall {
  from {
    transform: scale(0.8);
  }
  to {
    transform: scale(1);
  }
}
.star {
  margin-right: 0.5rem;
  animation: bigTosmall ease 0.5s infinite !important;
}
.site-header {
  display: block;
  // padding: 0 5px;
  padding: 0;
  position: fixed;
  top: 0;
  width: 100%;
  // height: 76px;
  // height: 120px;
  background-color: #fff;
  z-index: 233;
  transition: all 0.3s;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  .wwwnew {
    width: 100%;
    height: 60px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position-x: 40%;
    background-position-y: 50%;
    .toLink {
      display: block;
      width: 100%;
      height: 60px;
    }
    .close {
      position: absolute;
      top: 22px;
      right: 60px;
      width: 30px;
      cursor: pointer;
      .iconfont {
        font-size: 16px;
      }
    }
  }
}

.site-header.with-bottom-border {
  box-shadow: 0 1px 0 #e5e5e5;
}

.site-header .nav-main {
  display: flex;
  justify-content: space-between;
  max-width: 100%;
  height: 76px;
  margin: 0 auto;
  padding: 0 1%;
  z-index: 10;
}

@media (max-width: 1440px) {
  .site-header .nav-main {
    max-width: 100%;
  }
}

.site-header .nav-main .nav-header {
  display: flex;
  align-items: center;
}

.site-header .nav-main .nav-section {
  display: flex;
  flex: 1 1;
  justify-content: center;
  align-items: center;
}
.site-header .nav-main .nav-section a.is-active {
  color: $-color-main;
}
.site-header .nav-main .nav-section .iconfont {
  margin-left: 5px;
  font-size: 10px;
}
.site-header .nav-main .nav-section a:hover .iconfont {
  transform: rotate(180deg);
}

.site-header .nav-main .nav-footer {
  display: flex;
  align-items: center;
}

.site-header .nav-brand {
  width: 140px;
  height: 36.75px;
  margin-top: 3px;
  cursor: pointer;
  /* eslint-disable */
}
.site-header .nav-brand img {
  width: 100%;
  height: 100%;
  /* eslint-disable */
}

/*.site-header .nav-footer {*/
/*  width: 250px;*/
/*}*/
.site-header .nav-footer,
.site-header .nav-section {
  justify-content: flex-end;
  margin: 0;
}

.site-header .nav-footer > li,
.site-header .nav-section > li {
  /*margin-right: 12px;*/
}

.site-header .nav-footer > li > a,
.site-header .nav-footer > li > a:focus,
.site-header .nav-section > li > a,
.site-header .nav-section > li > a:focus {
  display: flex;
  color: #383838;
  align-items: center;
  font-size: 1em;
  padding: 17px 12px;
  transition: all 218ms;
  box-shadow: none;
}
.site-header .nav-footer .phone {
  display: flex;
  align-items: center;
  // margin-right: 10px;
}
.site-header .nav-footer .search {
  // display: flex;
  // align-items: center;
  margin-right: 10px;
  .iconfont {
    font-size: 20px;
  }
}

.site-header .nav-footer > li:last-child,
.site-header .nav-section > li:last-child {
  margin-right: 0;
}

.site-header .nav-footer > li:hover > a,
.site-header .nav-section > li:hover > a {
  color: $-color-main;
}

.site-header .nav-footer {
  position: relative;
}

.site-header .btn:hover {
  cursor: pointer;
}

.site-header .nav-footer .event-discount-badge span {
  display: block;
  margin-top: 25px;
  color: #fff;
  text-align: center;
  line-height: 22px;
  font-weight: 500;
}

@media (max-width: 1024px) {
  .site-header .nav-footer .event-discount-badge {
    display: none;
  }
}
.site-header .nav-footer > li > a.btn-gradient-green-link {
  display: flex;
  position: relative;
  width: 150px;
  height: 40px;
  align-items: center;
  justify-content: center;
  padding: 0 15px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  border-radius: 3px;
  &:hover {
    // border-color: $-color-main;
  }
  &.login-btn {
    font-size: 1rem;
  }
  &.demo {
    color: #000;
    border: 1px solid #000;
    font-size: 16px;
    font-weight: 500;
    margin-right: 20px;
  }
  &.login-btn {
    color: #000;
    font-size: 16px;
    font-weight: 500;
  }
  &.material {
    background-color: $-color-main;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    margin-right: 20px;
    width: 170px;
    &:hover {
      color: #fff;
    }
  }
  &.materialEn {
    background-color: $-color-main;
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    margin-right: 20px;
    width: 180px;
    &:hover {
      color: #fff;
    }
  }
}
.site-header .nav-footer > li > a.login-btn:hover {
  &:hover {
    // border-color: #fff;
  }
}

.site-header .nav-footer > li:hover > a.btn-gradient-green-link {
  // color: $-color-main;
}

.site-header .nav-footer > li > a.user-info {
  padding: 6px 0;
}

.site-header li.solution {
  position: relative;
  //   display: flex;
  //   align-items: center;
  font-weight: 600;
}

.site-header li.solution:hover {
  // cursor: pointer;
}

.site-header li.solution .solution-menu-wrap {
  text-align: left;
  position: absolute;
  display: none;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  //   left: 0;
  //   top: 60px;
}

.site-header li.solution .solution-menu {
  width: 200px;
  margin: 0;
  padding: 0;
  padding-top: 4px;
  padding-bottom: 4px;
  border: none;
  overflow-y: auto;
  border-radius: 4px;
  background-color: #fff;
}

.site-header li.solution .solution-menu a {
  display: block;
  padding: 10px 12px;
  color: #383838;
}

.site-header li.solution .solution-menu .menu-first {
  border-bottom: 1px solid $-color-main;
}

.site-header li.solution .solution-menu .menu-first:hover {
  color: #383838;
  background-color: #fff;
}

.site-header li.solution .solution-menu a:hover {
  color: $-color-main;
  background-color: #f5f5f5;
}

.site-header li.solution:hover .solution-menu-wrap {
  display: block;
}
.site-header li.solution:hover .product-menu-wrap {
  display: block;
}
.site-header li.solution:hover .source-menu-wrap {
  display: block;
}

.site-header li.solution .solution-menu-wrap.multiple {
  width: 400px;
  background-color: #fff;
}

.site-header li.solution .solution-menu {
  float: left;
  padding: 0;
}

.site-header .user-info {
  max-width: 124px;
  align-items: center;
}

.site-header .user-info .avatar {
  -ms-flex-negative: 0;
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  margin-right: 12px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.site-header .user-info .user-name {
  display: flex;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.site-header .user-info .user-name > span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.site-header .navbar-toggle-wrap {
  display: none !important;
}

.navbar-collapse .collapse-list {
  display: none;
}
.product-menu-wrap {
  position: absolute;
  background: #fff;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  width: 800px;
  display: none;
  left: -170px;
  .menu-title {
    font-size: 18px;
    padding: 20px 0 30px;
  }
  .menu-item:nth-child(2) {
    border-top: 1px solid #d0d0d0;
  }
  .product-left {
    float: left;
    width: 55%;
    padding: 20px 30px;
    box-sizing: border-box;
    .title {
      font-size: 16px;
      color: #333;
      margin-bottom: 10px;
    }
    a {
      display: block;
      margin-bottom: 25px;
      &:hover {
        .text,
        .title {
          color: $-color-main;
        }
      }
      .text {
        font-size: 13px;
        color: #888;
        margin-bottom: 10px;
      }
    }
    .ssq-app {
      font-size: 14px;
      background: #fff;
      display: flex;
      //   justify-content: space-around;
      padding: 0 0 8px 0;
      box-sizing: border-box;
      vertical-align: middle;
      margin-bottom: 10px;
      align-items: center;
      p {
        font-size: 13px;
        color: #888;
      }
      img {
        width: 60px;
        margin-right: 20px;
      }
    }
  }
  .product-right {
    background-color: #f7f8fc;
    padding: 20px 30px;
    float: left;
    width: 45%;
    .menu-item {
      margin-bottom: 40px;
      .btn {
        width: 90px;
        height: 26px;
        margin-bottom: 6px;
        margin-left: 0;
        font-size: 12px;
      }
    }

    .product-list {
      .list-item {
        font-size: 14px;
        color: #888;
        flex: 1;
        cursor: pointer;
        .title {
          font-size: 16px;
          color: #333;
          margin-bottom: 10px;
          //   margin-top: 10px;
        }
        p {
          color: #888;
          font-size: 13px;
          line-height: 1.5;
        }
      }
      .list-item:hover {
        p {
          color: $-color-main;
        }
      }
      .list-item:last-child {
        margin-top: 25px;
      }
    }
    .product-use {
      img {
        width: 60px;
        height: 60px;
        margin-right: 20px;
      }
      .iconfont {
        font-size: 60px;
        margin: 0 20px 0 0;
        color: $-color-main;
      }
    }
    .ssq-app,
    .ssq-register {
      font-size: 14px;
      background: #fff;
      display: flex;
      justify-content: left;
      box-sizing: border-box;
      vertical-align: middle;
      margin-bottom: 10px;
      align-items: center;
      p {
        font-size: 13px;
        color: #888;
      }
      .btn-text {
        // padding: 8px;
      }
    }
    .btn {
      display: flex;
      position: relative;
      width: 150px;
      height: 35px;
      font-size: 14px;
      align-items: center;
      justify-content: center;
      padding: 0 15px;
      text-align: center;
      transition: all 0.3s ease;
      border: 1px solid transparent;
      border-radius: 36px;
      background-color: $-color-main;
      color: #fff;
      margin-left: 20px;
      &:hover {
        background-color: #fff;
        border: 1px solid $-color-main;
        color: $-color-main;
      }
    }
  }
}
.source-menu-wrap {
  position: absolute;
  background: #f7f8fc;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  width: 800px;
  display: none;
  left: -350px;
  .menu-title {
    font-size: 18px;
    padding: 20px 0 30px;
  }
  .menu-item:nth-child(2) {
    border-top: 1px solid #d0d0d0;
  }
  .source-left {
    background: #fff;
    float: left;
    width: 50%;
    padding: 20px 30px;
    box-sizing: border-box;
    .cource-all {
      display: flex;
      .source-icon-left {
        margin: 8px 10px 0 0;
        .iconfont {
          font-size: 23px;
          color: #b9bed3;
        }
      }
      .title {
        font-size: 16px;
        color: #333;
        margin-bottom: 10px;
      }
      a {
        display: block;
        margin-bottom: 25px;
        &:hover {
          .text,
          .title {
            color: $-color-main;
          }
        }
        .text {
          font-size: 13px;
          color: #888;
          margin-bottom: 10px;
        }
      }
    }

    .ssq-app {
      font-size: 14px;
      background: #fff;
      display: flex;
      //   justify-content: space-around;
      padding: 0 0 8px 0;
      box-sizing: border-box;
      vertical-align: middle;
      margin-bottom: 10px;
      align-items: center;
      p {
        font-size: 13px;
        color: #888;
      }
      img {
        width: 60px;
        margin-right: 20px;
      }
    }
  }
  .source-right {
    // background-color: #fff;
    padding: 20px 30px;
    float: left;
    width: 50%;
    .menu-item {
      margin-bottom: 40px;
      .btn {
        width: 90px;
        height: 26px;
        margin-bottom: 6px;
        margin-left: 0;
        font-size: 12px;
      }
    }

    .product-list {
      .list-item {
        font-size: 14px;
        color: #888;
        flex: 1;
        cursor: pointer;
        margin-bottom: 25px;
        display: flex;
        .list-left {
          margin: 8px 10px 0 0;
          .iconfont {
            font-size: 26px;
            color: #b9bed3;
          }
        }
        .title {
          font-size: 16px;
          color: #333;
          margin-bottom: 10px;
          //   margin-top: 10px;
        }
        p {
          color: #888;
          font-size: 13px;
        }
      }
      .list-item:hover {
        p {
          color: $-color-main;
        }
      }
      .list-item:last-child {
        margin-top: 25px;
      }
    }
    .product-use {
      img {
        width: 60px;
        height: 60px;
        margin-right: 20px;
      }
      .iconfont {
        font-size: 60px;
        margin: 0 20px 0 0;
        color: $-color-main;
      }
    }
    .ssq-app,
    .ssq-register {
      font-size: 14px;
      background: #fff;
      display: flex;
      justify-content: left;
      box-sizing: border-box;
      vertical-align: middle;
      margin-bottom: 10px;
      align-items: center;
      p {
        font-size: 13px;
        color: #888;
      }
      .btn-text {
        // padding: 8px;
      }
    }
    .btn {
      display: flex;
      position: relative;
      width: 150px;
      height: 35px;
      font-size: 14px;
      align-items: center;
      justify-content: center;
      padding: 0 15px;
      text-align: center;
      transition: all 0.3s ease;
      border: 1px solid transparent;
      border-radius: 36px;
      background-color: $-color-main;
      color: #fff;
      margin-left: 20px;
      &:hover {
        background-color: #fff;
        border: 1px solid $-color-main;
        color: $-color-main;
      }
    }
  }
}
@media (max-width: 767px) {
  .star {
    display: inline-block;
    animation: bigTosmall ease 0.5s infinite !important;
  }
  .site-header {
    height: 56px;
    padding: 0 20px;
    .nav-main {
      padding: 0 0px;
    }
  }
  .site-header .nav-brand {
    width: 100px;
    height: 26.26px;
    img {
      width: 100px;
      height: 100%;
    }
  }

  .site-header .nav-main .nav-footer,
  .site-header .nav-main .nav-section {
    display: none;
  }
  .site-header .nav-main .nav-header {
    display: flex;
    align-items: center;
    height: 56px;
  }

  .site-header .navbar-toggle-wrap {
    display: flex !important;
    flex: 1 1 auto;
    align-items: center;
    justify-content: flex-end;
    height: 56px;
    .ssq-button-primary {
      font-size: 14px;
      text-align: center;
      margin-right: 15px;
      height: 30px;
      line-height: 30px;
      width: 140px;
      box-shadow: 0 0 0 0;
    }
    .ssq-button-primary-en {
      width: 150px;
      font-weight: 600;
      font-size: 18px;
    }
  }

  .site-header .navbar-toggle-wrap .navbar-toggle {
    padding: 0px;
    cursor: pointer;
    .iconfont {
      color: $-color-main;
      font-size: 26px;
    }
  }

  .site-header .navbar-toggle-wrap .navbar-toggle img {
    width: 15px;
    height: 12px;
  }

  .navbar-collapse .site-header .navbar-collapse.in,
  .site-header .navbar-collapse.collapsing {
    overflow: visible;
  }

  .navbar-collapse.collapsing {
    overflow-y: hidden;
  }
  .navbar-collapse {
    position: fixed;
    top: 56px;
    width: 100%;
    text-align: left;
    background-color: #fff;
    box-shadow: 0 4px 8px 0 rgba(82, 94, 102, 0.15);
    transition: all 0.5s ease;
    transform: translate3d(0, -130%, 0);
    z-index: 99;
    &.in {
      opacity: 1;
      transform: translate3d(0, 0, 0);
      z-index: 30;
    }
    &:before {
      @include border-1px(#a0a0a0);
    }
    .el-collapse {
      border-top: none;
    }
    .collapse-list .border-item {
      border-bottom: 1px solid #ebeef5;
      font-size: 16px !important;
    }
    .menu-collapse {
      .el-collapse-item__header {
        border: none;
        font-weight: 400;
        font-size: 16px;
        border-bottom: 1px solid #ebeef5;
      }
      .el-collapse-item__header.is-active {
        border-bottom: none;
      }

      .item-content {
        padding-left: 32px;
        a {
          display: block;
          padding: 10px 0;
          color: #383838;
          padding-left: 32px;
          font-size: 14px;
          //   border-bottom: 1px solid #ebeef5;
        }
      }
      .item-content-all {
        // padding-left: 32px;
        display: flex;
        justify-content: space-around;
        a {
          display: block;
          padding: 10px 0;
          color: #383838;
          // padding-left: 32px;
          font-size: 14px;
          //   border-bottom: 1px solid #ebeef5;
        }
      }
    }
  }

  .navbar-collapse .collapse-list {
    display: block;
    padding: 0 32px;
    transition: all 0.3s ease;
    height: auto;
    overflow: scroll;
  }

  .navbar-collapse .collapse-list hr {
    margin: 8px 32px;
  }

  .navbar-collapse .collapse-list > li > a {
    color: #383838;
    display: block;
    padding: 16px 0;
    position: relative;
    font-size: 14px;
    display: flex;
    align-items: center;
    &.split:after {
      @include border-1px(#a0a0a0);
    }
    &.split {
      .loginBtn {
        background-color: #000;
        color: #fff;
        font-size: 16px;
        padding: 5px 10px;
        border-radius: 3px;
        margin-right: 10px;
      }
      .loginBtn-demo {
        background-color: $-color-main;
        color: #fff;
        font-size: 16px;
        padding: 7px 10px;
        border-radius: 3px;
        height: 30px;
        font-weight: 600;
      }
    }
    &.split:active {
      color: #383838;
    }
  }

  .navbar-collapse .collapse-list > li > a.user-info {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    max-width: none;
  }

  .navbar-collapse .collapse-list > li:hover > a {
    color: #3da8f5;
  }
}

@media screen and (min-width: 767px) and (max-width: 1024px) {
  .site-header .nav-footer .phone {
    display: none;
  }
  .site-header .nav-section > li > a {
    padding-left: 10px;
    padding-right: 10px;
  }
}

.transparent .site-header {
  background-color: transparent;
}

.transparent .site-header .nav-brand {
  /*background-image: url(../images/global/logo-white.svg)*/
}

.transparent .site-header .nav-footer > li > a,
.transparent .site-header .nav-section > li > a {
  color: #fff;
}

.transparent .site-header .nav-footer > li > a:hover,
.transparent .site-header .nav-section > li > a:hover {
  color: rgba(255, 255, 255, 0.6);
}

.transparent .site-header .nav-footer > li > a.btn-gradient-blue-link:after {
  content: ' ';
  background-image: none;
  background-color: #fff;
}

.transparent .site-header .nav-footer > li:hover > a.btn-gradient-blue-link {
  color: #3da8f5;
}
</style>
