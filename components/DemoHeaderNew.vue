<template>
  <div class="demo-header">
    <div class="new-demo-container">
      <div class="brand">
        <a
          @click="handleIndex"
          class="nav-brand">
          <img width="140" src="@/assets/images/logo-bestsign.svg" alt="上上签logo，电子合同" v-if="!isMobile">
          <img width="100" src="@/assets/images/logo-bestsign.svg" alt="上上签logo，电子合同" v-if="isMobile">
        </a>
      </div>
      <div class="operation">
        
      </div>
    </div>
  </div>
</template>

<script>
import Qs from 'qs';
import isEmpty from 'lodash/isEmpty';
export default {
  name: 'DemoHeader',
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {
    handleIndex() {
      location.href = '/';
    },
  },
};
</script>

<style scoped lang="scss">
.demo-header {
  width: 100%;
  height: 79px;
  line-height: 79px;
  .new-demo-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    .brand {
      background: #fff;
      width: 50%;
      padding-left: 1%;
      .nav-brand {
        display: flex;
        align-items: center;
        width: 140px;
        height: 79px;
        cursor: pointer;
        img {
          // width: 110px;
        }
      }
    }
    .operation {
      width: 50%;
      padding-right: 1%;
      .login-con {
        float: right;
        .brand-text-login {
          cursor: pointer;
          padding: 0;
          color: #00aa64;
          &.border-left {
            border-left: 1px solid #00aa64;
          }
        }
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .new-demo-container {
    background: #fff;
    // height: 76px;
  }
  .operation {
    background: #fff;
  }
  .demo-header .new-demo-container .brand {
    padding-left: 20px;
    .nav-brand {
      height: 59px;
    }
  }
}
</style>
