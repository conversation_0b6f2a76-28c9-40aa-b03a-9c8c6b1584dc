<template>
  <div class="q-banner">
    <div class="q-header">
      <div class="container">
        <div class="logo">
          <img src="./images/logo.png" alt="">
        </div>
        <div class="menu" v-if="!isMobile">
          <nuxt-link to="/2019qianyinli/qyljchg">
            <span class="menu-item">精彩回顾</span>
          </nuxt-link>
          <nuxt-link to="/2019qianyinli?step=1">
            <span class="menu-item">大赛介绍</span>
          </nuxt-link>
          <!-- <span class="menu-item" @click="onLink">报名及投票</span> -->
          <!-- <nuxt-link to="/2019qianyinli?step=3">
            <span class="menu-item">参赛指引</span>
          </nuxt-link> -->
          <!-- <a href="https://bestsign-1300089911.cos.ap-shanghai.myqcloud.com/%E7%AD%BE%E5%BC%95%E5%8A%9B%E5%A4%A7%E8%B5%9B%E6%A1%88%E4%BE%8B%E6%A8%A1%E6%9D%BF.doc" class="menu-item">案例模板下载</a> -->
          <nuxt-link to="/2019qianyinli/result">
            <span class="menu-item">投票结果</span>
          </nuxt-link>
        </div>
        <div class="mobile-menu" v-if="isMobile" @click="menu = !menu">
          <span>{{ menu ? '关闭' : '菜单'}}</span>
        </div>
        <div class="menu-item-wrap" :class="{'open': menu}" v-if="isMobile">
          <nuxt-link to="/2019qianyinli/qyljchg">
            <span class="menu-item">精彩回顾</span>
          </nuxt-link>
          <nuxt-link to="/2019qianyinli?step=1">
            <span class="menu-item">大赛介绍</span>
          </nuxt-link>
          <!-- <span class="menu-item" @click="onLink">报名及投票</span> -->
          <!-- <nuxt-link to="/2019qianyinli?step=3">
            <span class="menu-item">参赛指引</span>
          </nuxt-link> -->
          <!-- <a href="https://bestsign-1300089911.cos.ap-shanghai.myqcloud.com/%E7%AD%BE%E5%BC%95%E5%8A%9B%E5%A4%A7%E8%B5%9B%E6%A1%88%E4%BE%8B%E6%A8%A1%E6%9D%BF.doc" class="menu-item">案例模板下载</a> -->
          <nuxt-link to="/2019qianyinli/result">
            <span class="menu-item">投票结果</span>
          </nuxt-link>
        </div>
      </div>
    </div>
    <div class="sologan">
      <img src="./images/sologan.png" alt="">
    </div>
    <div class="qrcode" v-if="!isMobile">
      <img src="./images/qrcode.svg" width="120" alt="">
    </div>
  </div>

</template>

<script>
export default {
  name: 'QBanner',
  props: {
    active: String,
  },
  data: () => ({
    menu: false,
  }),
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {
    onLink() {
      window.open('http://qyl123.v.vote8.cn', '_blank');
    },
  },
};
</script>

<style scoped lang="scss">
.q-banner {
  width: 100%;
  height: 38vw;
  background: url(./images/banner.jpg) no-repeat;
  background-size: 100% 100%;
  overflow: hidden;
  position: relative;
}
.sologan {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate3d(-50%, -50%, 0);
  img {
    width: 32vw;
  }
}
.qrcode {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 99;
}
.q-header {
  width: 100%;
  height: 100px;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 0 16px;
  .container {
    width: 100%;
    height: 100%;
    max-width: 1260px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .logo img {
    width: 22vw;
  }
  .menu {
    &-item {
      position: relative;
      display: inline-block;
      font-size: 16px;
      color: #fff;
      margin-left: 30px;
      padding: 18px 0;
      cursor: pointer;
      &:after {
        position: absolute;
        left: 0;
        bottom: 0;
        content: '';
        width: 100%;
        height: 3px;
        background: transparent;
      }
      &:hover,
      &.is-active {
        color: #00aa64;
        &:after {
          background: linear-gradient(to right, #00aa64, #15acff);
        }
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .q-banner {
    height: 40vh;
  }
  .q-header {
    height: 60px;
    .logo img {
      width: 40vw;
    }
  }
  .menu-item-wrap {
    position: absolute;
    width: 30vw;
    height: 100vh;
    right: calc(-30vw - 16px);
    top: 60px;
    background-color: rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
    transform: translateX(0);
    .menu-item {
      color: #fff;
      width: 100%;
      height: 40px;
      line-height: 40px;
      display: block;
      margin: 0;
      padding: 0;
      text-align: center;
      font-size: 14px;
    }
    &.open {
      transform: translateX(-100%);
    }
  }
  .mobile-menu {
    color: #fff;
    padding: 12px 16px;
    font-size: 14px;
  }
  .sologan {
    img {
      width: 50vw;
    }
  }
}
@media screen and (min-width: 767px) and (max-width: 1024px) {
  .q-header {
    height: 60px;
  }
  .menu-item-wrap {
    .menu-item {
      margin-left: 20px;
      &:after {
        background: transparent;
      }
    }
  }
  .q-header {
    .menu {
      &-item {
        &:after {
          background: transparent;
        }
        &:hover,
        &.is-active {
          &:after {
            background: transparent;
          }
        }
      }
    }
  }
  .mobile-menu {
    display: none;
  }
}
</style>
