<template>
  <div class="search-header">
    <!-- <h1>
		<nuxt-link to="/help/FAQ">帮助中心</nuxt-link>
	</h1>
    <el-input v-model="search" class="search-input" placeholder="搜索" @keyup.enter.native="handleSearch" v-if="searchShow">
      <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
    </el-input> -->
	<section class="section section-1 banner">
      <div class="container">
        <div class="input-wrap">
          <h1 class="article-headline">请问需要什么帮助？</h1>
          <div class="help-search">
			<el-input v-model="search" class="search-input" placeholder="搜索" @keyup.enter.native="handleSearch" v-if="searchShow">
				 <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
			</el-input>
          </div>
        </div>
      
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'Document<PERSON>eader',
  props: {
    searchShow: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      search: '',
    };
  },
  methods: {
    handleSearch() {
      this.$router.push({
        path: '/help/FAQ/search',
        query: {
          content: this.search,
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.search-header {
  position: relative;
  .container {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  h1 {
    font-size: 2.25rem;
    font-weight: 200;
    margin-bottom: 4.375rem;
    text-align: center;
    color: #fff;
  }
  a {
    color: #333;
  }
  .cancel {
    float: right;
    margin-right: 24px;
    line-height: 36px;
    cursor: pointer;
  }
  .input-wrap {
    padding-top: 4rem;
    text-align: center;
    color: #fff;
  }
  .help-search {
    min-width: 600px;
  }
  .banner {
    width: 100%;
    height: 25vw;
    background-size: cover;
    background-image: url(~assets/images/help/help-banner.jpg);
    background-position: 0 75%;
    background-repeat: no-repeat;
    font-size: 1.25vw;
  }
  /deep/ .el-input__inner:focus {
    border: none;
  }
}
@media (max-width: 768px) {
  .search-header {
    position: relative;
    padding: 8px 0;
    .banner {
      height: 67vw;
    }
    .help-search {
      min-width: auto;
      width: 100%;
    }
    h1 {
      display: inline-block;
      font-size: 24px;
      line-height: 36px;
    }
    .search-input {
      float: right;
      width: 100%;
    }
    .cancel {
      float: right;
      margin-right: 24px;
      line-height: 36px;
      cursor: pointer;
    }
  }
}
</style>
