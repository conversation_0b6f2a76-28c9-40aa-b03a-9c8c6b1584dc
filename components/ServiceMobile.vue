<template>
  <article class="help-service help-service__mobile">
    <header>
      <div class="nav-item item-1">
        <nuxt-link to="/help">帮助中心</nuxt-link>
      </div>
      <div class="nav-item"><i class="el-icon-arrow-right"></i>{{ text }}</div>
    </header>
    <el-collapse  v-model="activeNames" @change="handleChange">
      <el-collapse-item :title="title[0]" name="0" class="collapse-item-1">
        <p>我们24小时在线响应你的需求，并提供多种服务方式。</p>
        <div class="content">
          <div class="item">
            <img src="@/assets/images/icon/<EMAIL>">
            <div>
              <p>在线客服</p>
              <span>点击咨询</span>
            </div>
          </div>
          <div class="item">
            <img src="@/assets/images/icon/<EMAIL>">
            <div>
              <p>服务热线</p>
              <span><phonecall phoneNumber="+86 ************" :showDialog="true"></phonecall></span>
            </div>
          </div>
          <div class="item">
            <img src="@/assets/images/icon/<EMAIL>">
            <div>
              <p>企业邮箱</p>
              <span><a href="mailto:<EMAIL>"><EMAIL></a></span>
            </div>
          </div>
        </div>
      </el-collapse-item>
      <el-collapse-item :title="title[1]" name="1" class="collapse-item-2">
        <p>我们提供标准化的服务流程，确保问题可以得到及时满意的解决。</p>
        <div class="img-wrap">
          <img src="@/assets/images/help/help@<EMAIL>">
        </div>
      </el-collapse-item>
      <el-collapse-item :title="title[2]" name="2" class="collapse-item-3">
        <p>我们准备了多元化的服务，满足不同场景下电子签约需求。</p>
        <div class="content">
          <div class="item">
            <div class="img img-1"></div>
            <span>需求反馈</span>
          </div>
          <div class="item">
            <div class="img img-2"></div>
            <span>问题解析</span>
          </div>
          <div class="item" style="margin-left: -1vh;">
            <div class="img img-3"></div>
            <span>使用培训</span>
          </div>
          <div class="item" style="margin-left: 1vh;">
            <div class="img img-4"></div>
            <span>现场技术支持</span>
          </div>
          <div class="item">
            <div class="img img-5"></div>
            <span>紧急救援服务</span>
          </div>
          <div class="item">
            <div class="img img-6"></div>
            <span>协助二次开发</span>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </article>
</template>

<script>
export default {
  name: 'ServiceMobile',
  data() {
    return {
      title: ['人工服务', '服务流程', '服务内容'],
      page: this.$route.query.page || 'human',
      text: '-',
      activeNames: [],
    };
  },
  created() {
    let initIndex;
    if (this.page === 'human') {
      initIndex = 0;
    } else if (this.page === 'process') {
      initIndex = 1;
    } else {
      initIndex = 2;
    }
    this.text = this.title[initIndex];
    this.activeNames = [initIndex.toString()];
  },
  methods: {
    handleChange(e) {
      const index = this.activeNames[this.activeNames.length - 1];
      this.text = this.title[index];
    },
  },
};
</script>

<style lang="scss">
.help-service.help-service__mobile {
  header {
    padding: 0 26px;
    height: 52px;
    line-height: 52px;
    background-color: #07c3ab;
    font-size: 14px;
    display: flex;
    .nav-item {
      color: #ffffff;
      a {
        color: #ffffff;
      }
      i {
        margin: 0 10px;
      }
    }
  }
  .el-collapse-item {
    color: #323232;
    font-size: 12px;
    .el-collapse-item__header {
      padding: 0 26px;
      font-size: 14px;
      &.is-active {
        background-color: #eeeeee;
        color: #118375;
      }
    }
    .el-collapse-item__wrap {
      padding: 45px 25px;
    }
    .el-collapse-item__content {
      padding: 0;
      > p {
        margin-bottom: 38px;
      }
    }
    &.collapse-item-1 {
      .item {
        display: flex;
        flex-direction: row;
        &:not(:last-child) {
          margin-bottom: 26px;
        }
        img {
          width: 40px;
          height: 40px;
          margin-right: 23px;
        }
      }
    }
    &.collapse-item-2 {
      img {
        width: 100%;
        max-height: 541px;
      }
    }
    &.collapse-item-3 {
      .content {
        display: flex;
        flex-wrap: wrap;
        .item {
          margin-bottom: 44px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: calc(100% / 2);
          .img {
            margin-right: 26px;
            background-image: url(~assets/images/help/<EMAIL>);
            transform: scale(0.5);
          }
          .img-1 {
            width: 35px;
            height: 36px;
            background-position: -7px -318px;
          }
          .img-2 {
            width: 37px;
            height: 37px;
            background-position: -47px -319px;
          }
          .img-3 {
            width: 51px;
            height: 37px;
            background-position: -90px -318px;
          }
          .img-4 {
            width: 27px;
            height: 38px;
            background-position: -152px -318px;
          }
          .img-5 {
            width: 40px;
            height: 38px;
            background-position: -186px -319px;
          }
          .img-6 {
            width: 39px;
            height: 39px;
            background-position: -236px -319px;
          }
        }
      }
    }
  }
}
</style>
