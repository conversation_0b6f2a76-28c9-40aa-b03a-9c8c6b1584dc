<template>
  <div class="phonecall">
    <a :href="numString">{{ phoneNumber }}</a>
    <!--<span @click="handleOpen" v-else>{{ phoneNumber }}</span>-->
    <!--<el-dialog v-if="showDialog && !isIos" :visible.sync="visible" width="90%" :append-to-body="true">-->
      <!--确定拨打当前电话{{ phoneNumber }}吗？-->
      <!--<span slot="footer" class="dialog-footer">-->
        <!--<el-button @click="visible = false">取 消</el-button>-->
        <!--<el-button style="background-color: #00a664;border-color: #00a664" type="primary" @click="visible = false"><a :href="numString" style="color: #fff;">确 定</a></el-button>-->
      <!--</span>-->
    <!--</el-dialog>-->
  </div>
</template>

<script>
export default {
  name: 'Phonecall',
  props: {
    showDialog: {
      type: Boolean,
      default: false,
    },
    phoneNumber: {
      required: true,
      type: String,
      default: '',
    },
  },
  data() {
    return {
      visible: false,
      numString: `tel:${this.phoneNumber}`,
    };
  },
  computed: {
    isIos() {
      return this.$store.state.isIos;
    },
  },
  methods: {
    handleOpen() {
      if (this.showDialog) {
        this.visible = true;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.phonecall {
  display: inline-block;
  a {
    color: #323232;
  }
}
</style>
