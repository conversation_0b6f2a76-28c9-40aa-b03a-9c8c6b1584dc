* {
  // 去除ios 按钮按下的默认高亮效果
  -webkit-tap-highlight-color : transparent;
}
img[name=seal] {
  display: none;
}
input, button {
  outline: none;
}
*:not(input, textarea) {
  // -webkit-user-select: none;
}
table .cell{
  -webkit-user-select: all !important;
}

.section {
  width: 100%;
  padding:  0  1.5rem 5rem;
//   padding:9rem 1.5rem;
  &.is-full {
    padding: 0;
  }
  &.is-small {
    padding: 3rem 1.5rem;
  }
  &.is-tiny {
    padding-top: 1.5rem;
  }
  &.is-large {
    padding: 18rem 1.5rem;
  }
}
.headline {
  font-size: 2rem;
  font-weight: normal;
  &--large {
    font-size: 3.125rem;
    font-weight: 200;
  }
  &--main {
    font-size: 2.25rem;
    font-weight: 200;
  }
  &--main__sub {
    font-size: 1.3125rem;
    font-weight: 300;
  }
  &--sub {
    font-size: 1.0625rem;
    font-weight: 300;
  }
  &--num {
    font-size: 2.8125rem;
    font-weight: normal;
    font-family: source-han-sans-simplified-c, sans-serif;
  }
}
.comment {
  font-size: 0.75em;
  font-weight: normal;
}
.container {
  margin: 0 auto;
  position: relative;
}

.article-headline {
  text-align: center;
    font-size: 2.2rem;
    font-weight: 500;
    color: #090909;
}
.article-subtitle {
  // color: #888;
  margin: 1.75rem auto;
  max-width: 700px;
  line-height: 28px;
  font-size: 16px;
  text-align: center;
}
.section-1{
  padding: 5rem 0 2rem;
}
.section-1 .container .article{
	padding-left: 0!important;
}
.section-headline {
    color: #090909;
    font-size: 2rem;
    line-height: 2.5rem;
	font-weight: 450;
	padding:4.5rem 0;
  text-align: center;
	// padding: 70px 0 40px
  width: 80%;
  margin: 0 auto;
}
.section-headline-en{
  color: #090909;
  text-align: center;
  padding:4.5rem 0;
  font-weight: 600;
  font-size: 2.5rem;
  line-height: 1.5;
}
.section-subtitle {
  max-width: 700px;
  color: #888;
  line-height: 22px;
  margin: 0 auto;
 
}
.ssq-button-more{
	color:#00a664
}
.ssq-button-primary {
  width: 120px;
  height: 40px;
  min-width: 76px;
  font-size: 16px;
  font-weight: 500;
  padding: 0 12px;
  margin: 0;
  color: #fff;
  vertical-align: top;
  border: none;
  border-radius: 3px;
  line-height: 40px;
  outline: 0;
  box-sizing: border-box;
  transition: all .3s ease;
  user-select: none;
  position: relative;
  background: $-color-main;
  box-shadow: 0 2px 8px 0 rgba(36, 10, 147, .15);
  cursor: pointer;
  &:hover,
  &:active {
    box-shadow: 0 6px 18px 0 rgba(36, 10, 147, .15)
  }
  a {
    color: inherit;
  }
  &.is-white {
    background: #fff;
    color: $-color-main;
    border: 1px solid $-color-main;
    &:hover {
      background: $-color-main;
      color: #fff;
    }
  }
  &.is-text {
    background: transparent;
    box-shadow: none;
    color: #00a664;
    &:hover,
    &:active {
      box-shadow: none;
      color: #00a664;
    }
  }
  &.is-no-border {
    border: none;
  }
  &.transparent {
    background: transparent;
    border: 1px solid #00a664;
    color: #00a664;
  }
}
.h5-demo-button {
  display: inline-block;
  width: 120px;
  text-align: center;
  height: 40px;
  line-height: 40px;
  background-color: #00a664;
  border-radius: 40px;
  font-size: 16px;
  color: #fff;
  cursor: pointer;
  font-weight: 500;
  
}

.ssq-input {
  font-variant: tabular-nums;
  box-sizing: border-box;
  margin: 0 20px 0 0;
  list-style: none;
  position: relative;
  display: inline-block;
  padding: 4px 30px;
  width: 390px;
  height: 50px;
  line-height: 1.5;
  color: #bdbdbd;
  background-color: transparent;
  background-image: none;
  border: 1px solid #aaa;
  border-radius: 50px;
  transition: all 0.3s;
  vertical-align: middle;
  &:hover {
    border-color: #00a664;
    border-right-width: 1px !important;
  }
  &:focus {
    border-color: #00a664;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(2, 156, 139, 0.2);
    border-right-width: 1px !important;
  }
}


.card {
  box-sizing: content-box;
  width: 21.42rem;
  height: 20rem;
  margin: 15px auto;
//   border: 1px solid #ebeef5;
  background-color: #fff;
  color: #323232;
  transition: .3s;
  border-radius: 2px;
  overflow: hidden;
  // box-shadow: 0 0px 5px 0 #cccbcb;
  display: flex;
  flex-direction: column;
  border-radius: 10px;
  &:hover {
    transform: translate3d(0, -4px, 0);
    box-shadow: 0 8px 15px 0 rgba(82, 94, 102, 0.15);
  }
  .card-image {
    width: 100%;
    // height: 10rem;
    height: 11.45rem;
  }
  .card-content {
    height: 10rem;
    padding: 1rem;
    width: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    .header {
      font-size: 17px;
      font-weight: 400;
    }
    .body {
      color: #6e6e6e;
      line-height: 1.5rem;
      font-size: 14px;
      text-align: justify;
    }
    .footer {
      width: 100%;
      color: #6e6e6e;
      font-size: 14px;
      .time {
        float: right;
      }
    }
  }
}
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-justify {
  text-align: justify;
}

#udesk_btn_text {
  display: none;
}


@media screen and (min-width: 769px){
  body {
	overflow-x: hidden;
  }
}

/*desktop*/
@media screen and (min-width: 1088px){

  .container {
    max-width: 1000px;
    width: 1000px;
  }
}
/*widescreen*/
@media screen and (min-width: 1280px){
  .container {
    max-width: 1152px;
    width: 1152px;
  }
}
/*fullhd*/
@media screen and (min-width: 1472px){
  .container {
    max-width: 1344px;
    width: 1344px;
  }
}
@media screen and (max-width: 767px){
  .ssq-input {
    width: 100%;
  }
  .section {
    padding: 0 10px 40px;
  }
  .abstract{
	  a{
		  li{
			  font-size: 14px!important;
		  }
	  }
  }
  
    .section-headline {
      color: #090909;
      font-size: 1.9rem;
      line-height: 1.5;
      font-weight: 400;
      width: 100%;
    }
    .section-headline-en {
      font-weight: 600;
    }
  .container {
    padding: 0 5%;
    position: static;
  }
  .section-1 {
	.container{
		.desc {
			text-align: left;
			color: #86868b;
		}
		.article{
			padding: 0px 0px 80px!important;
			h1{
				// font-weight: 400!important;
				// color: #090909!important;
			}
		}
	} 
  }
  nav{
	  .nav-item{
		  font-size: 14px;
		  color: #86868b;
		  line-height: 24px;
	  }
  }
  .card {
    box-sizing: content-box;
    width: 100%;
    height: auto;
    margin: 40px 0 0 0;
    border: 1px solid #ebeef5;
    background-color: #fff;
    color: #323232;
    transition: .3s;
    border-radius: 2px;
    overflow: hidden;
    box-shadow: 0 0px 0px 0 #cccbcb;
    display: flex;
    flex-direction: column;
    border-radius: 10px;
    &:hover {
      transform: translate3d(0, -4px, 0);
      box-shadow: 0 8px 15px 0 rgba(82, 94, 102, 0.15);
    }
    .card-image {
      width: 100%;
      height: auto;
    }
    .card-content {
      height: 12rem;
      padding: 1rem;
      width: 100%;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: flex-start;
      .header {
        font-size: 17px;
        font-weight: 400;
      }
      .body {
        color: #6e6e6e;
        line-height: 1.5rem;
        font-size: 14px;
        text-align: justify;
      }
      .footer {
        width: 100%;
        color: #6e6e6e;
        font-size: 14px;
        .time {
          float: right;
        }
      }
    }
  }
}
.swiper-container {
  .ssq-button-prev,
  .ssq-button-next {
    position: absolute;
    z-index: 99;
    width: 28px;
    height: 28px;
    line-height: 28px;
    background: #dcdcdc;
    border-radius: 28px;
    top: 50%;
    transform: translateY(-50%);
    &:focus {
      outline: none;
    }
    .i {
      color: #fff;
    }
  }
  .ssq-button-prev {
    left: 10px;
  }
  .ssq-button-next {
    right: 10px;
  }
}

@mixin border-1px($color) {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  background: $color;
  width: 100%;
  height: 1px;
  transform: scaleY(0.5);
  transform-origin: 0 0;
}
$primary-color: #00aa64;

// .el-pager li.active,
.el-pagination.is-background .el-pager li:not(.disabled):hover,
.el-pagination.is-background button:hover{
  color: #000;
}
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: #000;
  color: #fff;
}
.el-pager li {
  font-family: 'source-han-sans-simplified-c';
}

.braft-output-content{

  p{
    min-height: 1em;
  }

  .image-wrap img{
    max-width: 100%;
    height: auto;
  }
  img{
    max-width: 100%;
    height: auto;
  }

  ul, ol{
    margin: 16px 0;
    padding: 0;
  }

  blockquote{
    margin: 0 0 10px 0;
    padding: 15px 20px;
    background-color: #f1f2f3;
    border-left: solid 5px #ccc;
    color: #666;
    font-style: italic;
  }

  pre{
    max-width: 100%;
    max-height: 100%;
    margin: 10px 0;
    padding: 15px;
    overflow: auto;
    background-color: #f1f2f3;
    border-radius: 3px;
    color: #666;
    font-family: monospace;
    font-size: 14px;
    font-weight: normal;
    line-height: 16px;
    word-wrap: break-word;
    white-space: pre-wrap;
    pre{
      margin: 0;
      padding: 0;
    }
  }
}
.article .video-wrap video{
	width: 100%;
	height: 100%;
}
.float-help{
	right:0!important;
	#notShowUdesk,.item{
		border-top-right-radius: 0!important;
		border-bottom-right-radius: 0!important;
	}

}
nav{
	.main-title{
		font-weight: 400;
		margin-top:20px;
	}
}
#body{
  font-family: 'MS UI Gothic';
}