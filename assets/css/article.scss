.article-change {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #f1f1f1;
  padding-top: 20px;
}
nav {
  margin-top: 25px;
  position: relative;
  text-align: center;
  .card-content {
    padding: 24px 20px;
    height: 135px;
    p {
      text-align: left;
    }
    .footer {
      align-self: flex-end;
    }
  }
  .el-row {
    display: flex;
    // justify-content: space-around;
    flex-wrap: wrap;
  }
  .nav-item {
    width: 48%;
    cursor: pointer;
    padding: 10px 0;
    &:first-child {
      margin-right: 2%;
    }
    &:last-child {
      margin-left: 2%;
    }
    &:hover {
      color: #00a664;
    }

    .last,
    .last-text {
      text-align: left;
    }
    .next {
      text-align: right;
    }
    .last,
    .next {
      font-size: 12px !important;
      color: #828282;
      font-size: 0.9rem;
      margin-bottom: 10px;
    }
    .last-text {
      font-size: 14px;
      // letter-spacing: 0;
      line-height: 20px;
      overflow: hidden;
      // text-overflow: ellipsis;
      white-space: normal;
      /* autoprefixer: off */
      -webkit-box-orient: vertical;
      display: -webkit-box;
      /* autoprefixer: on */
      // -webkit-line-clamp: 1;
      // max-width: 98%;
      height: 18px;
    }
    .next-text {
      display: flex;
      justify-content: flex-end;
      font-size: 14px;
      // letter-spacing: 0;
      line-height: 20px;
      overflow: hidden;
      // text-overflow: ellipsis;
      white-space: normal;
      /* autoprefixer: off */
      -webkit-box-orient: vertical;
      // display: -webkit-box;
      /* autoprefixer: on */
      // -webkit-line-clamp: 1;
      // max-width: 98%;
      height: 18px;
      text-align: right;
    }
  }
  .change-card {
    color: #00a664;
  }
  .back {
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 10px;
    .el-icon-arrow-left {
      padding-right: 5px;
      font-size: 22px;
      position: relative;
      top: 2px;
    }
    &:hover {
      color: #00a664;
    }
  }
}
.container {
  display: flex;
  max-width: 1344px;
  .article {
    flex: 1;
    margin: 20px 0 50px;
  }
  #content {
    padding-bottom: 45px;
    //   border-bottom: 1px solid #e5e5e5;
  }
  h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    line-height: 1.4;
    color: #333333;
    font-weight: 500;
  }
  .text-tag{
    display: flex;
    margin:20px 10%;
    width: 80%;
    .text-tag-item{
      margin: 10px 7px 7px 0;
      padding: 7px;
      background: #f5f5f5;
      border-radius: 5px;
      font-size: 0.9rem;
      color: #767676;
    }
  }
  .imgContainer{
    img{
      width: 100%;
      border-radius: 10px;
    }
  }
  .descContainer{
    width: 80%;
    margin: 5rem 10%;
  }
  .bigTitle{
    width: 80%;
    margin: 3rem 10%;
  }
  .bigTitle-en{
    font-weight: 600;
  }
  .imgLogo{
    width: 80%;
    margin: 50px 10%;
    img{
      width: 200px;
    }
  }
  .descContainer{
    .title{
      font-size: 1.3rem;
      color: #000;
      font-weight: 500;
      margin: 20px 0;
    }
    .content{
      line-height: 1.5;
      color: #767676;
    }
    .little-title{
      font-weight: 500;
      color: #181f28;
      margin: 20px 0;
    }
  }
  .descContainer-en{
    .title{
      font-weight: 600;
    }
    .content{
      font-size: 1.1rem;
    }
    .little-title{
      font-weight: 600;
    }
  }


}


@media screen and (max-width: 767px){
  .container{
      .article{
        background-color: #fafbfc;
        .article-side {
          display: none;
        }
        h1 {
          font-size: 24px;
          color: #515151;
          font-weight: 200;
          margin: 0;
          line-height: 35px;
          width: 100%;
        }
        .operate {
          display: block;
          .left-downLoad {
            margin-top: 10px;
            margin-left: 0;
            width: 72px;
          }
          .left {
            margin-left: 0;
            margin-top: 10px;
            .left-downLoad-1 {
              margin-left: 0;
            }
            .left-join {
              // height: 30px;
              // line-height: 30px;
              margin-left: 0;
            }
            .left-review {
              // height: 30px;
              // line-height: 30px;
              
            }
            
          }
        }
        .text-tag{
          .text-tag-item{
            font-size: 14px;
          }
        }
        .imgLogo{
          img{
            width: 130px;
          }
        }
        .descContainer{
          margin: 3rem 10%;
        }
      }
    }
  

}

