const resRules = {
  number: /^\d*$/, //纯数字，用于判断 docmentId 等
  // pass: /^[a-zA-Z0-9]{6,18}$/, //6-18位的数字，字母，或者数字字母组合, 密码的校验规则
  // pass: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,18}$/, //6-18位数字字母组合
  pass: /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z]).{6,18}$/, //至少1个大写字母，1个小写字母和1个数字,不能包含特殊字符
  signpass: /^[0-9]{6}$/, //6位的数字

  invitationCode: /^[0-9]{6}$/, //6位的数字

  // companyName: /^(([\u4e00-\u9fa50-9\（\）\(\)]{1,60})|(?![\u4e00-\u9fa5]+$)(?![a-zA-Z\（\）\(\)]+$)[\u4e00-\u9fa5A-Za-z0-9\（\）\(\)]{1,60})$/, // 只能用汉字、英文或（）；必须包含汉字。否则提示“请填写真实的企业名称
  companyName: /^.{1,60}$/,

  userName: /^(([a-zA-Z\s\·]{2,32})|([\u4e00-\u9fa5\s\·]{2,32}))$/, // 不能是英文和中文的组合，不能有标点，可以有空格和原点，不区分大小写，最长不超过32个字符

  userNameIgnore: /^(([a-zA-Z\s\·\*]{2,32})|([\u4e00-\u9fa5\s\·\*]{2,32}))$/, // 允许*的用户名
  // 旧的不能识别com.cn

  // userAccount: /^1[0-9]{10}|[a-zA-Z0-9]+([._\\-]*[a-zA-Z0-9])*@([a-zA-Z0-9]*[-a-zA-Z0-9]*){1,63}\.[a-zA-Z0-9]+$/,
  userAccount: /(^1[0-9]{10}$)|(^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-_]+(\.[a-zA-Z0-9-_]+)*\.[a-zA-Z0-9-_]{2,6}$)/,

  // userPhone: /0?(1[0-9])[0-9]{9}$/,
  userPhone: /^1[0-9]{10}$/,
  phone: /^[0-9]{10,11}$/, //日本的电话规则
  enPhone: /^\d{5,}$/, //英文版的电话校验

  // userEmail: /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
  userEmail: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-_]+(\.[a-zA-Z0-9-_]+)*\.[a-zA-Z0-9-_]{2,6}$/,
  email: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-_]+(\.[a-zA-Z0-9-_]+)*\.[a-zA-Z0-9-_]{2,6}$/,

  phoneVerifyCode: /^\d{6}$/,

  imageVerifyCode: /^\d{4}$/,

  loginCode: /^[a-zA-Z0-9]{6,18}$/, //6-18位的数字，字母，或者数字字母组合

  signCode: /^\d{6}$/, //6位数字

  // IDCardReg: /^(\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x))$/,

  IDCardReg: /(^([1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2})$)/,

  uniSocCreditCode: /[^_IOZSVa-z\W]{2}\d{6}[^_IOZSVa-z\W]{10}/g,
  dateFormOne: /(^[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$)|长期/, //'2012-09-09'
  specialSymbol: /[~!#$%^&*()+={}<>?\/\\,';:`]/, // 筛选包含特殊字符的账号姓名
  numberLabelCode: /^[-\.\d]*[-\.\d]*[-\.\d]*$/, // 数字标签
};

export default resRules;
