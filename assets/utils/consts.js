// ICP备案信息
export const BeiAn = {
  copyRight: 'Copyright © 2014-2025 杭州尚尚签网络科技有限公司',
  WangAn: '浙公网安备 33010602006436号',
  ICPInfo: '浙ICP备14031930号-3',
  ICPCn: '浙ICP备14031930号',
  ICPLink: 'https://beian.miit.gov.cn',
};

// 根据 BASE_URL 判断 ICP 备案号，只判断预发info和线上cn
export function handleICPEnv() {
  let ICPEnv = BeiAn.ICPCn;
  if (process.env.baseUrl && process.env.baseUrl.includes('.bestsign.info')) {
    ICPEnv = BeiAn.ICPInfo;
  }
  return ICPEnv;
}

export const JOB_CITIES = {
  hz: '杭州',
  bj: '北京',
  sz: '深圳',
  sh: '上海',
  cd: '成都',
  gz: '广州',
  nj: '南京',
  gy: '贵阳',
  cq: '重庆',
  sy: '沈阳',
  wh: '武汉',
  szo: '苏州',
};
export const JOB_TEXT_MAP = {
  all: '全部',
  tech: '产研',
  success: '客户成功',
  sale: '销售',
  market: '市场',
  hr: '人力资源',
  legal: '公关',
  om: '运营',
  solution: '解决方案',
};
