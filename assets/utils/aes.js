const CryptoJs = require('crypto-js');
// const utf8 = require('crypto-js/enc');
const key = CryptoJs.enc.Utf8.parse('44eb2e88a84656e756ec397bd2f18a7d');

function aes(code, target) {
  if (process.client) {
    return window.encodeURIComponent(
      CryptoJs.AES.encrypt(`${code}:${target}`, key, {
        mode: CryptoJs.mode.ECB,
        padding: CryptoJs.pad.Pkcs7,
      }).toString()
    );
  }
  return '';
}

function encode(params) {
  const string = JSON.stringify(params);
  if (process.client) {
    return window.encodeURIComponent(
      CryptoJs.AES.encrypt(string, key, {
        mode: CryptoJs.mode.ECB,
        padding: CryptoJs.pad.Pkcs7,
      }).toString()
    );
  }
  return '';
}
function demo(params) {
  const string = JSON.stringify(params);
  const bytes = CryptoJs.AES.decrypt(string, key);
  // const plaintext = bytes.toString(CryptoJS.enc.Utf8);
  return bytes.toString();
}
export { encode, demo };
export default aes;
