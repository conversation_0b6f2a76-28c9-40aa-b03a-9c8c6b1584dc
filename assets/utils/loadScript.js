/**
 * @description 异步加载js
 * @param scriptUrl js url
 */
export default function loadScript(scriptUrl) {
  if (!scriptUrl) {
    throw new Error('loadScript function no params scriptUrl');
  }
  // 是否已经加载过
  const isHasLoad = [].slice
    .call(document.querySelectorAll('script'))
    .some(item => {
      return item.getAttribute('src') === scriptUrl;
    });
  if (isHasLoad) {
    return Promise.resolve();
  }
  const script = document.createElement('script');
  return new Promise((resolve, reject) => {
    script.onload = () => {
      resolve();
    };
    script.onerror = () => {
      const parent = script.parentElement;
      if (parent) {
        parent.replaceChild(script);
      }
      reject();
    };
    script.src = scriptUrl;
    document.body.appendChild(script);
  });
}
