export default function({ route, app, store, req }) {
  const routeLanguage = route.params.lang || 'en';
  if (process.server) {
    req.headers['accept-language'] = routeLanguage;
    const useLanguage = req.headers['accept-language'];
    store.commit('SET_LANGUAGE', useLanguage);
    app.i18n.locale = useLanguage;
  }
  if (process.client) {
    const useLanguage = routeLanguage;
    store.commit('SET_LANGUAGE', useLanguage);
    app.i18n.locale = useLanguage;
  }
}
