export default function(context) {
  context.userAgent = process.server
    ? context.req.headers['user-agent']
    : navigator.userAgent;
  /* 年度报表的旧路由重新引导至新路由 */
  /*if (
    [
      '/page/share/annualShareCount',
      '/page/share/annual',
      '/page/share/annualPdf',
    ].includes(context.route.path)
  ) {
    const url = context.route.fullPath.replace('/page', '');
    context.redirect(url);
    return;
  }*/
  // 产品 价格 关于我们 引到新路由  在页面代码里面的路由/roduct/function和/product-function 都跳转product-function
  if (
    [
      '/product/function',
      '/product/judicial',
      '/product/check',
      '/product/download',
      '/product/price',
    ].includes(context.route.path)
  ) {
    const productUrl = context.route.fullPath.replace('product/', 'product-');
    context.redirect(301, productUrl);
    return;
  }
  if (['/about/about-us', '/about/join-us'].includes(context.route.path)) {
    const aboutUrl = context.route.fullPath.replace('/about/', '/');
    context.redirect(301, aboutUrl);
    // return;
  }
  // 2019年亲引力大赛做301跳转
  // if (['/qianyinli/result'].includes(context.route.path)) {
  //   context.redirect(301, '/2019qianyinli/result');
  //   // return;
  // }
  if (['/qianyinli', '/qianyinli/result'].includes(context.route.path)) {
    const qylUrl = context.route.fullPath.replace('qianyinli', '2019qianyinli');
    context.redirect(301, qylUrl);
    // return;
  }
  if (['/qianyinli/2019qyljchg'].includes(context.route.path)) {
    context.redirect(301, '/2019qianyinli/qyljchg');
    // return;
  }
  // wiki case news 的详情页引导至新路由
  const whiteList = ['wiki', 'case', 'news'];
  const firstPath = context.route.fullPath.split('/')[1];
  // console.log(firstPath);
  if (whiteList.includes(firstPath)) {
    const { params } = context;
    // console.log(params);
    if (params.hasOwnProperty('category') && params.category.match(/^\d/)) {
      context.redirect(301, `/${firstPath}/detail/${params.category}`);
    }
  }
  // wiki 的栏目分类路由跳转至新路由
  const wikiPath = context.route.fullPath.split('/')[1];
  if (wikiPath.indexOf('wiki') > -1) {
    const { params } = context;
    // if (!params || !params.category) {
    //   return;
    // }
    const redirectMap = {
      1: 'sign',
      2: 'contract',
      3: 'QA',
    };
    if (
      params.hasOwnProperty('category') &&
      params.category.split('-')[1].match(/^\d/)
    ) {
      const cat = params.category.split('-')[1];
      console.log(cat);
      const newURL = context.route.fullPath.replace(cat, redirectMap[cat]);
      context.redirect(301, newURL);
    }
  }
  // news 的栏目分类路由跳转至新路由
  const newsPath = context.route.fullPath.split('/')[1];
  if (newsPath.indexOf('news') > -1) {
    const { params } = context;
    // if (!params || !params.category) {
    //   return;
    // }
    const redirectMap = {
      1: 'company',
      2: 'industries',
      3: 'customer',
    };
    if (
      params.hasOwnProperty('category') &&
      params.category.split('-')[1].match(/^\d/)
    ) {
      const cat = params.category.split('-')[1];
      console.log(cat);
      const newURL = context.route.fullPath.replace(cat, redirectMap[cat]);
      context.redirect(301, newURL);
    }
  }
  // 这段代码放在下面不生效，放到上面就生效（wiki和news），case是生效的判断依据可能有影响~
  // wiki case news 的详情页引导至新路由
  // const whiteList = ['wiki', 'case', 'news'];
  // const firstPath = context.route.fullPath.split('/')[1];
  // // console.log(firstPath);
  // if (whiteList.includes(firstPath)) {
  //   const { params } = context;
  //   console.log(params);
  //   if (params.hasOwnProperty('category') && params.category.match(/^\d/)) {
  //     context.redirect(301, `/${firstPath}/detail/${params.category}`);
  //   }
  // }
  if (
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      context.userAgent
    )
  ) {
    context.store.commit('setIsMobile', true);
    if (context.userAgent.indexOf('iPhone') > -1) {
      context.store.commit('setIsIos', true);
    }
    if (context.route.path === '/h5-single/pc/Index') {
      context.redirect(
        process.env.baseUrl.indexOf('cn') > -1
          ? 'https://www.bestsign.cn/h5-single/app/Index'
          : 'https://www.bestsign.info/h5-single/app/Index'
      );
    }
  }
  if (
    /Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      context.userAgent
    )
  ) {
    context.store.commit('setIsLandMobile', true);
  }
  if (process.server && context.res.statusCode === 404) {
    context.redirect(
      process.env.baseUrl.indexOf('cn') > -1
        ? 'https://www.bestsign.cn'
        : 'https://www.bestsign.info'
    );
  }
}
