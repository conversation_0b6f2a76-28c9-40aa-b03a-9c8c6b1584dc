<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <title>使用说明</title>
  <style>
    html, body, .main {
      font-family: 'PingFang SC';
      width: 100%;
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      background-color: #fafafa;
    }
    .block {
      margin-top: 16px;
      padding: 16px;
      background-color: #fff;
    }
    .block > .title {
      font-size: 16px;
      margin-bottom: 12px;
      font-weight: bold;
      color: #333;
    }
    .block > .content {
      font-size: 12px;
      line-height: 20px;
      text-align: justify;
    }
  </style>
</head>
<body>
  <div class="main">
    <div class="block">
      <div class="title">什么是PC登录验证</div>
      <div class="content">当移动设备开启PC登录验证后，可以通过扫描二维码登录PC。在多台移动设备登录账号时，只有一台移动设备能打开PC登录验证，新设备打开登录验证时，老设备自动关闭。</div>
    </div>
    <div class="block">
      <div class="title">什么是关键操作验证</div>
      <div class="content">开启后，在PC端进行关键操作时，可以使用App进行意愿验证，执行操作。同样，在多台移动设备登录账号时，只有一台移动设备能打开关键操作验证，新设备打开登录验证时，老设备自动关闭。
        具体关键操作为：
        合同签署；</div>
    </div>
  </div>
</body>
</html>
