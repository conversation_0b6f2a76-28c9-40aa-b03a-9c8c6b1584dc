<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>在线客服</title>
</head>
<body>
<script src="https://cdnjs.cloudflare.com/ajax/libs/qs/6.5.2/qs.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/js-sha1/0.6.0/sha1.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/random-js/1.0.8/random.min.js"></script>
<script>
    // function parseUrl() {
	//     var searchHref = window.location.search.replace('?', '');
	//     var params = searchHref.split('&');
	//     var returnParam = {};
	//     params.forEach(function(param) {
	// 	    var paramSplit = param.split('=');
	// 	    returnParam[paramSplit[0]] = paramSplit[1];
	//     });
	//     return returnParam;
    // }

    var app_query = Qs.parse(window.location.search.slice(1))

    var userkey = 'f25b205a263df814b042c0ddaf72b11e'
    var random = new Random()
    var nonce = random.string(16)
    var timestamp = Date.parse(new Date())
    var web_token = app_query.account
    var sign_str = 'nonce=' + nonce + '&timestamp=' + timestamp + '&web_token=' + web_token + '&' + userkey
    var signature = sha1(sign_str).toUpperCase()
    var query = Qs.stringify({
        'nonce': nonce,
        'timestamp': timestamp,
        'web_token': web_token,
        'signature': signature,
        'c_cf_用户登录账号': web_token,
        'c_cf_用户类型': app_query.type,
        'c_cf_认证主体名': app_query.mainbody,
        'c_cf_用户咨询来源': app_query.from,
    })
    var url = '//bestsign.udesk.cn/im_client/?web_plugin_id=57144?' + query
    window.location.replace(url)
</script>
</body>
</html>