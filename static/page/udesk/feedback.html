<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>建议与咨询</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no">
    <style>
        * {
            margin: 0;
            padding: 0;
        }
        html,body{
            -webkit-tap-highlight-color: rgba(0,0,0,0);
            -webkit-text-size-adjust: none;

        }
        input, textarea{ border: 0;-webkit-appearance: none;outline: none; }
        
        .udesk-sdk-ticket .udesk-sdk-ticket-component-attachment-upload .attachment-upload-input .attachment-upload-item .attachment-upload-button {
            display: block;
            padding: 5px 15px;
            color: #000;
            border: 1px solid #888;
        }
        .udesk-sdk-ticket .udesk-sdk-ticket-component-attachment-upload {
            margin-top: 5px;
        }
        .content-guidance {
            position: fixed;
            top: 0;
            left: 0;
            background-color: #f8fbff;
            z-index: 1000;
            width: 100%;
        }
        .content-guidance p{
            padding: 0 12px;
        }
        .content-guidance p:first-child, .content-guidance p:nth-child(2) {
            background-color: rgb(48, 122, 232);
        }
        .content-guidance p:first-child span, .content-guidance p:nth-child(2) span {
            font-size: 15px !important;
            color: #fff;
        }
        .content-guidance p:first-child {
            padding-top: 18px;
        }
        .content-guidance p:nth-child(2) {
            padding-bottom: 18px;
        }
        .content-guidance p:nth-child(3), .content-guidance p:nth-child(4) {
            color: #888;
        }
        .content-guidance p:nth-child(3) {
            padding-top: 18px;
        }
        .content-guidance p:nth-child(4) {
            padding-bottom: 10px;
        }
        .udesk-sdk-ticket .udesk-sdk-ticket-new-ticket .udesk-sdk-ticket--component-dynamic-form {
            padding: 171px 12px 0;
        }
        .udesk-sdk-ticket .udesk-sdk-ticket-new-ticket .new-ticket-form .content-button {
            box-sizing: border-box;
            background-color: rgb(48, 122, 232);
            color: #fff;
            display: block;
            border: none;
            width: calc(100% - 24px);
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 25px;
        }
        .udesk-sdk-ticket .udesk-sdk-ticket-new-ticket {
            padding: 0 !important;
        }
        @media (min-width: 450px) {

        }
    </style>
</head>
<body>

<!--<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>-->
<script>
    (function () {
    	var date = new Date()
	    var token = "" + date.getFullYear() + date.getMonth() + date.getDate() + Math.floor(Math.random(4)*10000);

	    /* if your website does not introduce jQuery ,please cancel the ollowing annotation code.*/
	    var scriptDom = document.createElement("script");
	    scriptDom.src = "https://assets-cli.udesk.cn/ticket_js_sdk/static/vendor/js/jquery.min.js?t="+token;
	    document.body.appendChild(scriptDom);

	    var scriptDom = document.createElement("script");
	    scriptDom.src = "https://assets-cli.udesk.cn/ticket_js_sdk/1.0.1/js/sdk.min.js?t="+token;
	    document.body.appendChild(scriptDom);
	    var styleDom = document.createElement("link");
	    styleDom.rel = "stylesheet";
	    styleDom.href = "https://assets-cli.udesk.cn/ticket_js_sdk/1.0.1/css/sdk.min.css?t="+token;
	    document.body.appendChild(styleDom);
	    scriptDom.addEventListener('load', function() {
		    var udesk = UdeskSDK.ticketSDK.init({
			    // [Required] Must provide subdomain.
			    subdomain:'bestsign',
			    // [Required] Must provide client appId.
			    appid:'00fb4b786a026089',
			    // [Required] Must provide signature.
			    signature: 'cddae7467204b22837820f96c31a7f020a066b11',
			    // [Required] Must provide type.
			    type:'email',
			    // [Required] Must provide content.
			    content:'<EMAIL>'
		    });
		    // Your code goes here...
		    udesk.create({
			    type:'new'
		    })
	    }, false);
    })()
</script>
</body>
</html>