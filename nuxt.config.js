const pkg = require('./package');
// const querystring = require('querystring');

const stringifyQuery = function(query) {
  let params;
  if (process.client) {
    params = require('querystring').parse(
      window.sessionStorage.getItem('query')
    );
  }
  const mixin = { ...params, ...query };
  const isEmpty = require('lodash/isEmpty')(mixin);
  if (isEmpty) {
    return '';
  }
  // console.log(mixin);
  return '?' + require('querystring').stringify(mixin);
};

module.exports = {
  mode: 'universal',
  /*
  ** Headers of the page
  */
  head: {
    title: '上上签',
    meta: [
      { charset: 'utf-8' },
      {
        hid: 'viewport',
        name: 'viewport',
        content:
          'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no',
      },
      { hid: 'description', name: 'description', content: pkg.description },
      { 'http-equiv': 'Expires', content: '0' },
      { 'http-equiv': 'Pragma', content: 'no-cache' },
      { 'http-equiv': 'Cache-control', content: 'no-cache' },
      { 'http-equiv': 'Cache', content: 'no-cache' },
    ],
    link: [
      {
        rel: 'icon',
        type: 'image/x-icon',
        // href:
        //   'https://static.bestsign.cn/7cd76565a43aa9411736fd8beec25863b2a63922.png',
        href: '/favicon.ico',
      },
      // {
      //   rel: 'stylesheet',
      //   href: '//at.alicdn.com/t/c/font_1016367_930wfn69lzt.css',
      // },
    ],
    script: [
      // { src: 'https://hm.baidu.com/hm.js?1f46ec8ab8bbb8a41ddac8ef894ec63a', async: "async" },
      // { src: '//at.alicdn.com/t/font_1016367_iz3bx76wums.js', async: 'async' },
      // {
      //   src: '//at.alicdn.com/t/c/font_1016367_930wfn69lzt.js',
      //   async: 'async',
      // },
    ],
  },

  /*
  ** Customize the progress-bar color
  */
  loading: { color: '#00a664' },

  /*
  ** Global CSS
  */
  css: [
    '@/assets/css/index.scss',
    '@/assets/css/normalize.css',
    'element-ui/lib/theme-chalk/index.css',
    'vant/lib/index.css',
    'swiper/dist/css/swiper.css',
    '@/assets/fonts/iconfont.css',
  ],
  styleResources: {
    scss: '@/assets/css/config.scss',
  },
  env: {
    baseUrl:
      process.env.BASE_URL ||
      'http://localhost:3004' ||
      'http://**************:3004',
    // baseUrl: process.env.BASE_URL || 'http://**************:3001',
  },
  /**
   * router
   */
  router: {
    middleware: ['redirect', 'i18n', 'language-redirect'],
    extendRoutes(routes, resolve) {
      routes.push({
        name: 'evaluate-page',
        path: '/evaluate:page',
        component: resolve(__dirname, 'pages/evaluate/index.vue'),
      });
      //这里是用于分页时无论点击哪一页都是跳转到pages/evaluate/index.vue这个组件里面,在这里把静态的路由写成动态路由了，
      routes.push({
        name: 'case-page',
        path: '/case:page',
        component: resolve(__dirname, 'pages/case/index.vue'),
      });
      routes.push({
        name: 'wiki-page',
        path: '/wiki:page',
        component: resolve(__dirname, 'pages/wiki/index.vue'),
      });
      routes.push({
        name: 'news-page',
        path: '/news:page',
        component: resolve(__dirname, 'pages/news/index.vue'),
      });
      routes.push({
        name: 'product-price',
        path: '/product-price',
        component: resolve(__dirname, 'pages/product/price.vue'),
      });
      routes.push({
        name: 'product-function',
        path: '/product-function',
        component: resolve(__dirname, 'pages/product/function.vue'),
      });
      routes.push({
        name: 'product-judicial',
        path: '/product-judicial',
        component: resolve(__dirname, 'pages/product/judicial.vue'),
      });
      routes.push({
        name: 'product-check',
        path: '/product-check',
        component: resolve(__dirname, 'pages/product/check/index.vue'),
      });
      routes.push({
        name: 'product-download',
        path: '/product-download',
        component: resolve(__dirname, 'pages/product/download.vue'),
      });
      routes.push({
        name: 'about-us',
        path: '/about-us',
        component: resolve(__dirname, 'pages/about/about-us.vue'),
      });
      routes.push({
        name: 'join-us',
        path: '/join-us',
        component: resolve(__dirname, 'pages/about/join-us.vue'),
      });
      routes.push({
        name: '/product',
        path: '/product',
        component: resolve(__dirname, 'pages/product2/index.vue'),
      });
      routes.push({
        name: '/product-service',
        path: '/product-service',
        component: resolve(__dirname, 'pages/product2/service.vue'),
      });
      // 为所有路由添加语言前缀
      routes.forEach(route => {
        // 跳过已处理的路由
        if (!route.path.includes(':lang')) {
          route.path = '/:lang' + route.path;
        }
      });
    },
    scrollBehavior: function(to, from, savedPosition) {
      return { x: 0, y: 0 };
    },
    stringifyQuery,
  },
  /*
  ** Plugins to load before mounting the App
  */
  plugins: [
    '@/plugins/axios',
    '@/plugins/MessageToast',
    '@/plugins/element-ui',
    '@/plugins/vant',
    '@/plugins/scroll-to',
    '@/plugins/lazyload',
    '@/plugins/i18n.js',
    { src: '@/plugins/baiduGa', ssr: false },
    { src: '@/plugins/countUp', ssr: false },
    { src: '@/plugins/swiper', ssr: false },
    { src: '@/plugins/vueTouch', ssr: false },
    { src: '@/plugins/route', ssr: false },
    { src: '@/plugins/vueCookies', ssr: false },
    { src: '@/assets/fonts/iconfont.js', ssr: false },
  ],
  /*
  ** Nuxt.js modules
  */
  modules: [
    // Doc: https://github.com/nuxt-community/axios-module#usage
    // '@nuxtjs/axios',
    '@nuxtjs/proxy',
    [
      '@nuxtjs/component-cache',
      {
        max: 10000,
        maxAge: 1000 * 60 * 60,
      },
    ],
    '@nuxtjs/style-resources',
  ],
  proxy: {
    '/www/api': {
      // target: 'http://*************:8137',
      target: 'https://www.bestsign.tech',
      // target: 'https://www.bestsign.cn',
      // target: 'http://*************:8137',
      // target: 'http://************:8137', //新华为云
    },
    '/users': {
      // target: 'https://ent2.bestsign.tech',
      target: 'https://ent.bestsign.info',
      //   target: 'https://ent.bestsign.cn',
      // target: 'https://ent.bestsign.tech',
      // target: '192.168.30.200:8080',
      // target: 'http://192.168.3.125:8080',
      // target: 'http://192.168.3.132:8080',
      pathRewrite: { '^/users': '/users' },
    },
    '/users/ignore/captcha': {
      target: 'https://ent.bestsign.info',
      pathRewrite: { '^/users': '/users' },
    },

    '/contract-api': {
      target: 'https://ent.bestsign.info',
      pathRewrite: { '^/contract-api': '/contract-api' },
    },
    '/auth-center/user': {
      target: 'https://ent.bestsign.info',
      pathRewrite: { '^/auth-center': '/auth-center' },
    },
    '/ents/ignore': {
      target: 'https://ent.bestsign.info',
      pathRewrite: { '^/ents': '/ents' },
    },
    '/auth-center/user/login': {
      target: 'https://ent.bestsign.info',
      pathRewrite: { '^/auth-center': '/auth-center' },
    },
  },
  /*
  ** Axios module configuration
  */
  /*
  ** Build configuration
  */
  build: {
    /*
    ** You can extend webpack config here
    */
    babel: {
      compact: false,
      plugins: [
        [
          'component',
          { libraryName: 'element-ui', styleLibraryName: 'theme-chalk' },
        ],
        [
          'import',
          {
            libraryName: 'vant',
            libraryDirectory: 'lib',
          },
          'vant',
        ],
      ],
    },
    loaders: {
      less: {
        javascriptEnabled: true,
      },
    },
    extractCSS: true,
    extend(config, ctx) {
      // Run ESLint on save
      if (ctx.isDev && ctx.isClient) {
        config.module.rules.push({
          enforce: 'pre',
          test: /\.(js|vue)$/,
          loader: 'eslint-loader',
          exclude: /(node_modules)/,
          options: {
            fix: true,
          },
        });
      }
    },
    vue: {
      config: {
        disableHostCheck: true,
      },
    },
  },
};
