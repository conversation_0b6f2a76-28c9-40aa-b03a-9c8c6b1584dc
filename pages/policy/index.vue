<template>
  <article class="college-policy" :class="{'college-policy__mobile': isMobile}">
    <section class="section section-2 is-tiny">
      <div class="container">
        <h3 class="section-headline">政策解读</h3>
        <!-- <p class="section-subtitle">注册成为上上签官方微信会员，快速跻身电子签名专家。</p> -->
        <el-row>
          <template v-for="(item, index) in shownList">
            <el-col
              :key="index"
              :xs="24"
              :md="8"
              :sm="8"
              :lg="8">
              <div class="card">
              <!-- <nuxt-link class="card" :to="`/policy/${item.id}`"> -->
              <!-- 在上一层页码刷新当下页面后，这里用nuxt-link会把那个页码带过来，所以用a便签实现 -->
              <nuxt-link :to="`/policy/${item.id}`">
              <!-- 此处的lazy必须加上key,要不懒加载图片不更新 -->
                <img
                  :key="item.imgUrl"
                  v-lazy="formatUrl(item.imgUrl)"
                  class="card-image"
                  alt="为你提供包括2017到2018年电子签约市场专题报告在内的最新行业研究和趋势报告">
                <div class="card-content">
                  <div class="body text-justify">{{ item.policyTitle }}</div>
                  <p class="footer">
                    <span class="time">{{ releaseTime(item.releaseTime) }}</span>
                  </p>
                </div>
              </nuxt-link>
              </div>
              <!-- </nuxt-link> -->
            </el-col>
          </template>
        </el-row>
       
		  <div class="button-wrap">
          <el-pagination
		  	 background
            :page-size="12"
            :current-page="current"
            :total="total"
            @current-change="handleCurrentChange"
            :pager-count="5"
            layout="prev, pager, next">
			</el-pagination>
        </div>
      </div>
    </section>
  </article>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import moment from 'moment';
export default {
  name: 'CollegePolicy',
  layout: 'default',
  head() {
    return {
      title: '【最新】电子签约_电子合同政策解读_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '政策解读,电子签约,电子合同,电子签名,上上签电子签约',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签电子签约云平台推出最新电子签约、电子合同相关政策解读，企业或者个人可以随时在此版块查询电子签约类的相关政策，同时也会有电子合同行业的专家进行权威解读，旨在帮助有电子签约、电子合同需求的企业获得第一手的签约资讯和消息。',
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.bestsign.cn/policy',
        },
      ],
    };
  },
  data() {
    return {
      shownList: [],
      current: 1,
    };
  },
  async asyncData({ route, app }) {
    // const pageNum = route.query.page;
    // console.log('ssssss');
    // console.log(pageNum);
    const page = route.query.page || 1;
    // console.log('ssssss');
    // console.log(page);
    //支持服务端渲染（分页是客户端和服务端同时渲染实现的），实现当前页面刷新实现
    const res0 = await app.$axios.get(
      `/www/api/web/getPolicy?pageNum=${page}&pageSize=12`
    );
    return {
      shownList: res0.data,
      total: res0.totalNum,
      current: +page,
    };
  },
  computed: {
    // total() {
    //   // return this.res.totalNum;
    // },
    ...mapState(['isMobile']),
  },
  mounted() {
    this.$store.commit('changePath', { path: 'resource' });
    // this.getData(this.$route.query.page);
  },
  methods: {
    getData(page) {
      // this.shownList = this.list.slice(
      //   (parseInt(page) - 1) * 12,
      //   12 * parseInt(page)
      // );
      //支持客户端渲染（分页是客户端和服务端同时渲染实现的），实现点击页码跳转正确
      this.$axios
        .get(`/www/api/web/getPolicy?pageNum=${page}&pageSize=12`)
        .then(res => {
          // console.log(res, this.shownList);
          this.shownList = res.data;
        });
      if (+page === 1) {
        //此处不用路由跳转的原因是无论在那一页刷新页面后，点击第一页就调转到前面刷新的页码的路由，但是list渲染的数据是正确的
        // this.$router.push(this.$route.fullPath.replace(/page=\d+/, ''));
        location.href = this.$route.fullPath.replace(/\?page=\d+/, '');
        // this.$router.push();
      } else {
        this.$router.push({
          query: {
            page,
          },
        });
      }
      // console.log(this.$route);
      // let path = this.$route.fullPath;
      // if (path.include('page=')) {
      //   path = path.replace(/page=\d+/, `page=${page}`);
      // } else {
      //   if (path.include('?')) {
      //     path += `&page=${page}`;
      //   } else {
      //     path += +page === 1 ? '' : `?page=${page}`;
      //   }
      // }
      // this.$nextTick(() => {
      //   // location.href = location.href;
      // });
      // .then(() => {
      // });
    },
    handleDownload(id) {
      this.$router.push(`/policy/${id}`);
    },
    formatUrl(url) {
      const arr = url && url.split('?');
      return url && arr[0];
    },
    releaseTime(time) {
      return moment(time).format('YYYY-MM-DD');
    },
    handleCurrentChange(page) {
      this.current = page;
      this.getData(page);
    },
  },
};
</script>

<style scoped lang="scss">
.section-1 {
  padding: 0;
  position: relative;
  .img-banner {
    width: 100%;
    img {
      width: 100%;
    }
  }
  .article-headline {
    font-size: 2.5rem;
  }
  .container {
    position: relative;
    .title-wrap {
      position: absolute;
      left: 50%;
      top: 100px;
      transform: translate3d(-50%, 0, 0);
      color: #fff;
      width: 100%;
      text-align: center;
      .img-wrap {
        width: 100%;
        img {
          width: 100px;
        }
        p {
          margin-top: 30px;
        }
      }
    }
  }
  .img-wrap,
  img {
    width: 100%;
  }
}
.section-2 {
  text-align: center;
  background: #f9f9f9;
  .container {
    max-width: 70.75rem;
    .section-headline {
      color: #090909;
      font-weight: 400;
      font-size: 2.2rem;
    }

    /deep/.el-row {
      padding-top: 1.5rem;
      margin: 0 auto;
    }
  }
  .nav-wrap {
    margin-bottom: 40px;

    .el-col {
      cursor: pointer;
      margin-bottom: 40px;
      font-size: 18px;
      &:hover {
        color: #00a664;
      }
    }
  }
  .card {
    padding: 0;
    .card-image {
      width: 100%;
      // height: 185px;
    }
  }
  .card-content {
    padding: 36px 20px;
    .footer {
      align-self: flex-end;
    }
  }
  .button-wrap {
    margin-top: 60px;
    text-align: center;
  }
}
.college-policy__mobile {
  .section-1 {
    height: 300px;
    background: transparent;
    .article-headline {
      color: #383838;
      margin: 0 20px;
      font-weight: normal;
      font-size: 28px;
    }
  }
  .section-2 {
    margin-top: 0;
  }
}
/deep/ .el-pagination.is-background .el-pager li:not(.disabled).active {
  background: #00aa64;
}
@media screen and (max-width: 767px) {
  .section {
    padding: 50px 0px;
  }
  .section-headline {
    font-size: 24px;
    margin-bottom: 30px;
    font-weight: 400;
    color: #515151;
    padding: 0;
  }
  .section-2 {
    margin-top: 0;
    .container {
      /deep/.el-row {
        padding-top: 0;
      }
    }
    .section-subtitle {
      margin-bottom: 0px;
    }
  }
}
</style>
