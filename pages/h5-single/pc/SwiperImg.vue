<template>
  <div class="swiper-img" ref="bg">
    <div class="h5-container">
      <h3 class="section-header">上上签服务客户</h3>
      <div class="wrapper">
        <div class="swiper-container" v-swiper:mySwiper="swiperOption">
          <div class="swiper-wrapper">
            <div class="swiper-slide" v-for="(item, index) in list" :key="index">
              <div class="item">
                <img :src="item.img" alt="">
                <div class="text">
                  <h4>{{ item.text }}</h4>
                  <p class="line"></p>
                </div>
                <div class="client-img" @click="$router.push(`/case#${item.hash}`)">
                  <img :src="item.c" alt="">
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="arrow">
          <div class="ssq-button-prev">
            <i class="el-icon-arrow-left"></i>
          </div>
          <div class="ssq-button-next">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
      <div style="margin: 3rem auto 2rem;text-align: center">
        <div class="h5-demo-button"  @click="$scrollTo('body')">免费试用</div>
      </div>
    </div>
  </div>
</template>

<script>
import qiche_bg from '@/assets/images/h5-single/qiche_bg.jpg';
import qiche from '@/assets/images/h5-single/qiche.jpg';
import qiche_c from '@/assets/images/h5-single/qiche_c.png';
import b2b_bg from '@/assets/images/h5-single/b2b_bg.jpg';
import b2b from '@/assets/images/h5-single/b2b.jpg';
import b2b_c from '@/assets/images/h5-single/b2b_c.png';
import yinhang_bg from '@/assets/images/h5-single/yinhang_bg.jpg';
import yinhang from '@/assets/images/h5-single/yinhang.jpg';
import yinhang_c from '@/assets/images/h5-single/yinhang_c.png';
import renli_bg from '@/assets/images/h5-single/renli_bg.jpg';
import renli from '@/assets/images/h5-single/renli.jpg';
import renli_c from '@/assets/images/h5-single/renli_c.png';
import fangdichan_bg from '@/assets/images/h5-single/fangdichan_bg.jpg';
import fangdichan from '@/assets/images/h5-single/fangdichan.jpg';
import fangdichan_c from '@/assets/images/h5-single/fangdichan_c.png';
import hulianwang_bg from '@/assets/images/h5-single/hulianwang_bg.jpg';
import hulianwang from '@/assets/images/h5-single/hulianwang.jpg';
import hulianwang_c from '@/assets/images/h5-single/hulianwang_c.png';
import lingshou_bg from '@/assets/images/h5-single/lingshou_bg.jpg';
import lingshou from '@/assets/images/h5-single/lingshou.jpg';
import lingshou_c from '@/assets/images/h5-single/lingshou_c.png';
import wuliu_bg from '@/assets/images/h5-single/wuliu_bg.jpg';
import wuliu from '@/assets/images/h5-single/wuliu.jpg';
import wuliu_c from '@/assets/images/h5-single/wuliu_c.png';

export default {
  name: 'h5-single-pc-SwiperImg',
  data() {
    return {
      swiperOption: {
        initialSlide: 1,
        centeredSlides: true,
        loop: true,
        speed: 700,
        slidesPerView: 3,
        spaceBetween: 20,
        navigation: {
          nextEl: '.ssq-button-next',
          prevEl: '.ssq-button-prev',
        },
      },
      list: [
        {
          text: '汽车行业',
          bg: qiche_bg,
          img: qiche,
          c: qiche_c,
          hash: 'car',
        },
        {
          text: '人力资源行业',
          bg: renli_bg,
          img: renli,
          c: renli_c,
          hash: 'HR',
        },
        {
          text: '银行保险行业',
          bg: yinhang_bg,
          img: yinhang,
          c: yinhang_c,
          hash: 'finance',
        },
        {
          text: '物流行业',
          bg: wuliu_bg,
          img: wuliu,
          c: wuliu_c,
          hash: 'logistic',
        },
        {
          text: '零售制造行业',
          bg: lingshou_bg,
          img: lingshou,
          c: lingshou_c,
          hash: 'retail',
        },
        {
          text: '房地产行业',
          bg: fangdichan_bg,
          img: fangdichan,
          c: fangdichan_c,
          hash: 'lease',
        },
        {
          text: '互联网平台行业',
          bg: hulianwang_bg,
          img: hulianwang,
          c: hulianwang_c,
          hash: 'internet',
        },
        {
          text: 'B2B电商行业',
          bg: b2b_bg,
          img: b2b,
          c: b2b_c,
          hash: 'B2BB',
        },
      ],
      activeIndex: 0,
    };
  },
  mounted() {
    const that = this;
    that.handleBgChange(that.activeIndex);
    this.mySwiper.on('slideChange', function() {
      that.handleChange(this);
      that.handleBgChange(this.realIndex);
    });
  },
  methods: {
    handleChange(cur) {
      this.activeIndex = cur.realIndex;
    },
    handleBgChange(index) {
      const img = new Image();
      img.src = this.list[index].bg;
      img.onload = () => {
        this.$refs['bg'].style.backgroundImage = 'url(' + img.src + ')';
      };
    },
  },
};
</script>
<style lang="scss">
.swiper-img {
  .arrow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 577px;
  }
  .ssq-button-prev,
  .ssq-button-next {
    position: absolute;
    top: 50%;
    transform: translate3d(0, -50%, 0);
    background: transparent;
    cursor: pointer;
    > i {
      font-size: 36px;
      color: rgba(35, 24, 21, 0.5);
      &:hover {
        color: rgba(35, 24, 21, 1);
      }
    }
  }
  .ssq-button-prev {
    left: 30px;
  }
  .ssq-button-next {
    right: 30px;
  }
  .swiper-scrollbar {
    height: 3px;
    bottom: 0;
    background: rgba(255, 255, 255, 0.3);
    .swiper-scrollbar-drag {
      background: rgba(255, 255, 255, 0.7);
    }
    .tips {
      position: absolute;
      top: -20px;
      left: 0;
    }
  }
}
</style>
<style scoped lang="scss">
.swiper-img {
  width: 100%;
  padding: 70px 0 2rem;
  transition: all 0.7s ease;
  .wrapper {
    width: 100%;
    position: relative;
    height: 577px;
    margin-top: 70px;
  }
  .swiper-container {
    width: 100%;
    max-width: 1024px;
    position: relative;
    height: 577px;
    margin: 58px auto;
    .item {
      position: relative;
      transform: scale(0.85);
      transition: all 0.7s ease;
      img {
        width: 100%;
        border-radius: 8px;
      }
      .text {
        color: #fff;
        font-size: 24px;
        position: absolute;
        top: 45px;
        text-align: left;
        left: 50px;
        h4 {
          margin-bottom: 15px;
        }
        .line {
          width: 50px;
          height: 4px;
          background-color: #fff;
          margin-top: 15px;
        }
      }
      .client-img {
        width: 380px;
        position: absolute;
        left: -30px;
        bottom: -55px;
        z-index: 99;
        opacity: 0;
        transition: all 0.7s ease;
        transform: scaleY(0);
        transform-origin: top;
        cursor: pointer;
      }
    }
    .swiper-slide-active {
      .client-img {
        opacity: 1;
        transform: scaleY(1);
      }
      .item {
        transform: scale(1);
      }
    }
  }
}
</style>
