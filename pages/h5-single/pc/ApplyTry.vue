<template>
  <div class="apply-try-box" :class="{'mobile': isMobile}">
    <div class="h5-container header">
      <div class="img" @click="handleIndex"></div>
      <div class="tel" v-if="!isMobile">
        <i class="iconfont icon-tel"></i>
        <span>************</span>
      </div>
      <div v-else class="tel">
        <div class="ssq-button-primary" style="width: 100px;height: 30px;padding: 0;line-height: 30px;" @click="handleScroll">免费试用</div>
      </div>
    </div>
    <div class="section-1">
      <h1 class="headline"><img class="logo-text" src="@/assets/images/h5-single/logo-text.png" alt=""></h1>
      <h3 class="subtitle">1273万+标杆企业选择上上签</h3>
      <!--<el-form class="apply-form" :model="formData" :rules="rules" ref="ruleForm">
        <el-form-item label="" class="form-item" prop="customerName">
          <div class="el-input-con">
            <el-input
              class="phone"
              placeholder="输入姓名"
              v-model.trim="formData.customerName"
            >
            </el-input>
          </div>
        </el-form-item>
        <el-form-item label="" class="form-item" prop="companyName">
          <div class="el-input-con">
            <el-input
              class="phone"
              placeholder="输入公司名"
              v-model.trim="formData.companyName"
            >
            </el-input>
          </div>
        </el-form-item>
        <el-form-item label="" class="form-item" prop="userAccount">
          <div class="el-input-con">
            <el-input
              class="phone"
              placeholder="输入手机号"
              v-model.trim="formData.userAccount"
            >
            </el-input>
          </div>
        </el-form-item>
        <el-form-item v-if="showPictureVerCon" label="" class="pictureVer-item" prop="imageCode">
          <el-col :span="16">
            <el-input
              class="pictureVer"
              placeholder="请填写4位验证码"
              :maxlength="4"
              v-model.trim="formData.imageCode"
            ></el-input>
          </el-col>
          <el-col :offset="1" span="7">
            <PictureVerify
              class="form-pictureVerify"
              ref="pictureVerify"
              :imageKey="formData.imageKey"
              @change-imageKey="changeImageKey"
            />
          </el-col>
        </el-form-item>
        <el-form-item label="" class="phoneVerifyCode-item verify-code form-item" prop="phoneVerifyCode">
          <el-col :span="16">
            <el-input
              :disabled="phoneVerifyCodeDisabled"
              placeholder="请输入验证码"
              :maxlength="6"
              v-model.trim="formData.phoneVerifyCode"
            >
            </el-input>
          </el-col>
          <el-col :offset="1" :span="7">
            <CountDown
              :clickedFn="send"
              :disabled="countDownDisabled"
              :second="60"
              ref="btn"
              class="countDown code-sent"
            />
          </el-col>
        </el-form-item>
        <el-button class="submit" :loading="loading" @click="toRegister">体验Demo</el-button>
      </el-form>-->
      <!-- <register class="register" text="注册成功后，你将即刻免费获得5份电子合同<br>实名认证后再获得5份"></register> -->
      <register class="register"></register>
    </div>
  </div>
</template>

<script>
import CountDown from '@/components/CountDown.vue';
import PictureVerify from '@/components/PictureVerify.vue';
import resRules from '@/assets/utils/regs.js';
import { isPhoneOrMail } from '@/assets/utils/reg.js';
import aes from '@/assets/utils/aes.js';
import { encode } from '@/assets/utils/aes.js';
import Register from '../../demo/Register';

export default {
  name: 'h5-single-pc-ApplyTry',
  props: {
    supportType: {
      //开发者类型
      type: Array,
      default: function() {
        return [];
      },
    },
    clientId: {},
    redirectUrl: {},
    accountType: {
      type: String,
      default: 'mail_phone', //'mail' 'phone'
    },
    pageType: {
      type: String,
      default: 'register', //'register' 'registerStaff'
    },
    token: {
      //邀请注册需要token
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
    appName: {
      type: String,
      default: '',
    },
    account: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      resRules: resRules,
      formData: {
        customerName: '',
        companyName: '',
        userAccount: '',
        imageCode: '',
        phoneVerifyCode: '',
        verifyKey: '',
        imageKey: '',
      },
      rules: {
        customerName: [
          {
            required: true,
            message: '(请输入姓名)',
            trigger: 'blur',
          },
          {
            pattern: /^.*[^\d]{2,}.*$/,
            message: '(姓名不能为数字且不少于两个字符)',
            trigger: 'blur',
          },
        ],
        companyName: [
          {
            required: true,
            message: '(请输入公司名)',
            trigger: 'blur',
          },
          {
            pattern: /^.*[^\d]{2,}.*$/,
            message: '(公司名不能为数字且不少于两个字符)',
            trigger: 'blur',
          },
        ],
        userAccount: [
          {
            required: true,
            message: '(请输入手机号)',
            trigger: 'blur',
          },
          {
            pattern: resRules.userPhone,
            message: '(手机号格式错误)',
            trigger: 'blur',
          },
        ],
        phoneVerifyCode: [
          {
            required: true,
            message: '(请输入验证码)',
            trigger: 'blur',
          },
          {
            pattern: /^\d{6}$/,
            message: '(验证码错误)',
            trigger: 'blur',
          },
        ],
        imageCode: [
          {
            required: true,
            message: '(请输入图形验证码)',
            trigger: 'blur',
          },
          {
            pattern: resRules.imageVerifyCode,
            message: '(图形验证码错误)',
            trigger: 'blur',
          },
        ],
      },
      countDownDisabled: false,
      showPictureVerCon: false,
      phoneVerifyCodeDisabled: true,
    };
  },
  methods: {
    handleScroll() {
      this.$scrollTo('body');
    },
    handleBodyScroll() {
      const s1 = document.querySelector('.h5-container.header');
      const scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      const offsetTop = document.querySelector('.section-1').clientHeight;
      if (scrollTop === 0) {
        s1.style.background = 'transparent';
      } else {
        s1.style.background = 'white';
      }
    },
    // 更改图形验证码
    changeImageKey(value) {
      this.phoneVerifyCodeDisabled = false;
      this.formData.imageKey = value;
    },

    // 发送验证码
    send() {
      const { userAccount, imageKey, imageCode } = this.formData;
      this.$refs['ruleForm'].validateField(['userAccount'], error => {
        if (error) {
          return false;
        }
        let headersObj = {};
        if (imageCode !== '' && imageKey !== '') {
          headersObj = {
            // 'Content-Type': 'application/json; charset=utf-8',
            additionalImgVerCode: JSON.stringify({
              imageCode,
              imageKey,
            }),
            'request-source': 'WEB',
          };
        }
        // let host = 'https://ent.bestsign.info';
        // if (process.env.baseUrl.indexOf('cn') > -1) {
        //   host = 'https://ent.bestsign.cn';
        // }
        let host = '';
        if (process.env.NODE_ENV !== 'development') {
          host =
            process.env.baseUrl.indexOf('cn') > -1
              ? 'https://ent.bestsign.cn'
              : 'https://ent.bestsign.info';
        }
        this.$axios({
          url: `${host}/users/ignore/captcha/notice?encryptToken=${aes(
            'B001',
            userAccount
          )}`,
          method: 'post',
          headers: headersObj,
          data: {
            code: 'B001',
            sendType: isPhoneOrMail(userAccount) === 'phone' ? 'S' : 'E',
            target: userAccount,
            imageCode,
            imageKey,
          },
        })
          .then(res => {
            if (res) {
              this.$MessageToast.success('发送成功！');
              this.countDownDisabled = true;
              setTimeout(this.sended, 0);
              this.formData.verifyKey = res.value;
              this.phoneVerifyCodeDisabled = false;
            }
          })
          .catch(err => {
            const res = err.response.data;
            if (res.code == '902' || res.code == '100006') {
              if (this.showPictureVerCon) {
                setTimeout(() => {
                  this.$refs.pictureVerify.changeImg();
                }, 20);
              } else {
                this.showPictureVerCon = true;
              }
              if (res.message !== '图片验证码不能为空') {
                this.$MessageToast.error(res.message);
              } else {
                this.$MessageToast.error('请先填写图形验证码');
              }
            } else {
              this.$MessageToast.error(res.message);
            }
            this.$refs.btn.reset();
          });
      });
    },

    sended() {
      this.$refs.btn.run();
      this.countDownDisabled = false;
    },

    // 注册
    toRegister() {
      const {
        userAccount,
        customerName,
        companyName,
        verifyKey,
        phoneVerifyCode,
      } = this.formData;
      // 百度统计当用户点击“申请免费试用”成功提交信息之后，需要将当前页面URL地址，客户地区记录
      const url = location.href;
      window._hmt && window._hmt.push(['_trackEvent', 'h5-pc', 'submit', url]);

      const params = {
        contact: userAccount,
        customerName,
        companyName,
        verifyCode: phoneVerifyCode,
        verifyKey,
        applyUrl: url,
        type: 1,
      };
      this.$refs['ruleForm'].validate(valid => {
        if (!valid) {
          return false;
        }
        this.loading = true;
        this.$axios
          .post('/www/api/web/tryout/register', {
            ...params,
          })
          .then(res => {
            if (res && res.value === 'OK') {
              this.$MessageToast.success('申请试用成功！');
              const query = encode({
                phone: params.contact,
                companyName: params.companyName,
              });
              const isPre = window.location.hostname.indexOf('cn') > -1;
              window.location.href = `https://demo.bestsign.${
                isPre ? 'cn' : 'info'
              }/service?token=${query}`;
            }
            this.loading = false;
          })
          .catch(err => {
            const res = err.response.data;
            if (res.code === '903') {
              this.$MessageToast.error('申请失败请稍后再试');
            } else {
              this.$MessageToast.error(res.message);
            }
            this.loading = false;
          });
      });
    },

    handleIndex() {
      this.$router.push({
        path: '/',
        query: this.$route.query,
      });
    },
  },

  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    // 注册按钮状态
    registerBtnDisabled() {
      let commonFlag =
        this.resRules.pass.test(this.formData.pass) &&
        this.resRules.phoneVerifyCode.test(this.formData.phoneVerifyCode) &&
        this.registrationAgreement;
      if (this.accountType == 'mail_phone') {
        commonFlag =
          commonFlag &&
          this.resRules.userAccount.test(this.formData.userAccount);
      } else if (this.accountType == 'mail') {
        commonFlag =
          commonFlag && this.resRules.userEmail.test(this.formData.userAccount);
      }
      if (this.showPictureVerCon) {
        commonFlag =
          commonFlag &&
          this.resRules.imageVerifyCode.test(this.formData.imageVerifyCode);
      }
      return !commonFlag;
    },
    // 注册接口
    registerAjaxUrl() {
      // let host = 'https://ent.bestsign.info';
      // if (process.env.baseUrl.indexOf('cn') > -1) {
      //   host = 'https://ent.bestsign.cn';
      // }
      let host = '';
      if (process.env.NODE_ENV !== 'development') {
        host =
          process.env.baseUrl.indexOf('cn') > -1
            ? 'https://ent.bestsign.cn'
            : 'https://ent.bestsign.info';
      }
      switch (this.pageType) {
        case 'register':
          return `${host}/auth-center/user/v2/person-register`;
          break;
        case 'registerStaff':
          return `${host}/auth-center/user/employee-register`;
          break;
      }
    },
  },

  components: {
    Register,
    CountDown,
    PictureVerify,
  },
  mounted() {
    this.isMobile && window.addEventListener('scroll', this.handleBodyScroll);
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleBodyScroll);
  },
};
</script>

<style lang="scss">
.apply-try-box .apply-form {
  .el-input__inner {
    background-color: transparent;
    border-color: #fff;
    color: #fff;
  }
}
@media screen and (max-width: 767px) {
  .apply-try-box .apply-form {
    .el-form-item {
      margin-bottom: 16px;
    }
    .el-form-item__content {
      line-height: 1.7;
    }
    .el-input__inner {
      height: 36px;
      line-height: 37px;
    }
  }
}
</style>
<style scoped lang="scss">
.register {
  max-width: 30rem;
  width: 100%;
  margin: 3rem auto;
  background-color: #fff;
  color: #515151;
}
.apply-try-box {
  width: 100%;
  background: url(~assets/images/h5-single/banner.jpg) no-repeat;
  background-size: cover;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  .header {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .img {
      margin-top: 15px;
      width: 90px;
      height: 40px;
      cursor: pointer;
      background: url(~assets/images/h5-single/logo-fill.png) no-repeat;
      background-size: 90px 40px;
    }
    .tel {
      span {
        color: #fff;
        font-size: 16px;
      }
      .icon-tel {
        margin-right: 5px;
        color: #fff;
      }
    }
  }
  .section-1 {
    position: relative;
    width: 100%;
    text-align: center;
    color: #fff;
    padding-top: 60px;
    .headline {
      margin-top: 36px;
      margin-bottom: 0;
    }
    .subtitle {
      font-size: 28px;
      margin-top: 28px;
    }
    .logo-text {
      width: 41.7%;
    }
  }
  .apply-form {
    text-align: center;
    width: 100%;
    max-width: 370px;
    margin: 48px auto 0;
    box-sizing: border-box;
    .form-pictureVerify {
      width: 80px;
      height: 36px;
      vertical-align: top;
    }
    .title {
      margin-top: 3.5rem;
      margin-bottom: 2rem;
    }
    .form-item {
      width: 100%;
      &.verify-code {
        input {
          padding-right: 120px;
          margin-right: -120px;
          float: left;
        }
        .code-sent {
          position: relative;
          left: 0;
          width: 100%;
          height: 40px;
          line-height: 40px;
          outline: none;
          box-sizing: border-box;
          background-color: #fff;
          color: #00a664;
          cursor: pointer;
          border-radius: 4px;
          margin-left: 0;
        }
      }
    }
    .submit {
      height: 50px;
      width: 100%;
      border: none;
      background-color: #00a664;
      color: #fcfcfc;
      font-size: 14.66px;
      border-radius: 3px;
      outline: none;
      cursor: pointer;
    }
  }
}
.phoneVerifyCode-item {
  .el-input,
  .el-input input {
    /*width: 238px;*/
    height: 42px;
    float: left;
  }
  .countDown {
    position: absolute;
    top: 0px;
    left: 238px;
    width: 98px;
    height: 36px;
    margin-left: -1px;
  }

  .phoneVerifyCodeDisabledError {
    position: absolute;
    top: 0;
    left: 475px;
    display: none;
  }
  &:hover {
    .phoneVerifyCodeDisabledError {
      display: block;
    }
  }
}
.register-line {
  text-align: center;
  color: #666666;
  font-size: 12px;
  margin-top: 10px;

  a {
    color: #00a664;
  }
}
.tips {
  display: none;
}
.apply-try-box.mobile {
  .header {
    padding: 0 16px;
    height: 60px;
    position: fixed;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 999;
  }
}
@media screen and (max-width: 767px) {
  .apply-try-box {
    width: 100vw;
    background: url(~assets/images/h5-single/app/bg.jpg) no-repeat;
    padding: 0 18px;

    .section-1 {
      .headline {
        margin-top: 24px;
      }

      .logo-text {
        width: 100%;
      }

      .subtitle {
        font-size: 18px;
        margin-top: 18px;
      }
    }

    .apply-form {
      margin-top: 28px;
      .form-item {
        &.verify-code {
          .code-sent {
            height: 36px;
            line-height: 1.7;
          }
        }
      }
      .submit {
        height: 40px;
      }
    }

    .tips {
      position: relative;
      width: 80%;
      margin: 20px auto;
      display: inline-block;
      font-size: 12px;
      color: #fff;
      .hr {
        position: absolute;
        width: 100%;
        height: 1px;
        background: #fff;
      }
    }

    .phoneVerifyCode-item {
      .countDown {
        font-size: 12px;
      }
    }
  }
}
</style>
