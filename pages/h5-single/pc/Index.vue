<template>
  <div class="content h5-single-body">
    <ApplyTry></ApplyTry>
    <Introduce></Introduce>
    <index-usage :isWhiteBg="isMobile"></index-usage>
    <!-- <swiper-img /> -->
    <!-- <Company></Company> -->
    <begin-sign :scroll="true"></begin-sign>
    <v-footer></v-footer>
    <float-help></float-help>
  </div>
</template>

<script>
import ApplyTry from './ApplyTry';
import SwiperImg from './SwiperImg';
import Introduce from './Introduce';
import Company from './Company';
import Footer from './Footer';
import BeginSign from '@/components/BeginSign';
import FloatHelp from '@/components/FloatHelp.vue';
import IndexUsage from '@/components/Index/Usage.vue';
export default {
  name: 'h5-single-pc-Home',
  layout: 'blank',
  head() {
    return {
      title: '电子合同签约_电子签名_在线合同签约平台_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同,电子签名,电子签章,电子签约',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签是业内领先的在线电子合同签署，电子签名，电子签章的云服务平台，提供多种行业专属解决方案，同时上上签电子签名确保在线合同及电子签约过程合法有效性，实现电子合同在线签署、编辑、管理等功能。',
        },
      ],
    };
  },
  data() {
    return {
      show: false,
    };
  },
  components: {
    ApplyTry,
    SwiperImg,
    Introduce,
    Company,
    FloatHelp,
    BeginSign,
    'v-footer': Footer,
    IndexUsage,
  },
};
</script>

<style lang="scss">
.slide-enter-active,
.slide-leave-active {
  transition: all 200ms ease-in-out;
}
.h5-single-body {
  width: 100%;
  text-align: center;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  span.validation-error {
    display: none;
  }
  .fixedFooter {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  }
  .h5-container {
    max-width: 1240px;
    width: 100%;
    padding: 0 20px;
    margin: 0 auto;
    position: relative;
  }
  .hr {
    margin: 0 auto;
    width: 65px;
    height: 1px;
    background: #000;
  }
  .section-header {
    font-size: 30px;
    color: #5d5d5d;
    margin-bottom: 30px;
    font-weight: 400;
  }
}
</style>
