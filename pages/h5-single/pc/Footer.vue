<template>
  <footer class="footer">
    <div v-if="isMobile">
      <div class="container">
        <div class="left">
          <img src="@/assets/images/logo.svg" class="logo" width="80" alt="">
        </div>
        <div class="right">
          <span>Copyright © 2014-2022 杭州尚尚签网络科技有限公司</span>
          <span style="padding-left: 20px;">浙ICP备14031930号-1</span>
        </div>
      </div>
    </div>
    <div v-else>
      <div class="container">
        <div class="left">
          <img src="@/assets/images/logo.svg" class="logo" width="80" alt="">
          <p>Copyright © 2014-2022 杭州尚尚签网络科技有限公司 <span style="padding-left: 30px;">浙ICP备14031930号-1</span></p>
        </div>
        <div class="right">
          <div class="phone">
            <p>
              <img src="@/assets/images/landscape/phone.png" alt="">
              <span style="font-size: 14px;">24小时热线</span>
            </p>
            <p>+86 ************</p>
          </div>
          <div class="code">
            <img src="@/assets/images/wechat.jpg" width="80" alt="">
            <p>关注公众号</p>
            <p>获取更多案例</p>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'h5-single-pc-VFooter',
  computed: {
    isMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    handleScroll() {
      this.$scrollTo('body');
    },
  },
};
</script>

<style scoped lang="scss">
.footer {
  padding: 40px 0;
  .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .left {
    text-align: left;
  }
  .right {
    display: flex;
    align-items: center;
  }
  .phone {
    color: #515151;
    margin-right: 20px;
  }
  .code {
    color: #595757;
    text-align: center;
  }
}
@media screen and (max-width: 767px) {
  .footer {
    padding: 30px 0 80px;
  }
}
</style>
