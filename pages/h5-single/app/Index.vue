<template>
  <div class="appcontent" id="j_content">
    <div class="h5-single-body">
      <ApplyTry></ApplyTry>
      <introduce></introduce>
      <index-usage :isWhiteBg="isMobile"></index-usage>
      <begin-sign :scroll="true"></begin-sign>
      <v-footer></v-footer>
      <FloatHelp></FloatHelp>
    </div>
  </div>
</template>

<script>
import ApplyTry from '../pc/ApplyTry';
import Introduce from './Introduce';
import Footer from '../pc/Footer';
import BeginSign from '@/components/BeginSign';
import FloatHelp from '@/components/FloatHelp.vue';
import IndexUsage from '@/components/Index/Usage.vue';
export default {
  name: 'h5-single-app-index',
  layout: 'blank',
  components: {
    ApplyTry,
    Introduce,
    FloatHelp,
    'v-footer': Footer,
    BeginSign,
    IndexUsage,
  },
  head() {
    return {
      title: '电子合同签约_电子签名_在线合同签约平台_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同,电子签名,电子签章,电子签约',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签是业内领先的在线电子合同签署，电子签名，电子签章的云服务平台，提供多种行业专属解决方案，同时上上签电子签名确保在线合同及电子签约过程合法有效性，实现电子合同在线签署、编辑、管理等功能。',
        },
      ],
    };
  },
  mounted() {
    window._agl = window._agl || [];
    (function() {
      _agl.push(['production', '_f7L2XwGXjyszb4d1e2oxPybgD']);
      (function() {
        var agl = document.createElement('script');
        agl.type = 'text/javascript';
        agl.async = true;
        agl.src =
          'https://fxgate.baidu.com/angelia/fcagl.js?production=_f7L2XwGXjyszb4d1e2oxPybgD';
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(agl, s);
      })();
    })();
    window._agl && window._agl.push(['track', ['success', { t: 3 }]]);
  },
};
</script>

<style lang="scss">
.appcontent {
  width: 100%;
  height: 100%;
  overflow: auto;
}
.h5-single-body {
  width: 100%;
  text-align: center;
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}
</style>
