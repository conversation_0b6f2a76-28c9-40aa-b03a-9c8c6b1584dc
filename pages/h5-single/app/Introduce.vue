<template>
  <div class="introduce">
    <Contract ref="registerCom" />
    <div class="part part3">
      <div class="h5-container">
        <h3 class="section-header">上上签四大安全体系</h3>
        <div class="content">
          <el-row :gutter="10" class="column-wrap">
            <el-col :span="12">
              <div class="column">
                <h4>认证安全</h4>
                <div class="h4-sub">行业率先获得<br>全球7大安全认证</div>
                <ul class="list pl">
                  <li>ISO/IEC 27018<br>隐私数据保护认证</li>
                  <li>ISO/IEC 27001<br>安全管理体系认证</li>
                  <li>工信部<br>可信云认证</li>
                  <li>公安部信息系统安全<br>等级保护三级认证</li>
                  <li>ISO 38505-1:2017<br>数据治理认证</li>
                  <li>云计算SaaS<br>服务能力符合性评估三级认证</li>
                  <li>ISO 22301:2019<br>业务连续性管理体系认证</li>
                </ul>
                <div class="icon-wrap">
                  <i class="iconfont icon-h-icon11"></i>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="column">
                <h4>合规安全</h4>
                <div class="h4-sub">行业标准参与制定者</div>
                <ul class="list pl" style="padding-top: 18px;">
                  <li>三大权威CA机构<br>合作支持</li>
                  <li>全流程记录存证<br>全过程可出证</li>
                  <li>对接司法流程<br>全系统司法鉴定</li>
                </ul>
                <div class="icon-wrap">
                  <i class="iconfont icon-h-icon8"></i>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="column">
                <h4>运营安全</h4>
                <div class="h4-sub">可用性不低于99.99%<br>支持每秒6000+次签署</div>
                <ul class="list pl">
                  <li>高并发<br>每秒6000+次签署</li>
                  <li>高可用<br>7X24小时不间断运维<br>服务可用性99.99%</li>
                  <li>高可靠<br>同城双活,异地容灾<br>1900+天无宕机记录</li>
                </ul>
                <div class="icon-wrap">
                  <i class="iconfont icon-h-icon10"></i>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="column">
                <h4>使用安全</h4>
                <div class="h4-sub">金融级三层密钥加密<br>合同数据一文一密加密</div>
                <ul class="list pl">
                  <li>数据加密<br>金融级三层密钥加密<br>实现数据高强度保护</li>
                  <li>区块链技术<br>保证数据不可纂改<br>实时存证</li>
                </ul>
                <div class="icon-wrap">
                  <i class="iconfont icon-h-icon9"></i>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div style="margin: 3rem auto 2rem;text-align: center">
          <div class="h5-demo-button"  @click="$scrollTo('body')">免费试用</div>
        </div>
      </div>
    </div>
    <!-- <div class="part part4">
      <div class="section-header">上上签服务客户</div>
      <div class="img-wrapper">
        <img src="@/assets/images/h5-single/app/1.jpg" alt="" @click="$router.push('/case#HR')">
        <img src="@/assets/images/h5-single/app/2.jpg" alt="" @click="$router.push('/case#finance')">
        <img src="@/assets/images/h5-single/app/3.jpg" alt="" @click="$router.push('/case#car')">
        <img src="@/assets/images/h5-single/app/4.jpg" alt="" @click="$router.push('/case#lease')">
        <img src="@/assets/images/h5-single/app/5.jpg" alt="" @click="$router.push('/case#retail')">
        <img src="@/assets/images/h5-single/app/6.jpg" alt="" @click="$router.push('/case#logi')">
        <img src="@/assets/images/h5-single/app/7.jpg" alt="" @click="$router.push('/case#B2BB')">
        <img src="@/assets/images/h5-single/app/8.jpg" alt="" @click="$router.push('/case#internet')">
      </div>
      <div style="margin: 3rem auto 2rem;text-align: center">
        <div class="h5-demo-button"  @click="$scrollTo('body')">免费试用</div>
      </div>
    </div> -->
    <!-- <div class="part part5">
      <div class="section-header">上上签战略合作伙伴</div>
      <div class="img-wrapper">
        <img src="@/assets/images/h5-single/app/coop.jpg" alt="">
      </div>
    </div> -->
  </div>
</template>

<script>
import Contract from '@/components/Index/Contract_img.vue';
export default {
  name: 'h5-single-app-introduce',
  components: {
    Contract,
  },
};
</script>

<style scoped lang="scss">
.part {
  padding: 35px 18px;
  .section-header {
    font-size: 18px;
    line-height: 30px;
  }
  .column-wrap {
    margin: 0 auto;
    .column {
      &.bordered {
        border-right: 1px solid #a8a8a8;
      }
      h4 {
        padding-top: 20px;
        font-size: 18px;
        color: #00aa64;
      }
      .text-wrap {
        border-top: 1px solid #8c8c8c;
        flex: 1;
        margin-left: 20px;
      }
      .row {
        margin-top: 24px;
        height: 160px;
      }
      .iconfont {
        font-size: 40px;
      }
      .main-desc {
        margin: 20px auto 10px;
        font-size: 15px;
        color: #231815;
        font-weight: 500;
        text-align: left;
      }
      .sub-desc {
        line-height: 18px;
        font-size: 12px;
        color: #5d5d5d;
        text-align: left;
      }
    }
  }
  .header--num {
    color: #00aa64;
    font-size: 18px;
  }
}
.part2 {
  background-color: rgba(93, 93, 93, 0.08);
  .column-wrap {
    width: 100%;
    .column {
      margin-top: 35px;
      display: flex;
      h4 {
        border-top: 1px solid #00aa64;
        padding-top: 20px;
        width: 25%;
        font-size: 18px;
        text-align: left;
      }
    }
  }
}
.part3 {
  .section-header {
    padding-bottom: 25px;
  }
  .column-wrap {
    .column {
      position: relative;
      height: 450px;
      background-color: rgba(93, 93, 93, 0.08);
      overflow: hidden;
      margin-bottom: 10px;
      h4 {
        font-size: 16px;
        margin-bottom: 12px;
      }
      .h4-sub {
        line-height: 17px;
      }
      .list {
        li {
          margin-top: 12px;
          text-align: left;
          line-height: 18px;
          list-style-image: url(~assets/images/h5-single/dot.png);
        }
        &.pl {
          padding-left: 30px;
        }
      }
      .icon-wrap {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate3d(-50%, -50%, 0);
      }
      .iconfont {
        color: rgba(0, 0, 0, 0.05);
        font-size: 68px;
      }
    }
  }
}
.part4 {
  padding: 35px 0;
  .img-wrapper {
    width: 100%;
    margin-top: 25px;
    img {
      width: 100%;
      padding-bottom: 5px;
    }
  }
}
.part5 {
  padding: 35px 0 0;
  .img-wrapper {
    width: 100%;
    margin-top: 25px;
    img {
      width: 100%;
    }
  }
}
</style>
