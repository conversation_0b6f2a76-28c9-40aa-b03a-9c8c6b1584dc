<template>
  <article class="product-price">
    <div class="contract_price" v-if="!isMobile" id="contract_price">
    <div class="btn_switch">
      <div class="btn_anniu1" @click="change(0)" :class="{ newStyle:0===number}">与个人签署合同</div>
      <div class="btn_anniu2" @click="change(1)" :class="{ newStyle:1===number}">与企业签署合同</div>
    </div>
	  <p class="article-subtitle help">以下套餐价格对应标准版功能，如有疑问可拨打服务热线：************</p>
    <div class="tab">
       <div v-show="0===number" class="tabContainer">
        <table>
            <thead>
                <tr>
                    <!-- 模拟表头分割线的部分 -->
                    <th>
                        <div class="out">
                            <!-- <b>套餐</b>
                            <em>功能</em> -->
                            <div class="table-classic-top">套餐</div>
                            <div class="table-classic-bottom">功能</div>
                            <div class="f-tri f-blackTri"></div>
                            <div class="f-tri f-whiteTri"></div>
                        </div>
                    </th>
                    <!-- <th><div class="price_title">新用户免费试用套餐</div>
                      <div class="price_price">￥0</div>
                      <button class="price_button" @click="toYearDemo('recharge')">
                       免费试用
                      </button>
                    </th> -->
                    <th><div class="price_title">对私合同套餐A</div>
                      <div class="price_price">￥60</div>
                      <!-- <button class="price_button" @click="toDemo('62','duisiA')"> -->
                        <button class="price_button" @click="toDemo('33','duisiA')">
                        立即购买
                      </button>
                    </th>
                    <th><div class="price_title">对私合同套餐B</div>
                      <div class="price_price">￥570</div>
                      <!-- <button class="price_button" @click="toDemo('68','duisiB')"> -->
                        <button class="price_button" @click="toDemo('70','duisiB')">
                        立即购买
                      </button>
                    </th>
                    <th><div class="price_title">对私合同套餐C</div>
                      <div class="price_price">￥2400</div>
                      <!-- <button class="price_button" @click="toDemo('69','duisiC')"> -->
                        <button class="price_button" @click="toDemo('71','duisiC')"> 
                      立即购买
                      </button>
                    </th>
                    <th><div class="price_title">对私合同套餐D</div>
                      <div class="price_price">￥4500</div>
                      <!-- <button class="price_button" @click="toDemo('78','duisiD')"> -->
                        <button class="price_button" @click="toDemo('72','duisiD')">
                       立即购买
                      </button>
                    </th>
                    <th><div class="price_title">高级解决方案</div>
                      <div class="price_dsc">致电获取更多信息<br>************</div>
                      <button class="price_button_white" @click="toDesign('1')">
                        立即留言
                      </button>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>合同份数</td>
                    <td>10份</td>
                    <td>100份</td>
                    <td>500份</td>
                    <td>1000份</td>
                    <td style="color:rgb(233, 84, 28);">1000份以上</td>
                </tr>
                <tr>
                    <td>有效期</td>
                    <td>12个月</td>
                    <td>12个月</td>
                    <td>12个月</td>
                    <td>12个月</td>
                    <td>12个月</td>
                </tr>
                <tr>
                    <td>成员管理账号</td>
                    <td>初始3个</td>
                    <td>初始3个</td>
                    <td>初始3个</td>
                    <td>初始3个</td>
                    <td rowspan="4">合同用量超过1000份，您可以通过在线留言的方式获得专业服务支持。 </td>
                </tr>
                <tr>
                    <td>印章管理</td>
                    <td>不限制</td>
                    <td>不限制</td>
                    <td>不限制</td>
                    <td>不限制</td>
                </tr>
                <tr>
                    <td>合同模板</td>
                    <td>不限制</td>
                    <td>不限制</td>
                    <td>不限制</td>
                    <td>不限制</td>
                </tr>
            </tbody>
        </table>
      </div>
      <div v-show="1===number" class="tabContainer">
        <table>
            <thead>
                <tr>
                    <!-- 模拟表头分割线的部分 -->
                    <th>
                        <div class="out">
                            <!-- <b>套餐</b>
                            <em>功能</em> -->
                            <div class="table-classic-top">套餐</div>
                            <div class="table-classic-bottom">功能</div>
                            <div class="f-tri f-blackTri"></div>
                            <div class="f-tri f-whiteTri"></div>
                        </div>
                    </th>
                    <!-- <th><div class="price_title">新用户免费试用套餐</div>
                      <div class="price_price">￥0</div>
                      <button class="price_button" @click="toYearDemo('recharge')">
                        免费试用
                      </button>
                    </th> -->
                    <th><div class="price_title">对公合同套餐A</div>
                      <div class="price_price">￥200</div>
                      <!-- <button class="price_button" @click="toDemo('58','duigongA')"> -->
                        <button class="price_button" @click="toDemo('67','duigongA')">
                        立即购买
                      </button>
                    </th>
                    <th><div class="price_title">对公合同套餐B</div>
                      <div class="price_price">￥1900</div>
                      <!-- <button class="price_button" @click="toDemo('66','duigongB')"> -->
                        <button class="price_button" @click="toDemo('68','duigongB')">
                        立即购买
                      </button>
                    </th>
                    <th><div class="price_title">对公合同套餐C</div>
                      <div class="price_price">￥8000</div>
                      <!-- <button class="price_button" @click="toDemo('67','duigongC')"> -->
                        <button class="price_button" @click="toDemo('69','duigongC')">
                        立即购买
                      </button>
                    </th>
                    <th><div class="price_title">对公合同套餐D</div>
                      <div class="price_price">￥15000</div>
                      <!-- <button class="price_button" @click="toDemo('79','duigongD')"> -->
                        <button class="price_button" @click="toDemo('73','duigongD')">
                        立即购买
                      </button>
                    </th>
                    <th><div class="price_title">高级解决方案</div>
                      <div class="price_dsc">致电获取更多信息<br>************</div>
                      <button class="price_button_white" @click="toDesign('0')">
                        立即留言
                      </button>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>合同份数</td>
                    <td>10份</td>
                    <td>100份</td>
                    <td>500份</td>
                    <td>1000份</td>
                    <td style="color:rgb(233, 84, 28);">1000份以上</td>
                </tr>
                <tr>
                    <td>有效期</td>
                    <td>12个月</td>
                    <td>12个月</td>
                    <td>12个月</td>
                    <td>12个月</td>
                    <td>12个月</td>
                </tr>
                <tr>
                    <td>成员管理账号</td>
                    <td>初始3个</td>
                    <td>初始3个</td>
                    <td>初始3个</td>
                    <td>初始3个</td>
                    <td rowspan="4">合同用量超过1000份，您可以通过在线留言的方式获得专业服务支持。 </td>
                </tr>
                <tr>
                    <td>印章管理</td>
                    <td>不限制</td>
                    <td>不限制</td>
                    <td>不限制</td>
                    <td>不限制</td>
                </tr>
                <tr>
                    <td>合同模板</td>
                    <td>不限制</td>
                    <td>不限制</td>
                    <td>不限制</td>
                    <td>不限制</td>
                </tr>
            </tbody>
        </table>
      </div>
    </div>
    <!-- 添加定制方案 -->
    <el-dialog :visible.sync="open" width="600px" center>
      <div class="design_title">
        <h1>填写信息，联系我们的专业顾问</h1>
        <p>立刻联系: ************</p>
      </div>
      <el-form :model="dataForm" :rules="rules" ref="dataForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="姓名：" prop="customerName">
          <el-input v-model="dataForm.customerName"></el-input>
        </el-form-item>
        <el-form-item label="手机号：" prop="userPhone">
      <el-input v-model="dataForm.userPhone" placeholder="请输入你的手机号"></el-input>
    </el-form-item>
    <el-form-item label="短信验证：" prop="verifyCode">
      <el-input v-model.trim="dataForm.verifyCode" placeholder="请输入6位验证码">
        <template slot="append">
          <count-down class="countDown code-sent" :clickedFn="send" :disabled="countDownDisabled" ref="btn" :second="60"></count-down>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item label="图形验证：" v-if="showPictureVerCon" class="pictureVer-item" prop="imageCode">
      <el-input
        class="pictureVer"
        placeholder="请填写4位图形验证码"
        :maxlength="4"
        v-model.trim="dataForm.imageCode"
      >
        <template slot="append">
          <PictureVerify
            class="form-pictureVerify"
            ref="pictureVerify"
            :imageKey="dataForm.imageKey"
            @change-imageKey="changeImageKey"
          />
        </template>
      </el-input>
    </el-form-item>
        <!-- <el-form-item label="手机号：" prop="contact">
          <el-input v-model="dataForm.contact"></el-input>
        </el-form-item> -->
        <el-form-item label="邮箱：" prop="mail">
          <el-input v-model="dataForm.mail"></el-input>
        </el-form-item>
        <el-form-item label="公司名称:" prop="companyName">
          <el-input v-model="dataForm.companyName"></el-input>
        </el-form-item>
        <el-form-item label="需求内容:" prop="addInfo">
          <el-input 
            type="textarea" 
            :rows="2"
            placeholder="请描述您需要帮助的内容:(最多200个字符)" 
            v-model="dataForm.addInfo"
            maxlength="200">
          </el-input>
        </el-form-item>
      </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" class="submit_button" @click="submitForm">提交信息</el-button>
            </div>
        </el-dialog>
    </div>
    <div v-else class="contract_price" id="contract_price">
      <div class="btn_switch">
        <div class="btn_anniu1" @click="change(0)" :class="{ newStyle:0===number}">与个人签署合同</div>
        <div class="btn_anniu2" @click="change(1)" :class="{ newStyle:1===number}">与企业签署合同</div>
      </div>
      <div style="color: #e9541c; font-size: 14px;">温馨提示：请在电脑端完成购买流程。</div>
        <div v-show="0===number" class="tabContainer_wap">
          <div class="tab_wap">
            <!-- <div class="mold">
              <div class="price_title">新用户免费试用套餐</div>
              <div class="price_price">￥0</div>
              <button class="price_button" @click="toYearDemo('recharge')">
                免费试用
              </button>
            </div> -->
            <div class="mold">
              <div class="price_title">对私合同套餐A</div>
              <div class="price_price">￥60</div>
              <!-- <button class="price_button" @click="toDemo('62','duisiA')"> -->
                <button class="price_button" @click="toDemo('33','duisiA')">
                立即购买
              </button>
            </div>
            <div class="number">
              <div class="num_title">合同份数</div>
              <div class="num_dsc">10份</div>
            </div>
            <div class="detail" @click="clickFree">套餐功能详情  
              <svg class="icon" aria-hidden="true" v-if="showFree">
              <use xlink:href="#icon-xiangshangjiantou"></use>
              </svg>
              <svg class="icon" aria-hidden="true" v-if="!showFree">
              <use xlink:href="#icon-xiangxiajiantou"></use>
              </svg>
            </div>
            <div class="detail_tab" v-show="showFree">
              <div class="detail_dsc">
                <div class="detail_title">有效期</div>
                <div class="detail_content">12个月</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">成员管理账号</div>
                <div class="detail_content">初始3个</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">印章管理</div>
                <div class="detail_content">不限制</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">合同模板</div>
                <div class="detail_content">不限制</div>
              </div>
            </div>
          </div>
          <div class="tab_wap">
            <div class="mold">
              <div class="price_title">对私合同套餐B</div>
              <div class="price_price">￥570</div>
              <!-- <button class="price_button" @click="toDemo('68','duisiB')"> -->
                <button class="price_button" @click="toDemo('70','duisiB')">
                立即购买
              </button>
            </div>
            <div class="number">
              <div class="num_title">合同份数</div>
              <div class="num_dsc">100份</div>
            </div>
            <div class="detail" @click="clickA">套餐功能详情  
              <svg class="icon" aria-hidden="true" v-if="showA">
              <use xlink:href="#icon-xiangshangjiantou"></use>
              </svg>
              <svg class="icon" aria-hidden="true" v-if="!showA">
              <use xlink:href="#icon-xiangxiajiantou"></use>
              </svg>
            </div>
            <div class="detail_tab" v-show="showA">
              <div class="detail_dsc">
                <div class="detail_title">有效期</div>
                <div class="detail_content">12个月</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">成员管理账号</div>
                <div class="detail_content">初始3个</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">印章管理</div>
                <div class="detail_content">不限制</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">合同模板</div>
                <div class="detail_content">不限制</div>
              </div>
            </div>
          </div>
          <div class="tab_wap">
            <div class="mold">
              <div class="price_title">对私合同套餐C</div>
              <div class="price_price">￥2400</div>
              <!-- <button class="price_button" @click="toDemo('69','duisiC')"> -->
                <button class="price_button" @click="toDemo('71','duisiC')"> 
                立即购买
              </button>
            </div>
            <div class="number">
              <div class="num_title">合同份数</div>
              <div class="num_dsc">500份</div>
            </div>
            <div class="detail" @click="clickB">套餐功能详情  
              <svg class="icon" aria-hidden="true" v-if="showB">
              <use xlink:href="#icon-xiangshangjiantou"></use>
              </svg>
              <svg class="icon" aria-hidden="true" v-if="!showB">
              <use xlink:href="#icon-xiangxiajiantou"></use>
              </svg>
            </div>
            <div class="detail_tab" v-show="showB">
              <div class="detail_dsc">
                <div class="detail_title">有效期</div>
                <div class="detail_content">12个月</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">成员管理账号</div>
                <div class="detail_content">初始3个</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">印章管理</div>
                <div class="detail_content">不限制</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">合同模板</div>
                <div class="detail_content">不限制</div>
              </div>
            </div>
          </div>
          <div class="tab_wap">
            <div class="mold">
              <div class="price_title">对私合同套餐D</div>
              <div class="price_price">￥4500</div>
              <!-- <button class="price_button" @click="toDemo('78','duisiD')"> -->
                <button class="price_button" @click="toDemo('72','duisiD')">
                立即购买
              </button>
            </div>
            <div class="number">
              <div class="num_title">合同份数</div>
              <div class="num_dsc">1000份</div>
            </div>
            <div class="detail" @click="clickC">套餐功能详情  
              <svg class="icon" aria-hidden="true" v-if="showC">
              <use xlink:href="#icon-xiangshangjiantou"></use>
              </svg>
              <svg class="icon" aria-hidden="true" v-if="!showC">
              <use xlink:href="#icon-xiangxiajiantou"></use>
              </svg>
            </div>
            <div class="detail_tab" v-show="showC">
              <div class="detail_dsc">
                <div class="detail_title">有效期</div>
                <div class="detail_content">12个月</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">成员管理账号</div>
                <div class="detail_content">初始3个</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">印章管理</div>
                <div class="detail_content">不限制</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">合同模板</div>
                <div class="detail_content">不限制</div>
              </div>
            </div>
          </div>
          <div class="tab_wap">
            <div class="mold">
              <div class="price_title">高级解决方案</div>
              <div class="price_dsc">致电获取更多信息<br>************</div>
                <button class="price_button_white" @click="toDesign('1')">
                  立即留言
                </button>
            </div>
            <div class="number">
              <div class="num_title">合同份数</div>
              <div class="num_dsc">1000份以上</div>
            </div>
            <div class="detail" @click="clickSign">套餐功能详情  
              <svg class="icon" aria-hidden="true" v-if="showSign">
              <use xlink:href="#icon-xiangshangjiantou"></use>
              </svg>
              <svg class="icon" aria-hidden="true" v-if="!showSign">
              <use xlink:href="#icon-xiangxiajiantou"></use>
              </svg>
            </div>
            <div class="detail_tab" v-show="showSign">
              <div class="detail_dsc">
                <div class="detail_title">有效期</div>
                <div class="detail_content">12个月</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">成员管理账号</div>
                <div class="detail_content">初始3个</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">印章管理</div>
                <div class="detail_content">不限制</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">合同模板</div>
                <div class="detail_content">不限制</div>
              </div>
            </div>
          </div>
        </div>
        <div v-show="1===number" class="tabContainer_wap">
          <div class="tab_wap">
            <!-- <div class="mold">
              <div class="price_title">新用户免费试用套餐</div>
              <div class="price_price">￥0</div>
              <button class="price_button" @click="toYearDemo('recharge')">
                免费试用
              </button>
            </div> -->
            <div class="mold">
              <div class="price_title">对公合同套餐A</div>
              <div class="price_price">￥200</div>
              <!-- <button class="price_button" @click="toDemo('58','duigongA')"> -->
                <button class="price_button" @click="toDemo('67','duigongA')">
                立即购买
              </button>
            </div>
            <div class="number">
              <div class="num_title">合同份数</div>
              <div class="num_dsc">10份</div>
            </div>
            <div class="detail" @click="clickFree">套餐功能详情  
              <svg class="icon" aria-hidden="true" v-if="showFree">
              <use xlink:href="#icon-xiangshangjiantou"></use>
              </svg>
              <svg class="icon" aria-hidden="true" v-if="!showFree">
              <use xlink:href="#icon-xiangxiajiantou"></use>
              </svg>
            </div>
            <div class="detail_tab" v-show="showFree">
              <div class="detail_dsc">
                <div class="detail_title">有效期</div>
                <div class="detail_content">12个月</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">成员管理账号</div>
                <div class="detail_content">初始3个</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">印章管理</div>
                <div class="detail_content">不限制</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">合同模板</div>
                <div class="detail_content">不限制</div>
              </div>
            </div>
          </div>
          <div class="tab_wap">
            <div class="mold">
              <div class="price_title">对公合同套餐B</div>
              <div class="price_price">￥1900</div>
              <!-- <button class="price_button" @click="toDemo('66','duigongB')"> -->
                <button class="price_button" @click="toDemo('68','duigongB')">
                立即购买
              </button>
            </div>
            <div class="number">
              <div class="num_title">合同份数</div>
              <div class="num_dsc">100份</div>
            </div>
            <div class="detail" @click="clickA">套餐功能详情  
              <svg class="icon" aria-hidden="true" v-if="showA">
              <use xlink:href="#icon-xiangshangjiantou"></use>
              </svg>
              <svg class="icon" aria-hidden="true" v-if="!showA">
              <use xlink:href="#icon-xiangxiajiantou"></use>
              </svg>
            </div>
            <div class="detail_tab" v-show="showA">
              <div class="detail_dsc">
                <div class="detail_title">有效期</div>
                <div class="detail_content">12个月</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">成员管理账号</div>
                <div class="detail_content">初始3个</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">印章管理</div>
                <div class="detail_content">不限制</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">合同模板</div>
                <div class="detail_content">不限制</div>
              </div>
            </div>
          </div>
          <div class="tab_wap">
            <div class="mold">
              <div class="price_title">对公合同套餐C</div>
              <div class="price_price">￥8000</div>
              <!-- <button class="price_button" @click="toDemo('67','duigongC')"> -->
                <button class="price_button" @click="toDemo('69','duigongC')">
                立即购买
              </button>
            </div>
            <div class="number">
              <div class="num_title">合同份数</div>
              <div class="num_dsc">500份</div>
            </div>
            <div class="detail" @click="clickB">套餐功能详情  
              <svg class="icon" aria-hidden="true" v-if="showB">
              <use xlink:href="#icon-xiangshangjiantou"></use>
              </svg>
              <svg class="icon" aria-hidden="true" v-if="!showB">
              <use xlink:href="#icon-xiangxiajiantou"></use>
              </svg>
            </div>
            <div class="detail_tab" v-show="showB">
              <div class="detail_dsc">
                <div class="detail_title">有效期</div>
                <div class="detail_content">12个月</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">成员管理账号</div>
                <div class="detail_content">初始3个</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">印章管理</div>
                <div class="detail_content">不限制</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">合同模板</div>
                <div class="detail_content">不限制</div>
              </div>
            </div>
          </div>
          <div class="tab_wap">
            <div class="mold">
              <div class="price_title">对公合同套餐D</div>
              <div class="price_price">￥15000</div>
              <!-- <button class="price_button" @click="toDemo('79','duigongD')"> -->
                <button class="price_button" @click="toDemo('73','duigongD')">
                立即购买
              </button>
            </div>
            <div class="number">
              <div class="num_title">合同份数</div>
              <div class="num_dsc">1000份</div>
            </div>
            <div class="detail" @click="clickC">套餐功能详情  
              <svg class="icon" aria-hidden="true" v-if="showC">
              <use xlink:href="#icon-xiangshangjiantou"></use>
              </svg>
              <svg class="icon" aria-hidden="true" v-if="!showC">
              <use xlink:href="#icon-xiangxiajiantou"></use>
              </svg>
            </div>
            <div class="detail_tab" v-show="showC">
              <div class="detail_dsc">
                <div class="detail_title">有效期</div>
                <div class="detail_content">12个月</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">成员管理账号</div>
                <div class="detail_content">初始3个</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">印章管理</div>
                <div class="detail_content">不限制</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">合同模板</div>
                <div class="detail_content">不限制</div>
              </div>
            </div>
          </div>
          <div class="tab_wap">
            <div class="mold">
              <div class="price_title">高级解决方案</div>
              <div class="price_dsc">致电获取更多信息<br>************</div>
                <button class="price_button_white" @click="toDesign('0')">
                  立即留言
                </button>
            </div>
            <div class="number">
              <div class="num_title">合同份数</div>
              <div class="num_dsc">1000份以上</div>
            </div>
            <div class="detail" @click="clickSign">套餐功能详情  
              <svg class="icon" aria-hidden="true" v-if="showSign">
              <use xlink:href="#icon-xiangshangjiantou"></use>
              </svg>
              <svg class="icon" aria-hidden="true" v-if="!showSign">
              <use xlink:href="#icon-xiangxiajiantou"></use>
              </svg>
            </div>
            <div class="detail_tab" v-show="showSign">
              <div class="detail_dsc">
                <div class="detail_title">有效期</div>
                <div class="detail_content">12个月</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">成员管理账号</div>
                <div class="detail_content">初始3个</div>
              </div>
              <div class="detail_dsc">
                <div class="detail_title">印章管理</div>
                <div class="detail_content">不限制</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 添加定制方案 -->
    <el-dialog :visible.sync="open" width="90%" top="5vh" center>
      <div class="design_title">
        <h1>填写信息，联系我们的专业顾问</h1>
        <p>立刻联系: ************</p>
      </div>
             <el-form :model="dataForm" :rules="rules" ref="dataForm" label-width="100px" class="demo-ruleForm">
              <el-form-item  prop="customerName">
                <el-input v-model="dataForm.customerName" placeholder="姓名"></el-input>
              </el-form-item>
              <!-- <el-form-item prop="contact">
                <el-input v-model="dataForm.contact" placeholder="手机号"></el-input>
              </el-form-item> -->
              <el-form-item prop="userPhone">
      <el-input v-model="dataForm.userPhone" placeholder="请输入你的手机号"></el-input>
    </el-form-item>
    <el-form-item prop="verifyCode">
      <el-input v-model.trim="dataForm.verifyCode" placeholder="请输入6位验证码">
        <template slot="append">
          <count-down class="countDown code-sent" :clickedFn="send" :disabled="countDownDisabled" ref="btn" :second="60"></count-down>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item v-if="showPictureVerCon" class="pictureVer-item" prop="imageCode">
      <el-input
        class="pictureVer"
        placeholder="请填写4位图形验证码"
        :maxlength="4"
        v-model.trim="dataForm.imageCode"
      >
        <template slot="append">
          <PictureVerify
            class="form-pictureVerify"
            ref="pictureVerify"
            :imageKey="dataForm.imageKey"
            @change-imageKey="changeImageKey"
          />
        </template>
      </el-input>
    </el-form-item>
              <el-form-item  prop="mail">
                <el-input v-model="dataForm.mail" placeholder="邮箱"></el-input>
              </el-form-item>
              <el-form-item prop="companyName">
                <el-input v-model="dataForm.companyName" placeholder="公司名称"></el-input>
              </el-form-item>
              <el-form-item prop="addInfo">
                <el-input 
                  type="textarea" 
                  :rows="2"
                  placeholder="请描述您需要帮助的内容:(最多200个字符)" 
                  v-model="dataForm.addInfo"
                  maxlength="200">
                </el-input>
              </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" class="submit_button" @click="submitForm">提交信息</el-button>
            </div>
        </el-dialog>
      </div>
    
    <!-- <Usage></Usage> -->
    <el-dialog
          title="提示"
          :visible.sync="dialogVisible"
          width="50%"
          v-if="!isMobile">
          <span>提交成功。<br><br>上上签专业顾问将会24小时内与你联系</span>
        </el-dialog>
        <el-dialog
          title="提示"
          :visible.sync="dialogVisible"
          width="70%"
          v-if="isMobile">
          <span>提交成功。<br><br>上上签专业顾问将会24小时内与你联系</span>
        </el-dialog>
  </article>
</template>

<script>
import Qs from 'qs';
import Usage from '@/components/Index/Usage';
import resRules from '@/assets/utils/regs.js';
import CountDown from '@/components/CountDown.vue';
import PictureVerify from '@/components/PictureVerify.vue';
import aes from '@/assets/utils/aes.js';
import { isPhoneOrMail } from '@/assets/utils/reg.js';
export default {
  name: 'ProductPrice',
  components: {
    Usage,
    CountDown,
    PictureVerify,
  },
  head() {
    return {
      title: '电子合同价格介绍_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同免费试用,电子合同价格,电子合同套餐',
        },
        {
          hid: 'description',
          name: 'description',
          content: '上上签电子合同产品套餐价格介绍，欢迎免费试用。',
        },
      ],
    };
  },
  data() {
    return {
      swiperOption: {
        loop: true,
        navigation: {
          nextEl: '.ssq-button-next',
          prevEl: '.ssq-button-prev',
        },
      },
      number: 0,
      open: false,
      dialogVisible: false,
      formData: {
        linkUrl: '',
        linkTitle: '',
        onlineStatus: 0,
      },
      showFree: false,
      showA: false,
      showB: false,
      showC: false,
      showSign: false,
      dataForm: {
        customerName: '',
        companyName: '',
        // contact: '',
        mail: '',
        addInfo: '',
        verifyCode: '',
        verifyKey: '',
        userPhone: '',
      },
      showPictureVerCon: false,
      countDownDisabled: false,
      infoType: '',
      // form: {
      //   verifyCode: '',
      //   verifyKey: '',
      //   userPhone: '',
      // },
      rules: {
        customerName: [
          { required: true, message: '请输入您的姓名', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z]{2,10}$/,
            message: '请输入正确的姓名',
            trigger: 'blur',
          },
        ],
        userPhone: [
          { required: true, message: '请输入您的手机号', trigger: 'blur' },
          {
            pattern: resRules.userPhone,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
        mail: [
          { required: true, message: '请输入您的邮箱', trigger: 'blur' },
          {
            pattern: resRules.userEmail,
            message: '请输入正确的邮箱',
            trigger: 'blur',
          },
        ],
        companyName: [
          { required: true, message: '请输入您的公司名称', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9（）]{1,128}$/,
            message: '请输入正确的公司名称',
            trigger: 'blur',
          },
        ],
        addInfo: [
          { required: true, message: '请输入您的需求内容', trigger: 'blur' },
        ],
        verifyCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          {
            pattern: /^\d{6}.*$/,
            message: '请输入正确的验证码',
            trigger: 'blur',
          },
        ],
        imageCode: [
          { required: true, message: '请输入图形验证码', trigger: 'blur' },
          {
            pattern: /^\d{4}.*$/,
            message: '请输入正确的图形验证码',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  mounted() {
    this.$store.commit('changePath', { path: 'price' });
  },
  computed: {
    isMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toReg(param) {
      const query = sessionStorage.getItem('query');
      const url =
        window.location.host.indexOf('cn') > -1
          ? `https://ent.bestsign.cn/register?price_buy&${query}&${param}`
          : `https://ent.bestsign.info/register?price_buy&${query}&${param}`;
      window.open(url);
    },
    toDemo(id, contractType) {
      const query = sessionStorage.getItem('query');
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          ...Qs.parse(query),
          // [param]: '',
          utm_source: Qs.parse(query).utm_source,
          packageId: id,
          wwwScene: 'recharge',
          chongzhi: contractType,
        },
      });
    },
    toYearDemo(param) {
      const query = sessionStorage.getItem('query');
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          ...Qs.parse(query),
          [param]: '',
          utm_source: Qs.parse(query).utm_source,
        },
      });
    },
    change(num) {
      this.number = num;
    },
    handleActiveTab(element) {
      this.$scrollTo(element, 500, {
        offset: this.isMobile ? -80 : -120,
      });
      this.activeTab = element;
    },
    loginEnt() {
      const query = sessionStorage.getItem('query');
      console.log(query);
      const url =
        window.location.host.indexOf('cn') > -1
          ? 'https://ent.bestsign.cn'
          : 'https://ent.bestsign.info';
      window.open(url + '/?registerFrom=www' + query);
    },
    // 弹框出来表单重置为空
    reset() {
      this.dataForm.customerName = '';
      this.dataForm.companyName = '';
      this.dataForm.userPhone = '';
      this.dataForm.mail = '';
      this.dataForm.addInfo = '';
      this.dataForm.imageCode = '';
      this.dataForm.verifyCode = '';
    },
    toDesign(infoType) {
      // this.reset();
      this.open = true;
      this.infoType = infoType;
    },
    // submitForm() {
    // this.$axios
    //   .post('www/api/web/webfreecustomer/custom-recharge', {
    //     customerName: this.customerName,
    //     companyName: this.companyName,
    //     contact: this.contact,
    //     mail: this.mail,
    //     addInfo: this.addInfo,
    //   })
    //   .then(res => {
    //     if (res.code === '150001') {
    //       this.open = false;
    //       this.dialogVisible = true;
    //     } else {
    //       this.$message.error(res.message);
    //     }
    //   });
    // },
    // 更改图形验证码
    changeImageKey(value) {
      this.codeDisabled = false;
      this.dataForm.imageKey = value;
    },
    // 发送验证码
    send() {
      // debugger;
      const { userPhone, imageCode, imageKey } = this.dataForm;
      this.$refs['dataForm'].validateField(['userPhone'], error => {
        if (error) {
          return false;
        }
        let headersObj = {};
        if (imageCode !== '' && imageKey !== '') {
          headersObj = {
            // 'Content-Type': 'application/json; charset=utf-8',
            additionalImgVerCode: JSON.stringify({
              imageCode,
              imageKey,
            }),
            'request-source': 'WEB',
          };
        }
        // let host = 'https://ent.bestsign.info';
        // if (process.env.baseUrl.indexOf('cn') > -1) {
        //   host = 'https://ent.bestsign.cn';
        // }
        let host = '';
        if (process.env.NODE_ENV !== 'development') {
          host =
            process.env.baseUrl.indexOf('cn') > -1
              ? 'https://ent.bestsign.cn'
              : 'https://ent.bestsign.info';
        }
        this.loading = true;
        this.$axios({
          url: `${host}/users/ignore/captcha/notice?encryptToken=${aes(
            'B001',
            userPhone
          )}`,
          method: 'post',
          headers: headersObj,
          data: {
            code: 'B001',
            sendType: isPhoneOrMail(userPhone) === 'phone' ? 'S' : 'E',
            target: userPhone,
            imageCode,
            imageKey,
          },
        })
          .then(res => {
            this.loading = false;
            if (res) {
              this.$MessageToast.success('发送成功！');
              this.countDownDisabled = true;
              setTimeout(this.sended, 0);
              this.dataForm.verifyKey = res.value;
              this.codeDisabled = false;
            }
          })
          .catch(err => {
            this.loading = false;
            const res = err.response.data;
            if (res.code === '902' || res.code === '100006') {
              if (this.showPictureVerCon) {
                setTimeout(() => {
                  this.$refs.pictureVerify.changeImg();
                }, 20);
              } else {
                this.showPictureVerCon = true;
              }
              if (res.message !== '图片验证码不能为空') {
                this.$MessageToast.error(res.message);
              } else {
                this.$MessageToast.error('请先填写图形验证码');
              }
            } else {
              this.$MessageToast.error(res.message);
            }
            this.$refs.btn.reset();
          });
      });
    },
    sended() {
      this.$refs.btn.run();
      this.countDownDisabled = false;
    },
    // 检查校验码是否正确
    checkCode() {
      const { userPhone, verifyCode, verifyKey } = this.dataForm;
      const params = {
        target: userPhone,
        code: 'B001',
        verifyKey,
        verifyCode,
      };
      return new Promise((resolve, reject) => {
        this.$refs['dataForm'].validate(valid => {
          if (!valid) {
            this.loading = false;
            reject();
            // return false;
          }
          // let host = 'https://ent.bestsign.info';
          // if (process.env.baseUrl.indexOf('cn') > -1) {
          //   host = 'https://ent.bestsign.cn';
          // }
          let host = '';
          if (process.env.NODE_ENV !== 'development') {
            host =
              process.env.baseUrl.indexOf('cn') > -1
                ? 'https://ent.bestsign.cn'
                : 'https://ent.bestsign.info';
          }
          this.$axios
            .post(`${host}/users/ignore/captcha/verify`, params, {
              headers: {
                'Content-Type': 'application/json',
              },
            })
            .then(res => {
              // this.loading = false;
              if (res.value) {
                resolve();
              } else {
                this.$MessageToast.error(res.message);
                reject();
              }
            })
            .catch(err => {
              const res = err.response.data;
              this.$MessageToast.error(res.message);
              reject();
            });
        });
      });
    },
    submitForm(dataForm) {
      let self = this;
      this.loading = true;
      this.checkCode()
        .then(() => {
          this.$refs['dataForm'].validate(valid => {
            if (valid) {
              this.$axios
                .post('www/api/web/webfreecustomer/custom-recharge', {
                  customerName: this.dataForm.customerName,
                  companyName: this.dataForm.companyName,
                  contact: this.dataForm.userPhone,
                  mail: this.dataForm.mail,
                  addInfo: this.dataForm.addInfo,
                  infoType: this.infoType,
                  applyUrl: window.location.href,
                })
                .then(res => {
                  if (res.code === '150001') {
                    this.open = false;
                    this.dialogVisible = true;
                    this.reset();
                  } else {
                    this.$message.error(res.message);
                  }
                });
            }
          });
        })
        .finally(() => {
          self.loading = false;
        });
    },
    clickFree() {
      this.showFree = !this.showFree;
    },
    clickA() {
      this.showA = !this.showA;
    },
    clickB() {
      this.showB = !this.showB;
    },
    clickC() {
      this.showC = !this.showC;
    },
    clickSign() {
      this.showSign = !this.showSign;
    },
  },
};
</script>

<style scoped lang="scss">
.product-price {
  text-align: center;
  .section-1 {
    padding: 5rem 0;
    .article-subtitle {
      margin: 1.75rem auto;
    }
    .content {
      width: 90%;
      margin: 108px auto 0;
      display: flex;
      justify-content: space-around;
    }
    .plan-version {
      position: relative;
      display: inline-block;
      width: 100%;
      max-width: 300px;
      min-width: 205px;
      margin: 0 10px 40px;
      padding: 0;
      box-shadow: 0 8px 24px 0 rgba(82, 94, 102, 0.15);
      border: 1px solid #dcdcdc;
      border-top: none;
      .tag {
        padding: 3px 5px;
        color: #fff;
        background-color: #00a664;
        border-radius: 10px;
        &.less {
          padding-left: 0;
          padding-right: 0;
        }
      }
      .plan-header {
        position: relative;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        border-bottom: 1px solid #e5e5e5;
        background: #00a664;
        color: #fff;
        h4 {
          font-size: 1.5rem;
          margin: 0;
        }
        p {
          padding: 5px 0;
        }
      }
      .plan-price {
        padding-top: 32px;
        margin-left: 12px;
        margin-right: 12px;
        border-bottom: 1px solid #dcdcdc;
        .ssq-button-primary {
          width: 12rem;
        }
        .top-comment {
          text-align: center;
          height: 50px;
        }
        p {
          margin: 24px auto 32px;
          font-size: 14px;
          color: #8f8f8f;
        }
        .currency {
          position: relative;
          top: 8px;
          left: -4px;
          font-size: 20px;
          vertical-align: top;
        }
        .price-header {
          height: 80px;
          .money {
            font-size: 28px;
            color: #00a664;
            &.line-through {
              text-decoration-line: line-through;
            }
          }
        }
        .ssq-button-primary {
          margin-top: 15px;
          border: 1px solid #00aa64;
        }

        .comment {
          margin-top: 10px;
          line-height: 22px;
        }
        .contract {
          font-size: 14px;
          margin: 15px auto;
          line-height: 22px;
          padding-left: 80px;
          text-align: left;
        }
      }
      .plan-list {
        text-align: left;
        margin: 0 1.5rem;
        padding: 20px 0;
        min-height: 375px;
        &.bordered {
          border-bottom: 1px solid #eee;
          min-height: 550px;
        }
        ul {
          margin: 0 auto;
          h4 {
            margin: 0 0 0 16px;
            padding: 32px 0;
            color: #a6a6a6;
          }
          li {
            position: relative;
            font-size: 16px;
            margin-bottom: 20px;
            line-height: 1.5;
            padding-left: 30px;
            &.none {
              padding-left: 0;
            }
            .icon-gou-img {
              position: absolute;
              display: inline-block;
              left: 0;
              color: #00a664;
            }
          }
        }
      }
    }
  }
  .help {
    font-size: 16px;
    color: rgb(96, 96, 96);
    line-height: 30px;
    margin: 20px auto;
  }
  .contract_price {
    margin: 0 0 50px;
    // text-align: center;
    .btn_switch {
      margin: 20px;
      display: flex;
      justify-content: center;
      .btn_anniu1 {
        // width: 50%;
        padding: 13px;
        font-size: 18px;
        // font-weight: 500;
        border: 1px solid #e8e8e8;
        color: #000;
        outline: none;
        background: #fff;
        width: 15%;
        height: 45px;
        cursor: pointer;
        border-radius: 2px 0 0 2px;
      }
      .btn_anniu2 {
        // width: 50%;
        padding: 13px;
        font-size: 18px;
        // font-weight: 500;
        border-top: 1px solid #e8e8e8;
        border-right: 1px solid #e8e8e8;
        border-bottom: 1px solid #e8e8e8;
        color: #000;
        outline: none;
        background: #fff;
        width: 15%;
        height: 45px;
        cursor: pointer;
        border-radius: 0 2px 2px 0;
      }
      .newStyle {
        // border: 2px solid #808080;
        background-color: #f4f4f4;
        font-size: 18px;
        font-weight: bold;
      }
    }
    .tab {
      display: flex;
      justify-content: center;
      .tabContainer {
        table {
          border: 1px solid #fff;
          /*去掉表格之间的空隙*/
          border-collapse: collapse;
          margin: auto;
        }
        table tr:first-child th:first-child {
          border-top-left-radius: 2px;
        }

        table tr:first-child th:last-child {
          border-top-right-radius: 2px;
        }
        table tr:last-child td:first-child {
          border-bottom-left-radius: 2px;
        }

        table tr:last-child td:last-child {
          border-bottom-right-radius: 2px;
        }

        caption {
          font-size: 20px;
          font-weight: bold;
        }

        th,
        td {
          border: 1px solid #e8e8e8;
          width: 14rem;
          font-size: 1rem;
          line-height: 2rem;
          text-align: center;
          padding: 10px;
        }
        .price_title {
          font-size: 1.3rem;
        }
        .price_price {
          margin: 5px 5px;
          font-size: 1.3rem;
          color: rgb(233, 84, 28);
        }
        .price_dsc {
          margin: 5px 5px;
          font-size: 0.9rem;
          color: rgb(233, 84, 28);
          line-height: 18px;
        }
        .price_function {
          text-align: left;
        }
        .price_button {
          width: 8rem;
          height: 40px;
          min-width: 76px;
          padding: 0 12px;
          margin: 0;
          color: #fff;
          vertical-align: middle;
          border: none;
          border-radius: 26px;
          // line-height: 40px;
          outline: 0;
          box-sizing: border-box;
          transition: all 0.3s ease;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          position: relative;
          background: #00a664;
          box-shadow: 0 2px 8px 0 rgba(36, 10, 147, 0.15);
          cursor: pointer;
          font-size: 1rem;
          font-weight: 500;
        }
        .price_button_white {
          width: 8rem;
          height: 40px;
          min-width: 76px;
          padding: 0 12px;
          margin: 0;
          color: #00a664;
          vertical-align: middle;
          border: none;
          border-radius: 26px;
          // line-height: 40px;
          outline: 0;
          box-sizing: border-box;
          transition: all 0.3s ease;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          position: relative;
          background: #fff;
          box-shadow: 0 2px 8px 0 rgba(36, 10, 147, 0.15);
          cursor: pointer;
          border: 1px solid;
          font-size: 1rem;
        }

        /*模拟对角线*/
        .out {
          position: relative; /*让里面的两个子容器绝对定位*/
          .table-classic-top {
            top: -40px;
            right: 25px;
            width: auto;
            height: auto;
            position: absolute;
            z-index: 1;
            font-size: 1.3rem;
          }
          .table-classic-bottom {
            bottom: -40px;
            left: 25px;
            width: auto;
            height: auto;
            position: absolute;
            z-index: 1;
            font-size: 1.3rem;
          }
          .f-blackTri {
            position: absolute;
            left: 25px;
            top: -40px;
            z-index: 0;
            border-right: solid transparent 58px;
            border-left: solid #e8e8e8 58px;
            border-top: solid transparent 41px;
            border-bottom: solid #e8e8e8 41px;
          }
          .f-whiteTri {
            top: -39px;
            border-bottom-color: #fff !important;
            border-left-color: #fff !important;
            position: absolute;
            left: 25px;
            z-index: 0;
            border-right: solid transparent 58px;
            border-left: solid #e8e8e8 58px;
            border-top: solid transparent 41px;
            border-bottom: solid #e8e8e8 41px;
          }
        }
      }
    }
  }
  .design_title {
    text-align: center;
    margin-bottom: 10px;
    p {
      line-height: 2rem;
    }
  }
  .countDown {
    background-color: #fff;
    font-size: 14px;
    width: 100%;
    border: none;
    color: #00aa64;
  }
  /deep/.el-input-group__append {
    border-radius: 0;
    width: 125px;
    background-color: transparent;
  }
  .connection {
    margin: 1rem;
  }
  // .input {
  //   .el-input {
  //     margin: 10px;
  //     // width: 90%;
  //   }
  //   .el-textarea {
  //     margin: 10px;
  //   }
  // }
  .el-input {
    width: 85%;
    // -webkit-user-select: text !important;
  }
  .el-textarea {
    // -webkit-user-select: text !important;
    width: 85%;
  }
  .el-button--primary {
    color: #fff;
    background-color: #00a664;
    width: 180px;
    font-size: 1.1rem;
  }
  .swiper-container {
    .ssq-button-prev,
    .ssq-button-next {
      position: absolute;
      z-index: 99;
      width: 28px;
      height: 28px;
      line-height: 28px;
      background: #dcdcdc;
      border-radius: 28px;
      top: 50%;
      transform: translateY(-50%);
      &:focus {
        outline: none;
      }
      .i {
        color: #fff;
      }
    }
    .ssq-button-prev {
      left: 10px;
    }
    .ssq-button-next {
      right: 10px;
    }
  }
}
@media screen and (max-width: 767px) {
  .product-price {
    .el-input {
      width: 100%;
      // -webkit-user-select: text !important;
    }
    .el-textarea {
      width: 100%;
      // -webkit-user-select: text !important;
    }
    /deep/ .el-form-item__content {
      margin-left: 0 !important;
    }
    .container {
      max-width: 100%;
      width: 95%;
    }
    .section-1 {
      .article-headline {
        font-size: 2rem;
      }
      .article-subtitle {
        font-size: 0.9rem;
        padding: 0 1rem;
      }
    }
    .help {
      font-size: 14px;
      color: rgb(96, 96, 96);
      line-height: 42px;
    }
    .contract_price {
      margin: 0 2%;
      // text-align: center;
      // .input {
      //   display: flex;
      //   margin: 5px;
      //   flex-wrap: wrap;
      //   .el-input {
      //     margin: 10px;
      //   }
      //   .el-textarea {
      //     margin: 10px;
      //   }
      // }
      .btn_switch {
        margin: 1rem 0;
        display: flex;
        justify-content: center;
        .btn_anniu1 {
          // width: 50%;
          padding: 13px;
          font-size: 18px;
          border: 1px solid #e8e8e8;
          color: #000;
          outline: none;
          background: #fff;
          width: 50%;
          height: 45px;
          border-radius: 2px 0 0 2px;
        }
        .btn_anniu2 {
          // width: 50%;
          padding: 13px;
          font-size: 18px;
          border-top: 1px solid #e8e8e8;
          border-right: 1px solid #e8e8e8;
          border-bottom: 1px solid #e8e8e8;
          color: #000;
          outline: none;
          background: #fff;
          width: 50%;
          height: 45px;
          border-radius: 0 2px 2px 0;
        }
        .newStyle {
          // border: 2px solid #808080;
          background-color: #f4f4f4;
          font-size: 18px;
          font-weight: bold;
        }
      }
      .tab_wap {
        border: 1px solid #e8e8e8;
        margin: 2rem 0;
        border-radius: 2px;
        .mold {
          margin: 2rem 20%;
          .price_title {
            font-size: 1.5rem;
            color: #515151;
            font-weight: 500;
          }
          .price_price {
            margin: 5px 5px;
            font-size: 1.5rem;
            color: rgb(233, 84, 28);
            font-weight: 500;
          }
          .price_dsc {
            margin: 5px 5px;
            font-size: 1.1rem;
            color: rgb(233, 84, 28);
            line-height: 18px;
          }
          .price_function {
            text-align: left;
          }
          .price_button {
            width: 120px;
            height: 40px;
            min-width: 76px;
            padding: 0 12px;
            margin: 0;
            color: #fff;
            vertical-align: middle;
            border: none;
            border-radius: 26px;
            // line-height: 40px;
            outline: 0;
            box-sizing: border-box;
            transition: all 0.3s ease;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            position: relative;
            background: #00a664;
            box-shadow: 0 2px 8px 0 rgba(36, 10, 147, 0.15);
            cursor: pointer;
            font-size: 16px;
          }
          .price_button_white {
            width: 12rem;
            height: 40px;
            min-width: 76px;
            padding: 0 12px;
            margin: 0;
            color: #00a664;
            vertical-align: middle;
            border: none;
            border-radius: 26px;
            // line-height: 40px;
            outline: 0;
            box-sizing: border-box;
            transition: all 0.3s ease;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            position: relative;
            background: #fff;
            box-shadow: 0 2px 8px 0 rgba(36, 10, 147, 0.15);
            cursor: pointer;
            border: 1px solid;
            font-size: 1.3rem;
          }
        }
        .number {
          display: flex;
          height: 50px;
          .num_title {
            border-right: 1px solid #e8e8e8;
            border-top: 1px solid #e8e8e8;
            font-size: 1.2rem;
            padding: 15px;
            width: 35%;
          }
          .num_dsc {
            border-top: 1px solid #e8e8e8;
            font-size: 1.2rem;
            width: 65%;
            padding: 15px;
          }
        }
        .detail {
          background-color: #f4f4f4;
          height: 50px;
          font-size: 1.4rem;
          padding: 15px;
          color: #333;
        }
        .detail_tab {
          .detail_dsc {
            display: flex;
            // height: 50px;
            .detail_title {
              border-right: 1px solid #e8e8e8;
              border-bottom: 1px solid #e8e8e8;
              font-size: 1.2rem;
              padding: 15px;
              width: 35%;
              text-align: center;
            }
            .detail_content {
              border-bottom: 1px solid #e8e8e8;
              font-size: 1.2rem;
              width: 65%;
              padding: 15px;
              text-align: center;
            }
            .detail_title1 {
              border-right: 1px solid #e8e8e8;
              font-size: 1.2rem;
              padding: 15px;
              width: 35%;
              text-align: center;
            }
            .detail_content1 {
              font-size: 1.2rem;
              width: 65%;
              padding: 15px;
              text-align: center;
            }
          }
        }
      }
    }
  }
}
@media screen and (min-width: 767px) and (max-width: 1024px) {
  .product-price .section-1 .plan-version .plan-price .top-comment {
    margin-top: 0;
    height: 48px;
  }
}
</style>
