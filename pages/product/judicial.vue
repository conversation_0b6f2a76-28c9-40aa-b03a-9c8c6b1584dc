<template>
  <article class="product-judicial">
	<section class="section section-top">
      <div class="container">
        <h1 class="article-headline">公证仲裁</h1>
        <!-- <h1 class="article-headline">保驾护航。</h1> -->
        <p class="article-subtitle">上上签电子签约为你的每份合同提供强力法律保障，打造完整的可追溯证据链，提供第三方证明、在线公证、网络仲裁等专业服务，有效保护你的合法权益。</p>
        <button
          class="ssq-button-primary"
          @click="toDemo()">
          免费试用
        </button>
      </div>
      <!-- <div class="img-wrap">
        <img v-if="isMobile" src="@/assets/images/product/banner.png" alt="上上签多终端使用">
        <img v-else src="@/assets/images/product/banner.png" alt="上上签多终端使用">
      </div> -->
    </section>
    <section class="section section-1">
      <div class="container">
        <h1 class="section-headline">合法合规的电子合同具有法律效力</h1>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="8">
            <el-card
              :body-style="{ padding: '0px' }"
              shadow="hover"
              class="card-1">
			  <div class="card-icon">
				 <i class="iconfont icon icon-disanfangzhengming"></i>
			  </div>
              <div class="card-title">第三方证明 </div>
              <div class="card-content">上上签为客户出具签约证明，提供合同主体、认证证书、签署记录等证据链中的核心信息，证明客户通过上上签平台签署的文件合法合规、内容无篡改，有效防止抵赖。</div>
			</el-card>
          </el-col>
          <el-col :xs="24" :sm="8">
            <el-card
              :body-style="{ padding: '0px' }"
              shadow="hover"
              class="card-2">
			  <div class="card-icon">
				  <i class="iconfont icon icon-gongzhengshu"></i>
			  </div>
              <div class="card-title">公证书 </div>
              <div class="card-content">上上签联合各地公证处，对每一次签约全过程进行实时公证。上上签为客户申请出具公证书，证明签署行为合法，签署结果真实有效。</div>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="8">
            <el-card
              :body-style="{ padding: '0px' }"
              shadow="hover"
              class="card-3">
			    <div class="card-icon">
				  <i class="iconfont icon icon-wangluozhongcai"></i>
			  </div>
              <div class="card-title">网络仲裁 </div>
              <div class="card-content">上上签联合各地仲裁委，接入网络仲裁服务。遇到跨地区或小额纠纷时，网络仲裁帮助客户大幅缩短维权周期、降低维权成本。</div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </section>
    <section class="section section-2" v-if="!isMobile">
      <div class="container">
        <h3 class="section-headline">方便快捷的流程服务</h3>
        <div class="img-wrap">
		  <ul>
			  <li>
				<!-- <i class="iconfont icon-hezuozixun"></i> -->
				<div><img src="@/assets/images/product/judicial/denglu.png" alt=""></div>
			  	<p>登录上上签</p>
			  </li>
			  <li>
				<div><img src="@/assets/images/product/judicial/qianshu.png" alt=""></div>
			  	<p>已签署合同</p>
			  </li>
			  <li>
				<!-- <i class="iconfont icon-24xiaoshirexian"></i> -->
						<div><img src="@/assets/images/product/judicial/kefu.png" alt=""></div>
			  	<p>线上联系人工客服</p>
			  </li>
			  <li>
				<!-- <i class="iconfont icon-hezuozixun"></i> -->
					<div>	<img src="@/assets/images/product/judicial/shenqing.png" alt=""></div>
			  	<p>申请</p>
			  </li>
			  <li>
				<!-- <i class="iconfont icon-hezuozixun"></i> -->
				<div>		<img src="@/assets/images/product/judicial/cailiao.png" alt=""></div>
			  	<p>提交相关材料</p>
			  </li>
			  <li>
				<!-- <i class="iconfont icon-hezuozixun"></i> -->
						<div><img src="@/assets/images/product/judicial/wuliao.png" alt=""></div>
			  	<p>领取相应物料</p>
			  </li>
			  <li>
				<!-- <i class="iconfont icon-hezuozixun"></i> -->
					<div>	<img src="@/assets/images/product/judicial/xiaoshou.png" alt=""></div>
			  	<p>线下联系销售经理</p>
			  </li>
		  </ul>
		 <div class="step-warp" v-if="!isMobile">
			<el-steps  align-center>
				<el-step title=""></el-step>
				<el-step title=""></el-step>
				<el-step title=""></el-step>
				<el-step title=""></el-step>
				<el-step title=""></el-step>
				<el-step title=""></el-step>
				<el-step title=""></el-step>
			</el-steps>
		 </div>
        </div>
      </div>
    </section>
    <section class="section section-2" v-if="isMobile">
      <div class="container">
        <h3 class="section-headline">方便快捷的流程服务</h3>
        <div class="img-wrap">
     <div style="height:300px;">
  <el-steps class="judicial-steps" direction="vertical" :active="1">
    <el-step title="登录上上签"></el-step>
    <el-step title="已签署合同"></el-step>
    <el-step title="线上联系人工客服"></el-step>
    <el-step title="申请"></el-step>
    <el-step title="提交相关材料"></el-step>
    <el-step title="领取相应物料"></el-step>
    <el-step title="线下联系销售经理"></el-step>
  </el-steps>
</div>
        </div>
      </div>
    </section>
	<!-- <div class="space-line"></div> -->
    <section class="section section-3">
      <div class="container">
        <h3 class="section-headline">多次得到司法机构事实认定</h3>
		 <el-row class="section-content">
          <el-col :xs="24" :sm="11">
            <div class="box-wrap">
              <div class="box-title">法院</div>
              <p class="box-desc">成都武侯区法院、广州越秀区法院、广西兴业县法院、杭州余杭区法院、上海嘉定区法院等各地人民法院采信上上签电子合同证据。</p>
				<el-carousel ref="carouseCourt" indicator-position="none" :autoplay="true" arrow="never" :interval="4000" trigger="hover" @change="getCourtIndex">
					<el-carousel-item v-for="item in dataCourt" :key="item.title">
						<img :src="item.picSrc" alt="">
					</el-carousel-item>
				</el-carousel>
				<p class="carousel-title">{{courtName}}</p>
				<div class="carousel-pagation">
					<span class="pre-carousel" @click="preFun('carouseCourt')">
						<svg t="1591257486912" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5872" width="200" height="200"><path d="M654.222222 192.142222L245.361778 512 654.222222 831.118222l-17.607111 22.215111L199.111111 512 636.899556 170.666667 654.222222 192.142222z" p-id="5873"></path></svg>
					</span>
					<span class="pagation-line"></span>
					<span class="next-carousel" @click="nextFun('carouseCourt')">
						<svg t="1591257509027" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6012" width="200" height="200"><path d="M199.111111 192.142222L607.971556 512 199.111111 831.118222l17.607111 22.215111L654.222222 512 216.433778 170.666667 199.111111 192.142222z" p-id="6013"></path></svg>
					</span>
				</div>

            </div>
          </el-col>
          <el-col :xs="24" :sm="11">
            <div class="box-wrap">
              <div class="box-title">仲裁</div>
              <p class="box-desc">珠海仲裁委、衢州仲裁委、石家庄仲裁委等各地仲裁委认可上上签电子合同的法律效力。</p>
			  	<el-carousel ref="carouseAbitration" indicator-position="none" :autoplay="true" arrow="never" :interval="4000" trigger="hover" @change="getAbitrationIndex">
					<el-carousel-item v-for="item in dataAbitration" :key="item.title">
						<img :src="item.picSrc" alt="">
					</el-carousel-item>
				</el-carousel>
				<p class="carousel-title">{{abitrationName}}</p>
				<div class="carousel-pagation">
					<span class="pre-carousel" @click="preFun('carouseAbitration')">
						<svg t="1591257486912" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5872" width="200" height="200"><path d="M654.222222 192.142222L245.361778 512 654.222222 831.118222l-17.607111 22.215111L199.111111 512 636.899556 170.666667 654.222222 192.142222z" p-id="5873"></path></svg>
					</span>
					<span class="pagation-line"></span>
					<span class="next-carousel" @click="nextFun('carouseAbitration')">
						<svg t="1591257509027" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6012" width="200" height="200"><path d="M199.111111 192.142222L607.971556 512 199.111111 831.118222l17.607111 22.215111L654.222222 512 216.433778 170.666667 199.111111 192.142222z" p-id="6013"></path></svg>
					</span>
				</div>

            </div>
          </el-col>

        </el-row>

      </div>
    </section>
    <section class="section section-4">
      <div class="container">
        <h3 class="section-headline">强大的司法区块链联盟</h3>
        <div class="img-wrap">
			<!-- <div class="circle-line">
				<div class="circle-out circle">
					<div class="circle-center circle">
						<div class="circle-in circle"></div>
					</div>
				</div>
			</div> -->
			<div class="circle-logo">
				<!-- <ul>
					<li class="circle-li circle-li-1">
						<img src="@/assets/images/product/judicial-1.png" alt="">
					</li>
					<li class="circle-li circle-li-2">
						<img src="@/assets/images/product/judicial-2.png" alt="">
					</li>
					<li class="circle-li circle-li-3">
						<img src="@/assets/images/product/judicial-3.png" alt="">
					</li>
					<li class="circle-li circle-li-4">
						<img src="@/assets/images/product/judicial-4.png" alt="">
					</li>
					<li class="circle-li circle-li-5">
						<img src="@/assets/images/product/judicial-5.png" alt="">
					</li>
					<li></li>
					<li></li>
					<li></li>
					<li></li>
				</ul> -->
				<img src="@/assets/images/product/sifa.jpg" alt="">
			</div>
        </div>
      </div>
    </section>
  </article>
</template>

<script>
import Qs from 'qs';
export default {
  name: 'ProductJudicial',
  head() {
    return {
      title: '电子合同法律安全认证_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同司法认定,电子合同公证,电子合同法律效应',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '在上上签平台签署的电子合符合国家多重安全认证，也是全国唯一一家可以出公证书的电子合同平台，保护用户数据隐私和安全，我们还提供辅助公证、仲裁、司法鉴定等附加服务。',
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.bestsign.cn/product-judicial',
        },
      ],
    };
  },
  data() {
    return {
      active: 0,
      dataCourt: [
        {
          title: '成都市武侯区人民法院民事判决书',
          picSrc:
            'https://static.bestsign.cn/be1259d98c3dd76493e6b6831e655bf09214defb.jpg',
        },
        {
          title: '重庆市沙坪坝区人民法院民事判决书',
          picSrc:
            'https://static.bestsign.cn/9c2a739beffeba03b3d9107540acfeb0d1c9bc5d.jpg',
        },
        {
          title: '广东省广州市越秀区人民法院民事判决书',
          picSrc:
            'https://static.bestsign.cn/580ecff6ad4d42da40adee1d37ef41825b9ee747.jpg',
        },
        {
          title: '广东省深圳前海合作区人民法院民事判决书',
          picSrc:
            'https://static.bestsign.cn/13ccf10eb2a95c9ab250954e62e96833928b0351.jpg',
        },
        {
          title: '广东省深圳罗湖区人民法院民事判决书',
          picSrc:
            'https://static.bestsign.cn/ae5ff1d14f893f79142d65d5cd00651fe942ca21.jpg',
        },
        {
          title: '广西壮族自治区兴业县人民法院民事判决书',
          picSrc:
            'https://static.bestsign.cn/a166cdb5cf287d7e4e01b8bd888083222ef5debb.jpg',
        },
        {
          title: '杭州市余杭区人民法院民事判决书',
          picSrc:
            'https://static.bestsign.cn/29c9b2c8e9a48031fa778e7ca65a2d57fefdfc6a.jpg',
        },
        {
          title: '湖北省武汉东湖新技术开发区人民法院民事判决书',
          picSrc:
            'https://static.bestsign.cn/296ce6034bdf9f2c23c56cf4fbf00efbc327decf.jpg',
        },
        {
          title: '上海市嘉定区人民法院民事判决书',
          picSrc:
            'https://static.bestsign.cn/db64172b1c25d17f526de057bce27dcb0bcd66b4.jpg',
        },
        {
          title: '上海市闵行区人民法院民事判决书',
          picSrc:
            'https://static.bestsign.cn/c2249a4ea974bc7bcae4ac7f4bef8d96fc83b4d1.jpg',
        },
        {
          title: '四川省成都高新技术产业开发区人民法院民事判决书',
          picSrc:
            'https://static.bestsign.cn/1616a7903fa96552bc35fe4891f113f23a40913e.jpg',
        },
        {
          title: '云南省玉溪市红塔区人民法院民事判决书',
          picSrc:
            'https://static.bestsign.cn/7f5a227840e45f9bb9f59de99a56a961ec57432e.jpg',
        },
        {
          title: '浙江省杭州市余杭区人民法院民事判决书',
          picSrc:
            'https://static.bestsign.cn/cf8deeb3e86f3be448a8fdcd3b94161cc4e1cb4e.jpg',
        },
      ],
      dataAbitration: [
        {
          title: '衢州仲裁委员会裁决书',
          picSrc:
            'https://static.bestsign.cn/1256bc4ab27eb877bc8f4a2e1ec12a26be1d382d.jpg',
        },
        {
          title: '石家庄仲裁委员会裁决书',
          picSrc:
            'https://static.bestsign.cn/1ba610b5638aba29d2d3567583140f5d3a0b1367.jpg',
        },
        {
          title: '珠海仲裁委员会裁决书',
          picSrc:
            'https://static.bestsign.cn/e1769464432968b31eb60a8fb4d7be369b0f605c.jpg',
        },
      ],
      courtIndex: 0,
      courtName: '',
      abitrationIndex: 0,
      abitrationName: '',
      imgPosition: [
        {
          left: '',
          top: '',
        },
      ],
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: 'product' });
    this.getInfo();
  },
  methods: {
    preFun(ref) {
      // let _ref = ref
      this.$refs[ref].prev();
    },
    nextFun(ref) {
      let _ref = ref;
      this.$refs[_ref].next();
    },
    getCourtIndex(cur) {
      this.courtIndex = cur;
      this.getInfo();
    },
    getAbitrationIndex(cur) {
      this.abitrationIndex = cur;
      this.getInfo();
    },
    getInfo() {
      this.courtName = this.dataCourt.filter(
        (item, index) => index == this.courtIndex
      )[0].title;
      this.abitrationName = this.dataAbitration.filter(
        (item, index) => index == this.abitrationIndex
      )[0].title;
    },
    handleTab(index) {
      this.active = index;
    },
    handleChange(cur) {
      const activeIndex = cur.realIndex;
      this.activeIndex = activeIndex;
    },
    toDemo() {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/demo',
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
          product_judicial: '',
        },
      });
    },
  },
};
</script>
<style scoped lang="scss">
.product-judicial {
  text-align: center;
  .el-card {
    width: 100%;
    margin: 0 auto;
    background: #fafafa;
    border: none;
    border-radius: 2px;
  }
  .section {
    padding-bottom: 2rem;
  }
  .section-top {
    background: url('~assets/images/product/banner-legal.jpg') no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: 44vw;
    position: relative;
    display: flex;
    align-items: flex-end;
    background-position: center;
    padding: 4rem 0;
    .container {
      position: absolute;
      top: 15.5rem;
      left: 50%;
      transform: translate3d(-50%, 0, 0);
    }

    .article-headline {
      font-weight: 500;
      color: #fff;
      font-size: 2.875rem;
    }
    .article-subtitle {
      font-size: 1.2rem;
      line-height: 1.7;
      font-weight: 400;
      margin-bottom: 1.5rem;
      padding: 6px 10px;
      color: #fff;
    }
    h1 {
      margin: 0;
    }
    .page-headline {
      padding-bottom: 95px;
    }
    .page-abstract {
      margin: 0 auto 100px;
      line-height: 1.7;
    }
    .img-wrap,
    img {
      width: 100%;
      vertical-align: middle;
    }
    .ssq-button-primary {
      margin: 20px auto 0;
      background: #00aa64;
      width: 120px;
      height: 40px;
      line-height: 40px;
      display: block;
      padding: 0 15px;
    }
  }
  .section-1 {
    text-align: center;
    .article-headline {
      color: #090909;
      font-size: 2rem;
      line-height: 2.5rem;
      font-weight: 400;
    }
    .article-subtitle {
      margin: 28px auto 80px;
    }
    .el-row {
      //   max-width: 1220px;
      margin: 0 auto;
    }

    .card-icon {
      font-size: 60px;
      margin-top: 2rem;
      .iconfont {
        font-size: 4rem;
        cursor: pointer;
        color: #00a664;
      }
    }
    .el-card__body:hover {
      //   .iconfont {
      //     color: #00a664;
      //   }
    }
    .card-title {
      margin: 50px auto 30px;
      font-size: 20px;
      font-weight: 400;
      color: #1d1d1f;
    }
    .card-content {
      height: 180px;
      padding: 0 32px 30px 32px;
      color: #888;
      text-align: justify;
      line-height: 24px;
      color: #86868b;
    }
  }
  .section-2 {
    background: #fafafa;
    .container {
      .section-headline {
        margin-bottom: 60px;
      }
      .img-wrap {
        width: 100%;
        ul {
          display: flex;
          justify-content: center;
          .iconfont {
            font-size: 26px;
          }
          li {
            margin: 0 11px;
            width: 8rem;
            p {
              margin: 20px 0;
            }
            div {
              height: 50px;
              img {
                width: 40px;
                height: auto;
              }
            }
          }
        }
      }
      .step-warp {
        width: 100%;
        margin: 20px auto;
        /deep/ .el-step__line {
          background-color: #3f3f3f;
          top: 9px;
        }
        /deep/.el-step {
          width: 9.6rem;
        }
        /deep/ .el-step__icon {
          width: 10px;
          height: 10px;
          border-color: #3f3f3f;
        }
        /deep/ .el-step__icon-inner {
          visibility: hidden;
        }
      }
    }
  }
  .space-line {
    width: 100%;
    height: 1px;
    background: #595959;
    margin: 80px 0 20px 0;
  }
  .section-3 {
    .section-content {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
    }
    .timeline {
      width: 100%;
      margin: 100px auto;
      max-width: 1065px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: center;
      font-size: 16px;
      .item {
        position: relative;
        bottom: -1px;
        font-size: 20px;
        margin: 0 40px;
        padding: 20px 0;
        color: #888;
        cursor: pointer;
        &:hover,
        &.is-active {
          color: #00a664;
          border-bottom: 1px solid #00a664;
        }
      }
    }
    .text {
      width: 100%;
      margin: 0 auto;
      max-width: 1065px;
      text-align: left;
      p {
        margin: 15px 0;
      }
    }
    .img-wrap,
    img {
      width: 100%;
      height: 100%;
    }
    .section-content {
      //   margin-top: 100px;
      .box-wrap {
        background: #fafafa;
        margin: 0 16px;
        padding: 20px 40px 40px 40px;
        box-sizing: border-box;
      }
      .box-title {
        font-size: 1.6rem;
        font-weight: 400;
        color: #1d1d1f;
        margin: 30px 0;
      }
      .box-desc {
        line-height: 1.7;
        color: #86868b;
        font-size: 14px;
      }
      .el-carousel {
        margin-top: 60px;
        // background: #fff;
        /deep/ .el-carousel__indicators {
          margin-top: 80px;
        }
      }
      .carousel-title {
        margin: 40px auto 20px;
        font-size: 12px;
      }
      .carousel-pagation {
        .pagation-line {
          width: 150px;
          height: 1px;
          background: #c3bebe;
          display: inline-block;
          margin: 0 20px;
          vertical-align: middle;
        }
        .icon {
          cursor: pointer;
        }
        .icon:hover {
          color: #333;
        }
      }
      .swiper-container {
        margin-top: 30px;
      }
    }
  }
  .section-4 {
    padding-bottom: 5rem;
    .img-wrap {
      position: relative;
      img {
        width: 100%;
      }
      .circle {
        border-radius: 50%;
        border: 1px solid #d6d6dc;
        margin: 0 auto;
      }
      .circle-out,
      .circle-center {
        display: flex;
        align-items: center;
      }
      .circle-logo {
        width: 65%;
        margin: 0 auto;
        .circle-li {
          width: 100px;
          height: 100px;
          border-radius: 50%;
          border: 1px solid #dfdfdf;
          position: absolute;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            border-radius: 50%;
          }
        }
        .circle-li-1 {
          left: 44%;
          top: 13%;
        }
        .circle-li-2 {
          left: 31%;
          top: 27%;
        }
        .circle-li-3 {
          left: 44%;
          top: 41%;
        }
        .circle-li-4 {
          left: 53%;
          top: 73%;
        }
        .circle-li-5 {
          left: 56%;
          top: 31%;
        }
      }
      .logo-img {
        background-image: url(~assets/images/product/<EMAIL>);
      }
      .circle-in {
        width: 330px;
        height: 330px;
      }
      .circle-center {
        width: 450px;
        height: 450px;
      }
      .circle-out {
        width: 540px;
        height: 540px;
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .product-judicial {
    .article-headline {
      font-size: 28px;
    }
    .article-subtitle {
      font-size: 12px;
    }
    .el-card {
      margin-bottom: 20px;
    }
    .section-headline {
      font-size: 22px;
    }
    .section-top {
      background-size: cover;
      background-position: 44%;
      height: 100vw;
      .article-subtitle {
        font-size: 16px;
        line-height: 24px;
      }
      .container {
        top: 0;
        margin: 0;
        transform: translate3d(0, 0, 0);
        left: 0;
        padding-top: 5rem;
        h1 {
          margin: 0;
          font-size: 28px;
          font-weight: 500;
        }
      }
      .ssq-button-primary {
        width: 120px;
        height: 40px;
        font-size: 16px;
        line-height: 40px;
      }
    }
    .space-line {
      margin: 10px 0;
    }
    .section-1 {
      .card-title {
        margin: 30px auto 20px;
        font-size: 18px;
      }
      .card-content {
        height: 160px;
        font-size: 14px;
        color: #86868b;
      }
    }
    .section-2 {
      padding: 0 0 30px;
      .judicial-steps {
        /deep/ .el-step__title.is-wait {
          color: #86868b;
          //   font-weight: 700;
          font-size: 14px;
        }
        /deep/ .el-step__line {
          background-color: #86868b;
          top: 9px;
        }
        /deep/ .el-step__icon {
          width: 10px;
          height: 10px;
          border-color: #86868b;
        }
        /deep/ .el-step__icon-inner {
          visibility: hidden;
        }
        /deep/ .el-step__title.is-finish {
          color: #86868b;
          //   font-weight: 700;
          font-size: 14px;
        }
        /deep/ .el-step__head.is-finish {
          color: #86868b;
          border-color: #86868b;
        }
        /deep/ .el-step__title.is-process {
          font-size: 14px;
          color: #86868b;
          font-weight: 500;
        }
        /deep/ .el-step.is-vertical {
          // background-color: red;
          &:nth-child(2n) {
            // background-color: green;
            .el-step__head {
              margin-right: 50%;
              transform: translateX(50%);
            }
            .el-step__main {
              order: -1;
              text-align: right;
              padding-right: 12px;
            }
          }
          &:nth-child(2n + 1) {
            .el-step__head {
              margin-left: 50%;
              transform: translateX(-50%);
            }
          }
        }
      }
      .container {
        .section-headline {
          margin-bottom: 15px;
        }
        .img-wrap {
          img {
            width: 180px;
          }
          ul {
            display: flex;
            flex-wrap: wrap;
            justify-content: left;
            li {
              margin: 0px 10px;
              text-align: center;
              min-width: 70px;
            }
          }
        }
      }
    }
    .section-3 {
      .section-content {
        padding: 20px 5px 0px;
        .el-col:first-child {
          margin: 0 0 35px;
        }
        .box-wrap {
          padding: 2px 10px 30px;
          margin: 0;
        }
        .mobile-img {
          width: 100%;
          img {
            width: 85%;
          }
        }
        .img-wrap {
          margin-top: 30px;
          img {
            width: 80%;
          }
        }
      }
    }
    .section-4 {
      .img-wrap {
        margin: 0;
        .circle-logo {
          width: 90%;
        }
      }
    }
  }
}
@media screen and (min-width: 1280px) {
  .product-judicial .section-2 .container .step-warp {
    width: 80%;
  }
}
</style>
