<template>
  <article class="product-download">
    <section class="section section-1 is-tiny">
      <div class="container">
        <h1 class="article-headline">立即下载，<br/>随时随地，想签就签。</h1>
        <p class="article-subtitle">你不仅可以在网页端签署合同，也可以通过智能手机、iPad完成。</p>
        <div class="content">
          <div class="item">

            <div class="item-icon">
               <i class="iconfont icon-ios"></i>
               <i class="iconfont icon-Android"></i>
            </div>
			 <div class="item-header">iOS/安卓版</div>
            <div class="item-qrcode">
              <img src="@/assets/images/product/<EMAIL>">
            </div>
            <div class="item-desc">扫码下载</div>
          </div>
          <div class="item">
            <div class="item-icon">
              <i class="iconfont icon-WeChat"></i>
            </div>
			<div class="item-header">WeChat</div>
            <div class="item-qrcode">
              <img src="@/assets/images/product/<EMAIL>">
            </div>
            <div class="item-desc">扫码关注</div>
          </div>
        </div>
      </div>
    </section>
  </article>
</template>

<script>
export default {
  name: 'ProductDownload',
  head() {
    return {
      title: '电子合同手机版_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '上上签app,电子合同app下载,上上签手机版,电子合同手机版下载',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '下载上上签手机App，随时随地通过手机或iPad签署和管理电子合同。',
        },
      ],
    };
  },
  mounted() {
    this.$store.commit('changePath', { path: 'product' });
  },
};
</script>

<style scoped lang="scss">
.product-download {
  text-align: center;
  .section-1 {
    background: #fafafa;
    .article-subtitle {
      margin: 30px auto 60px;
      color: #282828;
    }
    .article-headline {
      font-weight: 500;
      line-height: 1.7;
      color: #090909;
      font-size: 2.2rem;
      //
    }
  }
  .content {
    width: 600px;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    .item {
      flex: 1;
      background: #fff;
      padding: 10px 0 50px;
      border-radius: 4px;
      .item-header {
        font-size: 18px;
        margin: 10px 0 40px 0;
      }
      .item-icon {
        margin: 40px 0 20px;
        .iconfont {
          font-size: 3rem;
        }
        .icon-WeChat {
          color: #52bd33;
        }
        .icon-Android {
          color: #39cb75;
          margin: 0 0 0 1.5rem;
        }
      }
      .item-qrcode {
        img {
          width: 120px;
        }
      }
      .item-desc {
        margin-top: 25px;
        color: #86868b;
      }
    }
    .item:first-child {
      margin-right: 25px;
    }
  }
}
@media screen and (max-width: 767px) {
  .product-download {
    .section-1 {
      .article-headline {
        font-size: 28px;
        font-weight: 400;
        text-align: center;
      }
      .article-subtitle {
        margin: 30px auto;
        font-size: 14px;
        color: #86868b;
        width: 80%;
      }
      .content {
        width: 100%;
        .item {
          width: 100%;
          margin-bottom: 20px;
          margin-right: 0;
          flex: auto;
        }
        .item .item-desc {
          font-size: 14px;
          color: #86868b;
        }
        .item .item-header {
          font-size: 16px;
        }
        .item .item-qrcode img {
          width: 80px;
        }
      }
    }
  }
}
</style>
