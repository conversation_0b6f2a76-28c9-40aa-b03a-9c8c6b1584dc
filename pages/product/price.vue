<template>
  <article class="product-price">
    <section class="section section-1 is-tiny">
      <div class="container">
        <h2 class="article-headline headline--large">选择适合你的方式，</h2>
        <h2 class="article-headline headline--large">免费试用。</h2>
        <p class="article-subtitle">安全高效的电子合同签约云平台，操作简单，合同秒发秒签，根据你的业务需求，选择适合你的上上签电子合同签约版本。</p>
        <div class="content" >
          <div class="plan-version">
			<div class="plan-icon plan-standard">
				<img src="@/assets/images/product/jichu.png" alt="">
			</div>
            <div class="plan-header">
              <h4>标准版</h4>
              <!-- <p>SaaS</p> -->
            </div>
            <div class="plan-price">
              <div class="price">
                <div class="price-header">
					<!-- <span class="money line-through">RMB  41,800</span> -->
          <span class="money">RMB 0</span>
					<div class="top-comment">
						<p>满足合同签署基本需求</p>
						<p>适用于小型初创公司（建议≤50人）使用</p>
					</div>
					
                </div>
                
              </div>
              <!-- <button class="ssq-button-primary">
                <a @click="toDemo('jiage-biaozhunban-button')">免费试用</a>
              </button> -->
              <button class="ssq-button-primary is-white" @click="handleActiveTab('#contract_price')" >
                选择套餐，立即购买
              </button>
              <p></p>
            </div>
            
          </div>
          <div class="plan-version">
			<div class="plan-icon plan-profession">
					<img src="@/assets/images/product/zhuanye.png" alt="">
			</div>
            <div class="plan-header">
              <h4>专业版</h4>
              <!-- <p>SaaS</p> -->
            </div>
           
            <div class="plan-price">
              <div class="price">
                <div class="price-header">
                  <span class="money">RMB 100,000</span>
					<div class="top-comment">
            <p>满足企业内部签署管理需求</p>
						<p>包含合同管理、审批管理等进阶功能，适合中型企业（建议50-249人）使用</p>
					</div>
                </div>
				
              </div>
              <button class="ssq-button-primary" @click="toDemo('jiage-zhuanyeban-button')">
                免费试用
              </button>
              <p></p>
            </div>
           
          </div>
          <div class="plan-version">
			<div class="plan-icon plan-power">
					<img src="@/assets/images/product/funeng.png" alt="">
			</div>
      <div class="recommend"><i class="iconfont icon-tuijianjiaobiao"></i></div>
            <div class="plan-header">
              <h4>旗舰版</h4>
              <h4>(含API)</h4>
              <!-- <p>SaaS+API</p> -->
            </div>
           
            <div class="plan-price">
              <div class="price">
                <div class="price-header">
                  <span class="money">RMB 250,000</span>
				  <div  class="top-comment">
            <p>满足多元业务签署协同及管控需求</p>
				   <p>适合拥有内部操作系统的大型集团公司（建议≥250人）使用</p>
				</div>
                </div>
              
              </div>
              <button class="ssq-button-primary" @click="toDemo('jiage-funengban-button')">
                免费试用
              </button>
              <p></p>
            </div>
           
          </div>
          <div class="plan-version">
			<div class="plan-icon plan-gongju">
					<img src="@/assets/images/product/gongju.png" alt="">
			</div>
            <div class="plan-header">
              <h4>工具版</h4>
              <!-- <p>SaaS</p> -->
            </div>
           
            <div class="plan-price">
              <div class="price">
                <div class="price-header">
                  <span class="money">RMB 0</span>
					<div class="top-comment">
            <p>满足灵活构建签署能力需求</p>
						<p>除适用于深度自定义对接使用</p>
					</div>
                </div>
              </div>
              <button class="ssq-button-primary" @click="toDemo('jiage-gongjuban-button')">
                免费试用
              </button>
              <p></p>
            </div>
           
          </div>
        </div>
        <div class="mobile-content" v-if="isMobile" style="display:none">
          <div class="swiper-container" v-swiper:mySwiper="swiperOption">
            <div class="swiper-wrapper">
              <div class="swiper-slide">
                <div class="plan-version">
					<div class="plan-icon plan-standard">
						
					</div>
					<div class="plan-header">
					<h4>标准版</h4>
					<!-- <p>SaaS</p> -->
					</div>
					<div class="plan-price">
					<div class="price">
						<div class="price-header">
							<span class="money line-through">RMB  41,800</span>
							<div class="top-comment">
								<p>满足企业日常合同签署需求</p>
								<p>适用于小型初创公司使用</p>
							</div>
							
						</div>
						<div class="comment">
							<ul>
								<li>以上为SaaS服务年费</li>
								<li>合同服务另收费</li>
								<li>对公合同：20元/份 </li>
								<li>对私合同：6元/份</li>
							</ul>
						</div>
						
					</div>
					<!-- <button class="ssq-button-primary">
						<a @click="toDemo('jiage-biaozhunban-button')">免费试用</a>
					</button> -->
           <button class="ssq-button-primary is-white" @click="handleActiveTab('#contract_price')">
                选择套餐，立即购买
              </button>
					<p></p>
					</div>
					
				</div>
              </div>
              <div class="swiper-slide">
				  <div class="plan-version">
						<div class="plan-icon plan-profession">
							
						</div>
						<div class="plan-header">
						<h4>专业版</h4>
						<!-- <p>SaaS</p> -->
						</div>
					
						<div class="plan-price">
						<div class="price">
							<div class="price-header">
							<span class="money">RMB 218,000</span>
								<div class="top-comment">
									<p>除合同签署外，拥有合同管理、模板管理等进阶功能，适合中大型企业使用</p>
								</div>
							</div>
							
							<div class="comment">
								<ul>
									<li>以上为合同管理服务年费</li>
									<li>合同服务另收费</li>
									<li>对公合同：20元/份 </li>
									<li>对私合同：6元/份</li>
								</ul>
							</div>
						</div>
						<button class="ssq-button-primary" @click="toDemo('jiage-zhuanyeban-button')">
							免费试用
						</button>
						<p></p>
						</div>
					</div>
              </div>
              <div class="swiper-slide">
				<div class="plan-version">
					<div class="plan-icon plan-power">
						
					</div>
					<div class="plan-header">
					<h4>赋能版</h4>
					<!-- <p>SaaS+API</p> -->
					</div>
				
					<div class="plan-price">
					<div class="price">
						<div class="price-header">
						<span class="money">RMB 418,000</span>
						<div  class="top-comment">
						<p>满足合同全生命周期管理需求，适合拥有内部操作系统的大型集团公司使用</p>
						</div>
						</div>
						<div class="comment">
							<ul>
								<li>以上为合同管理+API平台服务年费</li>
								<li>合同服务另收费</li>
								<li>对公合同：20元/份 </li>
								<li>对私合同：6元/份</li>
							</ul>
						</div>
					
					</div>
					<button class="ssq-button-primary" @click="toDemo('jiage-funengban-button')">
						免费试用
					</button>
					<p></p>
					</div>
				
				</div>
              </div>
            </div>
            <div class="ssq-button-prev">
              <i class="el-icon-arrow-left"></i>
            </div>
            <div class="ssq-button-next">
              <i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>
      </div>
    </section>
	<section class="section section-2 price-table" style="display:none">
		<div class="container">
			<div class="content" v-if="!isMobile">
				<el-table
					:data="tableData"
					border
					:header-cell-style="headerStyle"
					:span-method="columnMerge"
					:cell-style="cellStyle"
					:row-style="rowStyle"
					style="width: 100%">
					<el-table-column
						align="center"
						prop="name"
						label="功能"
						width="180">
					</el-table-column>
					<el-table-column
						align="center"
						prop="standard"
						label="标准版">
					</el-table-column>
					<el-table-column
						align="center"
						prop="profession"
						label="专业版">
					</el-table-column>
					<el-table-column
						align="center"
						prop="power"
						label="旗舰版（含API）">
				</el-table-column>
				</el-table>
			</div>
			<div class="content" v-if="isMobile">
				<el-collapse v-model="activeNames" @change="handleChange" accordion>
					<el-collapse-item title="标准版" name="1">
						<div class="table-item">
							<div class="table-item-title">基础功能</div>
							<div class="table-item-desc">
								<ul>
									<li>合同发送、签署</li>
									<li>个人实名认证</li>
									<li>企业实名认证</li>
									<li>合同模板</li>
									<li>批量签署</li>
                  <li>刷脸签署</li>
                  <li>二维码查验</li>
									<li>签署分享</li>
									<li>合同查询</li>
									<li>成员管理</li>
                  <li>角色管理</li>
									<li>印章管理</li>
									<li>审计日志</li>
								</ul>
							</div>
						</div>
						<div class="table-item">
							<div class="table-item-title">增值服务</div>
							<div class="table-item-desc">
								<ul>
                  <li>7*24小时服务支持</li>
                  <li>实时公证</li>
									<li>律师服务（另收费）</li>
									<li>在线出证（另收费）</li>
								</ul>
							</div>
						</div>
					</el-collapse-item>
					<el-collapse-item title="专业版" name="2">
						<div class="table-item">
							<div class="table-item-title">基础功能</div>
							<div class="table-item-desc">
								<ul>
									<li>包含“标准版”全部功能以及：</li>
									<li>签约须知</li>
									<li>合同附件</li>
									<li>资料提交</li>
									<li>模板授权</li>
									<li>模板管理</li>
									<li>签约数据导出</li>
									<li>统计报表</li>
									<li>归档管理</li>
                  <li>合同关联</li>
                  <li>内部审批</li>
                  <li>外部审批</li>
                  <li>企业档案柜</li>
                  <li>实名邀请</li>
                  <li>信息采集</li>
								</ul>
							</div>
						</div>
						<div class="table-item">
							<div class="table-item-title">增值服务</div>
							<div class="table-item-desc">
								<ul>
                  <li>7*24小时服务支持</li>
                  <li>实时公证</li>
									<li>律师服务（另收费）</li>
									<li>在线出证（另收费）</li>
									<li>产品使用培训服务</li>
									<li>专属客户成功顾问</li>
                  <!-- <li>行业专属场景咨询服务</li> -->
								</ul>
							</div>
						</div>
					</el-collapse-item>
					<el-collapse-item title="赋能版" name="3">
					<div class="table-item">
							<div class="table-item-title">基础功能</div>
							<div class="table-item-desc">
								<ul>
									<li>包含“专业版”全部功能以及：</li>
									<li>手写笔迹识别</li>
									<li>身份强一致性校验</li>
									<li>前台代收</li>
									<li>合同条款识别</li>
									<li>动态模版</li>
									<li>模板组合</li>
									<li>合同标签</li>
									<li>多业务线管理</li>
									<li>统一业务管理</li>
									<li>协作业务管理</li>
									<li>相对方管理</li>
                  <li>签署者付费</li>
									<li>客户Logo展示</li>
									<li>多语言</li>
									<li>API接口</li>
								</ul>
							</div>
						</div>
						<div class="table-item">
							<div class="table-item-title">增值服务</div>
							<div class="table-item-desc">
								<ul>
									<li>7*24小时服务支持</li>
                  <li>实时公证</li>
									<li>律师服务（另收费）</li>
									<li>在线出证（另收费）</li>
									<li>产品使用培训服务</li>
									<li>专属客户成功顾问</li>
                  <li>行业专属场景咨询服务</li>
								</ul>
							</div>
						</div>
					</el-collapse-item>
					
				</el-collapse>
			</div>
      <div class="charge-dsc">合同计费方式：按累进相加的阶梯计算模式，用量越大阶梯单价越低</div>
		</div>
	</section>
  <recharge></recharge>
  </article>
</template>

<script>
import Qs from 'qs';
import Usage from '@/components/Index/Usage';
import Recharge from './recharge';
// import Recharge from './recharge1';
export default {
  name: 'ProductPrice',
  components: {
    Usage,
    Recharge,
  },
  head() {
    return {
      title: '电子合同价格介绍_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同免费试用,电子合同价格,电子合同套餐',
        },
        {
          hid: 'description',
          name: 'description',
          content: '上上签电子合同产品套餐价格介绍，欢迎免费试用。',
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.bestsign.cn/product-price',
        },
      ],
    };
  },
  data() {
    return {
      activeNames: ['1'],
      swiperOption: {
        loop: true,
        navigation: {
          nextEl: '.ssq-button-next',
          prevEl: '.ssq-button-prev',
        },
      },
      tableData: [
        {
          name: '基础功能',
          standard: '包含功能',
          profession: '包含“标准版”全部功能以及：',
          power: '包含“专业版”全部功能以及：',
        },
        {
          name: '基础功能',
          standard: '合同发送、签署',
          profession: '签约须知	',
          power: '手写笔迹识别',
        },
        {
          name: '基础功能',
          standard: '个人实名认证',
          profession: '合同附件	',
          power: '身份强一致性校验',
        },
        {
          name: '基础功能',
          standard: '企业实名认证',
          profession: '资料提交',
          power: '前台代收',
        },
        {
          name: '基础功能',
          standard: '合同模板	',
          profession: '模板授权',
          power: '合同条款识别',
        },
        {
          name: '基础功能',
          standard: '批量签署	',
          profession: '模板管理	',
          power: '动态模版',
        },
        {
          name: '基础功能',
          standard: '刷脸签署',
          profession: '签约数据导出	',
          power: '模板组合',
        },
        {
          name: '基础功能',
          standard: '二维码查验',
          profession: '统计报表',
          power: '合同标签',
        },
        {
          name: '基础功能',
          standard: '签署分享',
          profession: '归档管理',
          power: '多业务线管理',
        },
        {
          name: '基础功能',
          standard: '合同查询',
          profession: '合同关联',
          power: '统一业务管理',
        },
        {
          name: '基础功能',
          standard: '成员管理',
          profession: '内部审批',
          power: '协作业务管理',
        },
        {
          name: '基础功能',
          standard: '角色管理',
          profession: '外部审批',
          power: '相对方管理',
        },
        {
          name: '基础功能',
          standard: '印章管理',
          profession: '企业档案柜',
          power: '签署者付费',
        },
        {
          name: '基础功能',
          standard: '审计日志',
          profession: '实名邀请',
          power: '客户Logo展示',
        },
        {
          name: '基础功能',
          standard: '-',
          profession: '信息采集',
          power: '多语言',
        },
        {
          name: '基础功能',
          standard: '-',
          profession: '-',
          power: 'API接口',
        },
        {
          name: '增值服务',
          standard: '7*24小时服务支持',
          profession: '7*24小时服务支持',
          power: '7*24小时服务支持',
        },
        {
          name: '增值服务',
          standard: '实时公证',
          profession: '实时公证',
          power: '实时公证',
        },
        {
          name: '增值服务',
          standard: '律师服务（另收费）',
          profession: '律师服务（另收费）',
          power: '律师服务（另收费）',
        },
        {
          name: '增值服务',
          standard: '在线出证（另收费）',
          profession: '在线出证（另收费）',
          power: '在线出证（另收费）',
        },
        {
          name: '增值服务',
          standard: '-',
          profession: '产品使用培训服务',
          power: '产品使用培训服务',
        },
        {
          name: '增值服务',
          standard: '-',
          profession: '专属客户成功顾问',
          power: '专属客户成功顾问',
        },
        {
          name: '增值服务',
          standard: '-',
          profession: '-',
          power: '行业专属场景咨询服务',
        },
      ],
    };
  },
  mounted() {
    this.$store.commit('changePath', { path: 'price' });
  },
  computed: {
    isMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    handleChange(val) {
      console.log(val);
    },
    handleActiveTab(element) {
      this.$scrollTo(element, 500, {
        offset: this.isMobile ? -80 : -120,
      });
      this.activeTab = element;
    },
    headerStyle({ row, rowIndex }) {
      if (rowIndex == 0) {
        return 'background:#333;color:#fff;font-size:16px;border-right:none';
      } else {
        return '';
      }
    },
    columnMerge({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (rowIndex % 16 === 0) {
          return {
            rowspan: 16,
            colspan: 1,
          };
        } else if (rowIndex % 23 === 0) {
          return {
            rowspan: 7,
            colspan: 1,
          };
        } else {
          return {
            rowspan: '',
            colspan: '',
          };
        }
      }
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 0) {
        return 'background:#fafafa';
      }
    },
    rowStyle({ row, column, rowIndex, columnIndex }) {},
    toReg(param) {
      const query = sessionStorage.getItem('query');
      const url =
        window.location.host.indexOf('cn') > -1
          ? `https://ent.bestsign.cn/register?price_buy&${query}&${param}`
          : `https://ent.bestsign.info/register?price_buy&${query}&${param}`;
      window.open(url);
    },
    toDemo(param) {
      const query = sessionStorage.getItem('query');
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          ...Qs.parse(query),
          [param]: '',
          utm_source: Qs.parse(query).utm_source,
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.product-price {
  text-align: center;
  .section-1 {
    .article-headline {
      text-align: left;
      padding-left: 10%;
      font-size: 2.2rem;
      font-weight: 500;
      color: #090909;
    }
    padding: 5rem 0;
    .article-subtitle {
      margin: 1.75rem auto;
      text-align: left;
      max-width: 800px;
      color: #86868b;
      width: 570px;
      margin-left: 10%;
    }
    .content {
      margin: 6rem auto 0;
      display: flex;
      justify-content: space-around;
      flex-wrap: nowrap;
      // padding: 0 4.6rem;
    }
    .plan-version {
      position: relative;
      display: inline-block;
      width: 100%;
      min-width: 205px;
      cursor: pointer;
      box-shadow: 0 8px 15px 0 rgba(82, 94, 102, 0.15);
      border-top: none;
      background: #fff;
      padding: 0 30px 80px;
      text-align: left;
      flex: 1;
      .recommend {
        position: absolute;
        right: 28px;
        top: -6px;
        height: 44px;
        width: 46px;
        z-index: 10;
        .iconfont {
          font-size: 74px;
          color: #00aa64;
        }
      }
      &:nth-child(2) {
        margin: 0 20px 0;
      }
      &:nth-child(4) {
        margin: 0 0 0 20px;
      }
      &:hover {
        box-shadow: 5px 8px 24px 10px rgba(82, 94, 102, 0.15);
      }
      .tag {
        padding: 3px 5px;
        color: #fff;
        background-color: #00a664;
        border-radius: 10px;
        &.less {
          padding-left: 0;
          padding-right: 0;
        }
      }
      .plan-icon {
        width: 100px;
        height: 100px;
        border-radius: 25px;
        margin-top: 52px;
        position: relative;
        img {
          width: 80%;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-41%, -36%);
        }
      }
      .plan-header {
        position: relative;
        display: flex;
        align-items: baseline;
        justify-content: flex-start;
        flex-direction: column;
        margin: 2.75rem 0;
        height: 60px;
        h4 {
          font-size: 2rem;
          margin: 0;
          font-weight: 400;
          color: #1d1d1f;
          line-height: 1.5;
        }
        p {
          padding: 5px 10px;
          border: 1px solid #7e7777;
          border-radius: 3px;
          color: #7e7777;
          margin-top: 20px;
        }
      }
      .plan-standard {
        background-image: linear-gradient(135deg, #67e4d4, #24d3a8);
      }
      .plan-profession {
        background-image: linear-gradient(135deg, #b5b0e3, #9491c2);
      }
      .plan-power {
        background-image: linear-gradient(135deg, #fdd89d, #f8c16b);
      }
      .plan-gongju {
        background-image: linear-gradient(135deg, #f7b29f, #f08c76);
      }
      .plan-price {
        // .price {
        //   height: 400px;
        // }
        .top-comment {
          text-align: left;
          margin-top: 20px;
        }
        p {
          //   margin: 24px auto 32px;
          font-size: 14px;
          color: #86868b;
          line-height: 1.7;
        }
        .currency {
          position: relative;
          top: 8px;
          left: -4px;
          font-size: 20px;
          vertical-align: top;
        }
        .price-header {
          // border-bottom: 1px solid #dcdcdc;
          text-align: left;
          padding-bottom: 40px;
          height: 130px;
          .money {
            font-size: 20px;
            color: #1d1d1f;
            font-weight: 500;
            &.line-through {
              text-decoration-line: line-through;
              text-decoration-color: #00aa64;
            }
          }
        }
        .ssq-button-primary {
          margin-top: 50px;
          border: 1px solid #00aa64;
          width: 80%;
          font-size: 1rem;
          height: 46px;
        }

        .comment {
          margin-top: 30px;
          line-height: 22px;
          li {
            font-size: 14px;
          }
          ul li:before {
            content: '';
            display: inline-block;
            width: 5px;
            height: 5px;
            background-color: #333;
            border-radius: 50%;
            margin-right: 10px;
            vertical-align: middle;
          }
        }
        .contract {
          font-size: 14px;
          margin: 15px auto;
          line-height: 22px;
          padding-left: 80px;
          text-align: left;
        }
      }
      .plan-list {
        text-align: left;
        margin: 0 1.5rem;
        padding: 20px 0;
        min-height: 375px;
        &.bordered {
          border-bottom: 1px solid #eee;
          min-height: 550px;
        }
        ul {
          margin: 0 auto;
          h4 {
            margin: 0 0 0 16px;
            padding: 32px 0;
            color: #a6a6a6;
          }
          li {
            position: relative;
            font-size: 16px;
            margin-bottom: 20px;
            line-height: 1.5;
            padding-left: 30px;
            &.none {
              padding-left: 0;
            }
            .icon-gou-img {
              position: absolute;
              display: inline-block;
              left: 0;
              color: #00a664;
            }
          }
        }
      }
    }
  }
  .help {
    font-size: 22px;
    color: rgb(96, 96, 96);
    line-height: 42px;
  }
  .swiper-container {
    .ssq-button-prev,
    .ssq-button-next {
      position: absolute;
      z-index: 99;
      width: 28px;
      height: 28px;
      line-height: 28px;
      background: #dcdcdc;
      border-radius: 28px;
      top: 50%;
      transform: translateY(-50%);
      &:focus {
        outline: none;
      }
      .i {
        color: #fff;
      }
    }
    .ssq-button-prev {
      left: 10px;
    }
    .ssq-button-next {
      right: 10px;
    }
  }
}
.section-2 {
  padding: 0rem 1.5rem 6rem;
  .container {
    padding: 0 4.6rem;
    .charge-dsc {
      margin: 20px 0;
      color: #86868b;
      font-size: 16px;
    }
  }
  /deep/ .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: #ffffff !important;
  }
}

@media screen and (max-width: 767px) {
  /deep/ .el-collapse-item__content {
    padding-bottom: 0;
  }
  .product-price {
    .container {
      max-width: 100%;
      width: 100%;
    }
    .section-1 {
      .content {
        padding: 0;
        flex-wrap: wrap;
      }
      .article-headline {
        font-size: 2rem;
        text-align: center;
      }
      .article-subtitle {
        font-size: 14px;
        color: #86868b;
        padding: 0 1rem;
        margin: 2rem auto;
        text-align: center;
        width: 80%;
      }
      .plan-header {
        height: auto;
        margin-bottom: 0;
      }
      .plan-version {
        width: auto;
        flex: auto;
        margin: 0 0 20px;
        min-width: auto;
        .plan-price {
          .ssq-button-primary {
            width: 200px;
            font-size: 16px;
          }
        }
        &:nth-child(2) {
          margin: 0 0 20px;
        }
        &:nth-child(4) {
          margin: 0 0 20px;
        }
        &:hover {
          box-shadow: 0 8px 15px 0 rgba(82, 94, 102, 0.15);
        }
      }
    }
    .section-2 {
      .container {
        padding: 0;
        .charge-dsc {
          margin: 20px 0;
          color: #86868b;
          font-size: 14px;
          line-height: 1.5;
        }
        /deep/ .el-collapse-item {
          border: 1px solid #adb0b8;
          .el-collapse-item__header {
            padding: 0 1rem;
            border-bottom: 1px solid #adb0b8;
            margin-bottom: -1px;
          }

          .el-collapse-item__wrap {
            border-top: 1px solid #adb0b8;
          }
          .table-item:last-child {
            border-top: solid 1px #dfe1e6;
          }
          &:nth-child(2) {
            border-top: none;
            border-bottom: none;
          }
        }
        .table-item {
          //   border-top: solid 1px #dfe1e6;
          display: table;
          //   width: calc(100% - 30px);
          width: 100%;
          //   padding: 15px 0;
          //   margin: 0 14px;
          .table-item-title {
            display: table-cell;
            vertical-align: middle;
            border-right: 1px solid #dfe1e6;
            width: 95px;
            font-weight: 700;
            color: #252b3a;
          }
          .table-item-desc {
            display: table-cell;
            vertical-align: middle;
            color: #575d6c;
            li {
              border-bottom: 1px solid #dfe1e6;
              padding: 10px 0;
            }
            li:last-child {
              border-bottom: none;
            }
          }
        }
      }
    }
    .help {
      font-size: 14px;
      color: rgb(96, 96, 96);
      line-height: 42px;
    }
  }
}
@media screen and (min-width: 767px) and (max-width: 1024px) {
  .product-price .section-1 .plan-version .plan-price .top-comment {
    margin-top: 0;
    height: 48px;
  }
}
</style>
