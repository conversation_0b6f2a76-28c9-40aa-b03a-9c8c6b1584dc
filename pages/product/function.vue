<template>
  <article class="product-function">
    <section class="section section-1">
      <div class="container">
        <h1 class="article-headline">电子合同功能</h1>
        <!-- <h1 class="article-headline">合同秒发秒签。</h1> -->
        <p class="article-subtitle">完美适应各种终端，无论是PC端、手机端，还是内部系统嵌入，随时随地便捷使用。合同秒发秒签，是你提升业务效率的好帮手。</p>
        <button
          class="ssq-button-primary"
          @click="toDemo()">
          免费试用
        </button>
      </div>
    </section>
    <section  id="section-2" class="section section-2" style="background:#fafafa">
      <div class="container">
		  <div class="section-headline">
			<h2 class="headline-title">签约管理系列</h2>
		  </div>
      <div class="little-title">
        <p class="headline-desc">0开发成本，即充即用，合同秒发秒签，满足各类合同工作流管理，</p>
			<p class="headline-desc">适应各终端（Web、APP、H5、小程序等）与平台（OA、HR、CRM等）。</p>
      </div>

        <el-row class="content" :gutter="0" center>
          <el-col :xs="24" :sm="5">
            <div class="box-wrap">
              <div class="box-title">标准版</div>
              <p class="box-desc">满足合同签署基本需求。</p>
              
            </div>
          </el-col>
          <el-col :xs="24" :sm="5">
            <div class="box-wrap">
              <div class="box-title">专业版</div>
              <p class="box-desc">满足企业内部签署管理需求。</p>
            </div>
          </el-col>
          <el-col :xs="24" :sm="5">
            <div class="box-wrap">
              <div class="box-title">旗舰版</div>
              <p class="box-desc">满足多元业务签署协同及管控需求。</p>
            </div>
          </el-col>
        </el-row>
      </div>
    </section>
    <section
      id="section-3"
      class="section section-3">
      <div class="container">
		<div class="section-headline">
			<h2 class="headline-title">签约工具系列</h2>
		</div>
    <div class="little-title">
      <p class="headline-desc">提供丰富的API接口嵌入，</p>
			<p class="headline-desc">可根据业务深度与使用习惯深度自定义。</p>
    </div>
        <el-row class="content">
          <el-col :xs="24" :sm="24">
            <div class="box-wrap">
              <i class="iconfont icon-webban"></i>
              <div class="box-title">工具版</div>
              <p class="box-desc">满足客户灵活构建签署能力需求。</p>
            </div>
          </el-col>
        </el-row>
      </div>
    </section>
    <section
      id="section-4"
      class="section section-4">
      <div class="container">
        <h2 class="section-headline">商业智能</h2>
        <el-row class="content">
          <el-col :xs="24" :sm="12">
            <div class="box-wrap">
              <div class="box-img img1"></div>
              <div class="box-title">区块链司法解决方案</div>
              <p class="box-desc">上上签将公证处、司法鉴定中心、网络仲裁委等权威司法机构纳入区块链生态节点，实现电子合同多方存证和全证据链溯源，保护客户数据安全和隐私，让合同更安全更具公信力。</p>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12">
            <div class="box-wrap">
              <div class="box-img img2"></div>
              <div class="box-title">AI合同管理</div>
              <p class="box-desc">引入AI人工智能技术，为客户提供签署方智能识别、合同用量智能监测与提醒等服务，并逐步开放更多人性化功能，持续优化客户的产品体验。</p>
            </div>
          </el-col>
        </el-row>
      </div>
    </section>
	<IndexContract/>

    <section class="section section-5">
      <div class="container">
        <h2 class="section-headline">产品功能 </h2>
        <p class="section-subtitle">
			一站式智能合同管理，简单易上手。
        </p>
      </div>
      <div class="img-wrap" v-if="!isMobile">
        <img v-if="!isMobile" src="~/assets/images/product/s6.jpg" alt="智能合同管理">
      </div>
    </section>
    <section class="section section-6" v-if="!isMobile">
      <div class="container">
        <div class="slide">
          <div class="slide-desc slide-left">
            <h3 class="title">合同实时签署</h3>
            <div class="desc-wrap">
              <p class="desc">短信、邮件、App等方式快速完成签署</p>
              <p class="desc">实时追踪合同状态，掌握当前签署进度</p>
            </div>
          </div>
          <div class="slide-img-wrap">
            <img
              :class="{'is-active': activeImg === 1}"
              src="@/assets/images/product/s2-1.jpg" alt="合同实时签署">
          </div>
        </div>
        <div class="slide">
          <div class="slide-img-wrap slide-left">
            <img
              src="@/assets/images/product/s2-2.jpg"
              alt="合同批量模版">
          </div>
          <div class="slide-desc">
            <h3 class="title">批量模板签署</h3>
            <div class="desc-wrap">
              <p class="desc">将变量字段导入合同模板，一键生成多份合同</p>
              <p class="desc">支持合同批量审批、发送、签署、撤销等操作</p>
            </div>
          </div>
        </div>
        <div class="slide">
          <div class="slide-desc slide-left">
            <h3 class="title">账户权限分级</h3>
            <div class="desc-wrap">
              <p class="desc">企业可设多级账号，后台统一管理</p>
              <p class="desc">严格控制合同审批、发送、签署权限</p>
            </div>
          </div>
          <div class="slide-img-wrap">
            <img
              src="@/assets/images/product/s2-3.jpg"
              alt="合同帐户权限分级">
          </div>
        </div>
        <div class="slide">
          <div class="slide-img-wrap slide-left">
            <img
              src="@/assets/images/product/s2-4.jpg"
              alt="印章集中管理">
          </div>
          <div class="slide-desc">
            <h3 class="title">印章集中管控</h3>
            <div class="desc-wrap">
              <p class="desc">可添加多个公章，签署时指定用章</p>
              <p class="desc">设置印章持有人，仅持有人可签署合同</p>
              <p class="desc">印章分配、使用等操作全部记录备案</p>
            </div>
          </div>
        </div>
        <div class="slide">
          <div class="slide-desc slide-left">
            <h3 class="title">合同管理查验</h3>
            <div class="desc-wrap">
              <p class="desc">高强度严格加密，按合同类别自动归档</p>
              <p class="desc">多状态、多标签检索、查看、下载合同</p>
              <p class="desc">在线验签，随时检验合同真实性、有效性</p>
            </div>
          </div>
          <div class="slide-img-wrap">
            <img
              src="@/assets/images/product/s2-5.jpg"
              alt="合同管理检查">
          </div>
        </div>
      </div>
    </section>
    <section v-if="isMobile" class="section section-6-m">
      <div class="container">
        <h3 class="title">合同实时签署</h3>
        <div class="desc-wrap">
          <p class="desc">短信、邮件、App等方式快速完成签署</p>
          <p class="desc">实时追踪合同状态，掌握当前签署进度</p>
        </div>
        <img src="@/assets/images/product/s2-1.jpg">
        <h3 class="title">批量模板签署</h3>
        <div class="desc-wrap">
          <p class="desc">将变量字段导入合同模板，一键生成多份合同</p>
          <p class="desc">支持合同批量审批、发送、签署、撤销等操作</p>
        </div>
        <img src="@/assets/images/product/s2-2.jpg" alt="">
        <h3 class="title">账户权限分级</h3>
        <div class="desc-wrap">
          <p class="desc">企业可设多级账号，后台统一管理</p>
          <p class="desc">严格控制合同审批、发送、签署权限</p>
        </div>
        <img src="@/assets/images/product/s2-3.jpg" alt="">
        <h3 class="title">印章集中管控</h3>
        <div class="desc-wrap">
          <p class="desc">可添加多个公章，签署时指定用章</p>
          <p class="desc">设置印章持有人，仅持有人可签署合同</p>
          <p class="desc">印章分配、使用等操作全部记录备案</p>
        </div>
        <img src="@/assets/images/product/s2-4.jpg" alt="">
        <h3 class="title">合同管理查验</h3>
        <div class="desc-wrap">
          <p class="desc">高强度严格加密，按合同类别自动归档</p>
          <p class="desc">多状态、多标签检索、查看、下载合同</p>
          <p class="desc">在线验签，随时检验合同真实性、有效性</p>
        </div>
        <img src="@/assets/images/product/s2-5.jpg" alt="">
      </div>
    </section>
  </article>
</template>

<script>
import Qs from 'qs';
import IndexContract from '@/components/Index/Contract.vue';
export default {
  name: 'productFunction',
  components: {
    IndexContract,
  },
  head() {
    return {
      title: '电子合同功能特色_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同功能,电子合同介绍,电子合同特色,电子合同流程',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '深入了解上上签产品，在线合同秒发秒签，帮你管理，简化合同签署流程，保护个人资料隐私安全，具备法律效益。',
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.bestsign.cn/product-function',
        },
      ],
    };
  },
  data() {
    return {
      isFixed: false,
      activeImg: 1,
      activeText: 1,
      activeTab: '#section-2',
      manageData: [
        {
          label: '智能档案',
          iconSrc: '',
        },
        {
          label: '智能模板',
          iconSrc: '',
        },
        {
          label: '智能起草',
          iconSrc: '',
        },
        {
          label: '智能收发',
          iconSrc: '',
        },
        {
          label: '智能签署',
          iconSrc: '',
        },
        {
          label: '智能审批',
          iconSrc: '',
        },
        {
          label: '智能管理',
          iconSrc: '',
        },
        {
          label: '合规管理',
          iconSrc: '',
        },
        {
          label: '权限管理',
          iconSrc: '',
        },
        {
          label: '灵活接口',
          iconSrc: '',
        },
        {
          label: '行业包配置',
          iconSrc: '',
        },
      ],
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: 'product' });
    window.addEventListener('scroll', this.handleScroll);
    if (this.$route.query.name) {
      this.$scrollTo('#section-2', 1000);
    }
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    goSign() {},
    handleActiveTab(element) {
      this.$scrollTo(element, 500, {
        offset: this.isMobile ? -150 : -70,
      });
      this.activeTab = element;
    },
    handleScroll() {
      const s2 = document.querySelector('#section-2').offsetTop;
      const s3 = document.querySelector('#section-3').offsetTop;
      const s4 = document.querySelector('#section-4').offsetTop;
      const scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      const offsetTop = document.querySelector('.section-1').clientHeight;
      if (scrollTop > offsetTop) {
        this.isFixed = true;
      } else {
        this.isFixed = false;
      }
      if (scrollTop > s2 && scrollTop < s3) {
        this.activeTab = '#section-2';
      } else if (scrollTop > s3 && scrollTop < s4) {
        this.activeTab = '#section-3';
      } else if (scrollTop > s4) {
        this.activeTab = '#section-4';
      }
    },
    toDemo() {
      // this.$router.push(`/demo?product_function`);
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/demo',
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
          product_function: '',
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.product-function {
  position: relative;
  text-align: center;
  .article-headline {
    font-weight: 500;
    color: #fff;
    font-size: 2.875rem;
  }
  .headline-title {
    color: #090909;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
  }
  .little-title {
    margin: 0px 10% 20px;
    line-height: 2rem;
  }

  .section-1,
  .section-5 {
    position: relative;
    .page-headline {
      padding-bottom: 95px;
    }
    .page-abstract {
      margin: 0 auto 100px;
      line-height: 1.7;
    }
    .img-wrap,
    img {
      width: 100%;
      vertical-align: middle;
    }
  }
  .section-1 {
    background: url('~assets/images/product/banner2.jpg') no-repeat;
    background-size: cover;
    width: 100%;
    height: 44vw;
    display: flex;
    align-items: center;
    background-position: center;
    padding: 4rem 0;
    .container {
      position: absolute;
      // top: 15.5rem;
      left: 50%;
      transform: translate3d(-50%, 0, 0);
    }
    .article-subtitle {
      font-size: 1.2rem;
      line-height: 1.7;
      font-weight: 400;
      margin-bottom: 1.5rem;
      padding: 6px 10px;
      color: #fff;
    }
    h1 {
      margin: 0;
    }
    .ssq-button-primary {
      margin: 20px auto 0;
      background: #00aa64;
      width: 120px;
      height: 40px;
      line-height: 40px;
      display: block;
      padding: 0 15px;
    }
  }
  .second-nav {
    position: absolute;
    width: 100%;
    height: 70px;
    line-height: 70px;
    border-top: 1px solid #bfc4c9;
    border-bottom: 1px solid #bfc4c9;
    z-index: 99;
    &.is-active {
      position: fixed;
      top: 60px;
      width: 100%;
      background-color: #fff;
      z-index: 23;
    }
    .container {
      display: flex;
      height: 70px;
      .item {
        flex: 1;
        display: flex;
        justify-content: center;
        position: relative;
        top: -1px;
      }
    }
    .nav-content {
      display: flex;
      align-items: center;
      /*height: 78px;*/
      color: #323232;
      .icon {
        height: 39px;
        background: url('~assets/images/index/index_icon.png');
      }
      .icon-1 {
        width: 40px;
        background-position: 0 -44px;
      }
      .icon-2 {
        width: 46px;
        background-position: -41px -44px;
      }
      .icon-3 {
        width: 55px;
        background-position: -132px -44px;
      }
      .icon-text {
        padding-left: 25px;
      }
      &:hover,
      &.is-active {
        cursor: pointer;
        color: #00a664;
        border-bottom: 2px solid #00a664;
        .icon-1 {
          background-position: 0 0;
        }
        .icon-2 {
          background-position: -41px 0;
        }
        .icon-3 {
          background-position: -132px 0;
        }
      }
    }
  }
  .section-2 {
    position: relative;
    // padding-top: 9rem;
    .content {
      box-sizing: border-box;
      max-width: 1200px;
      width: 100%;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      .el-col {
        margin: 0 5px;
        background: #fff;
        min-height: 300px;
        border-radius: 2px;
        padding: 60px 50px;
      }
      .box-wrap {
        max-width: 175px;
        margin: 0 auto;
        text-align: center;
        .box-title {
          font-size: 20px;
          margin: 0.75rem auto 1.75rem;
          font-weight: 400;
          color: #1d1d1f;
        }
        .box-desc {
          line-height: 1.5;
          color: #86868b;
          text-align: center;
        }
      }
    }
  }
  .section-3 {
    background-color: #fff;
    .content {
      padding: 0 2rem;
      .box-wrap {
        max-width: 320px;
        margin: 0 auto;
        text-align: center;
        .iconfont {
          font-size: 4rem;
          color: #00aa64;
          padding-bottom: 2rem;
          display: block;
        }
        &:hover .iconfont {
          color: #00aa64;
          cursor: pointer;
        }
        .box-title {
          font-size: 20px;
          margin: 0.75rem auto 1.75rem;
          font-weight: 400;
          color: #1d1d1f;
        }
        .box-desc {
          color: #86868b;
          line-height: 1.7;
          padding: 0 1rem;
        }
      }
    }
  }
  .section-4 {
    background: #fafafa;
    padding-top: 0;
    .content {
      .box-wrap {
        max-width: 400px;
        margin: 0 auto;
        text-align: left;
        position: relative;
        .box-img {
          margin: 0 auto;
          height: 167px;
          &.img1 {
            background-image: url(~assets/images/product/function.jpg);
            background-size: 100% 100%;
          }
          &.img2 {
            background-image: url(~assets/images/product/function2.jpg);
            background-size: 100% 100%;
          }
        }
        .box-title {
          font-size: 20px;
          margin: 40px 0 30px;
          font-weight: 400;
          padding: 0 30px;
          text-align: left;
          color: #1d1d1f;
        }
        .box-desc {
          padding: 0 30px;
          text-align: left;
          max-width: 390px;
          line-height: 24px;
          color: #86868b;
        }
      }
    }
  }
  .section-5 {
    padding: 0;
    .container {
      position: absolute;
      top: 24%;
      left: 60%;
      transform: translate3d(-50%, 0, 0);
      width: 400px;
      text-align: left;
      z-index: 1;
      .section-headline {
        font-size: 20px;
        color: #fff;
        padding-bottom: 40px;
      }
      .section-subtitle {
        margin-top: 15px;
        font-size: 35px;
        line-height: 1.3;
        color: #fff;
      }
    }
    // .img-wrap {
    //   background-image: url('~assets/images/product/s6.jpg');
    // }
    .img-wrap::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.2);
      transition: all 0.4s;
      z-index: 0;
    }
  }
  .section-6 {
    padding-top: 0;
    .slide {
      padding: 120px 64px 0;
      width: 100%;
      display: flex;
      .slide-left {
        margin-right: 80px;
      }
      .slide-desc {
        flex: 1;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        flex-direction: column;
        .title {
          margin-bottom: 45px;
          color: #1d1d1f;
          font-size: 1.6rem;
          font-weight: 400;
        }
        .desc {
          text-align: left;
          color: #86868b;
          line-height: 24px;
        }
        &.slide-left {
          margin-left: 30px;
        }
      }
      .slide-img-wrap {
        position: relative;
        width: 480px;
        height: 350px;
        img {
          width: 100%;
        }
      }
      .options {
        display: inline-block;
        font-size: 24px;
        border-bottom: 1px solid #e9e9e9;
        span {
          display: inline-block;
          padding-bottom: 16px;
          margin-right: 65px;
          cursor: pointer;
          &:last-child {
            padding-right: 0;
          }
          &.is-active {
            color: #01ac99;
            border-bottom: 1px solid #01ac99;
          }
        }
      }
      .option {
        margin-top: 32px;
      }
    }
  }
  .section-7 {
    background-color: #fafbfc;
    .section-subtitle {
      margin: 65px auto 140px;
    }
    .box-wrap {
      padding: 0 64px;
      display: flex;
      justify-content: space-between;
      .box-img {
        margin: 0 auto;
        height: 64px;
        background-image: url(~assets/images/product/function.jpg);
        &.img1 {
          width: 54px;
          background-position: 0 -196px;
        }
        &.img2 {
          width: 56px;
          background-position: -53px -196px;
        }
        &.img3 {
          width: 64px;
          background-position: -109px -196px;
        }
        &.img4 {
          width: 55px;
          background-position: -173px -196px;
        }
        &.img5 {
          width: 53px;
          background-position: -228px -196px;
        }
      }
      .box-title {
        margin-top: 45px;
        font-size: 20px;
        font-weight: 600;
      }
    }
  }
  .section-8 {
    background-color: #fff;
    .box-wrap {
      text-align: left;
      max-width: 400px;
      margin: 0 auto;
      text-align: left;
      position: relative;
      .box-title {
        font-size: 20px;
        text-align: left;
        margin-bottom: 20px;
        font-weight: 600;
      }
      .box-desc {
        margin-bottom: 40px;
      }
      .ssq-button-primary {
        text-align: center;
      }
    }
    .manage-list {
      display: flex;
      flex-wrap: wrap;
      max-width: 440px;
      .manage-item {
        margin: 10px;
      }
      .icon-img {
        width: 70px;
        height: 70px;
        border-radius: 15px;
        border: 1px solid #e2e2e2;
        margin-bottom: 15px;
        cursor: pointer;
      }
      .manage-item:hover .icon-img {
        background: #3f3f3f;
        transform: scale(1.1);
        transition: all 0.4s;
      }
      .manage-item:hover p {
        font-weight: 600;
      }
    }
  }
}
@media (max-width: 1025px) {
  .product-function {
    .section-6 {
      .slide .slide-img-wrap {
        width: 415px;
        height: 317px;
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .product-function {
    .headline-desc {
      line-height: 1.4;
      font-size: 1.2rem !important;
      font-weight: 400;
      color: #090909;
    }
    .section-1 {
      background-size: cover;
      background-position: 44%;
      height: 100vw;
      .container {
        top: 0;
        // padding: 4rem 5% 0;
        left: 0;
        transform: translate3d(0, 0, 0);
        padding-top: 5rem;
        .article-headline {
          font-size: 26px;
        }
        .article-subtitle {
          font-size: 16px;
          margin: 35px auto 0;
          line-height: 24px;
        }
        .ssq-button-primary {
          margin-top: 30px;
          width: 120px;
          height: 40px;
          line-height: 1.7;
          padding: 0;
        }
      }
    }
    .second-nav {
      height: 56px;
      line-height: 56px;
      &.is-active {
        top: 56px;
      }
      .container {
        height: 56px;
      }
      .nav-content {
        .icon-text {
          padding-left: 0;
        }
        &.is-active {
          border-bottom: none;
        }
      }
    }
    .section-2 {
      .content {
        .el-col {
          margin: 15px 0;
          min-height: auto;
          padding: 30px 30px;
        }
        .box-wrap {
          max-width: 210px;
          .box-desc {
            font-size: 14px;
          }
        }
      }
    }
    .section-3 {
      .content {
        padding: 0 1rem;
        .box-wrap {
          max-width: auto;
          .box-desc {
            font-size: 14px;
          }
        }
      }
    }
    .section-2,
    .section-3,
    .section-4 {
      .content {
        .box-wrap {
          margin-bottom: 20px;
          .box-title {
            font-size: 20px;
            line-height: 1.5;
            margin: 0.75rem auto 1.75rem;
            color: #1d1d1f;
            font-weight: 400;
          }
          p {
            line-height: 1.5;
            color: #86868b;
            text-align: center;
          }
          .box-img {
            transform: scale(1);
          }
          .box-desc {
            font-size: 14px;
            color: #86868b;
          }
        }
      }
    }
    .section-4 {
      .content {
        .box-wrap {
          .box-title {
            padding: 0;
            text-align: left;
          }
          .box-img {
            transform: scale(1);
          }
          .box-desc {
            padding: 0;
          }
        }
      }
    }
    .index-contract {
      padding-top: 0;
      .content {
        .box-wrap {
          text-align: center;
          .box-img {
            position: relative;
          }
          .box-desc {
            margin: 0 auto;
            text-align: center;
          }
        }
      }
    }

    .section-5 {
      background: url('~assets/images/product/s6.jpg') no-repeat;
      widows: 100%;
      background-size: cover;
      height: 70vw;
      position: relative;
      .container {
        width: auto;
        padding: 0 10px;
        top: 0;
        left: 0;
        bottom: 0;
        transform: translate3d(0, 0, 0);
        .section-subtitle {
          margin-top: 0;
        }
      }
      & ::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.2);
        transition: all 0.4s;
        z-index: -1;
      }
    }
    .section.section-6-m {
      padding-top: 0;
      .container {
        width: 100%;
        img {
          width: 100%;
        }
      }
      .title {
        padding-top: 65px;
        font-size: 20px;
        line-height: 1.5;
        color: #1d1d1f;
        font-weight: 400;
      }
      .desc-wrap {
        margin-top: 20px;
        font-size: 12px;
        color: #888;
        p {
          line-height: 22px;
          font-size: 14px;
          color: #86868b;
        }
      }
    }
    .section-7 {
      padding-left: 0;
      padding-right: 0;
      .section-subtitle {
        margin: 0 auto 50px;
        padding: 0 10px;
      }
      .box-wrap {
        display: block;
        padding: 0;
        .box {
          display: flex;
          align-items: center;
          padding-left: 50px;
          border-top: 1px solid #bfbfbf;
          &.bb {
            border-bottom: 1px solid #bfbfbf;
          }
          div {
            display: inline-block;
          }
          .box-img {
            transform: scale(0.65);
            margin: 0;
          }
          .box-title {
            text-align: left;
            flex: 1;
            margin: 0 0 0 50px;
            font-weight: 600;
            font-size: 16px;
            &.spec3 {
              margin-left: 40px;
            }
          }
        }
      }
    }
  }
}
</style>
