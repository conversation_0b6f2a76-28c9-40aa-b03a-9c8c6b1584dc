<template>
  <article class="product-check">
    <section class="section section-1">
      <div class="container">
        <h1 class="article-headline">在线验签</h1>
        <p class="article-subtitle">验证你持有的文档（PDF文件）是否为通过上上签平台签署完成的文档<br>验证你持有的文档（PDF文件）有无被篡改</p>
        <div class="upload">
          <!-- <button class="ssq-button-new">点击上传PDF格式合同</button> -->
          <el-button class="ssq-button-new" type="text">点击上传PDF格式合同
			  <i class="iconfont icon-xiangyoujiantou"></i>
		  </el-button>
          <input
            type="file"
            accept="application/pdf"
            @change="upload($event)">
        </div>
      </div>
      <!-- <div v-if="!isMobile" class="img-wrap">
        <img src="@/assets/images/product/check-banner.jpg">
      </div>
      <div v-else class="mobile-wrap">
        <img src="@/assets/images/product/wap-check-banner.jpg" alt="">
      </div> -->
    </section>

    <el-dialog
      visible.sync="visible"
    >
      {{ results.msg }}
    </el-dialog>
  </article>
</template>

<script>
import { MessageBox } from 'element-ui';
export default {
  name: 'ProductCheck',
  data() {
    return {
      visible: false,
      isSuccess: false,
      results: {},
      signResults: [],
    };
  },
  head() {
    return {};
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    host() {
      var host = window.location.host;
      var protocol = window.location.protocol;
      if (host === 'www.bestsign.cn') {
        return protocol + '//ent.bestsign.cn';
      } else if (host === 'www.bestsign.info') {
        return protocol + '//ent.bestsign.info';
      } else {
        return protocol + '//' + host;
      }
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: 'product' });
  },
  methods: {
    openModal() {
      this.visible = true;
    },
    upload(e) {
      const file = e.target.files[0];

      // this.$axios
      //   .$post('/contract-api/ignore/contracts/verifyContract', formData)
      //   // .$post(`${this.host}/contract-api/ignore/contracts/verifyContract`, formData)
      //   .then(res => {
      //     console.log(res)
      //     if (res.result) {
      //       this.isSuccess = true
      //       this.result = res
      //       this.signResults = res.signatureVerifyResults
      //     }
      //     loading.close()
      //   })
      // let host = '';
      // if (process.env.NODE_ENV !== 'development') {
      //   host =
      //     process.env.baseUrl.indexOf('cn') > -1
      //       ? 'https://ent.bestsign.cn'
      //       : 'https://ent.bestsign.info';
      // }
      // let host = 'https://ent.bestsign.info';
      // if (process.env.baseUrl.indexOf('cn') > -1) {
      //   host = 'https://ent.bestsign.cn';
      // }
      this.localFileUpload({
        url: '/www/api/web/verifyContract',
        file,
      });
    },
    localFileUpload(config) {
      const loading = this.$loading({
        lock: true,
        text: 'pdf上传中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      const xhr = new XMLHttpRequest();
      const _this = this;
      xhr.onload = function() {
        const res = JSON.parse(this.response);
        if (res.result) {
          // debugger;
          if (res.bestSign) {
            res.signatureVerifyResults.map(item => {
              if (item.hasOwnProperty('reason')) {
                item.reasonAry = item.reason.split(item.signatureName);
              }
              // console.log(item.reasonAry);
              // console.log(item.reason)
              return item;
            });
          } else {
            res.signatureVerifyResults.map(item => {
              if (item.saasContract) {
                if (item.hasOwnProperty('reason')) {
                  item.reasonAry = item.reason.split(item.signatureName);
                }
              }
              // console.log(item.reasonAry);
              // console.log(item.reason)
              return item;
            });
          }

          _this.$store.commit('setCheckResult', { result: res });
          _this.$router.push('/product/check/result');
          // _this.results = res
          // _this.signResults = res.signatureVerifyResults
        } else {
          MessageBox.alert(res.msg, '提示');
        }
        loading.close();
      };
      xhr.open('post', config.url, true);
      const oFormData = new FormData();
      oFormData.append('file', config.file);
      xhr.send(oFormData);
    },
  },
};
</script>

<style scoped lang="scss">
.product-check {
  text-align: center;
  .section-1 {
    // color: #fff;
    // padding: 0;
    // position: relative;
    background: url('~assets/images/product/check-banner.jpg') no-repeat;
    background-size: cover;
    width: 100%;
    height: 44vw;
    display: flex;
    align-items: center;
    background-position: center;
    padding: 4rem 0;
    .container {
      position: absolute;
      // top: 130px;
      left: 50%;
      transform: translate3d(-50%, 0, 0);
      // top: 13.5rem;
    }
    .article-headline {
      font-weight: 500;
      color: #fff;
      font-size: 2.875rem;
    }
    .article-subtitle {
      color: #fff;
      margin: 50px auto 65px;
      font-size: 1.2rem;
      font-weight: 400;
    }
    .img-wrap,
    img {
      width: 100%;
      vertical-align: middle;
    }
    // .ssq-button-primary {
    //   border: none;
    //   width: 220px;
    // }
    .el-button--text {
      color: #fff;
      background: 0 0;
      padding-left: 0;
      padding-right: 0;
      border-bottom: 1px solid #fff;
      padding-bottom: 4px;
    }
    .upload {
      display: inline-block;
      position: relative;
      input {
        opacity: 0;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
      }
      .iconfont {
        font-size: 14px;
      }
    }
  }
}
@media (max-width: 1024px) {
  .product-check .section-1 {
    .container {
      top: 50px;
    }
    .article-subtitle {
      margin: 30px auto 40px;
    }
  }
}
@media screen and (max-width: 767px) {
  .product-check {
    .section-1 {
      //   background-image: linear-gradient(180deg, #019b89, #05d2bb);
      background-size: cover;
      background-position: 44%;
      height: 100vw;
      .container {
        width: 95%;
        padding-top: 5rem;
        margin: 0;
        .article-headline {
          font-size: 28px;
          font-weight: 500;
        }
        .article-subtitle {
          font-size: 16px;
          line-height: 24px;
        }
      }
      .mobile-wrap {
        img {
          width: 100%;
        }
      }
    }
  }
}
</style>
