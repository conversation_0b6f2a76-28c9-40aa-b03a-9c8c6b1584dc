<template>
  <article>
    <section class="section section-2 check-result">
      <div class="container">
        <h1 class="section-headline">PDF文件验签完成</h1>
        <p class="section-subtitle">1、该PDF文件{{ results.msg }}</p>
        <p class="section-subtitle">2、最后签署时间：{{ lastSignTime }} </p>
        <p class="section-subtitle">3、该PDF文件包含{{ length }}个电子签名，详情如下：</p>
        <p class="section-subtitle">4、该PDF合同包含{{results.contractList.length}}个签署流程，可在以下签名列表展开查看。</p>
        <div class="content">
          <img v-if="!isMobile" src="@/assets/images/product/check@bg_ok.png">
          <div class="collapse-wrap">
            <el-collapse v-for="(item, index) in signResults" :key="index">
              <!-- <template v-for="(item, index) in signResults"> -->
                <!-- <el-collapse-item
                  :title="`签名${ index + 1 }：由${ item.signerName }签名${item.lock ? `(文档被${item.signatureName}锁定）` : ''}`"> -->
                  <el-collapse-item
                  :title="`签名${ index + 1 }：由${ item.signerName }签名`">
                  <el-collapse>
                    <el-collapse-item title="签名有效性">
                      <p>{{ item.validtySummary }}</p>
                      <p>{{ item.identitySummary }}</p>
                      <p>签名时间：{{ item.signTime }}</p>
                      <p v-if="item.saasContract">原因：{{ item.reasonAry[0]}}
                        <a @click="openContract(item.signatureName)"><span v-if="item.reason.includes(item.signatureName)" style="color:#00aa64; cursor: pointer;">{{item.signatureName}}</span></a>{{item.reasonAry[1]}}</p>
                      <!-- <p v-if="!item.saasContract">原因：{{ item.reasonAry[0]}}<span v-if="item.reason.includes(item.signatureName)">{{item.signatureName}}</span>{{item.reasonAry[1]}}</p> -->
                      <p v-if="!item.saasContract">原因：{{item.reason}}</p>
                      <p>位置：{{ item.location }}</p>
                      <p>签名{{ item.timeStampVerified ? '' : '不' }}包含嵌入的时间戳。时间戳时间{{ item.timeStampTime }}</p>
                      <p>该签名位于第{{ item.signPage }}页</p>
                    </el-collapse-item>
                    <el-collapse-item title="签名者证书信息">
                      <p>签名算法：{{ item.encryptionAlgorithm }} {{ item.digestAlgorithm }}</p>
                      <p>主题：{{ item.subjectDN }}</p>
                      <p>颁发者：{{ item.issuer }}</p>
                      <p>序列号：{{ item.serialNumber }}</p>
                      <p>有效起始日期：{{ item.validDate }}</p>
                      <p>有效截止日期：{{ item.expireDate }}</p>
                    </el-collapse-item>
                  </el-collapse>
                </el-collapse-item>
                  <div v-if="item.lock&&item.saasContract" class="last-step">文档被<a @click="openContract(item.signatureName)"><span :class="{'idColor':item.saasContract }">{{item.signatureName}}</span></a>锁定</div>
                  <div v-if="item.lock&&!item.saasContract" class="last-step">文档被<span :class="{'idColor':item.saasContract }">{{item.signatureName}}</span>锁定</div>
              <!-- </template> -->
            </el-collapse>
          </div>
        </div>
        <button style="margin-top: 25px" class="ssq-button-primary" @click="handleRouter">返回继续上传</button>
      </div>
    </section>
  </article>
</template>

<script>
export default {
  name: 'CheckResult',
  data() {
    return {};
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    results() {
      return this.$store.state.checkResult || {};
    },
    signResults() {
      return this.results.signatureVerifyResults || [];
    },
    length() {
      return this.signResults.length || 0;
    },
    lastSignTime() {
      const length = this.signResults.length;
      if (length) {
        return this.signResults[length - 1].signTime;
      }
      return '';
    },
  },
  methods: {
    handleRouter() {
      this.$router.push('/product/check');
    },
    openContract(contractID) {
      const contractUrl =
        window.location.host.indexOf('cn') > -1
          ? `https://ent.bestsign.cn/doc/detail/${contractID}`
          : `https://ent.bestsign.info/doc/detail/${contractID}`;
      window.open(contractUrl);
    },
  },
};
</script>
<style lang="scss">
@media screen and (max-width: 767px) {
  .check-result.section-2 {
    .el-collapse-item__header {
      height: auto;
    }
  }
}
</style>
<style scoped lang="scss">
.section-2 {
  text-align: center;
  .section-subtitle {
    margin: 5px auto 5px;
    text-align: left;
  }
  .content {
    width: 900px;
    height: 600px;
    margin: 0 auto;
    position: relative;
    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 900px;
    }
    .collapse-wrap {
      width: 750px;
      height: 450px;
      position: absolute;
      top: 0;
      left: 0;
      margin: 80px 100px 60px 60px;
      overflow: scroll;
      text-align: left;
      p {
        font-size: 12px;
        line-height: 22px;
        padding-left: 16px;
      }
      .last-step {
        color: #303133;
        line-height: 38px;
        font-size: 13px;
        font-weight: 500;
        .idColor {
          color: #00aa64;
          cursor: pointer;
        }
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .section-2 {
    .content {
      width: 100%;
      height: auto;
      margin: 0;
      .collapse-wrap {
        position: relative;
        width: 100%;
        height: auto;
        margin: 0;
      }
    }
  }
}
</style>
