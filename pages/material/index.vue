<template>
  <div class="demo">
    <demo-header></demo-header>
    <div class="demo-card">
      <div class="demo-left" v-if="!isMobile">
        <h4 class="left-bigTitle">{{$t('material.bigTitle')}}</h4>
        <div class="left-desc">{{$t('material.littleTitle')}}</div>
        <div class="left-img">
          <img src="https://static.bestsign.cn:443/c6867db4efc8a7b3776946101e6da7f5e86fcaf3.png" alt="注册图片" width="100%" v-if="isEN">
          <img src="https://static.bestsign.cn:443/53dc24f0d84c58d65d5afcbf715e7f12d4d75cb4.png" alt="注册图片" width="100%" v-if="!isEN">
        </div>
        <div class="partner-title">
          {{$t('material.partners')}}
        </div>
        <div class="partner-con">
          <!-- <div class="partner-list" v-for="item in logoList" :key="item">
            <img :src="item" alt="500强企业客户" width="50%">
          </div> -->
          <demo-logo></demo-logo>
        </div>

        
      </div>
      
      <div class="card-body">
        <register v-if="!isEN"></register>
        <en-register v-if="isEN"></en-register>
      </div>
    </div>
	<!-- <div class="register-footer"></div> -->
  </div>
</template>

<script>
import DemoHeader from '@/components/DemoHeaderNew.vue';
import Register from './Register';
import enRegister from './enRegister';
import DemoLogo from './demoLogo.vue';
import Client from '@/components/Solution/Client.vue';

export default {
  name: 'demo',
  layout: 'blank',
  head() {
    return {
      title: this.$tc('material.t'),
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.$tc('material.k'),
        },
        {
          hid: 'description',
          name: 'description',
          content: this.$tc('material.d'),
        },
      ],
    };
  },
  components: {
    DemoHeader,
    Register,
    DemoLogo,
    enRegister,
  },
  data() {
    return {
      // logoList: [
      //   'https://static.bestsign.cn:443/58ebf40129e4534a548131e5dc9973c176302859.png',
      //   'https://static.bestsign.cn:443/5e005b91d9ceb4c842fb4900849c1b1386aa3e00.png',
      //   'https://static.bestsign.cn:443/3cf07cf382ddb7110e2cfd353e0f4491e59e5cae.png',
      //   'https://static.bestsign.cn:443/21a70e1469a3dba81a3ff63c245f033ca78241e1.png',
      //   'https://static.bestsign.cn:443/39e84e16bdf7d4c114b5d22bdfbf8987e42d3416.png',
      //   'https://static.bestsign.cn:443/6bdcfab5201f0189531e3e127bedd3db3bbbdba0.png',
      // ],
      isEN: false,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    coreData() {
      return this.$store.state.coreData;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  mounted() {
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
};
</script>
<style lang="scss">
.demo {
  .el-select .el-input.is-focus .el-input__inner {
    border-color: #c0c4cc;
  }
  .el-button--primary.is-active,
  .el-button--primary:active {
    background: #f3c51e;
  }
}
.el-select-dropdown__item {
  text-align: left;
}
.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: #e7f5f1;
}
.el-select-dropdown__item.selected {
  color: #f3c51e;
}
</style>
<style scoped lang="scss">
.demo {
  width: 100%;
  /* height: 100vh; */
  position: relative;
  background: #fafafa;
  .demo-card {
    display: flex;
    /* padding-top: 3rem; */
    /* max-width: 1200px; */
    width: 100%;
    .card-header {
      height: 52px;
      line-height: 52px;
      padding: 0 32px;
      color: #f3c51e;
      background-color: #e7f5f1;
      text-align: center;
    }
    .demo-left {
      width: 50%;
      background: #fff;
      text-align: center;
      .left-bigTitle {
        text-align: center;
        margin-bottom: 35px;
        font-size: 30px;
        color: #333;
        font-weight: 450;
        margin-top: 40px;
        padding: 0 8%;
      }
      .left-desc {
        line-height: 1.5;
        color: #666;
        padding: 0 8%;
      }
      .left-img {
        margin: 1rem 20%;
      }
      .partner-title {
        margin: 3rem 0 1rem;
        color: #86868b;
      }
      .partner-con {
        margin: 0 10%;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        .partner-list {
        }
      }
    }
    .card-body {
      width: 50%;
      text-align: center;
      padding: 0 2rem 2rem;
      // height: calc(100vh - 79px);
    }
    .register {
      width: 30rem;
      margin: 0 auto;
      padding: 1rem 2rem 2rem;
      background: #fff;
    }
  }
  .register-footer {
    height: 200px;
    background: #fff;
  }
  .form-pictureVerify {
    width: 80px;
    height: 36px;
  }
}
@media screen and (max-width: 767px) {
  .demo {
    .demo-card {
      padding: 0;
      .card-body {
        width: 100%;
        display: block;
        padding: 2rem 1rem;
      }
      .register {
        width: 100%;
      }
    }
  }
}
@media screen and (min-width: 767px) and (max-width: 1024px) {
  .demo {
    .demo-card {
      padding: 0 1rem;
      .register {
        width: 40rem;
      }
    }
  }
}
</style>
