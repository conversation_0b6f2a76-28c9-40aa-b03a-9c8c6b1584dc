<template>
  <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="ruleForm register">
    <!-- <div class="text" v-if="text" v-html="text"></div> -->
    <div class="description">
      <div class="title">{{$t('material.formTitle')}}</div>
      <div class="line-1">
        {{$t('material.formDesc1')}}</div>
      <div div class="line-1">{{$t('material.formDesc2')}}</div>
    </div>
    <el-form-item prop="surname">
      <el-input v-model.trim="ruleForm.surname" :placeholder="`${$t('form.surname')}*`"></el-input>
    </el-form-item>
    <el-form-item prop="name">
      <el-input v-model.trim="ruleForm.name"  :placeholder="`${$t('form.name')}*`"></el-input>
    </el-form-item>
    <el-form-item prop="companyName">
      <el-input v-model.trim="ruleForm.companyName"  :placeholder="`${$t('form.company')}*`"></el-input>
    </el-form-item>
    <el-form-item prop="email">
      <el-input v-model.trim="ruleForm.email"  :placeholder="`${$t('form.email')}*`"></el-input>
    </el-form-item>
    <el-form-item prop="department">
      <el-input v-model.trim="ruleForm.department"  :placeholder="`${$t('form.department')}*`"></el-input>
    </el-form-item>
    <el-form-item prop="phone">
      <el-input v-model.trim="ruleForm.phone"  :placeholder="`${$t('form.phone')}*`"></el-input>
    </el-form-item>
    <el-form-item prop="memberCount">
      <el-select
        v-model.trim="ruleForm.memberCount"
        :placeholder="`${$t('form.member')}*`"
    >
        <el-option
            v-for="item in memberOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
        >
        </el-option>
    </el-select>
    </el-form-item>
    <el-form-item prop="onlinePlan">
      <el-select
        v-model.trim="ruleForm.onlinePlan"
        :placeholder="`${$t('form.plan')}*`"
    >
        <el-option
            v-for="item in planOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
        >
        </el-option>
    </el-select>
    </el-form-item>
    
    <div class="checkbox">
      <el-checkbox name="type" v-model="isChecked"></el-checkbox>
      <a class="protocol" href="https://static.bestsign.cn/43d5a41628cb37d3866a9495e16f8c4683a771ec.pdf" target="_blank"> {{$t('form.agreement.1')}}</a>
      <span class="agree">{{$t('form.agreement.2')}}</span>
    </div>
    <div class="desc">{{$t('form.agreement.3')}}</div>
    <div>
      <el-button :loading="loading" :disabled="!isChecked" type="primary" class="submit" @click="toMaterial">{{$t('form.agreement.4')}}</el-button>
    </div>
  </el-form>
  
</template>

<script>
import resRules from '@/assets/utils/regs.js';
import CountDown from '@/components/CountDown.vue';
import PictureVerify from '@/components/PictureVerify.vue';
import { isPhoneOrMail } from '@/assets/utils/reg.js';
import aes from '@/assets/utils/aes.js';
import Qs from 'qs';
import RegistrationProtocol from '../protocol/RegisterProtocol';

export default {
  name: 'Register',
  components: {
    RegistrationProtocol,
    CountDown,
    PictureVerify,
  },
  props: {
    text: {
      type: String,
      default: '',
    },
  },
  data() {
    function validateNoEmpty(rule, value, callback) {
      if (!value) {
        callback(new Error('このフィールドを入力してください。'));
      } else {
        callback();
      }
    }
    return {
      isEN: false,
      loading: false,
      showPictureVerCon: false,
      countDownDisabled: false,
      codeDisabled: true,
      isChecked: false,
      memberOptions: this.getOptions('memberOptions'),
      planOptions: this.getOptions('planOptions'),
      ruleForm: {
        surname: '',
        name: '',
        companyName: '',
        email: '',
        department: '',
        phone: '',
        memberCount: '',
        onlinePlan: '',
      },
      rules: {
        surname: [
          {
            required: true,
            validator: validateNoEmpty.bind(this),
            trigger: 'blur',
          },
        ],
        name: [
          {
            required: true,
            validator: validateNoEmpty.bind(this),
            trigger: 'blur',
          },
        ],
        companyName: [
          {
            required: true,
            validator: validateNoEmpty.bind(this),
            trigger: 'blur',
          },
        ],
        email: [
          {
            required: true,
            validator: validateNoEmpty.bind(this),
            trigger: 'blur',
          },
          {
            pattern: resRules.userEmail,
            message: '正しいメールアドレスを入力してください。',
            trigger: 'blur',
          },
        ],
        department: [
          {
            required: true,
            validator: validateNoEmpty.bind(this),
            trigger: 'blur',
          },
        ],
        onlinePlan: [
          {
            required: true,
            validator: validateNoEmpty.bind(this),
            trigger: 'blur',
          },
        ],
        memberCount: [
          {
            required: true,
            validator: validateNoEmpty.bind(this),
            trigger: 'blur',
          },
        ],
        phone: [
          {
            required: true,
            validator: validateNoEmpty.bind(this),
            trigger: 'blur',
          },
          {
            pattern: resRules.phone,
            message: '正しい携帯番号を入力してください。',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  methods: {
    toMaterial() {
      this.$refs['ruleForm'].validate(valid => {
        if (!valid) {
          this.loading = false;
          return false;
        } else {
          this.$axios
            .post(
              this.isEN
                ? 'www/api/web/enInformation'
                : 'www/api/web/jpInformation',
              {
                ...this.ruleForm,
                applyUrl: window.location.href,
              }
            )
            .then(res => {
              this.$router.push('/materialSuccessful');
            });
        }
      });
    },
    getOptions(name) {
      const obj = this.$t(`form.${name}`);
      return Object.keys(obj).map(key => ({
        value: obj[key],
        label: obj[key],
      }));
    },
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  mounted() {
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
};
</script>
<style lang="scss">
.register {
  .el-form-item.is-success .el-input__inner {
    border-color: #dcdfe6;
  }
  .el-input__inner {
    border: 1px solid #f7f7f9;
    background: #f7f7f9;
  }
  .el-input.is-active .el-input__inner,
  .el-input__inner:focus {
    border-color: #c0c4cc;
  }
  .el-input__inner:hover {
    border-color: #c0c4cc;
  }
  .el-form-item {
    margin-bottom: 20px;
  }
  .el-form-item__error {
    padding-top: 4px;
    text-align: right;
    font-weight: 500;
    color: #f50;
  }
  .el-form-item.is-error .el-input__inner {
    border-color: #f50;
  }
  .el-checkbox {
    margin-right: 8px;
  }
  .el-checkbox__inner:hover {
    border-color: #f3c51e;
  }
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #f3c51e;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #f3c51e;
    border-color: #f3c51e;
  }
  .el-input__inner {
    border-radius: 0;
  }
  .el-input-group__append {
    border-radius: 0;
    width: 125px;
    background-color: #f7f7f9;
    border: 1px solid #f7f7f9;
  }
  .el-button--primary {
    background-color: #f3c51e;
    border-color: #f3c51e;
    width: 100%;
    border-radius: 0;
    font-size: 17px;
  }
  .el-button--primary:focus,
  .el-button--primary:hover {
    background-color: #f3c51e;
    border-color: #f3c51e;
  }
  .el-select {
    width: 100%;
  }
  .title {
    text-align: center;
    margin-bottom: 1rem;
    line-height: 1.5rem;
    color: #818181;
    font-size: 1.2rem;
    font-weight: 600;
  }
  .radio-box {
    text-align: left;
  }
  .el-radio {
    margin-right: 10px;
  }
  .el-radio__input.is-checked .el-radio__inner {
    border-color: #f3c51e;
    background: #f3c51e;
  }
  // .el-radio__input.is-checked + .el-radio__label {
  //   color: #f3c51e;
  // }
}
</style>
<style scoped lang="scss">
.description {
  text-align: left;
  margin: 10px 0;
  .el-icon-success {
    color: #f3c51e;
    margin-right: 5px;
  }
  .title {
    text-align: left;
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.5rem;
    line-height: 1.5;
    font-weight: 400;
  }
  .line-1 {
    color: #666;
    line-height: 1.5;
  }
}
.countDown {
  background-color: #f7f7f9;
  font-size: 14px;
  width: 100%;
  border: none;
  color: #f3c51e;
}
.radio-box {
  text-align: left;
}
.register {
  border-radius: 10px;
  padding: 2.5rem 2rem 4rem;
  position: relative;
  //   background-color: #fff;
  color: #515151;
  .text {
    padding-bottom: 1rem;
    line-height: 1.5;
    font-size: 1rem;
    text-align: center;
  }
  .checkbox {
    margin-bottom: 10px;
    text-align: left;
    margin-top: 15px;
  }
  .login {
    // position: absolute;
    // left: 0;
    // bottom: 0;
    // width: 100%;
    height: 3rem;
    line-height: 3rem;
    text-align: center;
    font-size: 0.875rem;
    margin-top: 60px;
    border-bottom: 1px solid #eee;
    // background-color: #f0f0f0;
  }
  .select-right {
    width: 100%;
  }
  .protocol {
    color: #f3c51e;
    font-size: 14px;
    cursor: pointer;
  }
  .agree {
    font-size: 14px;
  }
  .desc {
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
    text-align: left;
    line-height: 1.5;
  }
  .tips {
    padding-top: 1rem;
    font-size: 0.875rem;
    a {
      color: #f3c51e;
      cursor: pointer;
    }
  }
}
.register-footer {
  widows: 100%;
  height: 150px;
  background: #fff;
}
</style>
