<template>
      <section class="demoLogo">
        <div class="common-title-line"></div>
        <div v-swiper:demoLogoSwiper="demoLogo.demoLogoSwiperOption" v-if="demoLogo.logoList.length">
            <div class="swiper-wrapper">
                <div class="swiper-slide" v-for="(item,index) in demoLogo.logoList" :key="index">
                    <div class="swiper-slide-item">
                        <img :src="item" alt="500强企业客户" width="100%">
                    </div>
                </div>
            
            </div>
        </div>
    </section>
</template>
<script>
export default {
  data() {
    return {
      demoLogo: {
        demoLogoSwiperActive: 0,
        demoLogoSwiperOption: {
          loop: true,
          initialSlide: 0,
          slidesPerView: 1,
          speed: 500,
          direction: 'vertical',
          // autoplay: true,
          autoplay: {
            delay: 3000,
            disableOnInteraction: false,
          },
          observer: true,
          observeParents: true,
          height: 100, //你的slide高度
        },
        logoList: [
          'https://static.bestsign.cn:443/ecafa0f4fe26d3c12b2c9b09cef0c46bd268f174.png',
          'https://static.bestsign.cn:443/728bd875aa37d18951ebf7ef73691c0619b4049d.png',
          'https://static.bestsign.cn:443/89f3d229c0d26b4428d794740b75857bb2afb381.png',
          'https://static.bestsign.cn:443/38566b6f844e2e4e4b502e25325d315d926f305f.png',
        ],
      },
    };
  },
  methods: {
    godemoLogoSwiper(index) {
      this.demoLogoSwiper.slideTo(index);
      this.demoLogo.demoLogoSwiperActive = index;
    },
  },
  mounted() {
    const self = this;
    if (this.demoLogo.logoList.length) {
      this.demoLogoSwiper.on('slideChange', function() {
        self.demoLogo.demoLogoSwiperActive = this.activeIndex;
      });
    }
  },
};
</script>
<style lang="scss" scoped>
.demoLogo {
  height: 100px;
  overflow: hidden;
  .swiper-container {
    .swiper-slide-item {
      width: 100%;
      height: 100px;
      position: relative;
      margin: 10px auto;
      .demoLogo-swiper-logo {
        margin-bottom: 8px;
      }
      .demoLogo-swiper-bg {
        position: absolute;
        bottom: 0;
        right: 20px;
      }
      p {
        color: #9ca7b4;
        width: 120px;
        padding-top: 30px;
      }
    }
  }
  .demoLogo-swiper-indicator {
    text-align: center;
    margin-top: 30px;
    span {
      display: inline-block;
      width: 44px;
      height: 44px;
      background-color: #fbfbfc;
      color: #c5c9cd;
      border-radius: 24px;
      line-height: 44px;
      text-align: center;
      font-size: 14px;
      margin: 0 3px;
      &.focus {
        background-color: #f6f7fa;
        color: #8c939b;
      }
    }
  }
}
</style>
