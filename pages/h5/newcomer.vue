<template>
  <div class="newcomer">
    <div class="container">
      <div class="img-wrapper">
        <img src="./img/coupon.jpg">
      </div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="ruleForm">
        <el-form-item prop="companyName">
          <el-input v-model.trim="ruleForm.companyName" size="small" placeholder="输入公司名称"></el-input>
        </el-form-item>
        <el-form-item prop="contact">
          <el-input v-model.trim="ruleForm.contact" size="small" placeholder="输入手机号码"></el-input>
        </el-form-item>
        <el-form-item prop="verifyCode">
          <el-input v-model.trim="ruleForm.verifyCode" size="small" placeholder="输入短信验证码" class="code-form-item">
            <template slot="append">
              <count-down class="countDown code-sent" :clickedFn="send" :disabled="countDownDisabled" ref="btn" :second="60"></count-down>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="showPictureVerCon" label="" class="pictureVer-item" prop="imageCode">
          <el-input
            size="small"
            class="pictureVer"
            placeholder="请填写4位验证码"
            :maxlength="4"
            v-model.trim="ruleForm.imageCode"
          >
            <template slot="append">
              <PictureVerify
                class="form-pictureVerify"
                ref="pictureVerify"
                :imageKey="ruleForm.imageKey"
                @change-imageKey="changeImageKey"
              />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" class="submit" @click="toRegister">免费领取</el-button>
        </el-form-item>
      </el-form>
      <div class="logo">
        <img src="./img/logo.svg">
      </div>
      <div class="para">本活动仅限新用户</div>
    </div>
    <el-dialog
      :visible.sync="visible"
      width="76%"
      top="40vh"
      class="dialog"
    >
      <img src="./img/icon-success.png">
      <h3>提交成功</h3>
      <p>我们将联系你，进行发放</p>
    </el-dialog>
  </div>
</template>

<script>
import resRules from '@/assets/utils/regs.js';
import CountDown from '@/components/CountDown.vue';
import PictureVerify from '@/components/PictureVerify.vue';
import { isPhoneOrMail } from '@/assets/utils/reg.js';
import aes from '@/assets/utils/aes.js';
export default {
  name: 'newcomer',
  layout: 'blank',
  components: {
    CountDown,
    PictureVerify,
  },
  head() {
    return {
      title: '上上签新人见面礼',
    };
  },
  data() {
    return {
      showPictureVerCon: false,
      countDownDisabled: false,
      codeDisabled: true,
      visible: false,
      ruleForm: {
        companyName: '',
        contact: '',
        imageCode: '',
        verifyCode: '',
        verifyKey: '',
        imageKey: '',
      },
      rules: {
        companyName: [
          {
            required: true,
            message: '请输入公司名称',
            trigger: 'blur',
          },
          {
            min: 2,
            message: '公司名称不能少于两个字符',
            trigger: 'blur',
          },
        ],
        contact: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur',
          },
          {
            pattern: resRules.userPhone,
            message: '手机号格式错误',
            trigger: 'blur',
          },
        ],
        verifyCode: [
          {
            required: true,
            message: '请输入验证码',
            trigger: 'blur',
          },
        ],
        imageCode: [
          {
            required: true,
            message: '请输入图形验证码',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  mounted() {
    if (process.client) {
      this.initWxData();
    }
  },
  methods: {
    // 更改图形验证码
    changeImageKey(value) {
      this.codeDisabled = false;
      this.ruleForm.imageKey = value;
    },
    // 发送验证码
    send() {
      const { contact, imageCode, imageKey } = this.ruleForm;
      this.$refs['ruleForm'].validateField(['contact'], error => {
        if (error) {
          return false;
        }
        let headersObj = {};
        if (imageCode !== '' && imageKey !== '') {
          headersObj = {
            // 'Content-Type': 'application/json; charset=utf-8',
            additionalImgVerCode: JSON.stringify({
              imageCode,
              imageKey,
            }),
            'request-source': 'WEB',
          };
        }
        // let host = 'https://ent.bestsign.info';
        // if (process.env.baseUrl.indexOf('cn') > -1) {
        //   host = 'https://ent.bestsign.cn';
        // }
        let host = '';
        if (process.env.NODE_ENV !== 'development') {
          host =
            process.env.baseUrl.indexOf('cn') > -1
              ? 'https://ent.bestsign.cn'
              : 'https://ent.bestsign.info';
        }
        this.$axios({
          url: `${host}/users/ignore/captcha/notice?encryptToken=${aes(
            'B001',
            contact
          )}`,
          method: 'post',
          headers: headersObj,
          data: {
            code: 'B001',
            sendType: isPhoneOrMail(contact) === 'phone' ? 'S' : 'E',
            target: contact,
            imageCode,
            imageKey,
          },
        })
          .then(res => {
            if (res) {
              this.$MessageToast.success('发送成功！');
              this.countDownDisabled = true;
              setTimeout(this.sended, 0);
              this.ruleForm.verifyKey = res.value;
              this.codeDisabled = false;
            }
          })
          .catch(err => {
            console.log(err);
            const res = err.response.data;
            if (res.code === '902' || res.code === '100006') {
              if (this.showPictureVerCon) {
                setTimeout(() => {
                  this.$refs.pictureVerify.changeImg();
                }, 20);
              } else {
                this.showPictureVerCon = true;
              }
              if (res.message !== '图片验证码不能为空') {
                this.$MessageToast.error(res.message);
              } else {
                this.$MessageToast.error('请先填写图形验证码');
              }
            } else {
              this.$MessageToast.error(res.message);
            }
            this.$refs.btn.reset();
          });
      });
    },

    sended() {
      this.$refs.btn.run();
      this.countDownDisabled = false;
    },

    // 申请试用
    toRegister() {
      const { companyName, verifyKey, verifyCode, contact } = this.ruleForm;
      // 百度统计当用户点击“申请免费试用”成功提交信息之后，需要将当前页面URL地址，客户地区记录
      const url = location.href;
      window._hmt &&
        window._hmt.push(['_trackEvent', 'register_url', 'submit', url]);

      const params = {
        contact,
        companyName,
        verifyCode,
        verifyKey,
        applyUrl: url,
        type: 3,
      };
      this.$refs['ruleForm'].validate(valid => {
        if (!valid) {
          return false;
        }
        this.$axios
          .post('/www/api/web/tryout/register', {
            ...params,
          })
          .then(res => {
            if (res && res.value === 'OK') {
              this.visible = true;
              // const isPre = window.location.hostname.indexOf('info') > -1;
              // window.location.href = isPre
              //   ? 'https://demo.bestsign.info/pages/index.html?v=20180925'
              //   : 'https://demo.bestsign.cn/pages/index.html?v=20180925';
            }
          })
          .catch(err => {
            const res = err.response.data;
            if (res.code === '903') {
              this.$MessageToast.error('领取失败请稍后再试');
            } else {
              this.$MessageToast.error(res.message);
            }
          });
      });
    },

    initWxData() {
      const wx = require('weixin-js-sdk');
      let apiHost =
        location.host.indexOf('info') > 0
          ? 'https://demo.bestsign.info'
          : 'https://demo.bestsign.cn';
      this.$axios
        .post(
          apiHost + '/demo/weixin/jsticket',
          {
            url: location.href.split('#')[0],
          },
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          }
        )
        .then(res => {
          let { data } = res;
          wx.config({
            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId: data.appId, // 必填，公众号的唯一标识
            timestamp: data.timestamp, // 必填，生成签名的时间戳
            nonceStr: data.nonceStr, // 必填，生成签名的随机串
            signature: data.signature, // 必填，签名
            jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'], // 必填，需要使用的JS接口列表
          });
          wx.error(function(res) {
            // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
            console.log(res);
          });
          /*自定义“分享给朋友”及“分享到QQ”按钮的分享内容*/
          wx.ready(function() {
            //需在用户可能点击分享按钮前就先调用
            wx.updateAppMessageShareData({
              title: '上上签新人见面礼', // 分享标题
              desc: '1200元电子合同体验券免费领', // 分享描述
              link: `${window.location.origin}/h5/newcomer`, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
              imgUrl: `${window.location.origin}/h5-gift-share.png`, // 分享图标
              success: function(obj) {
                //设置成功
                console.log(obj);
              },
              fail: function(obj) {
                console.log(obj);
              },
            });
            /*自定义“分享到朋友圈”及“分享到QQ空间”按钮的分享内容*/
            wx.updateTimelineShareData({
              title: '上上签新人见面礼', // 分享标题
              desc: '1200元电子合同体验券免费领', // 分享描述
              link: `${window.location.origin}/h5/newcomer`, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
              imgUrl: `${window.location.origin}/h5-gift-share.png`, // 分享图标
              success: function(obj) {
                //设置成功
                console.log(obj);
              },
              fail: function(obj) {
                console.log(obj);
              },
            });
          });
        })
        .catch(err => {});
    },
  },
};
</script>
<style lang="scss">
.newcomer {
  .code-form-item {
    .el-input-group__append {
      padding: 0 10px;
    }
  }
  .el-input-group__append {
    background-color: #fff;
  }
  .el-input-group--append {
    background-color: #fff;
    .el-input__inner {
      border-right: none;
    }
  }
  .submit {
    width: 100%;
    color: #fff;
    font-size: 18px;
    background: #ff6432;
    background-image: linear-gradient(-180deg, #ff8b32 0%, #ef4436 100%);
    border: 1px solid #979797;
    border-radius: 45px;
  }
  .el-dialog__body {
    padding-top: 5px;
    padding-bottom: 20px;
  }
}
</style>
<style scoped lang="scss">
.countDown {
  border: none;
  background-color: #fff;
  font-size: 12px;
}
.newcomer {
  .container {
    overflow-y: auto;
    /*-webkit-overflow-scrolling: touch;*/
  }
  .img-wrapper {
    width: 100%;
    img {
      width: 100%;
    }
  }
  .ruleForm {
    width: 76vw;
    margin: 22px auto;
  }
  .form-pictureVerify {
    width: 60px;
  }
  .logo {
    width: 19vw;
    margin: 25px auto 35px;
    img {
      width: 100%;
    }
  }
  .para {
    font-size: 10px;
    color: #9b9b9b;
    text-align: center;
  }
  .dialog {
    text-align: center;
    img {
      width: 31px;
      height: 31px;
    }
    h3 {
      margin-top: 15px;
      font-size: 18px;
      color: #333;
      font-weight: 500;
    }
    p {
      margin-top: 5px;
      font-size: 14px;
      color: #151515;
    }
  }
}
</style>
