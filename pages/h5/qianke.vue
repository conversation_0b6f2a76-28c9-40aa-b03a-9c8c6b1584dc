<template>
  <div class="qianke">
    <div class="container">
      <div class="img-wrapper">
        <img src="./img/qian.jpg">
      </div>
      <div class="button-wrap btn1">
        <el-button @click="visible = true">成为签客</el-button>
      </div>
      <div class="button-wrap btn2">
        <el-button @click="visible = true">成为签客</el-button>
      </div>
    </div>
    <!--<div class="tips">-->
      <!--<div class="tip green">-->
        <!--<img src="./img/icon-green.png" alt="">-->
        <!--<span>填写好友线索，好友在和上上签成交之后，推荐人可得相应奖励</span>-->
      <!--</div>-->
      <!--<div class="tip">-->
        <!--<img src="./img/icon-red.png" alt="">-->
        <!--<span>单笔成单金额达2万元，推荐人奖励为500元京东卡；<br><span style="padding-left: 15px">单笔成单金额达到4万元，推荐人的奖励1000元京东卡；</span> <br> <span style="padding-left: 15px">单笔成单金额达10万元，推荐人的奖励3000元京东卡；</span></span>-->
      <!--</div>-->
      <!--<div class="tip green">-->
        <!--<img src="./img/icon-green.png" alt="">-->
        <!--<span>成为合伙人后，添加工作人员微信，可实时了解推荐进度；</span>-->
      <!--</div>-->
      <!--<div class="tip">-->
        <!--<img src="./img/icon-red.png" alt="">-->
        <!--<span>活动不适用于被推荐者非新用户、推荐自己等情况，此等情况视为 无效推荐；</span>-->
      <!--</div>-->
      <!--<div class="tip green">-->
        <!--<img src="./img/icon-green.png" alt="">-->
        <!--<span>推荐成功的线索在90天内成单则视为成功推荐，逾期无效；</span>-->
      <!--</div>-->
      <!--<div class="tip">-->
        <!--<img src="./img/icon-red.png" alt="">-->
        <!--<span>活动截止时间后推荐的线索视为无效推荐，但是在活动期间提交的 线索，活动结束后90天内成单依然享受奖励；</span>-->
      <!--</div>-->
      <!--<div class="tip green">-->
        <!--<img src="./img/icon-green.png" alt="">-->
        <!--<span>活动时间：即日起至2019年12月31日截止；</span>-->
      <!--</div>-->
      <!--<div class="tip">-->
        <!--<img src="./img/icon-red.png" alt="">-->
        <!--<span>本活动的解释权归上上签所有；</span>-->
      <!--</div>-->
    <!--</div>-->
    <!--<div class="qrcode">-->
      <!--<img src="./img/qrcode.png" alt="">-->
    <!--</div>-->
    <!--<p style="text-align: center">更多问题解答和攻略，添加上上签小助手哦！</p>-->
    <el-dialog
      :visible.sync="visible"
      width="90%"
      class="dialog"
    >
      <el-form v-if="showForm" :model="ruleForm" :rules="rules" ref="ruleForm" class="ruleForm">
        <h3>填写相关资料</h3>
        <el-form-item prop="customerName">
          <el-input v-model.trim="ruleForm.customerName" size="small" placeholder="你的姓名"></el-input>
        </el-form-item>
        <el-form-item prop="contact">
          <el-input v-model.trim="ruleForm.contact" size="small" placeholder="你的手机号"></el-input>
        </el-form-item>
        <el-form-item prop="verifyCode">
          <el-input v-model.trim="ruleForm.verifyCode" size="small" placeholder="输入验证码">
            <template slot="append">
              <count-down class="countDown code-sent" :clickedFn="send" :disabled="countDownDisabled" ref="btn" :second="60"></count-down>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="showPictureVerCon" label="" class="pictureVer-item" prop="imageCode">
          <el-input
            size="small"
            class="pictureVer"
            placeholder="请填写4位验证码"
            :maxlength="4"
            v-model.trim="ruleForm.imageCode"
          >
            <template slot="append">
              <PictureVerify
                class="form-pictureVerify"
                ref="pictureVerify"
                :imageKey="ruleForm.imageKey"
                @change-imageKey="changeImageKey"
              />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="companyName">
          <el-input v-model.trim="ruleForm.companyName" size="small" placeholder="你推荐的用户姓名"></el-input>
        </el-form-item>
        <el-form-item prop="addInfo">
          <el-input v-model.trim="ruleForm.addInfo" size="small" placeholder="你推荐的用户手机"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" class="submit" @click="toRegister">提交</el-button>
        </el-form-item>
      </el-form>
      <div v-else class="success-content">
        <img src="./img/icon-2.png">
        <h3>提交成功</h3>
        <p>我们将联系你，进行发放</p>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import resRules from '@/assets/utils/regs.js';
import CountDown from '@/components/CountDown.vue';
import PictureVerify from '@/components/PictureVerify.vue';
import { isPhoneOrMail } from '@/assets/utils/reg.js';
import aes from '@/assets/utils/aes.js';

export default {
  name: 'qianke',
  layout: 'blank',
  components: {
    CountDown,
    PictureVerify,
  },
  head() {
    return {
      title: '上上签 签客计划',
    };
  },
  data() {
    return {
      showPictureVerCon: false,
      countDownDisabled: false,
      codeDisabled: true,
      visible: false,
      showForm: true,
      ruleForm: {
        customerName: '',
        addInfo: '',
        companyName: '',
        contact: '',
        imageCode: '',
        verifyCode: '',
        verifyKey: '',
        imageKey: '',
      },
      rules: {
        customerName: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          {
            min: 2,
            message: '姓名不能少于两个字符',
            trigger: 'blur',
          },
        ],
        contact: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur',
          },
          {
            pattern: resRules.userPhone,
            message: '手机号格式错误',
            trigger: 'blur',
          },
        ],
        verifyCode: [
          {
            required: true,
            message: '请输入验证码',
            trigger: 'blur',
          },
        ],
        imageCode: [
          {
            required: true,
            message: '请输入图形验证码',
            trigger: 'blur',
          },
        ],
        companyName: [
          {
            required: true,
            message: '请输入公司名称',
            trigger: 'blur',
          },
          {
            min: 2,
            message: '公司名称不能少于两个字符',
            trigger: 'blur',
          },
        ],
        addInfo: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur',
          },
          {
            pattern: resRules.userPhone,
            message: '手机号格式错误',
            trigger: 'blur',
          },
        ],
        code: [
          {
            required: true,
            message: '请输入验证码',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  mounted() {
    if (process.client) {
      this.initWxData();
    }
  },
  methods: {
    // 更改图形验证码
    changeImageKey(value) {
      this.codeDisabled = false;
      this.ruleForm.imageKey = value;
    },
    // 发送验证码
    send() {
      const { contact, imageCode, imageKey } = this.ruleForm;
      this.$refs['ruleForm'].validateField(['contact'], error => {
        if (error) {
          return false;
        }
        let headersObj = {};
        if (imageCode !== '' && imageKey !== '') {
          headersObj = {
            // 'Content-Type': 'application/json; charset=utf-8',
            additionalImgVerCode: JSON.stringify({
              imageCode,
              imageKey,
            }),
            'request-source': 'WEB',
          };
        }
        // let host = 'https://ent.bestsign.info';
        // if (process.env.baseUrl.indexOf('cn') > -1) {
        //   host = 'https://ent.bestsign.cn';
        // }
        let host = '';
        if (process.env.NODE_ENV !== 'development') {
          host =
            process.env.baseUrl.indexOf('cn') > -1
              ? 'https://ent.bestsign.cn'
              : 'https://ent.bestsign.info';
        }
        this.$axios({
          url: `${host}/users/ignore/captcha/notice?encryptToken=${aes(
            'B001',
            contact
          )}`,
          method: 'post',
          headers: headersObj,
          data: {
            code: 'B001',
            sendType: isPhoneOrMail(contact) === 'phone' ? 'S' : 'E',
            target: contact,
            imageCode,
            imageKey,
          },
        })
          .then(res => {
            if (res) {
              this.$MessageToast.success('发送成功！');
              this.countDownDisabled = true;
              setTimeout(this.sended, 0);
              this.ruleForm.verifyKey = res.value;
              this.codeDisabled = false;
            }
          })
          .catch(err => {
            const res = err.response.data;
            if (res.code == '902' || res.code == '100006') {
              if (this.showPictureVerCon) {
                setTimeout(() => {
                  this.$refs.pictureVerify.changeImg();
                }, 20);
              } else {
                this.showPictureVerCon = true;
              }
              if (res.message !== '图片验证码不能为空') {
                this.$MessageToast.error(res.message);
              } else {
                this.$MessageToast.error('请先填写图形验证码');
              }
            } else {
              this.$MessageToast.error(res.message);
            }
            this.$refs.btn.reset();
          });
      });
    },

    sended() {
      this.$refs.btn.run();
      this.countDownDisabled = false;
    },

    // 申请试用
    toRegister() {
      const {
        companyName,
        verifyKey,
        verifyCode,
        contact,
        customerName,
        addInfo,
      } = this.ruleForm;
      // 百度统计当用户点击“申请免费试用”成功提交信息之后，需要将当前页面URL地址，客户地区记录
      const url = location.href;
      window._hmt &&
        window._hmt.push(['_trackEvent', 'register_url', 'submit', url]);

      const params = {
        contact,
        companyName,
        verifyCode,
        verifyKey,
        customerName,
        addInfo,
        applyUrl: url,
        type: 2,
      };
      this.$refs['ruleForm'].validate(valid => {
        if (!valid) {
          return false;
        }
        this.$axios
          .post('/www/api/web/tryout/register', {
            ...params,
          })
          .then(res => {
            if (res && res.value === 'OK') {
              this.showForm = false;
              // const isPre = window.location.hostname.indexOf('info') > -1;
              // window.location.href = isPre
              //   ? 'https://demo.bestsign.info/pages/index.html?v=20180925'
              //   : 'https://demo.bestsign.cn/pages/index.html?v=20180925';
            }
          })
          .catch(err => {
            const res = err.response.data;
            if (res.code === '903') {
              this.$MessageToast.error('提交失败请稍后再试');
            } else {
              this.$MessageToast.error(res.message);
            }
          });
      });
    },
    initWxData() {
      const wx = require('weixin-js-sdk');
      console.log(wx);
      let apiHost =
        location.host.indexOf('info') > 0
          ? 'https://demo.bestsign.info'
          : 'https://demo.bestsign.cn';
      this.$axios
        .post(
          apiHost + '/demo/weixin/jsticket',
          {
            url: location.href.split('#')[0],
          },
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          }
        )
        .then(res => {
          let { data } = res;
          wx.config({
            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId: data.appId, // 必填，公众号的唯一标识
            timestamp: data.timestamp, // 必填，生成签名的时间戳
            nonceStr: data.nonceStr, // 必填，生成签名的随机串
            signature: data.signature, // 必填，签名
            jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'], // 必填，需要使用的JS接口列表
          });
          wx.error(function(res) {
            // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
            console.log(res);
          });
          /*自定义“分享给朋友”及“分享到QQ”按钮的分享内容*/
          wx.ready(function() {
            //需在用户可能点击分按钮前就先调用
            wx.updateAppMessageShareData({
              title: '推荐好友用上上签，获千元京东卡', // 分享标题
              desc: '签客计划招募中，提供有效线索，即可获得奖励！', // 分享描述
              link: `${window.location.origin}/h5/qianke`, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
              imgUrl: `${window.location.origin}/h5-qian-share.png`, // 分享图标
              success: function(obj) {
                //设置成功
                console.log(obj);
              },
              fail: function(obj) {
                console.log(obj);
              },
            });
            /*自定义“分享到朋友圈”及“分享到QQ空间”按钮的分享内容*/
            wx.updateTimelineShareData({
              title: '推荐好友用上上签，获千元京东卡', // 分享标题
              desc: '签客计划招募中，提供有效线索，即可获得奖励！', // 分享描述
              link: `${window.location.origin}/h5/qianke`, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
              imgUrl: `${window.location.origin}/h5-qian-share.png`, // 分享图标
              success: function(obj) {
                //设置成功
                console.log(obj);
              },
              fail: function(obj) {
                console.log(obj);
              },
            });
          });
        })
        .catch(err => {});
    },
  },
};
</script>
<style lang="scss">
.qianke {
  .el-form-item {
    margin-bottom: 10px;
  }
  .el-input-group__append {
    background-color: #f1f9f6;
    padding-left: 12px;
    padding-right: 12px;
  }
  .submit {
    width: 100%;
    color: #fff;
    font-size: 18px;
    background: #00aa64;
    border-radius: 4px;
    border: none;
  }
  .el-dialog__body {
    padding-top: 5px;
    padding-bottom: 20px;
  }
}
</style>
<style scoped lang="scss">
.countDown {
  border: none;
  background-color: #f1f9f6;
  font-size: 12px;
  color: #00aa64;
}
.qianke {
  *,
  *:before,
  *:after {
    box-sizing: border-box;
  }
  /*padding-bottom: 60px;*/
  .container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    /*-webkit-overflow-scrolling: touch;*/
  }
  .img-wrapper {
    width: 100%;
    img {
      width: 100%;
    }
  }
  .form-pictureVerify {
    width: 60px;
  }
  .button-wrap {
    position: absolute;
    left: 50%;
    transform: translate3d(-50%, 0, 0);
    opacity: 0;
    &.btn1 {
      top: 14.2%;
    }
    &.btn2 {
      top: 80%;
    }
    .el-button {
      width: 170px;
      height: 44px;
      /*border: none;*/
      /*color: #fff;*/
      /*background-color: transparent;*/
      /*background-image: url(./img/btn.png);*/
      /*background-size: 125px 35px;*/
    }
  }
  .tips {
    width: 100%;
    padding: 20px 10px;
    border-radius: 10px;
    font-size: 12px;
    .tip {
      padding: 4px 0;
      &.green {
        background-color: #f9fffe;
      }
    }
    img {
      width: 10px;
      vertical-align: baseline;
    }
  }
  .qrcode {
    width: 105px;
    height: 105px;
    margin: 20px auto 10px;
    border: 3px solid #09ac9e;
    padding: 10px;
    border-radius: 24px;
    img {
      width: 80px;
      height: 80px;
    }
  }
  .ruleForm {
    width: 70vw;
    margin: 0 auto;
    h3 {
      font-size: 18px;
      margin-bottom: 24px;
    }
  }
  .logo {
    width: 19vw;
    margin: 25px auto 35px;
    img {
      width: 100%;
    }
  }
  .para {
    font-size: 10px;
    color: #9b9b9b;
    text-align: center;
  }
  .dialog {
    text-align: center;
    img {
      width: 31px;
      height: 31px;
    }
    h3 {
      margin-top: 15px;
      font-size: 18px;
      color: #333;
      font-weight: 500;
    }
    p {
      margin-top: 5px;
      font-size: 14px;
      color: #151515;
    }
  }
}
</style>
