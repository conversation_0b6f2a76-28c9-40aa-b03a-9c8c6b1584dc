<template>
  <article>
	<!-- <nav class="nav-bar">
		<div class="abstract industry">
			<nuxt-link to="/wiki">
				<span class="active all-case">全部</span>
			</nuxt-link>
			<ul class="">
				<template v-for="item in categoryList">
					<nuxt-link :key="item.code" :to="`/wiki/list-${item.code}`">
					<li>{{ item.codeValue }}</li>
					</nuxt-link>
				</template>
			</ul>
		</div>
	</nav> -->
  <bread-nav :menu="menu"></bread-nav>
    <section class="section section-1">
      <div class="container">
        <div class="article">
			 <h1>{{ article.wikiTitle }}</h1>
			<div class="desc">
            <span style="margin-right:20px;">
            <!-- <strong>来源：</strong> -->
            来源：
            上上签
          </span>
          <span>
              <!-- <strong>发布时间：</strong> -->
              发布时间：
              {{ formatDate(releaseTime) }}
            </span>
            <!-- SEO(源码里面要时分秒，页面不要) -->
            <span style="display:none">
              发布时间：
              {{ releaseTime }}
            </span>
          </div>
          	<div  id="content"  class="braft-output-content" v-html="article.content"> </div>
			<nav class="article-change">
				<div class="nav-item">
				<nuxt-link
					v-if="article.prevId"
					:to="`/wiki/detail/${article.prevId}`"
				>上一篇：{{ article.prevTitle }}</nuxt-link>
				</div>
				<div class="nav-item">
				<nuxt-link
					v-if="article.nextId"
					:to="`/wiki/detail/${article.nextId}`"
				>下一篇：{{ article.nextTitle }}</nuxt-link>
				</div>
			</nav>
			<nav v-if="totalNum">
				<h2 class="main-title"> 相关推荐</h2>
				<el-button type="text" class="change-card" @click="getNewCard">换一换</el-button>
				<!-- <i class="iconfont icon-xiangyoujiantou"></i> -->
				<el-row>
					<template v-for="item in shownList">
						<el-col
						:key="item.id"
						:sm="8"
						:md="8"
						:lg="8"
						>
						 <nuxt-link
                  :to="`/wiki/detail/${item.id}`"
                  class="card"
                  @click="handleView(item.id)">
                  <img
                  :key="item.imgUrl"
                    v-lazy="formatUrl(item.imgUrl)"
                    class="card-image"
                    alt="上上签电子签约百科为你提供关于电子合同、使用介绍、解释等相关知识。了解未来发展趋势，以及如何在各个领域安全使用">
                  <div class="card-content">
                    <div class="body text-justify">{{ item.wikiTitle }}</div>
                    <p class="footer">
                      <span style="float: left">{{ categoryName(item.wikiType) }}</span>
                      <span class="time">{{ formatTime(item.releaseTime) }}</span>
                    </p>
                  </div>
                </nuxt-link>
						</el-col>
					</template>
					</el-row>
			</nav>
        </div>
        <aside class="article-side" v-if="!isLandMobile">
			<div class="icon-box">
					<i class="iconfont icon-weixin">
					<img
					:src="weixinLink"
					alt="二维码">
				</i>
				<a :href="shareUrl" target="_blank"><i class="iconfont icon-weibo"></i></a>
				
				<i class="iconfont icon-fanhui" @click.stop.prevent="handlePage()"></i>
				
			</div>
        </aside>
      </div>
    </section>
  </article>
</template>


<script>
import moment from 'moment';
import find from 'lodash/find';
import QRCode from 'qrcode';
export default {
  name: 'CollegeWikiId',
  layout: 'default',
  head() {
    return {
      title: this.tdkT || '上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keyword',
          content: this.tdkK || '电子合同,电子合同签署,电子签名,电子签章',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            this.tdkD ||
            '上上签是业内领先的在线电子合同签署，电子签名，电子签章的云服务平台，提供多种行业专属解决方案。',
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: `https://www.bestsign.cn/wiki/detail/${this.article.id}`,
        },
      ],
    };
  },
  async asyncData({ route, app, redirect }) {
    const id = route.params.id;
    // const current = route.params.hasOwnProperty('page')
    //   ? parseInt(params.page.replace('-p', ''))
    //   : 2;
    const res = await app.$axios.get(`www/api/web/getWiki/${id}`);
    const res0 = await app.$axios.get('/www/api/web/category/wiki');
    const res1 = await app.$axios.get(
      `www/api/web/getWiki?wikiType=${res.wikiType}&pageNum=1&pageSize=3&id=${
        route.params.id
      }`
    );
    if (res.length === 0) {
      redirect('/404');
    }
    return {
      article: res,
      // releaseTime: moment(res.releaseTime).format('YYYY-MM-DD'),
      releaseTime: res.releaseTime,
      shownListSeo: res1.data,
      categoryList: res0,
      tdkT: res.tdkT,
      tdkD: res.tdkD,
      tdkK: res.tdkK,
      totalPageNum: res1.totalPageNum,
      totalNum: res1.totalNum,
      menu: [
        {
          name: '合同百科',
          to: '/wiki',
        },
        {
          name: res0.find(o => o.code === res.wikiType).codeValue,
          to: `/wiki/list-${res.wikiType}`,
        },
        {
          name: '百科详情',
          to: '',
        },
      ],
    };
  },
  data() {
    return {
      article: {},
      // menu: [
      //   {
      //     name: '电子合同百科',
      //     to: '/wiki',
      //   },
      //   {
      //     name: '资讯详情',
      //     to: '',
      //   },
      // ],
      page: 0,
      shareUrl: '',
      link: '',
      weixinLink: '',
      shownList: [],
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },

    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  mounted() {
    this.shareUrl =
      'http://service.weibo.com/share/share.php?url=' +
      window.location.href +
      '&sharesource=weibo&title=' +
      this.article.caseName;
    this.link = window.location.href;
    this.qrcode();
    this.getNewCard();
  },
  methods: {
    qrcode() {
      let that = this;
      QRCode.toDataURL(this.link, function(err, base64) {
        that.weixinLink = base64;
      });
    },
    handlePage(id) {
      this.$router.push(`/wiki${id ? `/${id}` : ''}`);
    },
    formatUrl(url) {
      const arr = url && url.split('?');
      return url && arr[0];
    },
    formatTime(time) {
      return moment(time).format('YYYY-MM-DD');
    },
    categoryName(id) {
      const item = this.categoryList.find(o => o.code === id);
      return item && item.codeValue;
    },
    formatDate(date) {
      // let time = new Date(date.replace(new RegExp(/-/gm), '/'));
      // return (
      //   time.getFullYear() +
      //   '-' +
      //   ('0' + (time.getMonth() + 1)).substr(-2) +
      //   '-' +
      //   time.getDate()
      // );
      return moment(date).format('YYYY-MM-DD');
    },
    async getNewCard() {
      if (this.page == Math.ceil(this.totalNum / 3)) {
        this.page = 1;
      } else {
        this.page += 1;
      }
      var resAbout = await this.$axios.get(
        `/www/api/web/getWiki?wikiType=${this.article.wikiType}&pageNum=${
          this.page
        }&pageSize=3&id=${this.$route.params.id}`
      );
      this.shownList = resAbout.data;
    },
  },
};
</script>

<style scoped lang="scss">
.abstract {
  display: flex;
  justify-content: center;
  font-size: 16px;
  margin-bottom: 30px;
  margin: 0 auto;
  max-width: 1200px;
  ul {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
  }
  li {
    cursor: pointer;
    padding: 20px 30px;
    color: #333;
    &:hover {
      color: #9a9999;
    }
    &.active {
      color: #eee;
    }
  }
  li:last-child {
    padding-right: 0;
  }
}
.all-case {
  padding: 20px 30px;
  color: #333;
  display: flex;
  align-items: center;
  &:hover {
    color: #9a9999;
  }
}
.section-1 {
  background-color: #fafbfc;
  padding: 0;
  .main-title {
    font-weight: 400;
  }
  a {
    color: #323232;
    font-size: 16px;
    &:hover {
      color: #00aa64;
    }
  }
  .container {
    display: flex;
    .article {
      flex: 1;
      padding: 30px 0 80px 40px;
      border-radius: 5px;
      .desc {
        strong {
          font-weight: 500;
        }
      }
    }
    #content {
      padding-bottom: 45px;
      border-bottom: 1px solid #e5e5e5;
      img {
        width: 100% !important;
      }
    }
    aside {
      .icon-box {
        width: auto;
        margin-left: 16px;
        display: flex;
        flex-direction: column;
        text-align: center;
        position: fixed;
        .iconfont {
          font-size: 20px;
          cursor: pointer;
          color: #bfbfbf;
          margin: 5px 0;
          &:hover {
            color: #00a664;
          }
        }
        .icon-weixin {
          position: relative;
          img {
            position: absolute;
            width: 100px;
            right: 40px;
            top: -20px;
            display: none;
          }
          &:hover img {
            display: block;
          }
        }
      }

      .item-wrap {
        background-color: #fff;
        border: 1px solid #e5e5e5;
        border-radius: 5px;
        padding: 24px 18px;
        // margin-bottom: 15px;
        .img-wrap {
          width: 100%;
          text-align: center;
          img {
            width: 100%;
          }
        }
        h4 {
          margin: 36px auto;
          font-size: 18px;
          padding-bottom: 15px;
          border-bottom: 1px solid #e5e5e5;
        }
        .item {
          margin-bottom: 25px;
          cursor: pointer;
          color: #888;
          &:hover {
            color: #00a664;
          }
        }
      }
      .qrcode {
        min-width: 200px;
        margin-top: 20px;
        display: flex;
        justify-content: space-around;
        img {
          width: 80px;
          height: 80px;
        }
        p {
          // margin-top: 0.25rem;
          font-size: 12px;
          // margin-left: 0.3rem;
        }
      }
    }
    h1 {
      font-size: 30px;
      margin-bottom: 30px;
      width: 80%;
      line-height: 1.4;
      color: #090909;
      font-weight: 400;
    }
    .desc {
      text-align: right;
      font-size: 14px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e5e5e5;
      margin-bottom: 45px;
      color: #86868b;
    }
  }
  .article-change {
    text-align: left;
  }
  nav {
    margin-top: 25px;
    position: relative;
    text-align: center;
    // .card {
    //   height: 24rem;
    // }
    .card-content {
      padding: 24px 20px;
      height: 135px;
      p {
        text-align: left;
      }
      .footer {
        align-self: flex-end;
      }
    }
    .el-row {
      display: flex;
      // justify-content: space-around;
      flex-wrap: wrap;
      max-width: 70.75rem;
      margin: 0 auto;
    }
    .nav-item {
      cursor: pointer;
      padding: 10px 0;
      &:hover {
        color: #00a664;
      }
    }
    .change-card {
      color: #00a664;
    }
    .back {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 10px;
      .el-icon-arrow-left {
        padding-right: 5px;
        font-size: 22px;
        position: relative;
        top: 2px;
      }
      &:hover {
        color: #00a664;
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .section-1 {
    padding: 0 0 60px;
    .container {
      .article {
        padding: 30px 0px 80px 0;
      }
      .desc {
        text-align: left;
        color: #86868b;
        strong {
          font-weight: 500;
        }
      }
      h1 {
        font-size: 24px;
        color: #1d1d1f;
        font-weight: 400;
        margin-bottom: 30px;
        line-height: 35px;
        width: 100%;
      }
      h3 {
        font-size: 24px;
        margin-bottom: 40px;
      }
      nav {
        .back {
          cursor: pointer;
          left: 0;
          bottom: -35px;
          top: auto;
        }
      }
      .article-side {
        display: none;
      }
    }
    .nav-item {
      a {
        font-size: 14px;
        line-height: 24px;
      }
    }
    img {
      max-width: 100%;
    }
  }
}
</style>
