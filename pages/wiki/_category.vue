<template>
  <article :class="{'wiki__mobile': isMobile}">
    <section class="section section-1 is-tiny">
      <div class="container">
        <h1 class="article-headline">电子合同百科</h1>
        <!-- <p class="article-subtitle">注册成为上上签官方微信会员，快速跻身电子签名专家。</p> -->
        <!-- <div class="filter"> -->
          <!-- <a class="item" :class="{'is-active': filter === '0'}" href="/wiki" @click.stop="handleFilter(0)">全部</a>
          <a class="item" :class="{'is-active': filter === '1'}" href= "/wiki/list-1" @click.stop="handleFilter(1)">电子签名</a>
          <a class="item" :class="{'is-active': filter === '2'}" href= "/wiki/list-2" @click.stop="handleFilter(2)">电子合同</a>
          <a class="item" :class="{'is-active': filter === '3'}" href= "/wiki/list-3" @click.stop="handleFilter(3)">硬核问答</a> -->
        <!-- </div> -->
		  	<nav :class="['nav-bar',fixedTab?'fixed-bar':'']" ref="childNav">
        <ul class="abstract">
          <nuxt-link to="/wiki">
            <li>全部</li>
          </nuxt-link>
          <template v-for="item in categoryList">
            <nuxt-link :key="item.code" :to="`/wiki/list-${item.code}`">
              <li :class="{'active': wikiType == item.code}">{{ item.codeValue }}</li>
            </nuxt-link>
          </template>
        </ul>
			</nav>
        <div class="card-wrapper">
          <el-row>
            <template v-for="(item) in shownList">
              <el-col
                :key="item.id"
                :xs="24"
                :md="8"
                :lg="8"
                :sm="8"
              >
                <nuxt-link
                  :to="`/wiki/detail/${item.id}`"
                  class="card"
                  @click="handleView(item.id)">
                  <img
                  :key="item.imgUrl"
                    v-lazy="formatUrl(item.imgUrl)"
                    class="card-image"
                    alt="上上签电子签约百科为你提供关于电子合同、使用介绍、解释等相关知识。了解未来发展趋势，以及如何在各个领域安全使用">
                  <div class="card-content">
                    <div class="body text-justify">{{ item.wikiTitle }}</div>
                    <p class="footer">
                      <span style="float: left">{{ categoryName(item.wikiType) }}</span>
                      <span class="time">{{ releaseTime(item.releaseTime) }}</span>
                    </p>
                  </div>
                </nuxt-link>
              </el-col>
            </template>
          </el-row>
        </div>
        <div class="button-wrap">
          <el-pagination
		  background
            :page-size="12"
            :current-page="current"
            :total="count"
            @current-change="handleCurrentChange"
            :pager-count="5"
            layout="prev, pager, next"></el-pagination>
        </div>
        <!-- seo优化，由于分页是客户端渲染，源码里无法拿到路由 -->
        <div class="111" style="display:none" v-for="item in pageNums" :key="item">
          <a v-if="item === 1" :href="$route.path.split('-p')[0]">{{item}}</a>
          <a v-else :href="`${$route.path.split('-p')[0]}-p${item}`">{{item}}</a>
        </div>
      </div>
    </section>
  </article>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import moment from 'moment';
import findKey from 'lodash/findKey';
import Throttle from '@/assets/utils/throttle';
export default {
  name: 'WikiCategory',
  layout: 'default',
  head() {
    const tdk = {
      sign: {
        title:
          this.current > 1
            ? `电子签名百科_介绍_知识_攻略_电子合同百科（第${
                this.current
              }页）-上上签电子签约云平台`
            : '电子签名百科_介绍_知识_攻略_电子合同百科-上上签电子签约云平台',
        meta: [
          {
            hid: 'keywords',
            name: 'keywords',
            content:
              '电子签名攻略、电子签名知识、电子签名百科、上上签电子签约云平台',
          },
          {
            hid: 'description',
            name: 'description',
            content:
              '上上签电子签约云平台的电子合同百科板块汇聚了电子签名的相关介绍，攻略大全，相关知识，为您全方位且详细的解读有关电子签名的知识。',
          },
        ],
        link: [
          {
            rel: 'canonical',
            href: `https://www.bestsign.cn/wiki/list-${this.wikiType}`,
          },
        ],
      },
      contract: {
        title:
          this.current > 1
            ? `电子合同百科_介绍_知识_攻略_电子合同百科（第${
                this.current
              }页）-上上签电子签约云平台`
            : '电子合同百科_介绍_知识_攻略_电子合同百科-上上签电子签约云平台',
        meta: [
          {
            hid: 'keywords',
            name: 'keywords',
            content:
              '电子合同攻略、电子合同知识、电子合同百科、上上签电子签约云平台',
          },
          {
            hid: 'description',
            name: 'description',
            content:
              '上上签电子签约云平台的电子合同百科板块汇聚了电子合同的相关介绍，攻略大全，相关知识，为您全方位且详细的解读有关电子合同的知识。',
          },
        ],
        link: [
          {
            rel: 'canonical',
            href: `https://www.bestsign.cn/wiki/list-${this.wikiType}`,
          },
        ],
      },
      QA: {
        title:
          this.current > 1
            ? `电子签约问答_解读-电子合同百科-上上签电子签约云平台（第${
                this.current
              }页）-上上签电子签约云平台`
            : '电子签约问答_解读-电子合同百科-上上签电子签约云平台-上上签电子签约云平台',
        meta: [
          {
            hid: 'keywords',
            name: 'keywords',
            content:
              '电子签约攻略、电子合同知识、电子合同问答、上上签电子签约云平台',
          },
          {
            hid: 'description',
            name: 'description',
            content:
              '上上签电子签约云平台的电子签约硬核问答板块汇聚了电子签名的相关问题，专家解读，，为您全方位且详细的回答有关电子签约和电子合同的问题。',
          },
        ],
        link: [
          {
            rel: 'canonical',
            href: `https://www.bestsign.cn/wiki/list-${this.wikiType}`,
          },
        ],
      },
      signature: {
        title:
          '电子签章办理_电子签章申请_电子签章系统介绍-上上签电子签约云平台',
        meta: [
          {
            hid: 'keywords',
            name: 'keywords',
            content: '电子签章,电子签约,电子签名,电子合同,上上签电子签约云平台',
          },
          {
            hid: 'description',
            name: 'description',
            content:
              '上上签电子签约云平台的电子合同百科板块汇聚了电子签章的相关介绍，攻略大全，相关知识，为你全方位且详细的解读有关电子签章如何办理，电子签章如何申请、各平台电子签章系统介绍的相关知识，为你在选择第三方电子签章系统时提供强有力的支持。',
          },
        ],
        link: [
          {
            rel: 'canonical',
            href: `https://www.bestsign.cn/wiki/list-${this.wikiType}`,
          },
        ],
      },
      seal: {
        title:
          '电子印章法律效力_电子印章使用_电子印章怎么申请-上上签电子签约云平台',
        meta: [
          {
            hid: 'keywords',
            name: 'keywords',
            content: '电子印章,电子签约,电子签名,电子合同,上上签电子签约云平台',
          },
          {
            hid: 'description',
            name: 'description',
            content:
              '上上签电子签约云平台的电子合同百科板块汇聚了电子印章的相关介绍，攻略大全，相关知识，为你全方位且详细的解读有关电子印章的法律效力、电子印章如何使用以及电子印章怎么申请的的相关知识，为你在选择第三方电子印章系统时提供强有力的支持。',
          },
        ],
        link: [
          {
            rel: 'canonical',
            href: `https://www.bestsign.cn/wiki/list-${this.wikiType}`,
          },
        ],
      },
    };
    return tdk[this.wikiType];
  },
  data() {
    return {
      tabsHeight: '',
      fixedTab: false,
      // category: {
      //   1: 'sign',
      //   2: 'contract',
      //   3: 'Q&A',
      // },
      // category: {
      //   1: 'sign',
      //   2: 'contract',
      //   3: 'Q&A',
      // },
    };
  },
  async asyncData({ app, params }) {
    // const categorys = {
    //   1: 'sign',
    //   2: 'contract',
    //   3: 'Q&A',
    // };
    const { category } = params;
    const keys = category.split('-');
    // const wikiType = findKey(categorys, o => o === keys[1]);
    const wikiType = keys[1];
    // console.log(wikiType);
    const current = parseInt((keys[2] || 'p1').replace('p', ''));
    const res0 = await app.$axios.get('/www/api/web/category/wiki');
    const res = await app.$axios.get(
      // '/www/api/web/college2?status=0&column=wiki&pageNum=1&pageSize=999'
      `www/api/web/getWiki?wikiType=${wikiType}&pageNum=${current}&pageSize=12`
    );
    // const filterResult = res.data.filter(o => o.speaker === keys[1]);
    // const shownList = filterResult.slice((current - 1) * 12, 12 * current);
    const shownList = res.data;
    return {
      // categoryList: res0,
      categoryList: res0.sort((a, b) => a.sortIndex - b.sortIndex),
      current,
      wikiType,
      count: res.totalNum,
      shownList,
      pageNums: res.totalPageNum,
      // categorys,
    };
  },
  computed: {
    ...mapState(['isMobile']),
  },
  mounted() {
    this.$store.commit('changePath', { path: 'help' });
    if (!this.isMobile) {
      this.tabsHeight = this.$refs.childNav.offsetTop;
      window.addEventListener('scroll', Throttle(this.handleScroll, 100));
    }
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    ...mapActions(['setViewTimes']),
    handleView(id) {
      this.setViewTimes({
        id,
        type: '2',
      });
    },
    formatUrl(url) {
      const arr = url && url.split('?');
      return url && arr[0];
    },
    categoryName(id) {
      const item = this.categoryList.find(o => o.code === id);
      return item && item.codeValue;
    },
    releaseTime(time) {
      return moment(time).format('YYYY-MM-DD');
    },
    // handleFilter(index) {
    //   if (index) {
    //     this.$router.push(`/wiki/list-${index}`);
    //   } else {
    //     this.$router.push('/wiki');
    //   }
    // },
    handleCurrentChange(page) {
      const { category } = this.$route.params;
      if (page === 1) {
        this.$router.push(`/wiki/list-${this.wikiType}`);
      } else {
        this.$router.push(`/wiki/list-${this.wikiType}-p${page}`);
      }
    },
    handleScroll() {
      const scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      if (scrollTop > this.tabsHeight) {
        this.fixedTab = true;
      } else {
        this.fixedTab = false;
      }
    },
  },
};
</script>
<style scoped lang="scss">
.abstract {
  max-width: 1440px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  color: #323232;
  font-size: 16px;
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
  li {
    cursor: pointer;
    padding: 20px 18px;
    color: #6e6e6e;
    font-size: 1rem;
    text-align: center;
    &:hover {
      color: #00a664;
    }
    &.active {
      color: #00a664;
    }
  }
  .nuxt-link-exact-active {
    border-bottom: 2px solid #00aa64;
    li {
      color: #00aa64;
    }
  }
}
.fixed-bar {
  position: fixed;
  z-index: 20;
  top: 76px;
  background: #fff;
  box-shadow: 0 2px 20px 0 rgba(0, 39, 123, 0.13);
  width: 100%;
  left: 0;
  padding: 0;
  transition: all 0.2s ease;
  .abstract {
    border-bottom: none;
    margin-bottom: 0;
    margin: 0 auto;
  }
}
.section-1 {
  .container {
    max-width: 70.75rem;
  }
  text-align: center;
  .article-headline {
    font-size: 2.5rem;
    margin: 90px 0 50px;
    text-align: center;
    color: #090909;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
  }
  .article-subtitle {
    margin-bottom: 50px;
  }
  .nav-wrap {
    margin-bottom: 40px;
    .el-col {
      cursor: pointer;
      margin-bottom: 40px;
      font-size: 18px;
      &:hover {
        color: #00a664;
      }
    }
  }
  .image {
    width: 100%;
  }
  .card {
    // height: 305px;
    cursor: pointer;
  }
  .card-content {
    padding: 24px 20px;
    height: 135px;
    p {
      text-align: left;
    }
    .footer {
      align-self: flex-end;
    }
  }
  .button-wrap {
    margin-top: 60px;
    text-align: center;
  }
}
.filter {
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #00a664;
  margin-bottom: 32px;
  .item {
    line-height: 32px;
    font-size: 1rem;
    padding: 0 1.75rem;
    cursor: pointer;
    color: #515151;
    border-bottom: 3px solid transparent;
    &.is-active {
      color: #00a664;
      border-bottom-color: #00a664;
    }
  }
}
.wiki__mobile {
  .section-1 {
    padding: 0px 0px;
    head .article-headline {
      margin: 40px 0 20px;
    }
    .abstract {
      margin-bottom: 0;
    }
    .card {
      .card-content {
        .body {
          line-height: 2.2rem;
          color: #86868b;
        }
      }
    }
    .el-col-lg-8 {
      .card {
        // height: 217px;
        width: 100%;
        // img {
        //     height: 102px;
        // }
      }
    }
  }
  /deep/ .el-pagination.is-background .el-pager li:not(.disabled).active {
    background: #00aa64;
  }
  .fixed-bar {
    position: fixed;
    z-index: 20;
    top: 56px;
    background: #fff;
    box-shadow: 0 2px 20px 0 rgba(0, 39, 123, 0.13);
    width: 100%;
    left: 0;
    padding: 0;
    transition: all 0.2s ease;
    .abstract {
      border-bottom: none;
      margin-bottom: 0;
      margin: 0 auto;
    }
  }
  .article-headline {
    font-size: 2.2rem;
    color: #090909;
    font-weight: 500;
    margin: 40px 0 20px;
  }
  .filter {
    margin-bottom: 5px;
    .item {
      padding: 0 1rem;
    }
  }
}
@media (max-width: 768px) {
  .abstract {
    max-width: 1440px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    color: #323232;
    font-size: 16px;
    margin-bottom: 30px;
    li {
      cursor: pointer;
      padding: 20px 10px;
      color: #323232;
      &:hover {
        color: #00a664;
      }
      &.active {
        color: #00a664;
      }
    }
  }
  .article-headline {
    font-size: 24px;
    color: #515151;
    font-weight: 200;
    margin: 15px 50px;
  }
}
</style>
