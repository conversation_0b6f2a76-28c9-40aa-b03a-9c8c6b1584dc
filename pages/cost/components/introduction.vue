<!-- 版本介绍 -->
<template>
  <article class="product-price">
    <section class="section section-1">
      <div class="container">
        <h2 class="article-headline" v-if="isShow">{{bigTitle}}</h2>
        <h2 class="section-headline" v-if="!isShow">{{bigTitle}}</h2>
        <p class="article-subtitle" v-if="isShow">{{$t('packageType.desc')}}</p>
        <div class="package-type">
          <div v-for="(item,index) in packageType" :key="index" class="package-con">
            <div class="icon-container">
              <i :class="['iconfont ',item.iconfont]"></i>
            </div>
            <div class="type">{{item.title}}</div>
            <div class="iconStar-container">
              <i :class="['iconfont ',item.iconfontStar]"></i>
            </div>
            <div class="desc">{{item.desc}}</div>
            <div class="recharge">{{item.recharge}}</div>
            <!-- <div class="decs">{{$t('reason.recharge')}}</div> -->
            <div class="ssq-button-primary-con">
              <button
                class="ssq-button-primary"
                @click="toDemo()">
                {{$t('header.use')}}
              </button>
            </div>
            <div class="function-con">
              <div class="function" v-for="(i,index1) in item.function" :key="index1"><i class="iconfont icon-dian" v-if="index1"></i> {{i}}</div>
            </div>
          </div>
        </div>
        <div class="bottom-desc">{{$t('packageType.rechargeDesc')}}</div>
        <div class="ssq-button-primary is-white" @click="toMoreFunction" v-if="!isShow">{{$t('packageType.functionDesc')}}<i class="iconfont icon-jiantou"></i></div>
      </div>
    </section> 
  </article>
</template>

<script>
//import x from ''
import Qs from 'qs';
import { jumpToEntRegister } from '@/assets/utils/toRegister';
export default {
  components: {},
  props: {
    isShow: {
      type: Boolean,
      default: true,
    },
    bigTitle: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      packageType: [
        {
          iconfont: 'icon-biaozhunban',
          iconfontStar: 'icon-biaozhunxing',
          title: this.$t('manageSeries.type.1'),
          desc: this.$t('packageType.type1'),
          recharge: this.$t('reason.recharge1'),
          function: [
            this.$t('packageType.feature1'),
            this.$t('manageSeries.type1Feature.1'),
            this.$t('manageSeries.type1Feature.2'),
            this.$t('manageSeries.type1Feature.3'),
            this.$t('manageSeries.type1Feature.4'),
            this.$t('manageSeries.type1Feature.5'),
            this.$t('manageSeries.type1Feature.6'),
            this.$t('manageSeries.type1Feature.7'),
            this.$t('manageSeries.type1Feature.8'),
            this.$t('manageSeries.type1Feature.9'),
            this.$t('manageSeries.type1Feature.10'),
            this.$t('manageSeries.type1Feature.11'),
            this.$t('manageSeries.type1Feature.12'),
            this.$t('manageSeries.type1Feature.15'),
            this.$t('manageSeries.type1Feature.16'),
          ],
        },
        {
          iconfont: 'icon-zhuanyeban',
          iconfontStar: 'icon-zhuanyexing',
          title: this.$t('manageSeries.type.2'),
          desc: this.$t('packageType.type2'),
          recharge: this.$t('reason.recharge2'),
          function: [
            this.$t('packageType.feature2'),
            this.$t('manageSeries.type2Feature.1'),
            this.$t('manageSeries.type2Feature.2'),
            this.$t('manageSeries.type2Feature.3'),
            this.$t('manageSeries.type2Feature.4'),
            this.$t('manageSeries.type2Feature.5'),
            this.$t('manageSeries.type2Feature.6'),
            this.$t('manageSeries.type2Feature.7'),
            this.$t('manageSeries.type2Feature.8'),
            this.$t('manageSeries.type2Feature.9'),
          ],
        },
        {
          iconfont: 'icon-qijianban',
          iconfontStar: 'icon-qijianxing',
          title: this.$t('manageSeries.type.3'),
          desc: this.$t('packageType.type3'),
          recharge: this.$t('reason.recharge3'),
          function: [
            this.$t('packageType.feature3'),

            this.$t('manageSeries.type3Feature.1'),
            this.$t('manageSeries.type3Feature.2'),
            this.$t('manageSeries.type3Feature.3'),
            this.$t('manageSeries.type3Feature.4'),
            this.$t('manageSeries.type3Feature.5'),
            this.$t('manageSeries.type3Feature.6'),
            this.$t('manageSeries.type3Feature.7'),
            this.$t('manageSeries.type3Feature.8'),
            this.$t('manageSeries.type3Feature.9'),
            this.$t('manageSeries.type3Feature.10'),
            this.$t('manageSeries.type3Feature.11'),
          ],
        },
      ],
    };
  },
  computed: {},
  methods: {
    toDemo() {
      jumpToEntRegister();
    },
    toMoreFunction() {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/cost',
        query: {
          ...Qs.parse(query),
        },
      });
    },
  },
  mounted() {},
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>

<style lang='scss' scoped>
//@import url()
.product-price {
  .section-1 {
    background: #f5f5f7;
    padding: 1.5rem 0 5rem;
    .container {
      .article-headline {
        padding: 5.625rem 0 0;
      }
      .package-type {
        display: flex;
        justify-content: space-around;
        .package-con {
          margin: 0 1%;
          width: 34%;
          cursor: pointer;
          background: #fff;
          padding: 50px 30px;
          border-radius: 10px;
          &:hover {
            transform: translate3d(0, -4px, 0);
            box-shadow: 0 8px 15px 0 rgba(82, 94, 102, 0.15);
          }
          .icon-container {
            text-align: center;
            margin-bottom: 2rem;
            .iconfont {
              color: $-color-main;
              font-size: 3rem;
            }
          }
          .iconStar-container {
            text-align: center;
            margin: 10px 0;
            .iconfont {
              color: $-color-main;
              font-size: 0.8rem;
            }
          }
          .type {
            font-size: 1.8rem;
            margin: 0;
            font-weight: 500;
            color: #1d1d1f;
            line-height: 1.5;
            text-align: center;
            height: 6rem;
          }
          .desc {
            font-size: 0.9rem;
            color: #86868b;
            line-height: 1.5;
            margin: 1.5rem 0;
            text-align: center;
            // height: 3.5rem;
          }
          .recharge {
            font-size: 1.8rem;
            text-align: center;
            font-weight: 500;
            color: #000;
          }
          .decs {
            text-align: center;
            font-weight: 500;
            margin: 20px 0;
          }
          .ssq-button-primary-con {
            text-align: center;
            margin: 20px 0;
            .ssq-button-primary {
              width: 100%;
              height: 50px;
              border-radius: 3px;
              font-size: 1.2rem;
            }
          }
          .function-con {
            text-align: left;
            .function {
              line-height: 2;
              display: flex;
              align-items: center;
              color: #4a5a6f;
              &:first-child {
                margin-left: 20px;
              }
              .iconfont {
                color: $-color-main;
                font-weight: 800;
                font-size: 20px;
              }
            }
          }
        }
      }
      .bottom-desc {
        padding: 50px 0;
        text-align: center;
      }
      .ssq-button-primary {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        width: 160px;
        height: 50px;
        box-shadow: 0 0 0 0;
        .iconfont {
          font-size: 12px;
          margin-left: 20px;
        }
      }
      .is-white {
        &:hover {
          background: #fff;
          color: $-color-main;
        }
      }
    }
  }
}
@media (max-width: 768px) {
  .product-price {
    .section-1 {
      padding: 1.5rem 0;
      .container {
        max-width: 100%;
        width: 100%;
        .package-type {
          display: block;
          .package-con {
            margin: 20px 0;
            width: 100%;
            .type {
              height: auto;
            }
            .desc {
              font-size: 1.2rem;
              height: auto;
            }
            .function-con {
              font-size: 1.2rem;
            }
          }
        }
        .bottom-desc {
          line-height: 1.5;
          padding: 20px 0;
        }
      }
    }
  }
}
</style>
