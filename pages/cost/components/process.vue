<!-- 签署流程 -->
<template>
  <article class="process">
    <section class="section section-1" :class="{'section-1-en':isEN}">
      <div class="container">
        <h2 class="section-headline" :class="{'section-headline-en':isEN}">{{$t('packageType.processTitle')}}</h2>
        <div class="process-content">
          <div class="process-img" v-if="!isMobile">
            <img src="https://static.bestsign.cn:443/d393d893657bd4af2a3a225a41c7648bba9c16e5.png" alt="" v-if="!isEN">
            <img src="https://static.bestsign.cn:443/6f808e2b8cd7df77b333ceabbd22ead592865b28.png" alt="" v-if="isEN">
          </div>
          <div class="process-img" v-if="isMobile">
            <img src="https://static.bestsign.cn:443/4f6a1a08fbf1e21544f8640e284224383b225d93.png" alt="" v-if="!isEN">
            <img src="https://static.bestsign.cn:443/d43af84c3868f56c2722bf32e056d87285676559.png" alt="" v-if="isEN">
          </div>
        </div>
      </div>
    </section> 
  </article>
</template>

<script>
//import x from ''
export default {
  components: {},
  data() {
    return {
      isEN: false,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  methods: {},
  mounted() {
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>

<style lang='scss' scoped>
.process {
  .section-1 {
    padding: 1.5rem 0 2rem;
    .container {
      .process-content {
        margin: 0 auto;
        width: 70%;
        .process-img {
          img {
            width: 100%;
          }
        }
      }
    }
  }
  .section-1-en {
    padding: 1.5rem 0 5rem;
  }
}
@media screen and (max-width: 768px) {
  .process {
    .section-1 {
      padding: 1.5rem 0 0;
      .container {
        .process-content {
          width: 100%;
          .process-img {
            img {
            }
          }
        }
      }
    }
    .section-1-en {
      padding: 1.5rem 0 5rem;
    }
  }
}
</style>
