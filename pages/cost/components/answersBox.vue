<!-- 组件说明 -->
<template>
  <div class='autoRecharge-answersBox'>
    <div class="answersBoxTitle section-headline">{{$t('answersBox.title')}}</div>
    <div class="answersBox-content">
      <el-collapse v-model="activeNames" accordion>
        <el-collapse-item :title="$t('answersBox.1')" name="1">
          <div>{{$t('answersBox.2')}}</div>
        </el-collapse-item>
        <el-collapse-item :title="$t('answersBox.3')" name="2">
          <div>{{$tc('answersBox.4',1)}}</div>
          <div>{{$tc('answersBox.4',2)}}</div>
        </el-collapse-item>
        <el-collapse-item :title="$t('answersBox.5')" name="3">
          <div>{{$t('answersBox.6')}}</div>
        </el-collapse-item>
        <el-collapse-item :title="$t('answersBox.7')" name="4">
          <div>{{$t('answersBox.8')}}</div>
        </el-collapse-item>
        <el-collapse-item :title="$t('answersBox.9')" name="5">
          <div>{{$t('answersBox.10')}}</div>
        </el-collapse-item>
        <el-collapse-item :title="$t('answersBox.11')" name="6">
          <div>{{$t('answersBox.12')}}</div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
//import x from ''
export default {
  components: {},
  props: {
    isChongZhi: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeNames: ['1'],
    };
  },
  computed: {},
  methods: {},
};
</script>

<style lang='scss' scoped>
//@import url()
.autoRecharge-answersBox {
  padding: 1.5rem 0;
  .answersBoxTitle {
    text-align: center;
  }
  .answersBox-content {
    margin: 0 20rem 5rem;
  }
  /deep/ .el-collapse-item__header {
    height: 90px;
    font-size: 1.1rem;
  }
  /deep/ .el-collapse-item__content {
    font-size: 0.9rem;
    padding-right: 20rem;
  }
  .el-collapse-item .is-active {
    color: $-color-main;
  }
}
/deep/ .el-collapse-item__content {
  text-align: left;
}
@media screen and (max-width: 767px) {
  .autoRecharge-answersBox {
    padding: 1.5rem 0 0;
    .answersBox-content {
      margin: 0 30px;
      width: auto;
    }
    /deep/ .el-collapse-item__header {
      height: 60px;
      line-height: 24px;
      padding: 10px 0;
    }
  }
  /deep/ .el-collapse-item {
    .el-collapse-item__header {
      align-items: flex-start;
      line-height: 24px;
      text-align: left;
      .el-icon-arrow-right {
        margin-top: 6px;
      }
    }
    .el-collapse-item__content {
      text-align: left;
      padding-right: 0;
    }
  }
}
</style>
