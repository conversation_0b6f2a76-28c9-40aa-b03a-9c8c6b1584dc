<!-- 费用 -->
<template>
  <div class='cost'>
    <Introduction :bigTitle="bigTitle"></Introduction>
    <process></process>
    <answer></answer>
  </div>
</template>

<script>
import Introduction from './components/introduction.vue';
import Process from './components/process.vue';
import Answer from './components/answersBox.vue';
export default {
  components: {
    Introduction,
    Process,
    Answer,
  },
  head() {
    return {
      title: this.$t('costTDK.title'),
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.$t('costTDK.keyword'),
        },
        {
          hid: 'description',
          name: 'description',
          content: this.$t('costTDK.description'),
        },
      ],
    };
  },
  data() {
    return {
      bigTitle: this.$t('packageType.title'),
    };
  },
  computed: {},
  methods: {},
  mounted() {},
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>

<style lang='scss' scoped>
//@import url()
</style>
