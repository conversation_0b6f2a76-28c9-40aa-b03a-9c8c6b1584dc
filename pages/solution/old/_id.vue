<template>
  <article class="solution">
    <section class="section section-1" :style="{backgroundImage: `url(${isMobile ? pages.mobilePictureUrl : pages.pictureUrl})`}">
      <div class="container">
        <div class="section-content">
          <div class="text-wrapper">
            <h1 class="article-headline headline--large">{{ pages.solutionName }}解决方案</h1>
            <p class="article-subtitle">{{ pages.summary }}</p>
          </div>
          <div class="button-wrapper">
            <button class="ssq-button-primary is-white is-no-border" @click="toDemo">
              免费试用
            </button>
            <button v-if="!isMobile && hasVideoBtn" style="margin-left: 3rem" class="ssq-button-primary is-white is-no-border">
              <a
                href="javascript: void (0)"
                @click="handleVideo">观看视频</a>
            </button>
          </div>
        </div>
      </div>
    </section>
    <template v-for="(page, index) in content">
      <v-content :key="`${pages.id}-${index}`" :style="{backgroundColor: index % 2 === 0 ? '#fafbfc' : '#fff'}" :content="page"></v-content>
    </template>
    <law-safe></law-safe>
    <client :usage="usage"></client>
    <evaluate v-if="evaluate" :content="evaluate"></evaluate>
    <v-video
      :visible="dialogVisible"
      :videoUrl="src"
      @close="dialogVisible = false"></v-video>
  </article>
</template>

<script>
import Video from '@/components/Video.vue';
import Client from '@/components/Solution/Client.vue';
import Evaluate from '@/components/Solution/Evaluate.vue';
import LawSafe from '@/components/Solution/LawSafe.vue';
import Content from '@/components/Solution/Content.vue';
import { find, findKey } from 'lodash';

const getJson = () =>
  import('@/static/solution.json').then(m => m.default || m);

export default {
  name: 'solutionDetail',
  components: {
    'v-video': Video,
    Client,
    LawSafe,
    'v-content': Content,
    Evaluate,
  },
  head() {
    return {
      title: (this.seo && this.seo.title) || '上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keyword',
          content:
            (this.seo && this.seo.keywords) ||
            '电子合同,电子合同签署,电子签名,电子签章',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            (this.seo && this.seo.description) ||
            '上上签是业内领先的在线电子合同签署，电子签名，电子签章的云服务平台，提供多种行业专属解决方案。',
        },
      ],
    };
  },
  async asyncData({ redirect, params }) {
    const solutions = {
      28: 'education',
      27: 'supplyChain',
      25: 'B2B',
      21: 'logistics',
      20: 'internet',
      19: 'finance',
      18: 'industry',
      17: 'fintech',
      16: 'rent',
      15: 'HR',
      12: 'insurance',
    };
    const key = findKey(solutions, o => o === params.id);
    if (key === 'undefined') {
      redirect('/404');
      return false;
    }
    const response = await getJson();
    const item = find(response, o => o.id.toString() === key);
    const seo = item.seo;
    const list = item.content.slice(0, item.content.length - 1);
    const usage = item.content[item.content.length - 1];
    const evaluate = find(item.content, o => o.evaluate);

    return {
      pages: item,
      content: list,
      usage,
      seo,
      evaluate,
    };
  },
  data() {
    return {
      dialogVisible: false,
      src: '',
    };
  },
  computed: {
    hasVideoBtn() {
      return this.seo && this.seo.videoUrl;
    },
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: 'solution' });
  },
  methods: {
    formatUrl(url) {
      const arr = url && url.split('?');
      return url && arr[0];
    },
    toDemo() {
      const { params } = this.$route;
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          [params.id]: '',
        },
      });
    },
    handleVideo() {
      this.dialogVisible = true;
      this.src = this.hasVideoBtn;
    },
  },
};
</script>

<style scoped lang="scss">
.solution {
  text-align: center;
  .section-1 {
    height: 28vw;
    padding: 0;
    position: relative;
    color: #fff;
    text-align: center;
    background-size: 100% 105%;
    background-position-y: 85%;
    .img-wrap,
    img {
      width: 100%;
    }
    .container {
      position: relative;
      .ssq-button-primary.is-white {
        border: none;
      }
    }
    .section-content {
      position: absolute;
      top: 4rem;
      text-align: left;
      height: 15rem;
      padding-left: 1.5rem;
    }
    .article-headline {
      margin-bottom: 1rem;
      font-weight: 400;
    }
    .article-subtitle {
      color: #fff;
      width: 40rem;
    }
  }
  .abstract {
    color: #888;
    margin: 28px auto 48px;
    max-width: 700px;
  }
  .section-other {
    .section-headline {
      font-weight: 400;
    }
    .section-subtitle {
      margin: 75px auto 100px;
    }
    .section-img {
      width: 100%;
      img {
        max-width: 100%;
      }
    }
  }
}

@media (max-width: 768px) {
  .solution {
    .section-1 {
      height: 55vh;
      background-size: 100% 125%;
      background-position-y: 115%;
      .img-wrap,
      img {
        width: 100%;
        min-height: 600px;
      }
      .container {
        width: 100%;
        .section-content {
          width: 100%;
          text-align: center;
        }
        .article-headline {
          font-size: 28px;
        }
        .article-subtitle {
          font-size: 12px;
          padding: 0 20px;
          width: 100%;
        }
      }
    }
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .solution {
    .section-1 {
      height: 30vh;
      background-size: 100% 100%;
    }
  }
}
</style>
