<template>
  <article class="solution">
    <banner :banner="banner" />
	 <!-- <nav :class="['nav-bar',fixedTab?'fixed-bar':'']" id="childNav">
      <ul @click="scroll($event)">
        <li data-id="pain" :class="activeTab==1?'active-bar':''">行业痛点</li>
        <li data-id="scene" :class="activeTab==2?'active-bar':''">使用场景</li>
        <li data-id="solution" :class="activeTab==3?'active-bar':''">解决方案</li>
        <li data-id="evaluate" :class="activeTab==4?'active-bar':''">客户证言</li>
        <li data-id="client" :class="activeTab==5?'active-bar':''">客户案例</li>
      </ul>
    </nav> -->
    <description :dsc="dsc"></description>
    <pain id="pain" :pain="pain" :title="title"/>
    <!-- <scene id="scene" :scene="scene" :bgImg="sceneBg"/> -->
    <solution-case id="solution" :solution="solution" />
    <contractType :contractType="type"></contractType>
    <!-- <evaluate  id="evaluate" :evaluate="evaluate" /> -->
    <!-- <client id="client" :usage="usage" /> -->
  </article>
</template>

<script>
import { find, findKey } from 'lodash';
const getJson = () =>
  import('@/static/_jpSolution.json').then(m => m.default || m);

import Banner from '@/components/Solution/Banner';
import Pain from '@/components/Solution/Pain';
import Scene from '@/components/Solution/Scene';
import SolutionCase from '@/components/Solution/SolutionCase';
import Client from '@/components/Solution/Client';
import Evaluate from '@/components/Solution/Evaluate';
import Throttle from '@/assets/utils/throttle';
import Description from '@/components/Solution/description.vue';
import ContractType from '@/components/Solution/contractType.vue';

export default {
  name: 'Solution',
  components: {
    Banner,
    Scene,
    Pain,
    SolutionCase,
    Client,
    Evaluate,
    Description,
    ContractType,
  },
  computed: {
    language() {
      return this.$store.state.locale;
    },
  },
  async asyncData({ redirect, params }) {
    const solutions = {
      42: 'hr',
      40: 'law',
      41: 'houseRent',
    };
    const key = findKey(solutions, o => o === params.id);
    if (key === 'undefined') {
      redirect('/404');
      return false;
    }
    const response = await getJson.call(this);
    const item = find(response, o => o.id.toString() === key);
    let sceneBg = '';
    item.solution.map((item, index) => {
      switch (index) {
        case 0:
          item.imgUrl =
            'https://static.bestsign.cn:443/a1d15a163fa0558453da1cba8f98c9d69371d627.png';
          break;
        case 1:
          item.imgUrl =
            'https://static.bestsign.cn:443/15cb76e19b5b166292b8b6d84bb84976df71c66b.png';
          break;
        case 2:
          item.imgUrl =
            'https://static.bestsign.cn:443/3bc28e730390888ca90d17d35389ac7098845895.png';
          break;
        default:
          item.imgUrl = '';
      }
    });
    switch (item.id) {
      case 40:
        item.banner.pcImgUrl =
          'https://static.bestsign.cn:443/ac47cdd0b9eb10962df75777039cdbfdd8bad9d1.png';
        item.banner.mobileImgUrl =
          'https://static.bestsign.cn:443/ac47cdd0b9eb10962df75777039cdbfdd8bad9d1.png';
        // item.usage.pc = require('@/assets/images/solution/retail-case-pc.jpg');
        // item.usage.mp = require('@/assets/images/solution/retail-case-wap.jpg');
        sceneBg = require('@/assets/images/solution/usage4.jpg');
        break;
      case 41:
        item.banner.pcImgUrl =
          'https://static.bestsign.cn:443/2ac708e7fab499c8ad9c29eccb9c3e256d992d79.png';
        item.banner.mobileImgUrl =
          'https://static.bestsign.cn:443/2ac708e7fab499c8ad9c29eccb9c3e256d992d79.png';
        // item.usage.pc = require('@/assets/images/solution/retail-case-pc.jpg');
        // item.usage.mp = require('@/assets/images/solution/retail-case-wap.jpg');
        sceneBg = require('@/assets/images/solution/usage4.jpg');
        break;
      case 42:
        item.banner.pcImgUrl =
          'https://static.bestsign.cn:443/dde84c8934d5bdab873b49194fb33d34202f5422.png';
        item.banner.mobileImgUrl =
          'https://static.bestsign.cn:443/dde84c8934d5bdab873b49194fb33d34202f5422.png';
        // item.usage.pc = require('@/assets/images/solution/retail-case-pc.jpg');
        // item.usage.mp = require('@/assets/images/solution/retail-case-wap.jpg');
        sceneBg = require('@/assets/images/solution/usage4.jpg');
        break;
      default:
        item.banner.pcImgUrl = '';
        item.banner.mobileImgUrl = '';
    }

    return {
      banner: item.banner,
      pain: item.pain,
      scene: item.scene,
      solution: item.solution,
      usage: item.usage,
      evaluate: item.evaluate,
      seo: item.seo,
      sceneBg,
      dsc: item.description,
      title: item.title,
      type: item.type,
    };
  },
  data() {
    return {
      tabsHeight: '',
      fixedTab: false,
      painTop: '',
      sceneTop: '',
      solutionTop: '',
      evaluateTop: '',
      clientTop: '',
      activeTab: 0,
    };
  },
  head() {
    if (this.language === 'zh') {
      return {
        title: `${
          this.banner.title
        }行业电子签约解决方案 _BestSign电子签约云平台`,
        meta: [
          {
            hid: 'keywords',
            name: 'keyword',
            content: `${this.banner.title}电子合同,${
              this.banner.title
            }电子签约,${this.banner.title}电子签名,${
              this.banner.title
            }行业解决方案,BestSign电子签约云平台`,
          },
          {
            hid: 'description',
            name: 'description',
            content: `BestSign电子签约云平台的${this.banner.title}深度分析了${
              this.banner.title
            }行业下的合同签署痛点，解析企业如何应用电子合同解决线下签约难题，实现业务飞快运转，并提供${
              this.banner.title
            }行业优质电子签约、电子合同的使用场景和解决方案。为企业的电子签约、电子合同业务提供强有力的支持。`,
          },
        ],
        link: [
          {
            rel: 'canonical',
            href: `https://www.bestsign.cn/solution/${this.$route.params.id}`,
          },
        ],
      };
    } else {
      return {
        title: `${
          this.banner.title
        }領域電子契約ソリューション＿BestSign電子契約クラウド・プラットフォーム`,
        meta: [
          {
            hid: 'keywords',
            name: 'keyword',
            content: `${this.banner.title}領域電子契約、${
              this.banner.title
            }領域電子契約調印、${this.banner.title}領域電子署名、${
              this.banner.title
            }領域ソリューション、BestSign電子契約クラウド・プラットフォーム`,
          },
          {
            hid: 'description',
            name: 'description',
            content: `BestSign電子契約の${
              this.banner.title
            }ソリューション・モジュールが、${
              this.banner.title
            }の契約調印の欠点を掘り下げ、企業としてどのように電子契約を利用しオフラインでの契約調印の難題を解決できるかを分析することで、業務の効率的運営を実現させ${
              this.banner.title
            }領域における電子契約の応用シーン及びソリューションを提供し、企業の電子契約、電子契約業務をサーポートします。`,
          },
        ],
        link: [
          {
            rel: 'canonical',
            href: `https://www.bestsign.cn/solution/${this.$route.params.id}`,
          },
        ],
      };
    }
  },
  methods: {
    // scroll(e) {
    //   const { id } = e.target.dataset;
    //   // console.log(e.target.dataset);
    //   this.$scrollTo(`#${id}`);
    // },
    // handleScroll() {
    //   const scrollTop =
    //     window.pageYOffset ||
    //     document.documentElement.scrollTop ||
    //     document.body.scrollTop;
    //   const calculateTop = scrollTop + 300;
    //   if (calculateTop > this.painTop && calculateTop < this.sceneTop) {
    //     this.activeTab = 1;
    //   } else if (
    //     calculateTop > this.sceneTop &&
    //     calculateTop < this.solutionTop
    //   ) {
    //     this.activeTab = 2;
    //   } else if (
    //     calculateTop > this.solutionTop &&
    //     calculateTop < this.evaluateTop
    //   ) {
    //     this.activeTab = 3;
    //   } else if (
    //     calculateTop > this.evaluateTop &&
    //     calculateTop < this.clientTop
    //   ) {
    //     this.activeTab = 4;
    //   } else if (calculateTop > this.clientTop) {
    //     this.activeTab = 5;
    //   }
    //   //   console.log(scrollTop, calculateTop, this.activeTab);
    //   if (scrollTop + 80 > this.tabsHeight) {
    //     this.fixedTab = true;
    //   } else {
    //     this.fixedTab = false;
    //   }
    // },
  },
  mounted() {
    // this.$store.commit('changePath', { path: 'solution' });
    // this.tabsHeight = document
    //   .querySelector('#childNav')
    //   .getBoundingClientRect().top;
    // this.painTop = document.querySelector('#pain').offsetTop;
    // this.sceneTop = document.querySelector('#scene').offsetTop;
    // this.solutionTop = document.querySelector('#solution').offsetTop;
    // this.evaluateTop = document.querySelector('#evaluate').offsetTop;
    // this.clientTop = document.querySelector('#client').offsetTop;
    // console.log(
    //   this.painTop,
    //   this.sceneTop,
    //   this.solutionTop,
    //   this.evaluateTop,
    //   this.clientTop
    // );
    // window.addEventListener('scroll', Throttle(this.handleScroll, 100));
  },
  destroyed() {
    // window.removeEventListener('scroll', this.handleScroll);
  },
};
</script>

<style lang="scss">
$text-gray: #a8a8a8;
.solution {
  section {
    padding: 4rem 0;
  }
  .ssq-button-primary {
    border-radius: 40px;
    width: 120px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    &.white {
      background-color: #fff;
      color: #00aa64;
    }
    &.white-border {
      border: 1px solid #fff;
      color: #fff;
      background-color: rgba(255, 255, 255, 0.3);
    }
  }

  .container {
    // max-width: 1200px;
    width: 100%;
    margin: 0 auto;
  }
  .main-title {
    color: #090909;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
    text-align: center;
  }
  .nav-bar {
    // padding: 1rem 0;
    ul {
      display: flex;
      justify-content: center;
      li {
        font-size: 1rem;
        padding: 22px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
        position: relative;
        max-width: 125px;
        &::after {
          content: '';
          width: 100%;
          height: 1px;
          background-color: transparent;
          position: absolute;
          bottom: 0;
          left: 0;
        }
        &:hover {
          color: #00a664;
          border-color: #00a664;
        }
        &:hover::after {
          background-color: #00a664;
        }
      }
    }
  }
  .fixed-bar {
    position: fixed;
    z-index: 20;
    top: 76px;
    background: #fff;
    box-shadow: 0 2px 20px 0 rgba(0, 39, 123, 0.13);
    width: 100%;
    padding: 0;
    transition: all 0.2s ease;
    .active-bar {
      color: #00aa64;
      border-bottom: 2px solid #00aa64;
    }
  }
}
@media (max-width: 767px) {
  .solution {
    .banner {
      text-align: center;
      .container {
        padding: 0 10px;
      }
      .left-block {
        margin-left: 0;
        .ssq-button-primary {
          margin: 0 auto;
        }
      }
    }
    .pains {
      .container {
        display: block;
      }
    }
    .nav-bar {
      display: none;
    }
    .banner .left-block .main-title {
      text-align: center;
    }
    .main-title {
      font-size: 24px;
    }
  }
}
@media screen and (min-width: 767px) and (max-width: 1024px) {
  .solution {
    .main-title {
      font-size: 30px;
    }
  }
}
</style>
