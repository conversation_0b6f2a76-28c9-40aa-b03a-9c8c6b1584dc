<template>
  <article class="about-news">
    <section class="section text-center is-tiny">
      <div class="container">
        <h1 class="article-headline">上上签电子签约，助力更多企业业务效率升级</h1>
        <p class="article-subtitle">在这里，你可以看到专业的上上签新闻</p>
        <!-- <div class="filter">
          <div class="item" :class="{'is-active': category === 0}" @click="handleFilter(0)">全部</div>
          <div class="item" :class="{'is-active': category === 1}" @click="handleFilter(1)">公司新闻</div>
          <div class="item" :class="{'is-active': category === 2}" @click="handleFilter(2)">行业动态</div>
          <div class="item" :class="{'is-active': category === 3}" @click="handleFilter(3)">签约客户</div>
        </div> -->
			<nav :class="['nav-bar',fixedTab?'fixed-bar':'']" ref="childNav">
          <ul class="abstract">
          <nuxt-link to="/news">
            <li class="">全部</li>
          </nuxt-link>
          <template v-for="item in dynamicList">
            <nuxt-link :key="item.code" :to="`/news/list-${item.code}`">
              <li>{{ item.codeValue }}</li>
            </nuxt-link>
          </template>
        </ul>
			</nav>
        <div class="card-wrapper">
          <el-row>
            <template v-for="(item) in shownList">
              <el-col
                :key="item.id"
                :xs="24"
                :md="8"
                :lg="8"
                :sm="8"
              >
                <!-- <div class="card" @click="handleClick(item.id)"> -->
                  <div class="card">
                  <nuxt-link :to="{path: `/news/detail/${item.id}`}">
                    <img
                    :key="item.imgUrl"
                      v-lazy="item.imgUrl"
                      class="card-image">
                    <div class="card-content">
                      <p class="body">{{ item.dynamicTitle }}</p>
                      <p class="footer">
                        <span style="float: left">{{ categoryName(item.dynamicType) }}</span>
                        <span class="time">{{ releaseTime(item.releaseTime) }}</span>
                      </p>
                    </div>
                  </nuxt-link>
                </div>
              </el-col>
            </template>
          </el-row>
        </div>
        <div class="button-wrap">
          <el-pagination
          background
            :page-size="12"
            :current-page="current"
            :total="count"
            @current-change="handleCurrentChange"
            :pager-count="5"
            layout="prev, pager, next"></el-pagination>
        </div>
        <!-- seo优化，由于分页是客户端渲染，源码里无法拿到路由 -->
        <div style="display:none" v-for="item in pageNums" :key="item">
          <a v-if="item === 1" href="/news">{{item}}</a>
          <a v-else :href="`/news-p${item}`">{{item}}</a>
        </div>
      </div>
    </section>
  </article>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import moment from 'moment';
import Throttle from '@/assets/utils/throttle';
export default {
  name: 'News',
  layout: 'default',
  head() {
    return {
      title:
        this.current > 1
          ? `电子签约行业新闻动态_解读分享（第${
              this.current
            }页）_上上签电子签约云平台`
          : '电子签约行业新闻动态_解读分享-上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content:
            '电子签约,电子签名,电子合同,行业动态,新闻资讯,上上签电子签约云平台',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签电子签约云平台的新闻动态板块汇聚了电子签约行业的新闻动态，上上签最新消息和一手资讯，为您全方位且详细的解读有关上上签的新闻。',
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.bestsign.cn/news',
        },
      ],
    };
  },
  data() {
    return {
      category: 0,
      tabsHeight: '',
      fixedTab: false,
      // categories: {
      //   1: '公司新闻',
      //   2: '行业动态',
      //   3: '签约客户',
      // },
      // total: 0,
      // list: [],
    };
  },
  async asyncData({ app, route, params }) {
    // const _category = route.path;
    // const keys = _category.split('-');
    // const current = parseInt((keys[1] || 'p1').replace('p', ''));
    // return {
    //   current,
    // };
    const current = params.hasOwnProperty('page')
      ? parseInt(params.page.replace('-p', ''))
      : 1;
    const res0 = await app.$axios.get('/www/api/web/category/dynamic');
    const res = await app.$axios.get(
      `/www/api/web/getDynamic?pageNum=${current}&pageSize=12`
    );
    const res2 = await app.$axios.get(
      `/www/api/web/getDynamic?pageNum=1&pageSize=1000`
    );
    let xmlList = [
      {
        loc: 'https://www.bestsign.cn/news',
        priority: '1',
        lastmod: moment().format('YYYY-MM-DD'),
        changefreq: 'daily',
      },
    ];
    res2.data.forEach(it => {
      let xmlIt = {};
      xmlIt.loc = 'https://www.bestsign.cn/news/detail/' + it.id;
      xmlIt.priority = '1';
      xmlIt.lastmod = moment().format('YYYY-MM-DD');
      xmlIt.changefreq = 'daily';
      xmlList.push(xmlIt);
    });

    app.$axios
      .post('/www-api/updateMapText', { list: xmlList, column: 'news' })
      .then(function(response) {
        console.log(response);
      })
      .catch(function(error) {
        // console.log(error);
      });
    // const current = params.hasOwnProperty('page')
    //   ? parseInt(params.page.replace('-p', ''))
    //   : 1;
    const filterResult = res.data.filter(o => moment() > moment(o.releaseTime));
    const shownList = filterResult;
    return {
      dynamicList: res0,
      current,
      count: res.totalNum,
      shownList,
      pageNums: res.totalPageNum,
    };
  },
  computed: {
    ...mapState(['isMobile']),
  },
  mounted() {
    this.$store.commit('changePath', { path: 'about-us' });
    if (!this.isMobile) {
      this.tabsHeight = this.$refs.childNav.offsetTop;
      window.addEventListener('scroll', Throttle(this.handleScroll, 100));
    }
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    ...mapActions(['setViewTimes']),
    // 这个请求的目的是要在点击之后增加一个点击次数，现在这个已经在详情（_id）的里面的接口上做了这个事情
    // handleClick(id) {
    //   this.setViewTimes({
    //     id,
    //     type: '1',
    //   });
    // },
    // handleFilter(category) {
    //   this.category = category;
    //   // this.current = 1;
    //   // this.getData(1, category);
    //   // this.getListCount(category);
    // },
    // async getData(page = 1, category = 0) {
    //   console.log(page, category);
    //   try {
    //     const res = await this.$axios.get(
    //       `/www/api/web/dynamic?pageNum=${page}&pageSize=12&status=0${
    //         parseInt(category) ? `&category=${category}` : ''
    //       } `
    //     );
    //     this.list = res.filter(o => moment() > moment(o.releaseTime));
    //   } catch (e) {}
    // },
    // getListCount(category = 0) {
    //   this.$axios
    //     .get(
    //       `/www/api/web/dynamic?pageNum=1&pageSize=999${
    //         parseInt(category) ? `&category=${category}` : ''
    //       }`
    //     )
    //     .then(res => {
    //       this.total = res && res.length;
    //     });
    // },
    handleCurrentChange(page) {
      let url = `/news${page === 1 ? '' : `-p${page}`}`;
      this.$router.push(url);
    },
    releaseTime(time) {
      return moment(time).format('YYYY-MM-DD');
    },
    categoryName(id) {
      const item = this.dynamicList.find(o => o.code === id);
      return item && item.codeValue;
    },
    handleScroll() {
      const scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      if (scrollTop > this.tabsHeight) {
        this.fixedTab = true;
      } else {
        this.fixedTab = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.abstract {
  max-width: 500px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  color: #323232;
  font-size: 16px;
  margin: 0 auto;
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
  li {
    cursor: pointer;
    padding: 20px 18px;
    color: #323232;
    font-size: 1rem;
    &:hover {
      color: #00a664;
    }
    &.active {
      color: #00a664;
    }
  }
  a:hover {
    border-bottom: 2px solid #00aa64;
    color: #00aa64;
  }
  .nuxt-link-exact-active {
    border-bottom: 2px solid #00aa64;
    li {
      color: #00aa64;
    }
  }
}
.fixed-bar {
  position: fixed;
  z-index: 20;
  top: 76px;
  background: #fff;
  box-shadow: 0 2px 20px 0 rgba(0, 39, 123, 0.13);
  width: 100%;
  left: 0;
  padding: 0;
  transition: all 0.2s ease;
  .abstract {
    border-bottom: none;
    margin-bottom: 0;
    margin: 0 auto;
  }
}
.article-headline {
  margin-bottom: 50px;
  font-size: 2.2rem;
  font-weight: 500;
  margin: 90px 0 50px;
  color: #090909;
}
.card {
  margin: 0.9375rem 0;
}
.about-news {
  .container {
    max-width: 70.75rem;
  }
  .card-content {
    height: 135px;
    padding: 24px 20px;
    .body {
      text-align: justify;
    }
    .text-content {
      margin: 24px auto 0;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      text-align: center;
      word-break: break-all;
    }
  }
}
.button-wrap {
  margin-top: 110px;
  text-align: center;
}
.filter {
  display: flex;
  align-items: center;
  justify-content: center;
  // border-bottom: 1px solid #00a664;
  margin-bottom: 32px;
  .item {
    line-height: 32px;
    font-size: 1rem;
    padding: 0 1.75rem;
    cursor: pointer;
    color: #515151;
    border-bottom: 3px solid transparent;
    &.is-active {
      color: #00a664;
      border-bottom-color: #00a664;
    }
  }
}
@media screen and (max-width: 767px) {
  .section {
    padding: 30px 0px;
    .card {
      .card-content {
        .body {
          line-height: 2.2rem;
        }
      }
    }
  }
  .fixed-bar {
    position: fixed;
    z-index: 20;
    top: 56px;
    background: #fff;
    box-shadow: 0 2px 20px 0 rgba(0, 39, 123, 0.13);
    width: 100%;
    left: 0;
    padding: 0;
    transition: all 0.2s ease;
    .abstract {
      border-bottom: none;
      margin-bottom: 0;
      margin: 0 auto;
    }
  }
  .article-headline {
    font-size: 24px;
    color: #515151;
    font-weight: 200;
  }
  .filter {
    // display: flex;
    // align-items: center;
    // justify-content: center;
    // border-bottom: 1px solid #00a664;
    // margin-bottom: 32px;
    .item {
      // line-height: 32px;
      // font-size: 1rem;
      padding: 0 1rem;
      // cursor: pointer;
      // color: #515151;
      // border-bottom: 3px solid transparent;
      // &.is-active {
      //   color: #00a664;
      //   border-bottom-color: #00a664;
      // }
    }
  }
  .abstract {
    max-width: 1440px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    color: #323232;
    font-size: 16px;
    margin-bottom: 30px;
    li {
      cursor: pointer;
      padding: 20px 10px;
      color: #323232;
      &:hover {
        color: #00a664;
      }
      &.active {
        color: #00a664;
      }
    }
  }
  .article-headline {
    font-size: 24px;
    color: #515151;
    font-weight: 200;
    margin: 15px 0px;
  }
}
</style>
