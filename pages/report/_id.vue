<template>
  <article>
    <bread-nav :menu="menu"></bread-nav>
    <section class="section section-1">
      <div class="container">
        <div class="article">
			 <h1>{{ article.reportTitle }}</h1>
          <div class="operate">
            <div class="desc">
              <span style="margin-right:18px;">
              <!-- <strong>来源：</strong> -->
              来源：
              上上签
            </span>
            <span>
              <!-- <strong>发布时间：</strong> -->
              发布时间：
              {{ formatDate(releaseTime) }}
            </span>
            <!-- SEO(源码里面要时分秒，页面不要) -->
            <span style="display:none">
              发布时间：
              {{ releaseTime }}
            </span>
            
            </div>
            <div class="left-downLoad" @click="downLoad">立即下载</div>
          </div>
          	<div  id="content"  class="braft-output-content" v-html="article.content"> </div>
			<nav class="article-change">
				<div class="nav-item">
				<nuxt-link
					v-if="article.prevId"
					:to="`/report/${article.prevId}`"
				>上一篇：{{ article.prevTitle }}</nuxt-link>
				</div>
				<div class="nav-item">
				<nuxt-link
					v-if="article.nextId"
					:to="`/report/${article.nextId}`"
				>下一篇：{{ article.nextTitle }}</nuxt-link>
				</div>
			</nav>
			<nav>
				<h2 class="main-title"> 相关推荐</h2>
				<el-button type="text" class="change-card" @click="getNewCard">换一换</el-button>
				<el-row>
					<template v-for="item in shownList">
						<el-col
						:key="item.id"
						:sm="8"
						:md="8"
						:lg="8"
						>
						 <nuxt-link
                  :to="`/report/${item.id}`"
                  class="card"
                  @click="handleView(item.id)">
                  <img
                  :key="item.imgUrl"
                    v-lazy="formatUrl(item.imgUrl)"
                    class="card-image"
                    alt="上上签电子签约百科为你提供关于电子合同、使用介绍、解释等相关知识。了解未来发展趋势，以及如何在各个领域安全使用">
                  <div class="card-content">
                    <div class="body text-justify">{{ item.reportTitle }}</div>
                    <p class="footer">
                      <span class="time">{{ formatTime(item.releaseTime) }}</span>
                    </p>
                  </div>
                </nuxt-link>
						</el-col>
					</template>
					</el-row>
			</nav>
        </div>
        <aside class="article-side" v-if="!isLandMobile">
			<div class="icon-box">
				<i class="iconfont icon-weixin">
					<img
					:src="weixinLink"
					alt="二维码">
				</i>
				<a :href="shareUrl" target="_blank"><i class="iconfont icon-weibo"></i></a>
				<i class="iconfont icon-fanhui" @click.stop.prevent="handlePage()"></i>
				
			</div>
        </aside>
      </div>
    </section>
  </article>
</template>

<script>
import moment from 'moment';
import find from 'lodash/find';
import QRCode from 'qrcode';
import Qs from 'qs';
export default {
  name: 'CollegeWikiId',
  layout: 'default',
  head() {
    return {
      title: this.tdkT || '上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keyword',
          content: this.tdkK || '电子合同,电子合同签署,电子签名,电子签章',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            this.tdkD ||
            '上上签是业内领先的在线电子合同签署，电子签名，电子签章的云服务平台，提供多种行业专属解决方案。',
        },
      ],
    };
  },
  async asyncData({ route, app, redirect }) {
    const id = route.params.id;
    const res = await app.$axios.get(`www/api/web/getReport/${id}`);
    if (res.length === 0) {
      redirect('/404');
    }
    const page = route.query.page || 2;
    if (route.path.indexOf('p') == -1) {
      var res1 = await app.$axios.get(
        `/www/api/web/getReport?pageNum=${page}&pageSize=3`
      );
    } else {
      var res1 = await app.$axios.get(
        `/www/api/web/getReport?pageNum=${page}&pageSize=3`
      );
    }
    // console.log(res);
    return {
      article: res,
      // releaseTime: moment(res.releaseTime).format('YYYY-MM-DD'),
      releaseTime: res.releaseTime,
      // seo: res.summary ? JSON.parse(res[0].summary) : {},
      tdkT: res.tdkT,
      tdkD: res.tdkD,
      tdkK: res.tdkK,
      shownListSeo: res1.data,
      totalPageNum: res1.totalPageNum,
      contentName: res.reportTitle,
    };
  },
  data() {
    return {
      article: {},
      menu: [
        {
          name: '行业报告',
          to: '/report',
        },
        {
          name: '报告详情',
          to: '',
        },
      ],
      page: 2,
      shareUrl: '',
      link: '',
      weixinLink: '',
      shownList: [],
    };
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.linkFrom = from.path;
    });
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },

    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  mounted() {
    this.shareUrl =
      'http://service.weibo.com/share/share.php?url=' +
      window.location.href +
      '&sharesource=weibo&title=' +
      this.article.caseName;
    this.link = window.location.href;
    this.qrcode();
    this.getNewCard();
  },
  methods: {
    formatDate(date) {
      // let time = new Date(date.replace(new RegExp(/-/gm), '/'));
      // return (
      //   time.getFullYear() +
      //   '-' +
      //   ('0' + (time.getMonth() + 1)).substr(-2) +
      //   '-' +
      //   time.getDate()
      // );
      return moment(date).format('YYYY-MM-DD');
    },
    qrcode() {
      let that = this;
      QRCode.toDataURL(this.link, function(err, base64) {
        that.weixinLink = base64;
      });
    },
    handlePage(id) {
      this.$router.push({
        path: '/report',
        query: {
          page: 1,
        },
      });
    },
    formatTime(time) {
      return moment(time).format('YYYY-MM-DD');
    },
    formatUrl(url) {
      const arr = url && url.split('?');
      return url && arr[0];
    },
    async getNewCard() {
      if (this.page == this.totalPageNum) {
        this.page = 1;
      } else {
        this.page += 1;
      }
      var resAbout = await this.$axios.get(
        `/www/api/web/getReport?pageNum=${this.page}&pageSize=3&id=${
          this.$route.params.id
        }`
      );
      this.shownList = resAbout.data;
    },
    downLoad() {
      // debugger;
      const token = this.$cookies.get('access_token');
      // console.log(this.$cookies.get('access_token'));
      // const token = document.cookie
      // console.log(this.linkFrom);
      const applyType = this.linkFrom.includes('ownRegister') ? 1 : 2;
      // console.log(token);
      this.$axios
        .post(
          '/www/api/web/report/download',
          {
            url: location.href,
            contentId: this.$route.params.id,
            contentName: this.contentName,
            applyType: applyType,
          },
          {
            headers: {
              Authorization: `bearer ${token}`,
            },
          }
        )
        .then(res => {
          if (res.code === '150004') {
            this.$MessageToast.success(res.message);
            setTimeout(() => {
              const query = sessionStorage.getItem('query');
              this.$router.push({
                path: '/login',
                query: {
                  ...Qs.parse(query),
                  pageId: this.$route.params.id,
                  pagePath: this.$route.path.split('/')[1],
                  activityFrom: 'content',
                },
              });
            }, 2000);
          } else if (res.code === '150001') {
            if (res.result) {
              window.open(res.result);
            } else {
              this.$MessageToast.error('资料还未上传，敬请期待。');
            }
            // window.open(res.result);
            // window.open(
            //   'https://img02.ma.scrmtech.com/7161/1266/resource/1593599557/2019%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%B5%E5%AD%90%E7%AD%BE%E5%90%8D%E8%A1%8C%E4%B8%9A%E4%B8%93%E9%A2%98%E6%8A%A5%E5%91%8A.pdf'
            // );
          } else if (res.code === '150002') {
            // this.$MessageToast.err('下载失败');
            this.$message.error(res.message);
          }
        });
    },
  },
};
</script>

<style scoped lang="scss">
.abstract {
  display: flex;
  justify-content: space-between;
  font-size: 16px;
  margin-bottom: 30px;
  margin: 0 auto;
  max-width: 1200px;
  ul {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
  }
  li {
    cursor: pointer;
    padding: 20px 30px;
    color: #333;
    &:hover {
      color: #9a9999;
    }
    &.active {
      color: #eee;
    }
  }
  li:last-child {
    padding-right: 0;
  }
}
.all-case {
  padding: 20px 30px;
  color: #333;
  display: flex;
  align-items: center;
  &:hover {
    color: #9a9999;
  }
}
.section-1 {
  background-color: #fafbfc;
  padding: 0;
  a {
    color: #323232;
    &:hover {
      color: #00aa64;
    }
  }
  .main-title {
    font-weight: 400;
  }
  .container {
    display: flex;
    .article {
      flex: 1;
      padding: 30px 0 80px 40px;
      border-radius: 5px;
    }
    #content {
      padding-bottom: 45px;
      border-bottom: 1px solid #e5e5e5;
    }
    aside {
      .icon-box {
        width: auto;
        margin-left: 16px;
        display: flex;
        flex-direction: column;
        text-align: center;
        position: fixed;
        .iconfont {
          font-size: 20px;
          cursor: pointer;
          color: #bfbfbf;
          margin: 5px 0;
          &:hover {
            color: #00a664;
          }
        }
        .icon-weixin {
          position: relative;
          img {
            position: absolute;
            width: 100px;
            right: 40px;
            top: -20px;
            display: none;
          }
          &:hover img {
            display: block;
          }
        }
      }

      .item-wrap {
        background-color: #fff;
        border: 1px solid #e5e5e5;
        border-radius: 5px;
        padding: 24px 18px;
        // margin-bottom: 15px;
        .img-wrap {
          width: 100%;
          text-align: center;
          img {
            width: 100%;
          }
        }
        h4 {
          margin: 36px auto;
          font-size: 18px;
          padding-bottom: 15px;
          border-bottom: 1px solid #e5e5e5;
        }
        .item {
          margin-bottom: 25px;
          cursor: pointer;
          color: #888;
          &:hover {
            color: #00a664;
          }
        }
      }
      .qrcode {
        min-width: 200px;
        margin-top: 20px;
        display: flex;
        justify-content: space-around;
        img {
          width: 80px;
          height: 80px;
        }
        p {
          // margin-top: 0.25rem;
          font-size: 12px;
          // margin-left: 0.3rem;
        }
      }
    }
    h1 {
      font-size: 30px;
      margin-bottom: 30px;
      width: 80%;
      line-height: 1.4;
      color: #090909;
      font-weight: 400;
    }
    // .desc {
    //   text-align: right;
    //   font-size: 14px;
    //   padding-bottom: 10px;
    //   border-bottom: 1px solid #e5e5e5;
    //   margin-bottom: 45px;
    //   color: #86868b;
    // }
    .operate {
      display: flex;
      justify-content: space-between;
      padding-bottom: 10px;
      border-bottom: 1px solid #e5e5e5;
      margin-bottom: 45px;
      .left-downLoad {
        padding: 0 15px;
        height: 36px;
        color: #00aa64;
        text-align: center;
        margin-top: 5px;
        margin-left: 15px;
        line-height: 34px;
        background: #fff;
        border: 1px solid #00aa64;
        border-radius: 4px;
        cursor: pointer;
      }
      .desc {
        text-align: right;
        font-size: 14px;
        // padding-bottom: 10px;
        // border-bottom: 1px solid #e5e5e5;
        // margin-bottom: 45px;
        margin-top: 1rem;
      }
    }
  }
  .article-change {
    text-align: left;
  }
  nav {
    margin-top: 25px;
    position: relative;
    text-align: center;
    .card {
      // height: 20rem;
    }
    .card-content {
      padding: 24px 20px;
      height: 135px;
      p {
        text-align: left;
      }
      .footer {
        align-self: flex-end;
      }
    }
    .el-row {
      display: flex;
      // justify-content: space-around;
      flex-wrap: wrap;
      max-width: 70.75rem;
      margin: 0 auto;
    }
    .nav-item {
      cursor: pointer;
      padding: 10px 0;
      &:hover {
        color: #00a664;
      }
    }
    .change-card {
      color: #00a664;
    }
    .back {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 10px;
      .el-icon-arrow-left {
        padding-right: 5px;
        font-size: 22px;
        position: relative;
        top: 2px;
      }
      &:hover {
        color: #00a664;
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .section-1 {
    padding: 0 0 60px;
    .container {
      .article {
        padding: 30px 0 80px;
      }
      .desc {
        text-align: left;
        color: #86868b;
      }
      h1 {
        font-size: 24px;
        color: #090909;
        font-weight: 400;
        margin-bottom: 30px;
        line-height: 35px;
        width: 100%;
      }
      h3 {
        font-size: 24px;
        margin-bottom: 40px;
      }
      nav {
        .nav-item {
          font-size: 14px;
          line-height: 24px;
        }
        .back {
          cursor: pointer;
          left: 0;
          bottom: -35px;
          top: auto;
        }
      }
      .article-side {
        display: none;
      }
      .operate {
        display: block;
        .left-downLoad {
          margin-left: 20.5rem;
          height: 30px;
          line-height: 30px;
          width: 80px;
        }
      }
    }
    img {
      max-width: 100%;
    }
  }
}
</style>
