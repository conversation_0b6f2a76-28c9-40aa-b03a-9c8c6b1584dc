<template>
  <article>
    <bread-nav :menu="menu"></bread-nav>
    <section class="section section-1">
      <div class="container">
        <div class="article">
          <h1>{{ article.dynamicName }}</h1>
          <div class="desc">
            <span>
              <strong>发布时间：</strong>
              {{ releaseTime }}
            </span>
          </div>
          <div
            id="content"
            class="braft-output-content"
            v-html="article.content"></div>
          <nav>
            <div
              v-if="article.prevId"
              class="nav-item">上一篇：
              <nuxt-link :to="{ path: `/news/${article.prevId}`}">{{ article.prevDynamicName }}</nuxt-link>
            </div>
            <div
              v-if="article.nextId"
              class="nav-item">下一篇：
              <nuxt-link :to="{ path: `/news/${article.nextId}`}">{{ article.nextDynamicName }}</nuxt-link></div>
            <div
              class="back"
              @click="handlePage()">
              <i class="el-icon-arrow-left"></i>
              返回
            </div>
          </nav>
        </div>
        <aside class="article-side">
          <div class="item-wrap">
            <h4>相关推荐</h4>
            <template v-for="item in article.recommend">
              <div class="item" :key="item.id" @click="handlePage(item.id)">{{ item.dynamic_name }}</div>
            </template>
          </div>
        </aside>
      </div>
    </section>
  </article>
</template>

<script>
import moment from 'moment';
export default {
  name: 'AboutNewsId',
  head() {
    return {
      title: (this.seo && this.seo.title) || '上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keyword',
          content:
            (this.seo && this.seo.keyword) ||
            '电子合同,电子合同签署,电子签名,电子签章',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            (this.seo && this.seo.description) ||
            '上上签是业内领先的在线电子合同签署，电子签名，电子签章的云服务平台，提供多种行业专属解决方案。',
        },
      ],
    };
  },
  async asyncData({ route, app, redirect, $axios }) {
    const dynamicId = route.params.id;
    const res = await app.$axios.get(
      `/www/api/web/dynamic?dynamicId=${dynamicId}`
    );
    if (res.length === 0) {
      redirect('/404');
    }
    return {
      article: res[0],
      releaseTime: moment(res[0].releaseTime).format('YYYY-MM-DD'),
      seo: res[0].summary ? JSON.parse(res[0].summary) : {},
    };
  },
  data() {
    return {
      menu: [
        {
          name: '新闻动态',
          to: '/news',
        },
        {
          name: '新闻详情',
          to: '',
        },
      ],
    };
  },

  mounted() {
    const dynamicId = this.$route.params.id;
    window._hmt &&
      window._hmt.push(['_trackEvent', 'news', 'click', dynamicId]);
  },
  methods: {
    handlePage(id) {
      this.$router.push(`/news${id ? `/${id}` : ''}`);
    },
  },
};
</script>

<style scoped lang="scss">
.section-1 {
  background-color: #fafbfc;
  padding: 16px 0;
  .container {
    display: flex;
    .article {
      flex: 1;
      padding: 30px 40px 80px;
      background-color: #fff;
      border: 1px solid #e5e5e5;
      border-radius: 5px;
    }
    #content {
      padding-bottom: 45px;
      border-bottom: 1px solid #e5e5e5;
    }
    aside {
      width: 20%;
      margin-left: 16px;
      display: flex;
      flex-direction: column;
      .item-wrap {
        background-color: #fff;
        border: 1px solid #e5e5e5;
        border-radius: 5px;
        padding: 24px 18px;
        margin-bottom: 15px;
        .img-wrap {
          width: 100%;
          text-align: center;
          img {
            width: 100%;
          }
        }
        h4 {
          margin: 36px auto;
          font-size: 18px;
          padding-bottom: 15px;
          border-bottom: 1px solid #e5e5e5;
        }
        .item {
          margin-bottom: 25px;
          cursor: pointer;
          color: #888;
          &:hover {
            color: #00a664;
          }
        }
      }
    }
    h1 {
      font-size: 30px;
      margin-bottom: 60px;
    }
    .desc {
      text-align: right;
      font-size: 14px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e5e5e5;
      margin-bottom: 45px;
    }
    nav {
      margin-top: 25px;
      position: relative;
      .nav-item {
        cursor: pointer;
        padding: 10px 0;
        &:hover,
        a:hover {
          color: #00a664;
        }
      }
      .back {
        cursor: pointer;
        position: absolute;
        right: 0;
        top: 10px;
        .el-icon-arrow-left {
          padding-right: 5px;
          font-size: 22px;
          position: relative;
          top: 2px;
        }
        &:hover {
          color: #00a664;
        }
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .section-1 {
    .container {
      .article {
        padding: 30px 20px 50px;
      }
      h3 {
        font-size: 24px;
        margin-bottom: 40px;
      }
      nav {
        .back {
          cursor: pointer;
          left: 0;
          bottom: -35px;
          top: auto;
        }
      }
      .article-side {
        display: none;
      }
    }
    img {
      max-width: 100%;
    }
  }
}
</style>
