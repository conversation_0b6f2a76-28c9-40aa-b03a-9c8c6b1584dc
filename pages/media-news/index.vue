<template>
  <article class="about-news">
    <section class="section text-center is-tiny">
      <div class="container">
        <h1 class="article-headline">上上签电子签约，助力更多企业业务效率升级</h1>
        <p class="article-subtitle">在这里，你可以看到专业的媒体报道</p>
        <div class="card-wrapper">
          <el-row>
            <template v-for="(item) in shownList">
              <el-col
                :key="item.id"
                :xs="24"
                :md="8"
                :lg="6"
              >
                <div class="card" @click="handleClick(item.id)">
                  <nuxt-link :to="{path: `/news/${item.id}`}">
                    <img
                    :key="item.pictureUrl"
                      v-lazy="item.pictureUrl"
                      class="card-image">
                    <div class="card-content">
                      <p class="body">{{ item.dynamicName }}</p>
                      <p class="footer">
                        <span style="float: left">{{ category[item.category] }}</span>
                        <span class="time">{{ releaseTime(item.releaseTime) }}</span>
                      </p>
                    </div>
                  </nuxt-link>
                </div>
              </el-col>
            </template>
          </el-row>
        </div>
        <div class="button-wrap" v-if="total > 12">
          <el-pagination
            :page-size="12"
            :current-page="parseInt(current)"
            :total="total"
            @current-change="handleCurrentChange"
            layout="prev, pager, next"></el-pagination>
        </div>
      </div>
    </section>
  </article>
</template>

<script>
import { mapActions } from 'vuex';
import moment from 'moment';
export default {
  name: 'News',
  layout: 'default',
  head() {
    return {
      title: '行业新闻动态_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同行业新闻,电子合同行业资讯,电子合同行业事件',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签新闻资讯提供最新企业动态，电子合同行业内最新资讯以及电子签约领域热门事件和专业解读。',
        },
      ],
    };
  },
  data() {
    return {
      category: {
        1: '公司新闻',
        2: '行业动态',
        3: '签约客户',
      },
      current: '1',
      shownList: [],
    };
  },
  async asyncData({ app, query }) {
    // category 4 媒体报道
    const res = await app.$axios.get(
      `/www/api/web/dynamic?pageNum=1&pageSize=999&category=4`
    );
    return {
      list: res.filter(o => moment() > moment(o.releaseTime)),
    };
  },
  computed: {
    total() {
      return this.list.length;
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: 'news' });
    this.init();
  },
  methods: {
    ...mapActions(['setViewTimes']),
    handleClick(id) {
      this.setViewTimes({
        id,
        type: '1',
      });
    },
    init() {
      const { page } = this.$route.query;
      this.current = page;
      this.getData(page);
    },
    getData(page = '1') {
      this.shownList = this.list.slice(
        (parseInt(page) - 1) * 12,
        12 * parseInt(page)
      );
      this.$router.push({
        query: {
          page,
        },
      });
    },
    handleCurrentChange(page) {
      this.getData(page);
    },
    releaseTime(time) {
      return moment(time).format('YYYY-MM-DD');
    },
  },
};
</script>

<style scoped lang="scss">
.article-headline {
  margin-bottom: 50px;
}

.about-news .card-content {
  height: 135px;
  padding: 24px 20px;
  .body {
    text-align: justify;
  }
  .text-content {
    margin: 24px auto 0;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-align: center;
    word-break: break-all;
  }
}
.button-wrap {
  margin-top: 110px;
  text-align: center;
}
.filter {
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #00a664;
  margin-bottom: 32px;
  .item {
    line-height: 32px;
    font-size: 1rem;
    padding: 0 1.75rem;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    &.is-active {
      color: #00a664;
      border-bottom-color: #00a664;
    }
  }
}
@media screen and (max-width: 767px) {
  .article-headline {
    font-size: 28px;
    margin-bottom: 30px;
  }
}
</style>
