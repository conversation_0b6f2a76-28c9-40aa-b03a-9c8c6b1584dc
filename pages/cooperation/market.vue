<template>
  <article class="market-cooperation">
    <section class="section section-1">
      <div class="container">
        <h1 class="article-headline">与你携手，推动电子签约<br/>科技加速普及。</h1>
          <p class="article-subtitle">期待与你一起推动电子签约市场普及，助力企业降本增效。</p>
      </div>
      <div class="img-wrap">
        <!-- <img v-if="!isMobile" src="~/assets/images/cooperation/market-banner.jpg">
        <img v-if="isMobile" src="~/assets/images/cooperation/<EMAIL>"> -->
      </div>
    </section>
	<section class="nav-bar">
		<div :class="['abstract industry',fixedTab?'fixed-bar':'']" ref="childNav">
			<ul>
				<nuxt-link
					:class="{'is-active': activeNav === 'channel'}"
					to="/cooperation/channel">
					<li>渠道合作</li>
					</nuxt-link>
				<nuxt-link
					:class="{'is-active': activeNav === 'market'}"
					to="/cooperation/market">
					<li>市场合作</li>
				</nuxt-link>
			</ul>
		</div>
	</section>
    <section class="section section-2">
      <div class="container">
        <h2 class="section-headline">观看近期活动</h2>
        <!-- <div class="card-wrapper">
          <el-row>
            <el-col
              :md="12"
              :lg="8"
            >
              <div class="card">
                <div class="image">
                  <img src="~/assets/images/cooperation/<EMAIL>">
                  <div class="mask">已结束</div>
                </div>
                <div class="card-content">
                  <h4 class="header">中美贸易摩擦与全球供应链建设座谈会</h4>
                  <div class="body">
                    <div class="para">
                      <img src="~/assets/images/icon/<EMAIL>">
                      <span>2018年9月26日</span>
                    </div>
                    <div class="para">
                      <img src="~/assets/images/icon/<EMAIL>">
                      <span>北京朝阳区</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col
              :md="12"
              :lg="8"
            >
              <div class="card">
                <div class="image">
                  <img src="~/assets/images/cooperation/<EMAIL>">
                  <div class="mask">已结束</div>
                </div>
                <div class="card-content">
                  <h4 class="header">上上签「智创」沙龙——2018互金风控趋势前瞻</h4>
                  <div class="body">
                    <div class="para">
                      <img src="~/assets/images/icon/<EMAIL>">
                      <span>2018年5月30日</span>
                    </div>
                    <div class="para para2">
                      <img src="~/assets/images/icon/<EMAIL>">
                      <span>北京海淀创业大街11号海置创投大厦7层创业邦Demo Space</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div> -->
		<section class="is-tiny">
		<div class="container">
			<el-row>
			<template v-for="(item, index) in shownList">
				<el-col
				:key="index"
				:md="8"
				:lg="8"
				:sm="8"
				>
				<div class="card">
					<!-- <nuxt-link :to="{path: `/activity/${item.id}`, query: {}}"> -->
					<!-- 在上一层页码刷新当下页面后，这里用nuxt-link会把那个页码带过来，所以用a便签实现 -->
					<a :href="`/activity/${item.id}`">
						<img
            :key="item.imgUrl"
						v-lazy="item.imgUrl"
						class="card-image">
						<div class="card-content">
						<p class="body">{{ item.activityTitle }}</p>
						<p class="footer">
							<!-- <span style="float: left">{{ categories[item.speaker] }}</span> -->
							<span class="time">{{ releaseTime(item.releaseTime) }}</span>
						</p>
						</div>
					</a>
					<!-- </nuxt-link> -->
					</div>
				</el-col>
			</template>
			</el-row>
		</div>
		</section>
       <p class="show-more" @click="seeMore">查看更多
		   <i class="iconfont icon-xiangyoujiantou"></i>
	   </p>
      </div>
    </section>
    <section class="section section-3">
      <div class="container">
        <h2 class="section-headline">生态合作伙伴</h2>
        <div class="img-wrap">
          <img v-if="!isMobile" src="~/assets/images/cooperation/<EMAIL>">
          <img v-if="isMobile" src="~/assets/images/cooperation/<EMAIL>">
        </div>
      </div>
    </section>
    <section class="section section-4">
      <div class="container">
        <div class="content">
          <div class="item">
            <p class="desc-1">市场部邮箱</p>
            <p class="desc-2"><EMAIL></p>
          </div>
        </div>
      </div>
    </section>
	<!-- <section class="section section-6">
		<div class="container">
			<div class="use-demo">
				<h2>立即体验上上签电子签约</h2>
				<p>合同秒发秒签，适配各种终端，线上线下无缝切换。</p>
				<span class="demo-btn" @click="handleDemo">免费试用 
					<i class="iconfont icon-xiangyoujiantou"></i>
				</span>
			</div>
			<img src="~/assets/images/cooperation/<EMAIL>">
		</div>
	</section> -->
  </article>
</template>

<script>
import moment from 'moment';
import find from 'lodash/find';
import Throttle from '@/assets/utils/throttle';
export default {
  name: 'Market',
  layout: 'layout_cooperation',
  head() {
    return {
      title: '市场合作_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同行业大会,电子合同行业沙龙,电子合同行业发展',
        },
        {
          hid: 'description',
          name: 'description',
          content: '各种行业沙龙以及座谈会，推动电子合同行业发展加速。',
        },
      ],
    };
  },
  async asyncData({ app, params, route }) {
    const res1 = await app.$axios.get(
      `/www/api/web/getActivity?pageNum=1&pageSize=12`
    );

    return {
      shownList: res1.data.slice(0, 3),
      pageNums: res1.totalPageNum,
      totalNum: res1.totalNum,
      pageNum: res1.pageNum,
    };
  },
  data() {
    return {
      tabsHeight: '',
      fixedTab: false,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    activeNav() {
      return this.$store.state.path;
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: 'market' });
    if (!this.isMobile) {
      this.tabsHeight = this.$refs.childNav.offsetTop;
      window.addEventListener('scroll', Throttle(this.handleScroll, 100));
    }
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    seeMore() {
      this.$router.push('/activity');
    },
    handleDemo() {
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          id: this.$route.query.id,
          utm_source: this.$route.query.utm_source,
          hp_right: '',
        },
      });
    },
    releaseTime(time) {
      return moment(time).format('YYYY-MM-DD');
    },
    handleScroll() {
      const scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      if (scrollTop > this.tabsHeight) {
        this.fixedTab = true;
      } else {
        this.fixedTab = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.market-cooperation {
  text-align: center;
  .section-1 {
    padding: 0;
    position: relative;
    .article-headline {
      font-weight: 400;
      color: #fff;
      font-size: 2.2rem;
    }
    .article-subtitle {
      font-size: 1.2rem;
      line-height: 1.7;
      font-weight: 400;
      margin-bottom: 1.5rem;
      padding: 6px 10px;
      color: #fff;
    }
    .container {
      position: absolute;
      left: 50%;
      top: 100px;
      transform: translate3d(-50%, 0, 0);
    }
    .img-wrap,
    img {
      width: 100%;
    }
    .img-wrap {
      background: url('~assets/images/cooperation/market-banner.jpg');
      background-size: cover;
      height: 29vw;
    }
  }
  .nav-bar {
    .abstract {
      display: flex;
      justify-content: center;
      font-size: 16px;
      margin-bottom: 30px;
      margin: 0 auto;
      max-width: 1100px;
      border-bottom: 1px solid #eee;
      a:hover {
        border-bottom: 2px solid #00aa64;
        color: #00aa64;
      }
      ul {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
      }
      li {
        cursor: pointer;
        padding: 20px 18px;
        color: #6e6e6e;
        font-size: 1rem;
        text-align: center;
        &:hover {
          color: #00a664;
        }
      }
      .nuxt-link-exact-active {
        border-bottom: 2px solid #00aa64;
        a {
          color: #00aa64;
        }
      }
    }
    .fixed-bar {
      position: fixed;
      z-index: 20;
      top: 76px;
      background: #fff;
      box-shadow: 0 2px 20px 0 rgba(0, 39, 123, 0.13);
      width: 100%;
      left: 0;
      padding: 0;
      transition: all 0.2s ease;
      max-width: 100%;
      .abstract {
        border-bottom: none;
        margin-bottom: 0;
        margin: 0 auto;
      }
    }
  }
  .section-2 {
    .card-wrapper {
      border-bottom: 1px solid #bfbfbf;
      padding-bottom: 50px;
    }
    .card {
      padding: 0;
      height: 22rem;
      margin: 0 10px;
      .image {
        position: relative;
        height: 185px;
        img {
          width: 100%;
          height: 100%;
        }
        .mask {
          position: absolute;
          left: 0;
          bottom: 0;
          height: 45px;
          line-height: 45px;
          color: #fff;
          text-align: center;
          font-size: 18px;
          width: 100%;
          background-color: rgba(0, 0, 0, 0.3);
        }
      }

      .card-content {
        width: 100%;
        background-color: #fff;
        padding: 48px 20px;
        text-align: justify;
        .header {
          margin-bottom: 20px;
          font-size: 20px;
          line-height: 28px;
          color: #1d1d1f;
          font-weight: 400;
        }
        .body {
          width: 100%;
          color: #6e6e6e;
          .para {
            display: flex;
            font-size: 14px;
            color: #888;
            margin-bottom: 12px;
            img {
              width: 20px;
              height: 20px;
              margin-right: 10px;
            }
            span {
              flex: 1;
            }
          }
        }
      }
    }
    .show-more {
      color: #00aa64;
      font-size: 14px;
      cursor: pointer;
      margin-top: 5rem;
      .iconfont {
        font-size: 14px;
      }
    }
  }
  .is-tiny {
    background-color: #fafbfc;
    padding: 60px 0;
    a {
      color: #323232;
      &:hover {
        color: #00aa64;
      }
    }
    .container {
      display: flex;
      justify-content: center;
      .el-row {
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;
        max-width: 70.75rem;
        margin: 0 auto;
      }
      .article {
        flex: 1;
        padding: 30px 0 80px 40px;
        border-radius: 5px;
      }
      #content {
        padding-bottom: 45px;
        border-bottom: 1px solid #e5e5e5;
      }
      h1 {
        font-size: 30px;
        margin-bottom: 60px;
        width: 65%;
        line-height: 1.4;
      }
      .desc {
        text-align: right;
        font-size: 14px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e5e5e5;
        margin-bottom: 45px;
      }
    }
    .article-change {
      text-align: left;
    }
  }
  .ssq-button-primary {
    margin-top: 30px;
  }
  .section-3 {
    background-color: #fafbfc;
    .img-wrap {
      //   margin-top: 75px;
      img {
        width: 90%;
      }
    }
  }
  .section-4 {
    padding: 1rem 1.5rem;
    background-image: url('~assets/images/cooperation/marketbg.jpg');
    .content {
      width: 700px;
      margin: 30px auto;
      display: flex;
      justify-content: space-around;
    }
    .item {
      text-align: center;
      .desc-1 {
        margin: 18px auto;
        color: #fff;
        font-size: 20px;
      }
      .desc-2 {
        color: #fff;
      }
    }
  }
}
.section-6 {
  background: #fbfbfb;
  .container {
    padding: 5rem 10rem;
  }
  .use-demo {
    float: left;
    h2 {
      text-align: left;
    }
    p {
      font-size: 14px;
      margin: 15px 0;
    }
    .demo-btn {
      color: #00aa64;
      font-size: 14px;
      float: left;
      cursor: pointer;
    }
    .iconfont {
      font-size: 14px;
      cursor: pointer;
    }
  }
}

@media (max-width: 767px) {
  .market-cooperation {
    .section-1 {
      .container {
        width: 100%;
        top: 50px;
      }
      .img-wrap {
        height: 78vw;
      }
      .article-headline {
        font-size: 28px;
        font-weight: 400;
      }
      .article-subtitle {
        font-size: 16px;
        line-height: 24px;
      }
    }
    .nav-bar .fixed-bar {
      top: 55px;
    }
    .section-2 {
      .container {
        padding: 0 2%;
      }
      .is-tiny {
        padding: 0;
        .container {
          padding: 0;
        }
      }
      background-color: #fafbfc;
      .card-wrapper {
        margin-top: 0;
      }
      .card {
        width: 100%;
        height: 330px;
        margin: 0 auto;
        margin-bottom: 20px;
        .image {
          height: 158px;
        }
      }
      .show-more {
        margin-top: 0;
      }
    }
    .section-3 {
      background-color: #fff;
      .img-wrap {
        margin-top: 0;
      }
    }
    .section-4 {
      .content {
        width: 100%;
        margin: 50px auto;
      }
    }
    .section-6 {
      .container {
        padding: 2rem 5%;
        .use-demo {
          text-align: center;
          padding-bottom: 2rem;
          h2 {
            text-align: center;
          }
          .demo-btn {
            float: initial;
          }
        }
        img {
          width: 100%;
        }
      }
    }
  }
}
// @media (max-width: 1440px) {
//   .market-cooperation .section-2 {
//     .card {
//       height: 380px;
//       .image {
//         height: 158px;
//       }
//     }
//   }
// }
</style>
