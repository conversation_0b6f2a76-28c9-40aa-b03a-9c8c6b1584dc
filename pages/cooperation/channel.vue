<template>
  <article class="operation-channel">
    <section class="section section-1">
      <div class="container">
        <div class="section-content">
          <h1 class="article-headline">企业效率革命，<br/>从电子签约开始。</h1>
          <p class="article-subtitle">立即加入上上签电子签约生态合作伙伴，为客户构建高效的电子签约解决方案，携手共赢。</p>
        </div>
      </div>
      <div class="img-wrap">
        <!-- <img v-if="!isMobile" src="@/assets/images/cooperation/channel-banner.jpg">
        <img v-if="isMobile" src="@/assets/images/cooperation/<EMAIL>"> -->
      </div>
      <!-- <nav class="nav" v-if="!isMobile">
        <div class="item-wrap">
          <div
            class="item"
            @click="handleTab('.section-2')"
          >合作优势</div>
          <div
            class="item"
            @click="handleTab('.section-3')">战略合作伙伴</div>
          <div
            class="item"
            @click="handleTab('.section-4')">伙伴权益</div>
          <div
            class="item"
            @click="handleTab('.section-5')">合作条件及流程</div>
        </div>
      </nav> -->
    </section>
	<section class="nav-bar">
		<div :class="['abstract industry',fixedTab?'fixed-bar':'']" ref="childNav">
			<ul>
				<nuxt-link
					:class="{'is-active': activeNav === 'channel'}"
					to="/cooperation/channel">
					<li>渠道合作</li>
					</nuxt-link>
				<nuxt-link
					:class="{'is-active': activeNav === 'market'}"
					to="/cooperation/market">
					<li>市场合作</li>
				</nuxt-link>
			</ul>
		</div>
	</section>
    <section class="section section-2">
      <div class="container">
        <h3 class="section-headline">我们的优势</h3>
        <div class="content-wrap">
          <div class="item">
            <div class="iconfont icon-gaopinzhichanpinfuwu"></div>
            <div class="title">高品质产品与服务</div>
            <div class="para">千万级单日签约量，毫秒级接口反馈速度，银行级产品安全性，70%+NPS客户推荐值，确保伙伴与客户无忧。</div>
          </div>
          <div class="item">
            <div class="iconfont icon-xiangmujishixiangying"></div>
            <div class="title">项目支持即时响应</div>
            <div class="para">7×24小时在线服务，专属售前工程师对点服务，免费开放二十余省项目团队调用权限，确保伙伴与客户需求得到即时响应。</div>
          </div>
          <div class="item">
            <div class="iconfont icon-chengshuyingyongfangan"></div>
            <div class="title">全行业成熟应用方案</div>
            <div class="para">五十余类成熟应用方案，各行业标杆客户使用案例，多家知名律所携手，多地公证处、仲裁委提供司法支持，助力伙伴业务拓展。</div>
          </div>
        </div>
      </div>
    </section>
    <Cooperation class="section-3"></Cooperation>
    <section class="section section-4">
      <div class="container">
        <h3 class="section-headline">合作伙伴权益</h3>
        <div class="content-wrap">
          <div class="item">
            <div class="icon"></div>
			<div class="wrap-body">
				<div class="title">丰厚利润</div>
            	<div class="para">提供多种合作模式，伙伴可根据自身情况，灵活选择，并获取对应的高额分润，确保伙伴收益。</div>
			</div>
          </div>
          <div class="item">
            <div class="icon "></div>
			<div class="wrap-body">
				<div class="title">品牌赋能</div>
				<div class="para">为合作伙伴认证并颁发授权书，为伙伴的每个项目提供专属售后服务函，确保伙伴共享上上签的品牌价值。</div>
			</div>
		  </div>
          <div class="item">
            <div class="icon"></div>
			<div class="wrap-body">
				<div class="title">支持配套</div>
				<div class="para">开放六大办事处、二十余省项目团队资源，供伙伴免费调配；提供丰富与实时更新的培训课程与DEMO账户，并配备专属售前工程师对点服务；提供定期项目复盘支持与案例分享。保障伙伴的后勤完备</div>
			</div>
		  </div>
          <div class="item">
            <div class="icon"></div>
			<div class="wrap-body">
				<div class="title">秩序规范</div>
				<div class="para">价格体系统一，报备机制严明，为合作伙伴营造良好的生态环境。</div>
          	</div>
		  </div>
        </div>
      </div>
    </section>
    <section class="section section-5">
      <div class="container">
        <h3 class="section-headline">合作条件及流程</h3>
        <div class="content-wrap">
          <div class="img-wrap">
				<ul>
					<li>
						<i class="iconfont icon-hezuozixun"></i>
						<p>合作咨询</p>
					</li>
					<li>
						<i class="iconfont icon-zizhipinggu"></i>
						<p>资质评估</p>
					</li>
					<li>
						<i class="iconfont icon-hezuoqiatan"></i>
						<p>合作洽谈</p>
					</li>
					<li>
						<i class="iconfont icon-xieyiqianding"></i>
						<p>协议签订</p>
					</li>
					<li>
						<i class="iconfont icon-zhichipeitao"></i>
						<p>支持配套</p>
					</li>
					<li>
						<i class="iconfont icon-yewukaizhan"></i>
						<p>业务开展</p>
					</li>
				
				</ul>
          </div>
		 <div class="step-warp" v-if="!isMobile">
			<el-steps  align-center>
				<el-step title=""></el-step>
				<el-step title=""></el-step>
				<el-step title=""></el-step>
				<el-step title=""></el-step>
				<el-step title=""></el-step>
				<el-step title=""></el-step>
			</el-steps>
		 </div>
        </div>
        <p class="concat-us">联系我们</p>
        <div class="email"><EMAIL></div>
      </div>
    </section>
  </article>
</template>

<script>
import Cooperation from '@/components/Partner.vue';
import Throttle from '@/assets/utils/throttle';
export default {
  name: 'Channel',
  layout: 'layout_cooperation',
  components: {
    Cooperation,
  },
  head() {
    return {
      title: '渠道合作_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同合作伙伴,电子合同合作权益,电子合同合作优势',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '成为上上签签约伙伴，帮你构建有效的电子签约解决方案，了解合作伙伴权益以及合作流程。',
        },
      ],
    };
  },
  data() {
    return {
      tabsHeight: '',
      fixedTab: false,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    activeNav() {
      return this.$store.state.path;
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: 'channel' });
    if (!this.isMobile) {
      this.tabsHeight = this.$refs.childNav.offsetTop;
      window.addEventListener('scroll', Throttle(this.handleScroll, 100));
    }
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    handleTab(element) {
      this.$scrollTo(element);
    },
    handleDemo() {
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          id: this.$route.query.id,
          utm_source: this.$route.query.utm_source,
          hp_right: '',
        },
      });
    },
    handleScroll() {
      const scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      if (scrollTop > this.tabsHeight) {
        this.fixedTab = true;
      } else {
        this.fixedTab = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.operation-channel {
  text-align: center;
  .section-1 {
    position: relative;
    padding: 0;
    color: #fff;
    .nav-section {
      color: #fff;
      position: absolute;
      display: flex;
      min-width: 1200px;
      left: 50%;
      transform: translate(-50%, 0);
      justify-content: space-between;
      border-bottom: 1px solid #eee;
      .operation-all {
        padding: 10px 15px 10px 0;
        color: #fff;
      }
      .operation-caterage {
        display: flex;
        color: #eee;
        .operation-item {
          padding: 10px 15px;
          a {
            color: #fff;
          }
          .nuxt-link-active {
            color: #e5e5e5;
          }
        }
        .operation-item:last-child {
          padding-right: 0;
        }
      }
    }
    .img-wrap,
    img {
      width: 100%;
      height: 100%;
    }
    .img-wrap {
      background: url('~assets/images/cooperation/channel-banner.jpg');
      background-size: cover;
      height: 29vw;
    }
    .article-subtitle,
    .article-headline {
      color: #fff;
    }
    .article-headline {
      font-weight: 500;
      color: #fff;
      font-size: 2.2rem;
    }
    .article-subtitle {
      font-size: 1.2rem;
      line-height: 1.7;
      font-weight: 400;
      margin-bottom: 1.5rem;
      padding: 6px 10px;
      color: #fff;
    }
    .section-content {
      width: 100%;
      position: absolute;
      top: 6rem;
      left: 50%;
      transform: translate3d(-50%, 0, 0);
    }
    .nav {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 67px;
      line-height: 67px;
      background-color: rgba(0, 0, 0, 0.2);
      .item-wrap {
        width: 1080px;
        margin: 0 auto;
        display: flex;
        text-align: center;
        color: #fff;
        font-size: 18px;
        .item {
          flex: 1;
          cursor: pointer;
        }
        .item.is-active,
        .item:hover {
          background-color: rgba(0, 0, 0, 0.4);
        }
      }
    }
  }
  .nav-bar {
    .abstract {
      display: flex;
      justify-content: center;
      font-size: 16px;
      margin-bottom: 30px;
      margin: 0 auto;
      max-width: 1100px;
      border-bottom: 1px solid #eee;
      a:hover {
        border-bottom: 2px solid #00aa64;
        color: #00aa64;
      }
      ul {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
      }
      li {
        cursor: pointer;
        padding: 20px 18px;
        color: #6e6e6e;
        text-align: center;
        font-size: 1rem;
        &:hover {
          color: #00a664;
        }
      }
      .nuxt-link-exact-active {
        border-bottom: 2px solid #00aa64;
        a {
          color: #00aa64;
        }
      }
    }
    .fixed-bar {
      position: fixed;
      z-index: 20;
      top: 76px;
      background: #fff;
      box-shadow: 0 2px 20px 0 rgba(0, 39, 123, 0.13);
      width: 100%;
      left: 0;
      padding: 0;
      transition: all 0.2s ease;
      max-width: 100%;
      .abstract {
        border-bottom: none;
        margin-bottom: 0;
        margin: 0 auto;
      }
    }
  }

  .section-2 {
    .container {
      max-width: 1344px;
    }
    .content-wrap {
      display: flex;
      justify-content: space-around;
      .item {
        text-align: center;
        background: #fafafa;
        padding: 60px 50px;
        box-sizing: border-box;
        cursor: pointer;
        width: 32.66%;
        .title {
          margin: 0.75rem auto 1.75rem;
          font-size: 20px;
          line-height: 1.5;
          color: #1d1d1f;
          font-weight: 400;
        }
        .para {
          line-height: 1.5;
          color: #86868b;
          text-align: center;
        }
        .iconfont {
          margin: 0 auto;
          font-size: 55px;
          color: #00aa64;
          padding-bottom: 2rem;
        }
      }
    }
  }
  .section-4 {
    background: #f8f8f8;
    .content-wrap {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      .item {
        // width: 46%;
        text-align: center;
        margin: 13px;
        background: #fff;
        .wrap-body {
          padding: 0 60px 60px;
        }
        .title {
          text-align: left;
          padding: 35px 0 20px 0;
          color: #1d1d1f;
          font-size: 1.6rem;
          font-weight: 400;
        }
        .para {
          max-width: 320px;
          line-height: 1.5;
          color: #86868b;
          text-align: left;
        }
        .icon {
          margin: 0 auto;
          width: 100%;
          height: 17rem;
          background-size: 100% 100%;
        }
      }
      .item:nth-child(1) .icon {
        background-image: url('~assets/images/cooperation/quanyi1.jpg');
      }
      .item:nth-child(2) .icon {
        background-image: url('~assets/images/cooperation/quanyi2.jpg');
      }
      .item:nth-child(3) .icon {
        background-image: url('~assets/images/cooperation/quanyi3.jpg');
      }
      .item:nth-child(4) .icon {
        background-image: url('~assets/images/cooperation/quanyi4.jpg');
      }
    }
  }
  .section-5 {
    background-color: #fff;
    .content-wrap {
      position: relative;
      margin: 0 auto 110px;
      width: 940px;
      .img-wrap {
        width: 100%;
        ul {
          display: flex;
          justify-content: center;
          .iconfont {
            font-size: 26px;
          }
          li {
            margin: 0 30px;

            p {
              margin: 20px 0;
              font-size: 16px;
              color: #86868b;
            }
          }
        }
        img {
          width: 80%;
        }
      }
      .step-warp {
        width: 80%;
        margin: 20px auto;
        /deep/ .el-step__line {
          background-color: #3f3f3f;
          top: 9px;
        }
        /deep/ .el-step__icon {
          width: 10px;
          height: 10px;
          border-color: #3f3f3f;
        }
        /deep/ .el-step__icon-inner {
          visibility: hidden;
        }
      }
    }
    .concat-us {
      color: #090909;
      font-size: 2rem;
      line-height: 2.5rem;
      font-weight: 400;
    }
    .email {
      margin-top: 30px;
      font-size: 14px;
      color: #86868b;
    }
  }
  .section-6 {
    background: #fbfbfb;
    padding-top: 5rem;
    .container {
      padding: 0 10rem;
    }
    .use-demo {
      float: left;
      h2 {
        text-align: left;
        color: #1d1d1f;
        font-size: 1.6rem;
        font-weight: 400;
      }
      p {
        font-size: 16px;
        margin: 15px 0;
        line-height: 1.5;
        color: #86868b;
      }
      .demo-btn {
        color: #00aa64;
        font-size: 14px;
        float: left;
        cursor: pointer;
      }
      .iconfont {
        font-size: 14px;
        cursor: pointer;
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .operation-channel {
    .section-1 {
      position: relative;
      padding: 0;
      color: #333;
      .article-headline {
        font-size: 28px;
        color: #fff;
        font-weight: 500;
      }
      .article-subtitle {
        font-size: 14px;
        color: #fff;
        padding: 0 20px;
      }
      .section-content {
        width: 100%;
        top: 50px;
      }
      .img-wrap {
        height: 78vw;
      }
    }
    .nav-bar .fixed-bar {
      top: 55px;
    }
    .section-2 {
      padding-bottom: 0;
      .container {
        padding: 0 2%;
      }
      .content-wrap {
        margin-top: 0px;
        display: flex;
        flex-wrap: wrap;
        .item {
          margin-bottom: 40px;
          width: 100%;
          padding: 40px 30px;
          .title {
            margin: 0px auto 24px;
            font-size: 18px;
          }
          .para {
            font-size: 14px;
            color: #86868b;
          }
          .icon {
            transform: scale(0.8);
          }
        }
      }
    }
    .section-4 {
      .content-wrap {
        margin-top: 0px;
        display: block;
        .item {
          width: 100%;
          margin: 0px auto 40px;
          .wrap-body {
            padding: 0 30px 40px;
            .title {
              margin: 20px auto;
              padding: 0;
              font-size: 18px;
            }
            .para {
              font-size: 14px;
              color: #86868b;
            }
          }
        }
      }
    }
    .section-5 {
      .container {
        .content-wrap {
          margin: 50px auto;
          width: 100%;
          .img-wrap {
            display: block;
            width: 100%;
            ul {
              display: flex;
              flex-wrap: wrap;
              li {
                margin: 0 20px;
                p {
                  font-size: 16px;
                  color: #86868b;
                }
              }
            }
          }
        }
      }
    }
    .section-6 {
      .container {
        padding: 0 5%;
        .use-demo {
          text-align: center;
          padding-bottom: 2rem;
          h2 {
            text-align: center;
          }
          .demo-btn {
            float: initial;
          }
        }
        img {
          width: 100%;
        }
      }
    }
  }
}
</style>
