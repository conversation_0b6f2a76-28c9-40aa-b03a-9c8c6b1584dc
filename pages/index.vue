<template>
<div>
  <article class="homepage">
    <index-banner></index-banner>
    <use-number></use-number>
    <logo></logo>
    <description v-if="!isEN"></description>
    <en-solution v-if="isEN"></en-solution>
    <evaluate></evaluate>
    <index-reason></index-reason>
    <index-introduction :bigTitle="bigTitle" :isShow="false" v-if="!isEN"></index-introduction>
    <answer v-if="!isEN"></answer>
    
    <!-- <product-value id="jpValue"></product-value>
    
    <index-solution></index-solution>
    <sign-process id="jpSign"></sign-process>
    <index-usage :isWhiteBg="isMobile" id="jpUse"></index-usage>
    <index-safe></index-safe>    
    <index-question id="jpHelp"></index-question> -->
  </article>
</div>
</template>

<script>
import IndexBanner from '@/components/Index/Banner.vue';
import Evaluate from '@/components/Index/Evaluate.vue';
import Logo from '@/components/Index/logo500.vue';
import UseNumber from '@/components/Index/useNumber.vue';
import Description from '@/components/Index/description.vue';
import IndexReason from '@/components/Index/Reason.vue';
import IndexIntroduction from '@/pages/cost/components/introduction.vue';
import Answer from '@/pages/cost/components/answersBox.vue';
import IndexUsage from '@/components/Index/useJp.vue';
import IndexSolution from '@/components/Index/Solution.vue';
import IndexQuestion from '@/components/Index/Question.vue';
import moment from 'moment';
import ProductValue from '@/components/Index/productValue.vue';
import SignProcess from '@/components/Index/signProcess.vue';
import IndexSafe from '@/components/Index/SafeReliable.vue';
import EnSolution from '../components/Index/enSolution.vue';

export default {
  name: 'homepage',
  components: {
    IndexBanner,
    Logo,
    UseNumber,
    Description,
    Evaluate,
    IndexReason,
    IndexIntroduction,
    IndexUsage,
    IndexSolution,
    ProductValue,
    SignProcess,
    IndexSafe,
    IndexQuestion,
    Answer,
    EnSolution,
  },
  head() {
    return {
      title: this.$t('homepageTDK.title'),
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.$t('homepageTDK.keyword'),
        },
        {
          hid: 'description',
          name: 'description',
          content: this.$t('homepageTDK.description'),
        },
      ],
    };
  },
  data() {
    return {
      bigTitle: this.$t('reason.bigTitle'),
      isEN: true,
    };
  },

  // async fetch({ app, store, params }) {
  //   const link = await app.$axios.get('/www/api/web/getFriendLink');
  //   const recent = await app.$axios.get(
  //     '/www/api/web/getDynamic?pageNum=1&pageSize=4'
  //   );
  //   store.commit('setFriendLink', link);
  //   store.commit('setRecentNews', recent);
  //   // console.log(111);
  //   // debugger;
  // },

  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    // friendLink() {
    //   return this.$store.state.friendLink;
    // },
    recentNews() {
      return this.$store.state.recentNews;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: '' });
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
  created() {},
};
</script>
<style scoped lang="scss">
.homepage {
  text-align: center;
  //   padding-top: 76px;
  .section-1 {
    position: relative;
    padding: 0;
    width: 100%;
    height: 35vw;
    background-size: 100% 115%;
    background-position-y: -5vw;
    background-repeat: no-repeat;
    &.carousel {
      position: relative;
      .container {
        height: 100%;
        display: flex;
        align-items: center;
        text-align: left;
        max-width: 1180px;
        &.auto {
          height: auto;
          background-color: rgba(0, 0, 0, 0.7);
        }
        .main-text {
          padding-left: 20px;
          h3 {
            font-size: 36px;
            font-weight: 400;
            margin-bottom: 20px;
          }
          h4 {
            font-size: 16px;
            line-height: 26px;
            color: #626262;
            max-width: 480px;
            font-weight: 300;
            margin-bottom: 45px;
          }
          .ssq-button-primary {
            background: #00aa64;
          }
          .tips {
            color: #00aa64;
            font-size: 18px;
            margin-top: 30px;
            font-weight: 400;
          }
        }
      }
    }
    .ssq-button-primary {
      img {
        margin-left: 5px;
        width: 20px;
        vertical-align: bottom;
      }
    }
  }
  .section-2 {
    .image-link {
      padding: 0 10px;
      max-width: 600px;
      img {
        width: 100%;
      }
      &:first-child {
        padding-left: 0;
      }
      &:last-child {
        padding-right: 0;
      }
    }
  }
}

@media screen and (max-width: 767px) {
  .homepage {
    // padding-top: 56px;
    .headline--main {
      font-size: 1.75rem;
    }
    .section-2 {
      padding-top: 10px;
      .image-link {
        flex: 1;
        width: 33.3%;
        padding: 0 5px;
      }
    }
  }
}
</style>
