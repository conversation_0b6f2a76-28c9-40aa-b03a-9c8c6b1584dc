<template>
  <article>
  <bread-nav :menu="menu"></bread-nav>
    <section class="section section-1">
      <div class="container">
        <div class="article">
          <h1 class="bigTitle" :class="{'bigTitle-en':isEN}">{{ article.customerName }}</h1>
          <!-- <div class="text-tag">
            <div v-for="(item, index) in  article.altList" :key="index" class="text-tag-item">
              <div>{{item}}</div>
            </div>
          </div> -->
          <div class="imgContainer">
            <img :src="article.contentImgUrl" alt="">
          </div>
          <div class="imgLogo">
            <img :src="article.logoImgUrl" alt="">
          </div>
          <div class="descContainer" :class="{'descContainer-en':isEN}">
            <div class="title">{{$t('case.companyDesc')}}</div>
            <div class="content">{{article.companyDsec}}</div>
          </div>
          <div class="descContainer" :class="{'descContainer-en':isEN}">
            <div class="title">{{$t('case.effect')}}</div>
            <div class="little-title">{{article.resultTitle1}}</div>
            <div class="content">{{article.result1}}</div>
            <div class="content">{{article.result11}}</div>
            <div class="little-title">{{article.resultTitle2}}</div>
            <div class="content">{{article.result2}}</div>
            <div class="little-title">{{article.resultTitle3}}</div>
            <div class="content">{{article.result3}}</div>
            <div class="little-title">{{article.resultTitle4}}</div>
            <div class="content">{{article.result4}}</div>
          </div>
        </div>
      </div>
    </section>
  </article>
</template>

<script>
import moment from 'moment';
const getJsonEn = () =>
  import('@/static/_caseEN.json').then(m => m.default || m);
const getJson = () => import('@/static/_case.json').then(m => m.default || m);
const getJsonJP = () =>
  import('@/static/_caseJP.json').then(m => m.default || m);
export default {
  name: 'CaseDetail',
  components: {},
  head() {
    return {
      title: this.article.customerName + this.$t('caseTDK.detailTitle'),
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.$t('caseTDK.keyword'),
        },
        {
          hid: 'description',
          name: 'description',
          content: this.$t('caseTDK.description'),
        },
      ],
    };
  },
  async asyncData({ route, app, redirect, store, context }) {
    const id = route.params.id;
    if (store.state.locale.indexOf('en') != -1) {
      var caseResponse = await getJsonEn();
    } else if (store.state.locale === 'ja') {
      var caseResponse = await getJsonJP();
    } else {
      var caseResponse = await getJson();
    }
    var shownContentDataP1 = caseResponse.filter(item => item.pageNum === 1)[0]
      .data;
    var shownContentDataP2 = caseResponse.filter(item => item.pageNum === 2)[0]
      .data;
    var shownContentData = shownContentDataP1.concat(shownContentDataP2);
    var content = shownContentData.filter(item => item.id == id);
    const res = content[0];
    const menu = [
      {
        name: store.state.locale.indexOf('en') != -1 ? 'Cases' : 'お客様事例',
        to: '/case',
      },
      {
        name: res.customerName,
        to: '',
      },
    ];
    return {
      article: res,
      releaseTime: res.releaseTime,
      tdkT: res.tdkT,
      tdkD: res.tdkD,
      tdkK: res.tdkK,
      menu,
    };
  },
  data() {
    return {
      isEN: false,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  async created() {
    try {
      const id = this.$nuxt.$route.params.id;
      if (this.$store.state.locale.indexOf('en') != -1) {
        var caseResponse = await getJsonEn();
      } else if (this.$store.state.locale === 'ja') {
        var caseResponse = await getJsonJP();
      } else {
        var caseResponse = await getJson();
      }
      var shownContentDataP1 = caseResponse.filter(
        item => item.pageNum === 1
      )[0].data;
      var shownContentDataP2 = caseResponse.filter(
        item => item.pageNum === 2
      )[0].data;
      var shownContentData = shownContentDataP1.concat(shownContentDataP2);
      var content = shownContentData.filter(item => item.id == id);
      const res = content[0];
      this.menu = [
        {
          name: this.isEN ? 'Cases' : 'お客様事例',
          to: '/case',
        },
        {
          name: res.customerName,
          to: '',
        },
      ];
      this.article = res;
      this.releaseTime = res.releaseTime;
      this.tdkT = res.tdkT;
      this.tdkD = res.tdkD;
      this.tdkK = res.tdkK;
    } catch (error) {
      console.error('Error fetching JSON data:', error);
    }
  },
  mounted() {
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
  methods: {
    formatDate(date) {
      // 把"-"用"/"替换，解决Safari浏览器的时间变为NAN
      let time = new Date(date.replace(new RegExp(/-/gm), '/'));
      // let time = new Date(date);
      return (
        time.getFullYear() +
        '-' +
        ('0' + (time.getMonth() + 1)).substr(-2) +
        '-' +
        // time.getDate()
        ('0' + time.getDate()).substr(-2)
      );
      // return moment(date).format('YYYY-MM-DD');
      // 此处一句代替了上面的好多句
    },
    formatUrl(url) {
      const arr = url && url.split('?');
      return url && arr[0];
    },
    industryType(id) {
      const item = this.industryList.find(o => o.code === id);
      return item && item.codeValue;
    },
    shareWb() {},
  },
};
</script>

<style scoped lang="scss">
@import '@/assets/css/article.scss';
.nav-bar {
  border-bottom: 1px solid #eee;
}
.video {
  height: 100%;
}
.abstract {
  display: flex;
  justify-content: space-between;
  font-size: 16px;
  margin-bottom: 30px;
  margin: 0 auto;
  max-width: 1200px;
  ul {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
  }
  li {
    cursor: pointer;
    padding: 20px 30px;
    color: #333;
    &:hover {
      color: #9a9999;
    }
    &.active {
      color: #eee;
    }
  }
  li:last-child {
    padding-right: 0;
  }
}
.all-case {
  padding: 20px 30px;
  color: #333;
  display: flex;
  align-items: center;
  &:hover {
    color: #9a9999;
  }
}
.section-1 {
  background-color: #fafbfc;
  padding: 0;
  a {
    color: #323232;
    &:hover {
      color: #5f5f5f;
    }
  }
  .bottom-card {
    max-width: 70.75rem;
    margin: 0 auto;
  }
  nav {
    margin-top: 25px;
    position: relative;
    text-align: center;
    .card {
      height: 22.5rem;
      padding-bottom: 20px;
      box-shadow: 0 0px 5px 0 #cccbcb;
    }
    .card-image {
      width: 50%;
      height: auto;
      padding-top: 20px;
      border-top: 1px solid #eee;
      margin: 2.5rem 0 10px;
      //   -webkit-filter: grayscale(1);
      //   filter: grayscale(1);
    }
    // .el-col:hover .card-image {
    //   -webkit-filter: grayscale(0);
    //   filter: grayscale(0);
    // }
    .card-content {
      text-align: center;
      height: auto;
      // padding-bottom: 60px;
      padding: 1rem;
      .header {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        font-size: 1.4rem;
        line-height: 2.25rem;
        margin: 4rem auto 1rem;
      }
      .body {
        // overflow: hidden;
        // text-overflow: ellipsis;
        // display: -webkit-box;
        // -webkit-line-clamp: 2;
        // -webkit-box-orient: vertical;
        // text-align: justify;
        // word-break: break-all;
        // line-height: 1.25rem;
        // padding: 0 30px;
        // height: 50px;
        overflow: hidden;
        text-overflow: ellipsis;
        /* autoprefixer: off */
        -webkit-box-orient: vertical;
        display: -webkit-box;
        /* autoprefixer: on */
        -webkit-line-clamp: 3;
        text-align: justify;
        word-break: break-all;
        line-height: 1.25rem;
        padding: 0 30px;
        height: 3.75rem;
      }
      .footer {
        align-self: flex-end;
        a {
          color: #00a664;
        }
      }
    }
    .el-row {
      display: flex;
      // justify-content: space-around;
      flex-wrap: wrap;
    }
    .nav-item {
      cursor: pointer;
      padding: 10px 0;
      &:hover {
        color: #00a664;
      }
    }
    .change-card {
      color: #00a664;
    }
    .back {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 10px;
      .el-icon-arrow-left {
        padding-right: 5px;
        font-size: 22px;
        position: relative;
        top: 2px;
      }
      &:hover {
        color: #00a664;
      }
    }
  }
  .article-change {
    text-align: left;
  }
}
@media screen and (max-width: 767px) {
  .section-1 {
    .container {
      .bigTitle {
        padding: 3rem 10%;
      }
      .bigTitle-en {
        font-weight: 600;
      }
      .imgContainer {
        img {
          max-width: 100%;
        }
      }
      .descContainer {
        .title {
          font-size: 1.5rem;
        }
        .little-title {
          font-size: 1.2rem;
          line-height: 1.5;
        }
        .content {
          font-size: 1.3rem;
        }
      }
      .article {
        h1 {
          font-weight: 600;
        }
      }
    }
  }
}
</style>
