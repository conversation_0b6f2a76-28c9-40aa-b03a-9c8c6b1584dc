<template>
  <article :class="{ 'case__isMobile': isMobile }">
	<section class="banner">
		<div class="container">
			<h1 class="section-headline headline--main" :class="{'section-headline-en':isEN}">{{$t('case.title')}}</h1>
		</div>
	</section>
    <section class="section section-1 is-tiny">
      <div class="container">
        <el-row :gutter="1" justify="center">
          <template v-for="(item, index) in shownList">
            <el-col
              :key="index"
              :md="8"
              :lg="8"
              :sm="8"
            >
              <nuxt-link class="card"   :to="`/${language}/case/detail/${item.id}`">
                <div>
                  <div class="img-footer">
                    <img
                      :key="item.newImgUrl"
                      v-lazy="formatUrl(item.newImgUrl)"
                      class="card-image">
				          </div>
                  <div class="card-content" :class="{'card-content-en':isEN}">
                    <h4 class="header">{{ item.customerName }}</h4>
                    <p class="body">{{ item.companyDsec }}</p>
                    <!-- <div class="text-tag" v-if="item.altList && item.altList.length">
                      <div v-for="(item, index) in item.altList" :key="index" class="text-tag-item">
                        <div>{{item}}</div>
                      </div>
                    </div> -->
                  </div>
                  
                </div>
              </nuxt-link>
            </el-col>
          </template>
        </el-row>
        <div class="button-wrap">
          <el-pagination
		     background
            :page-size="9"
            :current-page="pageNum"
            :total="totalNum"
            @current-change="handlePageChange"
            :pager-count="5"
            layout="prev, pager, next">
			</el-pagination>
        </div>
      </div>
    </section>
  </article>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import Qs from 'qs';
import moment from 'moment';
import Throttle from '@/assets/utils/throttle';
const getJsonEn = () =>
  import('@/static/_caseEN.json').then(m => m.default || m);
const getJson = () => import('@/static/_case.json').then(m => m.default || m);
const getJsonJP = () =>
  import('@/static/_caseJP.json').then(m => m.default || m);

export default {
  name: 'caseIndex',
  head() {
    return {
      title: this.$t('caseTDK.title'),
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.$t('caseTDK.keyword'),
        },
        {
          hid: 'description',
          name: 'description',
          content: this.$t('caseTDK.description'),
        },
      ],
    };
  },
  data() {
    return {
      altList: ['上上签', '电子签名'],
      tabsHeight: '',
      fixedTab: false,
      isEN: false,
    };
  },
  //服务端发起的异步请求
  async asyncData({ app, params, route, store }) {
    const page = route.path.split('p')[1];
    if (store.state.locale.indexOf('en') != -1) {
      var caseResponse = await getJsonEn();
    } else if (store.state.locale === 'ja') {
      var caseResponse = await getJsonJP();
    } else {
      var caseResponse = await getJson();
    }
    if (route.path.indexOf('p') == -1) {
      var shownListFilter = caseResponse.filter(item => item.pageNum === 1);
      var caseResponseShow = caseResponse.filter(item => item.pageNum === 1);
    } else {
      var shownListFilter = caseResponse.filter(item => item.pageNum === 2);
      var caseResponseShow = caseResponse.filter(item => item.pageNum === 2);
    }
    const shownList = shownListFilter[0].data;
    const totalNum = caseResponseShow[0].totalNum;
    const pageNum = caseResponseShow[0].pageNum;
    return {
      shownList,
      totalNum,
      pageNum,
    };
  },
  computed: {
    ...mapState(['isMobile']),
    language() {
      return this.$store.state.locale;
    },
  },
  mounted() {},
  methods: {
    ...mapActions(['setViewTimes']),
    format(time) {
      return moment(time).format('YYYY-MM-DD');
    },
    formatUrl(url) {
      const arr = url && url.split('?');
      return url && arr[0];
    },
    releaseTime(time) {
      return moment(time).format('YYYY-MM-DD');
    },
    handlePageChange(page) {
      this.pageNum = page;
      const query = sessionStorage.getItem('query');
      if (page === 1) {
        this.$router.push({
          path: `/${this.language}/case`,
          query: {
            ...Qs.parse(query),
          },
        });
      } else {
        this.$router.push({
          path: `/${this.language}/case-p${page}`,
          query: {
            ...Qs.parse(query),
          },
        });
      }
    },
  },
  async created() {
    try {
      const page = this.$nuxt.$route.path.split('p')[1];
      if (this.$store.state.locale.indexOf('en') != -1) {
        var caseResponse = await this.$axios.get('/_caseEN.json');
      } else if (this.$store.state.locale === 'ja') {
        var caseResponse = await this.$axios.get('/_caseJP.json');
      } else {
        var caseResponse = await this.$axios.get('/_case.json');
      }
      if (this.$nuxt.$route.path.indexOf('p') == -1) {
        var shownListFilter = caseResponse.filter(item => item.pageNum === 1);
        var caseResponseShow = caseResponse.filter(item => item.pageNum === 1);
      } else {
        var shownListFilter = caseResponse.filter(item => item.pageNum === 2);
        var caseResponseShow = caseResponse.filter(item => item.pageNum === 2);
      }
      this.shownList = shownListFilter[0].data;
      this.totalNum = caseResponseShow[0].totalNum;
      this.pageNum = caseResponseShow[0].pageNum;
    } catch (error) {
      console.error('Error fetching JSON data:', error);
    }
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
};
</script>

<style scoped lang="scss">
.article-headline {
  padding: 5.625rem 0 3.125rem;
  text-align: center;
  font-size: 2.2rem;
  font-weight: 500;
  margin: 0;
  color: #090909;
}
.article-text {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.7;
  font-size: 18px;
}
.nav-bar {
  background: #f9f9f9;
  padding-top: 2rem;
  padding-bottom: 30px;
}
.abstract {
  display: flex;
  justify-content: center;
  font-size: 16px;
  border-bottom: 1px solid #eee;
  margin-bottom: 30px;
  margin: 0 auto;
  max-width: 1200px;
  .all {
    border-bottom: 2px solid #00aa64;
    color: #00aa64;
  }
  a:hover {
    // border-bottom: 2px solid #00aa64;
    color: #00aa64;
  }
  ul {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
  }
  li {
    cursor: pointer;
    padding: 20px 18px;
    color: #86868b;
    font-size: 1rem;
    text-align: center;
    &:hover {
      color: #00a664;
    }
  }
  //   li:last-child {
  //     padding-right: 0;
  //   }
  .nuxt-link-exact-active {
    // border-bottom: 1px solid #00aa64;
    li {
      color: #00aa64;
    }
  }
}
.fixed-bar {
  position: fixed;
  z-index: 20;
  top: 66px;
  background: #fff;
  box-shadow: 0 2px 20px 0 rgba(0, 39, 123, 0.13);
  width: 100%;
  padding: 0;
  transition: all 0.2s ease;
}
/deep/ .el-pagination.is-background .el-pager li:not(.disabled).active {
  background: #000;
  border-bottom: 2px solid #000;
}
.banner {
  background: #f5f5f7;
  //   position: relative;
  //   padding: 0;
  //   width: 100%;
  //   color: #fff;
  //   height: 41.67vw;
  //   background-size: 100% 100%;
  //   background-image: url('https://static.bestsign.cn/a1c00a4fffb55d2c5adf1f90a4fada1d650df998.jpg');
  //   background-repeat: no-repeat;
}

.section-1 {
  text-align: center;
  background: #f5f5f7;
  padding: 2rem 0 5rem;
  .container {
    max-width: 75.75rem;
  }
  a {
    color: #323232;
  }
  .card {
    width: 94%;
    height: auto;
    margin: 1rem 0;
    .card-image {
      width: 100%;
      height: 18.2rem;
    }
  }

  .el-col:hover .card-image {
    -webkit-filter: grayscale(0);
    filter: grayscale(0);
  }
  .card-content {
    text-align: center;
    height: auto;
    // padding-bottom: 40px;
    padding: 10px 30px 50px;
    .header {
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      font-size: 1.1rem;
      line-height: 2.25rem;
      font-weight: 500;
    }
    .body {
      // overflow: hidden;
      // text-overflow: ellipsis;
      // display: -webkit-box;
      // -webkit-line-clamp: 2;
      // -webkit-box-orient: vertical;
      // text-align: justify;
      // word-break: break-all;
      // line-height: 1.25rem;
      // padding: 0 30px;
      // height: 50px;
      overflow: hidden;
      text-overflow: ellipsis;
      /* autoprefixer: off */
      -webkit-box-orient: vertical;
      display: -webkit-box;
      /* autoprefixer: on */
      -webkit-line-clamp: 2;
      text-align: justify;
      word-break: break-all;
      line-height: 1.5;
      padding: 5px 0 0;
      color: #666;
      font-size: 0.9rem;
    }
    .text-tag {
      display: flex;
      margin-top: 0.5rem;
      .text-tag-item {
        margin: 20px 7px 7px 0;
        padding: 0.3rem 0.7rem;
        background: #f5f5f5;
        border-radius: 13px;
        font-size: 0.5rem;
        color: #767676;
      }
    }

    .footer {
      align-self: flex-end;
      a {
        color: #00a664;
      }
    }
  }
  .card-content-en {
    .header {
      font-weight: 600;
    }
    .body {
      font-size: 1.1rem;
    }
  }
  .img-footer {
    transform: scale(1);
    // margin: 2.5rem 0 10px;
    // .line {
    //   width: 50%;
    //   height: 1px;
    //   background: #bebebe;
    //   margin: 0 auto;
    // }
    img {
      // width: 50%;
      // height: 100%;
      // margin-top: 20px;
      //   -webkit-filter: grayscale(1);
      //   filter: grayscale(1);
    }
  }
}

.button-wrap {
  margin-top: 60px;
  text-align: center;
}
.case-btn {
  color: #00a664;
  border: 1px solid #00a664;
  background: #fff;
  transition: all 0.3s ease;
  &:hover {
    background: #00a664;
    color: #fff;
  }
}
@media (max-width: 768px) {
  .fixed-bar {
    top: 54px;
  }
  .content {
    margin-bottom: 72px;
  }
  .headline {
    font-size: 28px;
    text-align: center;
    max-width: 78.5%;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 64px;
  }
  .abstract {
    width: 100%;
    font-size: 14px;
    margin-left: auto;
    margin-right: auto;
    flex-wrap: wrap;
    a {
      min-width: 75px;
    }
    li {
      padding: 12px 6px;
      white-space: nowrap;
    }
  }

  .case {
    width: 84.5%;
    height: 312px;
    margin: 23px 0;
  }
  .case-btn {
    margin-top: 40px;
    background-color: #fff;
  }
  .section-1 {
    .container {
      padding: 0 2%;
    }
    .card-image {
      -webkit-filter: grayscale(0);
      filter: grayscale(0);
    }
  }
}
@media (max-width: 1200px) {
  .card {
    margin: 0.8rem auto;
  }
}
// 移动端样式覆盖
.case__isMobile {
  background: #fff;
  .article-headline {
    // padding: 116px 0 0;
  }
  .section-1 {
    .article-headline {
      color: #515151;
      font-size: 24px;
      font-weight: 200;
      margin: 0 40px 50px 40px;
    }
    .abstract {
      li {
        border: solid 1px #bfbfbf;
        height: 27px;
        line-height: 27px;
        border-radius: 13px;
        padding: 0 13px;
        color: #86868b;
        margin-right: 4px;
        margin-top: 10px;
        &.active {
          color: #ffffff;
          border: none;
          background-image: linear-gradient(60deg, #019b89, #06b9a5);
        }
      }
    }
    .card {
      height: auto;
      width: 100%;
      border-radius: 10px;
      .footer {
        align-self: center;
      }
      .card-image {
        // height: 22.8rem;
        height: auto;
      }
      .card-content {
        .header {
          font-size: 1.5rem;
        }
        .body {
          line-height: 2.2rem;
          color: #86868b;
          font-size: 1.3rem;
        }
      }
      img {
        -webkit-filter: grayscale(0);
        filter: grayscale(0);
      }
    }
  }
}
</style>
