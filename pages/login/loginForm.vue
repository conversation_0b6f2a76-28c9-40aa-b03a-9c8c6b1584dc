<template>
  <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="ruleForm login">

    <div class="title">
      <div class="left">立即登录</div>
      <div class="right"><a @click='toOwnRegegister'><span>马上注册</span><i class="iconfont icon-xiangyou<PERSON>ant<PERSON>"></i></a></div>
    </div>
    <!-- <el-form-item prop="fullName">
      <el-input v-model.trim="ruleForm.fullName" placeholder="请输入你的姓名"></el-input>
    </el-form-item> -->
    <div class="formTitle">手机号/邮箱</div>
    <el-form-item prop="account">
      <el-input v-model.trim="ruleForm.account"  placeholder="请输入你的手机号或邮箱"></el-input>
    </el-form-item>
    <div class="formTitle">验证码</div>
    <el-form-item prop="verifyCode">
      <el-input v-model.trim="ruleForm.verifyCode" placeholder="请输入6位验证码">
        <template slot="append">
          <count-down class="countDown code-sent" :clickedFn="send" :disabled="countDownDisabled" ref="btn" :second="60"></count-down>
        </template>
      </el-input>
    </el-form-item>
    <div v-if="showPictureVerCon" class="formTitle">图形验证码</div>
    <el-form-item v-if="showPictureVerCon" class="pictureVer-item" prop="imageCode">
      <el-input
        class="pictureVer"
        placeholder="请填写4位图形验证码"
        :maxlength="4"
        v-model.trim="ruleForm.imageCode"
      >
        <template slot="append">
          <PictureVerify
            class="form-pictureVerify"
            ref="pictureVerify"
            :imageKey="ruleForm.imageKey"
            @change-imageKey="changeImageKey"
          />
        </template>
      </el-input>
    </el-form-item>
    <!-- <el-form-item prop="contractSize" class="radio-box">
        <label style="margin-right:13px;">年合同签署量</label>
        <el-radio v-model="contractSize" label="0">0-999</el-radio>
        <el-radio v-model="contractSize" label="1">1000以上</el-radio>
    </el-form-item>
    <el-form-item prop="clueMail">
      <el-input v-model.trim="ruleForm.clueMail"  placeholder="请输入你的邮箱"></el-input>
    </el-form-item>
    <el-form-item prop="companyName">
      <el-input v-model.trim="ruleForm.companyName"  placeholder="请输入公司名称，我们为你提供企业级解决方案"></el-input>
    </el-form-item>
    <el-form-item prop="loginPassword">
      <el-input v-model.trim="ruleForm.loginPassword" type="password"  maxlength= 18 placeholder="请输入6-18位数字、大小写字母组成的密码"></el-input>
    </el-form-item> -->
    <!-- <div class="checkbox">
      <el-checkbox name="type" v-model="isChecked">注册即同意</el-checkbox>
      <a class="protocol" @click="protocolVisible = true">注册协议</a>
    </div> -->
    <div>
      <el-button type="primary" class="submit" @click="toLogin">提交</el-button>
    </div>
    <!-- <div class="tips">注：若注册遇到问题，请<a @click="toService">联系客服</a>，或拨打 ************</div> -->
    <!-- <div class="login">已有账号？<a class="protocol" @click="toLogin">立即登录</a></div> -->
    <!-- <el-dialog title="注册协议" :visible.sync="protocolVisible">
      <registration-protocol></registration-protocol>
    </el-dialog> -->
  </el-form>
</template>

<script>
import resRules from '@/assets/utils/regs.js';
import CountDown from '@/components/CountDown.vue';
import PictureVerify from '@/components/PictureVerify.vue';
import { isPhoneOrMail } from '@/assets/utils/reg.js';
import aes from '@/assets/utils/aes.js';
import Qs from 'qs';
import RegistrationProtocol from '../protocol/RegisterProtocol';

export default {
  name: 'LoginForm',
  components: {
    RegistrationProtocol,
    CountDown,
    PictureVerify,
  },
  props: {
    text: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      protocolVisible: false,
      loading: false,
      showPictureVerCon: false,
      countDownDisabled: false,
      codeDisabled: true,
      isChecked: false,
      ruleForm: {
        account: '',
        // clueMail: '',
        // fullName: '',
        // imageCode: '',
        // imageKey: '',
        verifyCode: '',
        verifyKey: '',
        // loginPassword: '',
        // registerSource: '',
      },
      contractSize: '0',
      rules: {
        account: [
          { required: true, message: '请输入手机号或邮箱', trigger: 'blur' },
          {
            pattern: resRules.userPhone | resRules.userEmail,
            message: '请输入正确的手机号或邮箱',
            trigger: 'blur',
          },
        ],
        verifyCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          {
            pattern: /^\d{6}.*$/,
            message: '请输入正确的验证码',
            trigger: 'blur',
          },
        ],
        imageCode: [
          { required: true, message: '请输入图形验证码', trigger: 'blur' },
          {
            pattern: /^\d{4}.*$/,
            message: '请输入正确的图形验证码',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  methods: {
    // 更改图形验证码
    changeImageKey(value) {
      this.codeDisabled = false;
      this.ruleForm.imageKey = value;
    },
    // 发送验证码
    send() {
      const { account, imageCode, imageKey } = this.ruleForm;
      this.$refs['ruleForm'].validateField(['account'], error => {
        if (error) {
          return false;
        }
        let headersObj = {};
        if (imageCode !== '' && imageKey !== '') {
          headersObj = {
            // 'Content-Type': 'application/json; charset=utf-8',
            additionalImgVerCode: JSON.stringify({
              imageCode,
              imageKey,
            }),
            'request-source': 'WEB',
          };
        }
        let host = '';
        if (process.env.NODE_ENV !== 'development') {
          host =
            process.env.baseUrl.indexOf('cn') > -1
              ? 'https://ent.bestsign.cn'
              : 'https://ent.bestsign.info';
        }
        // // let host = 'https://ent.bestsign.info';
        // if (process.env.baseUrl.indexOf('cn') > -1) {
        //   host = 'https://ent.bestsign.cn';
        // }
        this.$axios({
          url: `${host}/users/ignore/captcha/notice?encryptToken=${aes(
            'B010',
            account
          )}`,
          method: 'post',
          headers: headersObj,
          data: {
            code: 'B010',
            sendType: isPhoneOrMail(account) === 'phone' ? 'S' : 'E',
            target: account,
            imageCode,
            imageKey,
          },
        })
          .then(res => {
            if (res) {
              this.$MessageToast.success('发送成功！');
              this.countDownDisabled = true;
              setTimeout(this.sended, 0);
              this.ruleForm.verifyKey = res.value;
              this.codeDisabled = false;
            }
          })
          .catch(err => {
            const res = err.response.data;
            if (res.code === '902' || res.code === '100006') {
              if (this.showPictureVerCon) {
                setTimeout(() => {
                  this.$refs.pictureVerify.changeImg();
                }, 20);
              } else {
                this.showPictureVerCon = true;
              }
              if (res.message !== '图片验证码不能为空') {
                this.$MessageToast.error(res.message);
              } else {
                this.$MessageToast.error('请先填写图形验证码');
              }
            } else {
              this.$MessageToast.error(res.message);
            }
            this.$refs.btn.reset();
          });
      });
    },

    sended() {
      this.$refs.btn.run();
      this.countDownDisabled = false;
    },
    toLogin() {
      let host = '';
      if (process.env.NODE_ENV !== 'development') {
        host =
          process.env.baseUrl.indexOf('cn') > -1
            ? 'https://ent.bestsign.cn'
            : 'https://ent.bestsign.info';
      }
      const params = {
        ...this.ruleForm,
      };
      delete params.imageKey;
      delete params.imageCode;
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          this.$axios
            .post(`${host}/auth-center/user/login`, params)
            .then(res => {
              if (!res.hasOwnProperty('code')) {
                document.cookie = `access_token=${
                  res.access_token
                };refresh_token=${res.refresh_token}`;
                localStorage.setItem('access_token', res.access_token);
                const query = sessionStorage.getItem('query');
                // this.$router.push({
                //   path: `/${this.$route.query.pagePath}/${
                //     this.$route.query.pageId
                //   }`,
                //   query: {
                //     id: Qs.parse(query).id,
                //     utm_source: Qs.parse(query).utm_source,
                //   },
                // });
                // console.log(this.$route.query.activityFrom);
                if (this.$route.query.activityFrom === 'content') {
                  this.$router.push({
                    path: `/${this.$route.query.pagePath}/${
                      this.$route.query.pageId
                    }`,
                    query: {
                      id: Qs.parse(query).id,
                      utm_source: Qs.parse(query).utm_source,
                      activityFrom: this.$route.query.activityFrom,
                    },
                  });
                } else if (this.$route.query.activityFrom === 'recommend') {
                  this.$router.push({
                    // path: `/${this.$route.query.pagePath}`,
                    path: '/inviter',
                    query: {
                      id: Qs.parse(query).id,
                      utm_source: Qs.parse(query).utm_source,
                      activityFrom: this.$route.query.activityFrom,
                    },
                  });
                } else {
                  //activityFrom在正常的场景下是不会为空的，但是避免url的赋值粘贴，当activityFrom为空则跳到首页
                  // activityFrom为content就是内容获客（在activity和report两个栏目的详情里面），为recommend则为推荐注册，（目前的就只有这两个场景）
                  this.$router.push({
                    // path: `/${this.$route.query.pagePath}`,
                    path: '/',
                    query: {
                      id: Qs.parse(query).id,
                      utm_source: Qs.parse(query).utm_source,
                      activityFrom: this.$route.query.activityFrom,
                    },
                  });
                }
              }
              // else if (res.code === '110058') {
              //   this.$MessageToast.success(res.message);
              //   setTimeout(() => {
              //     const query = sessionStorage.getItem('query');
              //     this.$router.push({
              //       path: '/ownRegister',
              //       query: {
              //         ...Qs.parse(query),
              //         pageId: this.$route.params.id,
              //         pagePath: this.$route.path.split('/')[1],
              //       },
              //     });
              //   }, 2000);
              // }
            })
            .catch(err => {
              const res = err.response.data;
              this.$MessageToast.error(res.message);
            });
        }
      });
    },
    toOwnRegegister() {
      const query = sessionStorage.getItem('query');
      // console.log(query);
      // console.log(this.$route.query.pageId);
      // console.log(this.$route.query.pagePath);
      this.$router.push({
        path: '/ownRegister',
        query: {
          ...Qs.parse(query),
          utm_source: Qs.parse(query).utm_source,
          pageId: this.$route.query.pageId,
          pagePath: this.$route.query.pagePath,
          activityFrom: this.$route.query.activityFrom,
        },
      });
    },
    tolink(route) {
      var query = Object.entries(this.$route.query)
        .map(([key, value]) => {
          return `${key}=${value}`;
        })
        .join('&');
      // debugger;
      if (route.indexOf('?') > -1) {
        route = route + '&' + query;
      } else {
        route = route + '?' + query;
      }
      return route;
    },
    toService() {
      const url = this.tolink(
        'http://bestsign.udesk.cn/im_client/?web_plugin_id=23490'
      );
      window.open(url);
      // console.log(url);
    },
  },
  // mounted() {
  //   // this.tolink(this.$route.fullPath);
  //   console.log(this.$route);
  //   console.log(window.location);
  // },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
};
</script>
<style lang="scss">
.login {
  .el-form-item.is-success .el-input__inner {
    border-color: #dcdfe6;
  }
  .el-input.is-active .el-input__inner,
  .el-input__inner:focus {
    border-color: #00aa64;
    box-shadow: 0 0 2px rgba(0, 170, 100, 0.5);
  }
  .el-form-item {
    margin-bottom: 15px;
  }
  .el-form-item__error {
    padding-top: 2px;
  }
  .el-checkbox {
    margin-right: 8px;
  }
  .el-checkbox__inner:hover {
    border-color: #00aa64;
  }
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #00aa64;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #00aa64;
    border-color: #00aa64;
  }
  .el-input__inner {
    border-radius: 0;
  }
  .el-input-group__append {
    border-radius: 0 2px 2px 0;
    width: 125px;
    background-color: transparent;
  }
  .el-button--primary {
    background-color: #00aa64;
    border-color: #00aa64;
    width: 100%;
    border-radius: 0;
    font-size: 17px;
    border-radius: 5px;
  }
  .formTitle {
    height: 14px;
    font-size: 14px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    color: #484848;
    line-height: 14px;
    margin-bottom: 6px;
  }
  .mobileNum {
    display: flex;
    justify-content: space-between;
    .left {
      width: 33%;
      // /deep/ .el-select {
      //   width: 70%;
      // }
    }
    .right {
      width: 65%;
    }
  }
  .title {
    // position: absolute;
    // text-align: center;
    // margin-bottom: 1rem;
    // line-height: 1.5rem;
    // color: #333;
    // font-size: 1.2rem;
    // font-weight: 600;
    margin-bottom: 24px;
    height: 40px;
    .left {
      float: left;
      font-size: 22px;
      color: #333;
      margin-bottom: 24px;
      font-weight: 800;
    }
    .right {
      float: right;
      cursor: pointer;
      a {
        color: #96999f;
        span {
          text-decoration: underline;
        }
        .iconfont {
          font-size: 13px;
        }
      }
    }
  }
  .radio-box {
    text-align: left;
  }
  .el-radio {
    margin-right: 15px;
  }
  .el-radio__input.is-checked .el-radio__inner {
    border-color: #00aa64;
    background: #00aa64;
  }
  .el-radio__input.is-checked + .el-radio__label {
    color: #00aa64;
  }
}
</style>
<style scoped lang="scss">
.countDown {
  background-color: #fff;
  font-size: 14px;
  width: 100%;
  border: none;
  color: #00aa64;
}
.radio-box {
  text-align: left;
}
.login {
  padding: 2.5rem 2rem 4rem;
  position: relative;
  background-color: #fff;
  color: #515151;
  .text {
    padding-bottom: 1rem;
    line-height: 1.5;
    font-size: 1rem;
    text-align: center;
  }
  .checkbox {
    margin-bottom: 20px;
    text-align: left;
  }
  .login {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 3rem;
    line-height: 3rem;
    text-align: center;
    font-size: 0.875rem;
    background-color: #f0f0f0;
  }
  .protocol {
    color: #00aa64;
    font-size: 14px;
    cursor: pointer;
  }
  .tips {
    padding-top: 1rem;
    font-size: 0.875rem;
    a {
      color: #00aa64;
      cursor: pointer;
    }
  }
}
</style>
