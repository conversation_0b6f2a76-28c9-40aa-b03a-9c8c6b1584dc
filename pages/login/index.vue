<template>
  <div class="demo-own">
    <own-demo-header></own-demo-header>
    <div class="demo-card">
      <div class="card-body">
        <!-- <div class="description">
           <div class="title">立即注册，体验电子合同签署流程</div>
          <div class="line-2">仅需1分钟，即可了解电子合同签约全流程，<br>体验合同秒发秒签，适配各种终端，线上线下无缝切换。<br>助力企业实现降本增效，效率提升看得见。</div>
          <img src="./banner.jpg" alt="">
        </div> -->
        <login-form></login-form>
      </div>
    </div>
  </div>
</template>

<script>
import OwnDemoHeader from '@/components/OwnDemoHeader.vue';
import LoginForm from './loginForm';

export default {
  name: 'Login',
  layout: 'blank',
  head() {
    return {
      title: '登录_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同,电子签名,电子签章,电子印章,电子签约,网上签约',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签是业内领先的在线电子合同签署，电子签名，电子签章，电子印章的云服务平台，为企业提供合同全生命周期智能管理服务，帮助企业实现降本增效，全领域电子签约解决方案，随时随地完成电子合同的实时签署与线上智能，确保其安全、合法合规、不可篡改，终身服务有保障。',
        },
      ],
    };
  },
  components: {
    OwnDemoHeader,
    LoginForm,
  },
};
</script>
<style lang="scss">
.demo-own {
  background: url(./login_bg.jpg) no-repeat center;
  background-size: 100% 100%;
  position: relative;
  .el-select .el-input.is-focus .el-input__inner {
    border-color: #00aa64;
  }
  .el-button--primary.is-active,
  .el-button--primary:active {
    background: #00aa64;
  }
}
.el-select-dropdown__item {
  text-align: left;
}
.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: #e7f5f1;
}
.el-select-dropdown__item.selected {
  color: #00aa64;
}
</style>
<style scoped lang="scss">
.demo-own {
  width: 100%;
  height: 100vh;
  position: relative;
  .demo-card {
    // margin: 3rem auto;
    // padding: 3rem;
    // max-width: 1200px;
    width: 100%;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    .card-header {
      height: 52px;
      line-height: 52px;
      padding: 0 32px;
      color: #00aa64;
      background-color: #e7f5f1;
      text-align: center;
    }
    .card-body {
      // display: flex;
      // justify-content: space-between;
      margin: 0 auto;
      width: 600px;
      // position: absolute;
      // left: 50%;
      // top: 50%;
      // transform: translateX(-50%) translateY(-50%);
    }
    .description {
      .title {
        font-size: 2.25rem;
        line-height: 1.75;
        margin-bottom: 1.5rem;
      }
      .line-1 {
        margin-bottom: 1.75rem;
      }
      .line-2 {
        margin-bottom: 3rem;
        line-height: 1.75;
      }
    }
    .login {
      width: 100%;
      // box-shadow: 0 0 8px 0 #ddd;
      background: #fff;
      border-radius: 2px;
      border: 1px solid #f6f6fa;
      padding: 70px;
      box-sizing: border-box;
    }
  }
  .form-pictureVerify {
    width: 80px;
    height: 36px;
  }
}
@media screen and (max-width: 767px) {
  .demo-own {
    .demo-card {
      padding: 0 1rem;
      .card-body {
        display: block;
        width: 100%;
      }
      .description {
        text-align: center;
        img {
          display: none;
        }
      }
      .login {
        width: 100%;
        padding: 10px;
      }
    }
  }
}
@media screen and (min-width: 767px) and (max-width: 1024px) {
  .demo-own {
    .demo-card {
      padding: 0 1rem;
      .description {
        img {
          width: 75%;
        }
      }
      .login {
        width: 100%;
      }
    }
  }
}
</style>
