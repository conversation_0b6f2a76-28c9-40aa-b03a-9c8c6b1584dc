<template>
  <article>
	<nav class="nav-bar">
		<div class="abstract industry">
			<nuxt-link to="/case">
				<span class="active all-case">全部</span>
			</nuxt-link>
			<ul class="">
				<template v-for="item in industryList">
					<nuxt-link :key="item.code" :to="`/evaluate/list-${item.code}`">
					<li>{{ item.codeValue }}</li>
					</nuxt-link>
				</template>
			</ul>
		</div>
	</nav>
    <!-- <bread-nav :menu="menu"></bread-nav> -->
    <section class="section section-1">
      <div class="container">
        <div class="article">
			<h3>{{article.customerName}}</h3>
          <h1>{{ article.caseName }}</h1>
          <!-- <div class="desc">
			<span>
				<strong>发布时间：</strong>
				{{ releaseTime }}
			</span>
          </div> -->
          <div
            id="content"
            class="braft-output-content"
            v-html="article.content"></div>
          <nav>
			<h2 class="main-title"> 相关推荐</h2>
			 <el-button type="text" class="change-card" @click="getNewCard">换一换</el-button>
			 <!-- <i class="iconfont icon-xiangyoujiantou"></i> -->
			<el-row>
				<template v-for="(item) in shownList">
					<el-col
					:key="item.id"
					:sm="8"
					:md="8"
					:lg="6"
					>
					 <nuxt-link class="card" :to="`/case/detail/${item.id}`">
						<div @click="handleAdd(item.id)">
							<div class="card-content">
								<h4 class="header">{{ item.customerName }}</h4>
								<p class="body">{{ item.caseName }}</p>
							</div>
							<img
								:key="item.imgUrl"
								v-lazy="formatUrl(item.imgUrl)"
								class="card-image">
						</div>
					</nuxt-link>
					</el-col>
				</template>
				</el-row>
          </nav>
        </div>
        <aside class="article-side" v-if="!isLandMobile">
          <!-- <div class="item-wrap">
            <h4>相关推荐</h4>
            <template v-for="item in article.recommend">
              <div class="item" :key="item.id">
                <nuxt-link :to="`/case/detail/${item.id}`">{{ item.customer_name }}</nuxt-link>
              </div>
            </template>
          </div>
          <div class="qrcode">
              <img
                src="@/assets/images/qr-wechat.png"
                alt="二维码">
              <p style="line-height: 25px;color:#808080;">扫码加小签-电子签约顾问<br>领取行业资料<br>观看行业公开课</p>
            </div> -->
			<div class="icon-box">
				<i class="iconfont icon-weixin">
					<img
					src="@/assets/images/qr-wechat.png"
					alt="二维码">
				</i>
				<i class="iconfont icon-weibo"></i>
				<i class="iconfont icon-fanhui" @click.stop.prevent="handlePage()"></i>
				
			</div>
        </aside>
      </div>
    </section>
  </article>
</template>
<script>
import moment from 'moment';
import find from 'lodash/find';
export default {
  name: 'evaluateDetail',
  head() {
    return {
      title: this.tdkT || '上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keyword',
          content: this.tdkK || '电子合同,电子合同签署,电子签名,电子签章',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            this.tdkD ||
            '上上签是业内领先的在线电子合同签署，电子签名，电子签章的云服务平台，提供多种行业专属解决方案。',
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: `https://www.bestsign.cn/evaluate/detail/${this.article.id}`,
        },
      ],
    };
  },
  async asyncData({ route, app, redirect }) {
    const current = route.params.hasOwnProperty('page')
      ? parseInt(route.params.page.replace('-p', ''))
      : 1;
    const id = route.params.id;
    const res = await app.$axios.get(`www/api/web/getEvaluate/${id}`);
    const res0 = await app.$axios.get('/www/api/web/category/industry');
    const res1 = await app.$axios.get(
      // '/www/api/web/college2?status=0&column=evaluate&pageNum=1&pageSize=99'
      `/www/api/web/getEvaluate?pageNum=${current}&pageSize=3`
    );
    const caseList = res1.data;
    let xmlList = [
      {
        loc: 'https://www.bestsign.cn/evaluate',
        priority: '1',
        lastmod: moment().format('YYYY-MM-DD'),
        changefreq: 'daily',
      },
    ];
    caseList.forEach(it => {
      let xmlIt = {};
      xmlIt.loc = 'https://www.bestsign.cn/evaluate/detail/' + it.id;
      xmlIt.priority = '1';
      xmlIt.lastmod = moment().format('YYYY-MM-DD');
      xmlIt.changefreq = 'daily';
      xmlList.push(xmlIt);
    });
    const shownList = caseList;
    // if (res.length === 0 || res[0].column != 'evaluate') {
    // if (res.length === 0) {
    //   redirect('/404');
    // }
    return {
      article: res,
      releaseTime: moment(res.releaseTime).format('YYYY-MM-DD'),
      // seo: res[0].summary ? JSON.parse(res[0].summary) : {}, //新数据是对的
      // seo: res[0].summary ? JSON.parse(res[0].summary) : {}, //老数据有问题先改成这样
      tdkT: res.tdkT,
      tdkD: res.tdkD,
      tdkK: res.tdkK,
      shownList,
      industryList: res0,
      totalPageNum: res1.totalPageNum,
    };
  },
  data() {
    return {
      article: {},
      menu: [
        {
          name: '客户证言',
          to: '/evaluate',
        },
        {
          name: '证言详情',
          to: '',
        },
      ],
      page: 1,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    handlePage(id) {
      this.$router.push(`/evaluate${id ? `/${id}` : ''}`);
    },
    formatUrl(url) {
      const arr = url && url.split('?');
      return url && arr[0];
    },
    async getNewCard() {
      if (this.page == this.totalPageNum) {
        this.page = 1;
      } else {
        this.page += 1;
      }
      var res1 = await this.$axios.get(
        `/www/api/web/getEvaluate?pageNum=${this.page}&pageSize=3`
      );
      this.shownList = res1.data;
    },
  },
};
</script>

<style scoped lang="scss">
.nav-bar {
  border-bottom: 1px solid #eee;
}
.abstract {
  display: flex;
  justify-content: space-between;
  font-size: 16px;
  margin-bottom: 30px;
  margin: 0 auto;
  max-width: 1200px;
  ul {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
  }
  li {
    cursor: pointer;
    padding: 20px 30px;
    color: #333;
    &:hover {
      color: #9a9999;
    }
    &.active {
      color: #eee;
    }
  }
  li:last-child {
    padding-right: 0;
  }
}
.all-case {
  padding: 20px 30px;
  color: #333;
  display: flex;
  align-items: center;
  &:hover {
    color: #9a9999;
  }
}

.section-1 {
  background-color: #fafbfc;
  padding: 60px 0;
  a {
    color: #323232;
    &:hover {
      color: #00aa64;
    }
  }
  .container {
    display: flex;
    .article {
      flex: 1;
      padding: 30px 0 80px 40px;
      border-radius: 5px;
      .video-wrap video {
        height: 100%;
      }
    }
    #content {
      padding-bottom: 45px;
      border-bottom: 1px solid #e5e5e5;
    }
    aside {
      .icon-box {
        width: auto;
        margin-left: 16px;
        display: flex;
        flex-direction: column;
        text-align: center;
        .iconfont {
          font-size: 20px;
          cursor: pointer;
          color: #bfbfbf;
          margin: 5px 0;
          &:hover {
            color: #00a664;
          }
        }
        .icon-weixin {
          position: relative;
          img {
            position: absolute;
            width: 100px;
            right: 40px;
            top: -20px;
            display: none;
          }
          &:hover img {
            display: block;
          }
        }
      }

      .item-wrap {
        background-color: #fff;
        border: 1px solid #e5e5e5;
        border-radius: 5px;
        padding: 24px 18px;
        // margin-bottom: 15px;
        .img-wrap {
          width: 100%;
          text-align: center;
          img {
            width: 100%;
          }
        }
        h4 {
          margin: 36px auto;
          font-size: 18px;
          padding-bottom: 15px;
          border-bottom: 1px solid #e5e5e5;
        }
        .item {
          margin-bottom: 25px;
          cursor: pointer;
          color: #888;
          &:hover {
            color: #00a664;
          }
        }
      }
      .qrcode {
        min-width: 200px;
        margin-top: 20px;
        display: flex;
        justify-content: space-around;
        img {
          width: 80px;
          height: 80px;
        }
        p {
          // margin-top: 0.25rem;
          font-size: 12px;
          // margin-left: 0.3rem;
        }
      }
    }
    h1 {
      font-size: 30px;
      margin-bottom: 60px;
      width: 65%;
      line-height: 1.4;
    }
    .desc {
      text-align: right;
      font-size: 14px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e5e5e5;
      margin-bottom: 45px;
    }
  }
  nav {
    margin-top: 25px;
    position: relative;
    text-align: center;
    .card {
      height: 24rem;
    }

    .card-image {
      width: 50%;
      height: 5rem;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }
    .card-content {
      text-align: center;
      height: auto;
      padding-bottom: 60px;
      .header {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        font-size: 1.4rem;
        line-height: 2.25rem;
        margin: 4rem 0 1rem;
      }
      .body {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-align: justify;
        word-break: break-all;
        line-height: 1.25rem;
        padding: 0 30px;
      }
      .footer {
        align-self: flex-end;
        a {
          color: #00a664;
        }
      }
    }
    .el-row {
      display: flex;
      justify-content: space-around;
    }
    .nav-item {
      cursor: pointer;
      padding: 10px 0;
      &:hover {
        color: #00a664;
      }
    }
    .change-card {
      color: #00a664;
    }
    .back {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 10px;
      .el-icon-arrow-left {
        padding-right: 5px;
        font-size: 22px;
        position: relative;
        top: 2px;
      }
      &:hover {
        color: #00a664;
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .section-1 {
    .container {
      .article {
        padding: 30px 20px 80px;
      }
      .card-main {
        .card-left {
          .name {
            font-size: 14px;
          }
          p {
            font-size: 14px;
            color: #86868b;
          }
          .title {
            font-size: 18px;
          }
        }
      }
      h1 {
        font-size: 24px;
        color: #515151;
        font-weight: 200;
        margin-bottom: 30px;
        line-height: 35px;
      }
      h3 {
        font-size: 24px;
        margin-bottom: 40px;
      }
      nav {
        .back {
          cursor: pointer;
          left: 0;
          bottom: -35px;
          top: auto;
        }
      }
    }
    img {
      max-width: 100%;
    }
  }
}
</style>
