<template>
  <article :class="{ 'case__isMobile': isMobile }">
	<section class="banner">
		<div class="container">
			<h1 class="article-headline">我们致力做好每个服务细节，让每位客户成为终身客户。</h1>
			<!-- <p  class="article-text">上上签不仅仅是一种电子签约工具，而是一种新的商业模式，借助合同签署、管理的自动化，推动业务快速前进，让你的公司充满竞争力。</p> -->
		</div>
	</section>
	<section>
		<nav :class="['nav-bar',fixedTab?'fixed-bar':'']" ref="childNav" id="childNav">
			<div class="abstract industry">
				
				<ul class="">
					<nuxt-link to="/evaluate">
					<li class="active">全部</li>
				</nuxt-link>
					<template v-for="item in industryList">
						<nuxt-link :key="item.code" :to="`/evaluate/list-${item.code}`">
						<li>{{ item.codeValue }}</li>
						</nuxt-link>
					</template>
				</ul>
			</div>
		</nav>
	</section>
    <section class="section section-1 is-tiny">
      <div class="container">
        <el-row>
          <template v-for="(item, index) in shownList">
            <el-col  :key="index" v-if="!isMobile">
				<div class="card-main"  v-if="(index%2==0)">
					<div class=" card-media" @click="handleVideo(index)">
						<p  v-if="item.evaluateVideo">观看视频
								<i class="iconfont icon-bofang"></i>
							</p>
							<img :src="item.videoCover" >
					</div>
					<div class="card-content">
						<h4 class="name">{{ item.customerName }}</h4>
						<div v-html="item.evaluateContent"></div>
						<img :src="item.customerIcon" alt="" class="company-logo">
					</div>
        		</div>
				<div class="card-main"  v-else>
					<div class="card-content">
						<h4 class="name">{{ item.customerName }}</h4>
						<div v-html="item.evaluateContent"></div>
						<img :src="item.customerIcon" alt="" class="company-logo">
					</div>
						<div class="card-media" @click="handleVideo(index)">
						<p  v-if="item.evaluateVideo">观看视频
							<i class="iconfont icon-bofang"></i>
						</p>
						<img :src="item.videoCover" >
					</div>
        		</div>
        	</el-col>
			 <el-col  :key="index" v-if="isMobile">
				<div class="card-main">
					<div class=" card-media" @click="handleVideo(index)">
						<p class="play-video" v-if="item.evaluateVideo">观看视频
								<i class="iconfont icon-bofang"></i>
							</p>
							<img :src="item.videoCover" >
					</div>
					<div class="card-content">
						<h4 class="name">{{ item.customerName }}</h4>
						<div class="content-text" v-html="item.evaluateContent"></div>
						<img :src="item.customerIcon" alt="" class="company-logo">
					</div>
        		</div>
        	</el-col>
          </template>
        </el-row>
        <div class="button-wrap">
          <el-pagination
            :page-size="3"
            :current-page.sync="current"
            :total="count"
            :pager-count="5"
			background
            @current-change="handlePageChange"
            layout="prev, pager, next"></el-pagination>
        </div>
        <!-- seo优化，由于分页是客户端渲染，源码里无法拿到路由 -->
        <div style="display:none" v-for="item in pageNums" :key="item">
          <a v-if="item === 1" :href="$route.path.split('-p')[0]">{{item}}</a>
          <a v-else :href="`${$route.path.split('-p')[0]}-p${item}`">{{item}}</a>
        </div>
      </div>
	  	<!-- <v-video
		:visible="dialogVisible"
		videoUrl="https://static.bestsign.cn/51a5e71e-6c39-483a-b991-14f6a1626b71.mp4"
		@close="dialogVisible = false">
		</v-video> -->
    </section>
    <v-video
            :visible="dialogVisible"
            :videoUrl="src"
            @close="dialogVisible = false">
            </v-video>
  </article>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import Video from '@/components/Video.vue';
import moment from 'moment';
import findKey from 'lodash/findKey';
import Throttle from '@/assets/utils/throttle';
export default {
  name: 'evaluateList',
  components: {
    'v-video': Video,
  },
  head() {
    return {
      title:
        this.current > 1
          ? `电子合同${this.industryName}案例（第${
              this.current
            }页）_上上签电子签约云平台`
          : `电子合同${this.industryName}案例_上上签电子签约云平台`,
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: `${this.industryName}电子合同,${this.industryName}电子签约,${
            this.industryName
          }电子签名,${this.industryName}成功案例,上上签电子签约云平台`,
        },
        {
          hid: 'description',
          name: 'description',
          content: `上上签电子签约云平台的${
            this.industryName
          }行业成功案例板块汇聚了${
            this.industryName
          }行业下的电子合同优秀案例，为您提供${
            this.industryName
          }行业优质的电子签约成功案例。为您的电子签约业务提供强有力的支持。`,
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: `https://www.bestsign.cn/evaluate/${
            this.$route.params.category
          }`,
        },
      ],
    };
  },
  data() {
    return {
      dialogVisible: false,
      src: '',
      tabsHeight: '',
      fixedTab: false,
    };
  },
  async asyncData({ app, params }) {
    const { category } = params;
    // const solutions = {
    //   31: 'other',
    //   25: 'car',
    //   30: 'business',
    //   22: 'logistics',
    //   26: 'it',
    //   23: 'retail',
    //   21: 'fang',
    //   19: 'hr',
    //   18: 'finance',
    //   2: 'qqq',
    //   1: 'uuu',
    //   3: 'vvv',
    // };
    const keys = category.split('-');
    // const industryId = findKey(solutions, o => o === keys[1]);
    const industryId = keys[1];
    const current = parseInt((keys[2] || 'p1').replace('p', ''));
    const res0 = await app.$axios.get('/www/api/web/category/industry');
    // let industryName = '';
    // res0.forEach(o => {
    //   if (o.code === industryId) {
    //     industryName = o.codeValue;
    //   }
    // });
    const res1 = await app.$axios.get(
      `/www/api/web/getEvaluate?industry=${industryId}&pageNum=${current}&pageSize=3`
    );
    // const caseList = res1.map(item => {
    //   if (item.summary.indexOf('sortIndex') > -1) {
    //     let summary = JSON.parse(item.summary);
    //     item.sortIndex = summary.sortIndex;
    //     item.title = summary.title;
    //     item.keyword = summary.keyword;
    //     item.description = summary.description;
    //   } else {
    //     item.sortIndex = -1;
    //   }

    //   return item;
    // });

    // caseList.sort(function(a, b) {
    //   if (a.sortIndex == b.sortIndex) {
    //     return b.id - a.id;
    //   } else {
    //     return b.sortIndex - a.sortIndex;
    //   }
    // });
    // console.log(caseList);
    // const filterResult = res1.data.filter(
    //   o => moment() > moment(o.releaseTime) && o.speaker == industryId
    // );
    const filterResult = res1.data;
    // const shownList = filterResult.slice((current - 1) * 12, 12 * current);
    const shownList = filterResult;
    const industryName = res0.find(o => o.code === res1.data[0].industry)
      .codeValue; //由于list的所有的industry都是一样的，所以拿第一个就可以,这里也和列表左下角的一样从列表的所属类别（英文）匹配出字典类别（中文）
    return {
      // industryList: res0.sort((a, b) => b.sortIndex - a.sortIndex),
      industryList: res0,
      industryName,
      // solutions,
      industryId,
      current,
      shownList,
      count: res1.totalNum,
      pageNums: res1.totalPageNum,
    };
  },
  computed: {
    ...mapState(['isMobile']),
  },
  mounted() {
    this.$store.commit('changePath', { path: 'case' });
    if (!this.isMobile) {
      this.tabsHeight = this.$refs.childNav.offsetTop;
      window.addEventListener('scroll', Throttle(this.handleScroll, 100));
    }
  },
  methods: {
    ...mapActions(['setViewTimes']),
    format(time) {
      return moment(time).format('YYYY-MM-DD');
    },
    formatUrl(url) {
      const arr = url && url.split('?');
      return url && arr[0];
    },
    releaseTime(time) {
      return moment(time).format('YYYY-MM-DD');
    },
    industryType(id) {
      const item = this.industryList.find(o => o.code === id);
      return item && item.codeValue;
    },
    // 这个请求的目的是要在点击之后增加一个点击次数，现在这个已经在详情（_id）的里面的接口上做了这个事情
    // handleAdd(id) {
    //   this.setViewTimes({
    //     id,
    //     type: '4',
    //   });
    //   // this.$router.push(`/case/detail/${id}`);
    // },
    handlePageChange(page) {
      let toPath = this.$route.path.split('-p')[0];
      if (page > 1) {
        toPath = toPath + `-p${page}`;
      }
      this.$router.push({ path: toPath });
    },
    handleVideo(val) {
      if (this.shownList[val].evaluateVideo.length !== 0) {
        this.dialogVisible = true;
        this.src = this.shownList[val].evaluateVideo;
        // console.log(this.shownList[1].evaluateVideo);
        // console.log(this.shownList[2].evaluateVideo);
      }
    },
    handleScroll() {
      const scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      if (scrollTop + 80 > this.tabsHeight) {
        this.fixedTab = true;
      } else {
        this.fixedTab = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
section {
  background: #fafbfc;
}
.article-headline {
  padding: 5.625rem 0 3.125rem;
  text-align: center;
  margin: 0;
  font-size: 2.2rem;
  font-weight: 500;
  color: #090909;
}
.article-text {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.7;
  font-size: 18px;
}
.nav-bar {
  padding-top: 2rem;
}
.abstract {
  display: flex;
  justify-content: center;
  font-size: 16px;
  margin-bottom: 30px;
  margin: 0 auto;
  max-width: 1100px;
  border-bottom: 1px solid #eee;
  a:hover {
    border-bottom: 2px solid #00aa64;
    color: #00aa64;
  }
  ul {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
  }
  li {
    cursor: pointer;
    padding: 20px 18px;
    color: #6e6e6e;
    text-align: center;
    font-size: 1rem;
    &:hover {
      color: #00a664;
    }
  }
  .nuxt-link-exact-active {
    border-bottom: 2px solid #00aa64;
    a {
      color: #00aa64;
    }
  }
}
.fixed-bar {
  position: fixed;
  z-index: 20;
  top: 76px;
  background: #fff;
  box-shadow: 0 2px 20px 0 rgba(0, 39, 123, 0.13);
  width: 100%;
  padding: 0;
  transition: all 0.2s ease;
}
.section-subtitle {
  margin: 50px auto 80px;
  line-height: 28px;
}
.section-1 {
  text-align: center;
  background: #fafbfc;
  padding-top: 3rem;
  .container {
    /deep/.el-col {
      // margin-bottom: 6rem;
    }
  }
  a {
    color: #323232;
  }
  .card-main {
    display: flex;
    padding: 0;
    justify-content: space-between;
    background: #fff;
    margin-bottom: 3.75rem;
    .card-media {
      width: 40rem;
      height: 28.5rem;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 2px;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
        border-radius: 2px;
      }
      p {
        position: absolute;
        color: #fff;
      }
      &::before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.2);
      }
    }
    .card-content {
      text-align: left;
      width: 50%;
      padding: 4rem 5rem 0 5rem;
      .name {
        margin-bottom: 20px;
        font-size: 20px;
      }
      .company-logo {
        width: 100px;
        margin-top: 20px;
      }
    }
  }
  .card-video {
    width: 100%;
    height: 25rem;
    margin: 3rem 0;
    position: relative;
    border-radius: 5px;
    img {
      width: 100%;
      height: 100%;
      border-radius: 5px;
      cursor: pointer;
    }
    p {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      .iconfont {
        font-size: 18px;
        margin-left: 5px;
      }
    }
  }

  .card-content {
    .header {
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      font-size: 1.25rem;
      line-height: 2.25rem;
    }
    .body {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      text-align: justify;
      word-break: break-all;
      line-height: 1.25rem;
    }
    .footer {
      align-self: flex-end;
      a {
        color: #00a664;
      }
    }
  }
}

.button-wrap {
  // margin-top: 110px;
  padding: 30px 0;
  text-align: center;
  /deep/ .el-pager {
    li {
      background-color: #fff !important;
      border: 1px solid #eee !important;
    }
    .active {
      background-color: #00aa64 !important;
    }
  }
  /deep/ .btn-prev,
  /deep/ .btn-next {
    background-color: #fff !important;
    border: 1px solid #eee !important;
  }
}
.case-btn {
  color: #00a664;
  border: 1px solid #00a664;
  background: #fff;
  transition: all 0.3s ease;
  &:hover {
    background: #00a664;
    color: #fff;
  }
}
@media (max-width: 768px) {
  .section {
    // padding: 18px 10px 40px;
    padding: 0;
  }
  .content {
    margin-bottom: 72px;
  }
  .headline {
    font-size: 28px;
    text-align: center;
    max-width: 78.5%;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 64px;
  }
  .section-1 {
    .card-main {
      .card-media {
        width: 100%;
        height: auto;
      }
    }
  }
  .section-subtitle {
    padding: 0 10px;
  }
  .abstract {
    width: 86.5%;
    font-size: 14px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 45px;
    flex-wrap: wrap;
    ul {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: center;
      a {
        min-width: 67px;
      }
    }
    li {
      padding: 12px 6px;
      white-space: nowrap;
    }
  }
  .case {
    width: 84.5%;
    height: 312px;
    margin: 23px 0;
  }
  .case-btn {
    margin-top: 40px;
    background-color: #fff;
  }
}
// 移动端样式覆盖
.case__isMobile {
  background: #fff;
  .article-headline {
    padding: 2rem 1rem;
  }
  .section-1 {
    .article-headline {
      color: #515151;
      font-size: 24px;
      font-weight: 200;
      margin: 0 40px 50px 40px;
    }
    .card-main {
      display: flex;
      padding: 0;
      flex-wrap: wrap;
      margin-bottom: 20px;
      .play-video {
        font-size: 16px;
      }
      .play-video {
        height: auto;
      }
      .card-content {
        width: 100%;
        padding: 2rem;
        .content-text {
          font-size: 14px;
          color: #86868b;
          line-height: 24px;
        }
      }
    }
    .card-video {
      height: 16rem;
    }
    .abstract {
      li {
        border: solid 1px #bfbfbf;
        height: 27px;
        line-height: 27px;
        border-radius: 13px;
        padding: 0 13px;
        color: #6e6e6e;
        margin-right: 4px;
        margin-top: 10px;
        &.active {
          color: #ffffff;
          border: none;
          background-image: linear-gradient(60deg, #019b89, #06b9a5);
        }
      }
    }
    .card {
      // height: 320px;
      .card-image {
        // height: 161px;
      }
      .footer {
        align-self: center;
      }
      .card-content {
        .body {
          line-height: 2.2rem;
        }
      }
    }
  }
}
</style>
