<template>
  <article class="certificate">
    <section class="section section-1">
      <div class="container">
        <h1 class="article-headline">区块链存证查询</h1>
        <p class="article-subtitle">在线查询通过上上签平台签署完成的合同的区块链存证内容</p>
        <div class="inquire">
          <el-form :model="ruleForm" ref="ruleForm" label-width="100px">
            <el-form-item label="合同编号：" prop="contractId">
              <el-input v-model="ruleForm.contractId"></el-input>
            </el-form-item>
            <el-form-item>
            </el-form-item>
            <el-form-item label="上传合同：" prop="contract">
                <el-upload
                    action="#"
                    class="upload-demo"
                    accept=".pdf"
                    :on-change="handleChange"
                    :on-success="handleFileSuccess"
                    :file-list="fileList"
                >
                    <el-button>选择PDF文件</el-button>
                </el-upload>
            </el-form-item>
          </el-form>
        </div>
        <div class="btn-container">
          <button class="ssq-button-primary is-white" @click="toInquire('ruleForm')">点击查询</button>
        </div>
        <div class="space"></div>
        <div class="result-container">
          <template>
            <el-table
              :data="tableData"
              border
              style="width: 100%"
              :header-cell-style="{background:'rgb(250, 250, 250)',color:'#606266'}">
              <el-table-column
                prop="platform"
                label="区块链"
                align="center">
              </el-table-column>
              <el-table-column
                prop="businessTypeCN"
                label="存证项目"
                align="center">
              </el-table-column>
              <el-table-column
                prop="notaryId"
                label="存证ID"
                align="center">
              </el-table-column>
              <el-table-column
                prop="notaryContent"
                label="存证hash"
                align="center">
                </el-table-column>
                <el-table-column
                prop="txHash"
                label="区块链地址"
                align="center">
                </el-table-column>
                <el-table-column
                prop="timeStamp"
                label="存证时间"
                align="center">
                </el-table-column>
            </el-table>
          </template>
        </div>
      </div>
      <!-- <div v-if="!isMobile" class="img-wrap">
        <img src="@/assets/images/product/check-banner.jpg">
      </div>
      <div v-else class="mobile-wrap">
        <img src="@/assets/images/product/wap-check-banner.jpg" alt="">
      </div> -->
    </section>

    <el-dialog
      visible.sync="visible"
    >
      {{ results.msg }}
    </el-dialog>
  </article>
</template>

<script>
import { MessageBox } from 'element-ui';
export default {
  name: 'Certificate',
  data() {
    return {
      visible: false,
      isSuccess: false,
      results: {},
      signResults: [],
      ruleForm: {
        contractId: '',
      },
      fileList: [],
      tableData: [
        // {
        //   platform: '蚂蚁链',
        //   businessTypeCN: '王小虎',
        //   notaryId: 'wiwoeqhdiqg',
        //   notaryContent: 'cnajkhc',
        //   txHash: '上海市普陀区金沙江路 1518 弄',
        //   timeStamp: '2016-05-02',
        // },
      ],
    };
  },
  head() {
    return {
      title: '电子合同区块链存证查询_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content:
            '电子合同区块链存证查询,合同互联网存证查询,电子签约,电子签章',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签电子签约云平台推出电子合同区块链存证查询功能，企业或者个人可以上传在上上签平台签署完成的合同，在线查询区块链存证内容，此功能旨在为企业或者个人提供合同互联网存证查询有效的支持。',
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.bestsign.cn/certificate',
        },
      ],
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    host() {
      var host = window.location.host;
      var protocol = window.location.protocol;
      if (host === 'www.bestsign.cn') {
        return protocol + '//ent.bestsign.cn';
      } else if (host === 'www.bestsign.info') {
        return protocol + '//ent.bestsign.info';
      } else {
        return protocol + '//' + host;
      }
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: 'product' });
  },
  methods: {
    toInquire(formName) {
      if (this.ruleForm.contractId === '') {
        this.$MessageToast.error('请输入合同编号');
      } else if (this.ruleForm.file === undefined) {
        this.$MessageToast.error('请上传合同文件');
      } else {
        const formData = new FormData();
        for (let k in this.ruleForm) {
          formData.append(k, this.ruleForm[k]);
          // console.log(k);
          // console.log(this.form[k]);
        }
        // debugger;
        this.$refs[formName].validate(valid => {
          if (valid) {
            // debugger;
            // loading要做
            this.$axios({
              method: 'post',
              url: '/www/api/web/blockChain-certificate-storage',
              headers: {
                'Content-Type': 'multipart/form-data',
              },
              data: formData,
            }).then(res => {
              // 返回数据是正确的
              if (res.code === '0') {
                res.data.map((item, index) => {
                  switch (item.platform) {
                    case 'antfs':
                      item.platform = '蚂蚁链';
                      break;
                  }
                });
                this.tableData = res.data;
              } else {
                // 返回数据是错的
                this.$message.error({
                  message:
                    '查询无存证记录，请确认合同编号与合同文件是否正确、或该合同是否享有区块链存证服务',
                });
              }
            });
          }
        });
      }
    },
    handleChange(file, fileList) {
      // debugger;
      this.ruleForm.file = file.raw;
      // console.log('上传');
      if (fileList.length > 1) {
        fileList.splice(0, 1);
      }
    },
    handleFileSuccess() {
      // this.$message.success('文件上传成功');
    },
  },
};
</script>

<style scoped lang="scss">
.certificate {
  text-align: center;
  .section-1 {
    // color: #fff;
    // padding: 0;
    // position: relative;
    // background: url('~assets/images/product/check-banner.jpg') no-repeat;
    // background-size: cover;
    // width: 100%;
    // height: 44vw;
    // display: flex;
    // align-items: center;
    // background-position: center;
    // padding: 4rem 0;
    .container {
      // position: absolute;
      // top: 130px;
      // left: 50%;
      // transform: translate3d(-50%, 0, 0);
      // top: 13.5rem;
      .inquire {
        text-align: left;
        width: 500px;
        margin: 0 auto;
        /deep/ .el-input__inner {
          width: 400px;
          &:focus {
            border-color: #00aa64;
          }
        }
        .el-button {
          &:hover {
            color: #fff;
            border-color: #00aa64;
            background-color: #00aa64;
          }
          &:focus {
            color: #fff;
            border-color: #00aa64;
            background-color: #00aa64;
          }
        }
        /deep/ .el-form-item__label {
          font-size: 17px;
          color: #090909;
        }
      }
      .btn-container {
        margin-top: 40px;
        .ssq-button-primary {
          width: 220px;
        }
      }
      .space {
        width: 100%;
        height: 10px;
        border-top: 1px solid #dcdfe6;
        margin-top: 40px;
      }
      .result-container {
        margin: 40px 5%;
        // border: 1px solid #090909;
      }
    }
    .article-headline {
      padding: 5.625rem 0 2rem;
      text-align: center;
      font-size: 2.2rem;
      font-weight: 500;
      margin: 0;
      color: #090909;
    }
    .article-subtitle {
      color: #86868b;
      margin: 0 auto 48px;
      font-size: 1rem;
      font-weight: 400;
    }
    .img-wrap,
    img {
      width: 100%;
      vertical-align: middle;
    }

    .el-button--text {
      color: #090909;
      background: 0 0;
      padding-left: 0;
      padding-right: 0;
      border-bottom: 1px solid #fff;
      padding-bottom: 4px;
    }
    .upload {
      display: inline-block;
      position: relative;
      input {
        opacity: 0;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
      }
      .iconfont {
        font-size: 14px;
      }
    }
  }
}
@media (max-width: 1024px) {
  .certificate .section-1 {
    .container {
      // top: 50px;
    }
    .article-subtitle {
      margin: 0 auto 48px;
    }
  }
}
@media screen and (max-width: 767px) {
  .certificate {
    .section-1 {
      //   background-image: linear-gradient(180deg, #019b89, #05d2bb);
      // background-size: cover;
      // background-position: 44%;
      // height: 100vw;
      .container {
        width: 100%;
        padding-top: 0rem;
        margin: 0;
        .inquire {
          /deep/ .el-input__inner {
            width: 200px;
          }
        }
        .result-container {
          margin: 40px 0;
          // border: 1px solid #090909;
        }
        .article-headline {
          padding: 40px 0 20px;
          font-size: 28px;
          font-weight: 500;
        }
        .article-subtitle {
          font-size: 14px;
          line-height: 24px;
          margin: 0 auto 30px;
        }
      }
      .mobile-wrap {
        img {
          width: 100%;
        }
      }
    }
  }
}
</style>
