<template>
  <article class="help-feedback">
    <section class="section">
      <div
        id="udesk"
        class="container"></div>
    </section>
  </article>
</template>

<script>
export default {
  name: 'HelpFeedback',
  head() {
    return {
      title: '问题反馈_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '上上签电子合同,电子合同使用建议,电子合同使用问题',
        },
        {
          hid: 'description',
          name: 'description',
          content: '在此提交任何关于上上签电子合同，电子签名的问题和建议看法。',
        },
      ],
    };
  },
  mounted() {
    this.$store.commit('changePath', { path: 'help' });
    this.init();
  },
  methods: {
    init() {
      var date = new Date();
      var token =
        date.getFullYear() +
        date.getMonth() +
        date.getDate() +
        Math.floor(Math.random(4) * 10000);

      /* if your website does not introduce jQuery ,please cancel the ollowing annotation code.*/
      var scriptDom = document.createElement('script');
      scriptDom.src =
        'https://assets-cli.udesk.cn/ticket_js_sdk/static/vendor/js/jquery.min.js?t=' +
        token;
      document.body.appendChild(scriptDom);

      var scriptDom = document.createElement('script');
      scriptDom.src =
        'https://assets-cli.udesk.cn/ticket_js_sdk/1.0.1/js/sdk.min.js?t=' +
        token;
      document.body.appendChild(scriptDom);
      var styleDom = document.createElement('link');
      styleDom.rel = 'stylesheet';
      styleDom.href =
        'https://assets-cli.udesk.cn/ticket_js_sdk/1.0.1/css/sdk.min.css?t=' +
        token;
      document.body.appendChild(styleDom);
      const container = document.querySelector('#udesk');
      scriptDom.addEventListener(
        'load',
        function() {
          var udesk = UdeskSDK.ticketSDK.init({
            subdomain: 'bestsign',
            appid: '00fb4b786a026089',
            signature: 'cddae7467204b22837820f96c31a7f020a066b11',
            type: 'email',
            content: '<EMAIL>',
          });
          udesk.create({
            type: 'new',
            container,
          });
        },
        false
      );
    },
  },
};
</script>

<style scoped lang="scss">
.help-feedback {
  .udesk-sdk-ticket
    .udesk-sdk-ticket-component-attachment-upload
    .attachment-upload-input
    .attachment-upload-item
    .attachment-upload-button {
    display: block;
    padding: 5px 15px;
    color: #000;
    border: 1px solid #888;
  }
  .udesk-sdk-ticket .udesk-sdk-ticket-component-attachment-upload {
    margin-top: 5px;
  }
  .content-guidance {
    position: fixed;
    top: 0;
    left: 0;
    background-color: #f8fbff;
    z-index: 1000;
    width: 100%;
  }
  .content-guidance p {
    padding: 0 12px;
  }
  .content-guidance p:first-child,
  .content-guidance p:nth-child(2) {
    background-color: rgb(48, 122, 232);
  }
  .content-guidance p:first-child span,
  .content-guidance p:nth-child(2) span {
    font-size: 15px !important;
    color: #fff;
  }
  .content-guidance p:first-child {
    padding-top: 18px;
  }
  .content-guidance p:nth-child(2) {
    padding-bottom: 18px;
  }
  .content-guidance p:nth-child(3),
  .content-guidance p:nth-child(4) {
    color: #888;
  }
  .content-guidance p:nth-child(3) {
    padding-top: 18px;
  }
  .content-guidance p:nth-child(4) {
    padding-bottom: 10px;
  }
  .udesk-sdk-ticket
    .udesk-sdk-ticket-new-ticket
    .udesk-sdk-ticket--component-dynamic-form {
    padding: 171px 12px 0;
  }
  .udesk-sdk-ticket
    .udesk-sdk-ticket-new-ticket
    .new-ticket-form
    .content-button {
    box-sizing: border-box;
    background-color: rgb(48, 122, 232);
    color: #fff;
    display: block;
    border: none;
    width: calc(100% - 24px);
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 25px;
  }
  .udesk-sdk-ticket .udesk-sdk-ticket-new-ticket {
    padding: 0 !important;
  }
}
</style>
