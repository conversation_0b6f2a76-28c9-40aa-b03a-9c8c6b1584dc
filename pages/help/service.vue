<template>
  <article class="help-service help-service__pc">
    <!-- <section class="section section-1">
      <img src="@/assets/images/help/<EMAIL>">
    </section> -->
    <section class="section section-2">
      <div class="container">
        <div class="nav">
          <div class="nav-item">{{ text }}</div>
        </div>
        <div class="tab-wrap">
          <!-- <div class="tab">
            <div class="item">
              <span
                :class="{'is-active': page === 'human'}"
                @click="handlePageChange('human')"><i class="el-icon-caret-right"></i>人工服务</span>
            </div>
            <div class="item">
              <span
                :class="{'is-active': page === 'process'}"
                @click="handlePageChange('process')"><i class="el-icon-caret-right"></i>服务流程</span>
            </div>
            <div class="item">
              <span
                :class="{'is-active': page === 'content'}"
                @click="handlePageChange('content')"><i class="el-icon-caret-right"></i>服务内容</span>
            </div>
          </div> -->
          <div class="tab-content">
            <div
              v-if="page === 'human'"
              class="tab-panel panel-1">
              <p>我们24小时在线响应你的需求，并提供多种服务方式。</p>
              <div class="content">
                <div class="item" style="cursor: pointer" @click="handleHelp">
				  <i class="iconfont icon-zaixianzixun1"></i>
                  <p>在线客服</p>
                  <span>点击咨询</span>
                </div>
                <div class="item">
                   <i class="iconfont icon-24xiaoshirexian"></i>
                  <p>服务热线</p>
                  <phonecall phoneNumber="************" :showDialog="isMobile"></phonecall>
                </div>
                <div class="item">
                     <i class="iconfont icon-qiyeyouxiang"></i>
                  <p>企业邮箱</p>
                  <span><EMAIL></span>
                </div>
              </div>
            </div>
            <div
              v-if="page === 'process'"
              class="tab-panel panel-2">
              <p>我们提供标准化的服务流程，确保问题可以得到及时满意的解决。</p>
              <div class="img-wrap">
                <img src="@/assets/images/help/<EMAIL>">
              </div>
            </div>
            <div
              v-if="page === 'content'"
              class="tab-panel panel-3">
              <p>我们准备了多元化的服务，满足不同场景下电子签约需求。</p>
              <div class="content">
                <div class="item">
                  <!-- <div class="img img-1"></div> -->
				  <i class="iconfont icon-xuqiudengji"></i>
                  <span>需求反馈</span>
                </div>
                <div class="item">
                  <i class="iconfont icon-daifenxi"></i>
                  <span>问题解析</span>
                </div>
                <div class="item">
                   <i class="iconfont icon-shiwuzhongxin_jinxiupeixun"></i>
                  <span>使用培训</span>
                </div>
                <div class="item">
                   <i class="iconfont icon-jishufuwu"></i>
                  <span>现场技术支持</span>
                </div>
                <div class="item">
                  <i class="iconfont icon-jishuzhichizhichizhinengzhinengfuwufuwuke"></i>
                  <span>紧急救援服务</span>
                </div>
                <div class="item">
                   <i class="iconfont icon-kaifajichushezhi"></i>
                  <span>协助二次开发</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </article>
  <!-- <service-mobile v-else /> -->
</template>

<script>
import { mapState } from 'vuex';
import ServiceMobile from '~/components/ServiceMobile.vue';
export default {
  name: 'HelpService',
  props: {
    page: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      //   page: this.$route.query.page || 'human',
    };
  },
  head() {
    const step = this.$route.query.step || 'process';
    const seo = {
      process: {
        title: '产品服务流程_上上签电子签约云平台',
        keywords: '电子合同使用问题,电子合同',
        description:
          '我们提供标准化的服务流程，确保使用上上签电子合同过程中的问题可以得到及时满意的解决',
      },
      content: {
        title: '产品服务内容_上上签电子签约云平台',
        keywords: '电子合同使用场景,电子签约',
        description: '我们准备多元化服务，满足不同场景下使用上上电子签约的需求',
      },
    };
    return {
      title: seo[step].title,
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: seo[step].keywords,
        },
        {
          hid: 'description',
          name: 'description',
          content: seo[step].description,
        },
      ],
    };
  },
  components: {
    ServiceMobile,
  },
  computed: {
    ...mapState(['isMobile']),
    text() {
      switch (this.page) {
        case 'human':
          return '人工服务';
        case 'process':
          return '服务流程';
        case 'content':
          return '服务内容';
      }
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: 'help' });
  },
  methods: {
    handlePageChange(page) {
      this.page = page;
      this.$router.push({
        query: {
          page,
        },
      });
    },
    handleHelp() {
      this.$store.commit('setServiceVisible', true);
    },
  },
};
</script>

<style scoped lang="scss">
.help-service.help-service__pc {
  .container {
    width: 100%;
  }
  a {
    color: #323232;
  }
  .section-1 {
    padding: 0;
    img {
      width: 100%;
    }
  }
  .section-2 {
    // padding-top: 50px;
    .nav {
      display: flex;
      .nav-item {
        cursor: pointer;
        text-align: center;
        margin: 0 auto;
        color: #090909;
        font-size: 2rem;
        line-height: 2.5rem;
        font-weight: 400;
      }
      .item-1 > a {
        color: #323232;
      }
      .item-1 > a:hover {
        color: #00a664;
      }
      .el-icon-arrow-right {
        margin: 0 10px;
      }
    }
    .tab-wrap {
      margin-top: 40px;
      display: flex;
      .tab {
        width: 30%;
        .item {
          color: #888;
          padding: 8px 0;
          i {
            margin-right: 15px;
          }
          span {
            cursor: pointer;
            &:hover,
            &.is-active {
              color: #00a664;
            }
          }
        }
      }
      .tab-content {
        flex: 1;
        .panel-1 {
          p {
            text-align: center;
          }
          .content {
            margin-top: 80px;
            display: flex;
            text-align: center;
            .iconfont {
              display: block;
              font-size: 3rem;
              color: #00aa64;
              margin-bottom: 2rem;
            }
            .item {
              flex: 1;
              p {
                margin: 30px auto 10px;
                color: #606266;
                font-size: 14px;
              }
              img {
                width: 50px;
              }
            }
          }
        }
        .panel-2 {
          text-align: center;
          p {
            margin-bottom: 50px;
          }
          .img-wrap {
            width: 80%;
          }
          img {
            width: 80%;
          }
        }
        .panel-3 {
          p {
            text-align: center;
          }
          .content {
            margin-top: 80px;
            display: flex;
            flex-wrap: wrap;
            .item {
              margin-bottom: 80px;
              //   display: flex;
              //   align-items: center;
              width: calc(100% / 3);
              text-align: center;
              .iconfont {
                font-size: 3rem;
                color: #00aa64;
                margin-right: 10px;
                margin-bottom: 20px;
                display: block;
              }
            }
          }
        }
      }
    }
  }
}
</style>
