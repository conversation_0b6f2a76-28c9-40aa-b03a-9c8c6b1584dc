<template>
  <article class="help-center" :class="{'help-center__mobile' : isMobile, 'help-center__pc' : !isMobile}">
    <section class="section section-1">
      <div class="container">
        <div class="input-wrap">
          <h1 class="article-headline">你好，请问需要什么帮助？</h1>
          <div>
            <button
              type="button"
              class="ssq-button-primary"
            >
              <nuxt-link to="/help/FAQ" style="color: #fff;">帮助中心</nuxt-link>
            </button>
          </div>
        </div>
        <div class="img-wrap">
          <img src="@/assets/images/help/help@index_bg.jpg">
        </div>
      </div>
    </section>
    <section class="section section-2">
      <div class="container">
        <h3 class="section-headline">用户指南，让你快速上手</h3>
        <div class="nav-wrap">
          <nuxt-link
            to="/help/guide?step=starter"
            class="nav-item">
            <div class="img img-1"></div>
            <p>我刚开始使用上上签</p>
            <img
              class="icon"
              src="@/assets/images/turn-right.png">
          </nuxt-link>
          <nuxt-link
            to="/help/guide?step=deeper"
            class="nav-item">
            <div class="img img-2"></div>
            <p>我想更深入的使用</p>
            <img
              class="icon"
              src="@/assets/images/turn-right.png">
          </nuxt-link>
        </div>
      </div>
    </section>
    <section class="section section-3">
      <div class="container">
        <h3 class="section-headline">帮助中心，迅速答疑解惑</h3>
        <nuxt-link
          to="/help/FAQ"
          target="_blank"
          class="content">
          <div class="item">
            <i class="icon icon-1"></i>
            <span>注册登录问题</span>
          </div>
          <div class="item">
            <i class="icon icon-2"></i>
            <span>实名认证问题</span>
          </div>
          <div class="item">
            <i class="icon icon-3"></i>
            <span>合同签署问题</span>
          </div>
          <div class="item">
            <i class="icon icon-4"></i>
            <span>合同管理问题</span>
          </div>
          <div class="item">
            <i class="icon icon-5"></i>
            <span>法律法规问题</span>
          </div>
          <div class="item">
            <i class="icon icon-6"></i>
            <span>产品费用问题</span>
          </div>
          <div class="item">
            <i class="icon icon-7"></i>
            <span>接口类问题</span>
          </div>
        </nuxt-link>
      </div>
    </section>
    <section class="section section-4">
      <div class="container">
        <h3 class="section-headline">24小时在线为你服务</h3>
        <div class="content">
          <div class="item">
            <i class="icon icon-1"></i>
            <phonecall phoneNumber="************" :showDialog="isMobile"></phonecall>
          </div>
          <div class="item">
            <i class="icon icon-2"></i>
            <span><EMAIL></span>
          </div>
        </div>
      </div>
    </section>
    <section class="section section-5">
      <div class="container">
        <h3 class="section-headline">标准化服务流程为你快速响应</h3>
        <div class="content">
          <nuxt-link
            to="/help/service?page=human"
            class="item">
            <i class="icon icon-1"></i>
            <p>人工服务</p>
          </nuxt-link>
          <nuxt-link
            to="/help/service?page=process"
            class="item">
            <i class="icon icon-2"></i>
            <p>服务流程</p>
          </nuxt-link>
          <nuxt-link
            to="/help/service?page=content"
            class="item">
            <i class="icon icon-3"></i>
            <p>服务内容</p>
          </nuxt-link>
        </div>
      </div>
    </section>
    <section class="section section-6">
      <div class="container">
        <h3 class="section-headline">更多帮助方式</h3>
        <div class="content">
          <!-- 老板要求，视频不公开 -->
         <!-- <nuxt-link
            to="/help/video"
            class="item">
            <i class="icon icon-1"></i>
            <span>视频课堂</span>
          </nuxt-link>-->
          <nuxt-link
            to="/help/feedback"
            class="item">
            <i class="icon icon-2"></i>
            <span>问题反馈</span>
          </nuxt-link>
        </div>
      </div>
    </section>
  </article>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'HelpIndex',
  head() {
    return {
      title: '服务中心_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content:
            '电子合同使用帮助,电子合同使用问题,电子合同使用方法,电子合同使用介绍',
        },
        {
          hid: 'description',
          name: 'description',
          content: '服务中心帮你快速解决电子合同使用中的问题。',
        },
      ],
    };
  },
  computed: mapState(['isMobile']),
  mounted() {
    this.$store.commit('changePath', { path: 'resource' });
  },
};
</script>

<style scoped lang="scss">
.help-center.help-center__pc {
  text-align: center;
  a {
    color: #323232;
  }
  .section-3,
  .section-5 {
    background-color: #fafbfc;
  }
  .section-1 {
    padding: 0;
    > .container {
      display: flex;
      align-items: center;
      justify-content: center;
      .input-wrap {
        margin-right: 140px;
        text-align: left;
      }
      .article-headline {
        margin-bottom: 70px;
      }
      .ssq-input {
        width: 335px;
      }
      .img-wrap {
        width: 400px;
        img {
          width: 100%;
        }
      }
    }
  }
  .section-2 {
    .nav-wrap {
      width: 850px;
      margin: 75px auto 0;
      display: flex;
      .nav-item {
        flex: 1;
        cursor: pointer;
        .img {
          width: 58px;
          height: 58px;
          margin: 0 auto;
          background-image: url(~assets/images/help/<EMAIL>);
        }
        p {
          margin: 30px auto 20px;
          font-size: 16px;
        }
        .img-1 {
          background-position: -14px -11px;
        }
        .img-2 {
          background-position: -72px -11px;
        }
        &:hover {
          .img-1 {
            background-position: -14px -69px;
          }
          .img-2 {
            background-position: -72px -69px;
          }
        }
      }
    }
  }
  .section-3 {
    padding-bottom: 90px;
    .content {
      width: 1120px;
      margin: 75px auto 0;
      display: flex;
      flex-wrap: wrap;
      .item {
        margin-bottom: 40px;
        padding-right: 115px;
        display: flex;
        width: 25%;
        align-items: center;
        cursor: pointer;
        &:hover {
          span {
            color: #00a664;
          }
        }
      }
      i {
        width: 37px;
        height: 37px;
        display: inline-block;
        margin-right: 30px;
        background-image: url(~assets/images/help/<EMAIL>);
      }
      .icon-1 {
        background-position: -10px -197px;
      }
      .icon-2 {
        background-position: -49px -197px;
      }
      .icon-3 {
        background-position: -88px -197px;
      }
      .icon-4 {
        background-position: -127px -197px;
      }
      .icon-5 {
        background-position: -166px -197px;
      }
      .icon-6 {
        background-position: -205px -197px;
      }
      .icon-7 {
        background-position: -244px -197px;
      }
    }
  }
  .section-4,
  .section-5,
  .section-6 {
    .content {
      width: 860px;
      margin: 75px auto 0;
      display: flex;
    }
    .icon {
      background-image: url(~assets/images/help/<EMAIL>);
    }
    .item {
      flex: 1;
    }
    span {
      font-size: 18px;
    }
  }
  .section-4,
  .section-6 {
    .item {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .icon {
      width: 58px;
      height: 58px;
      margin-right: 30px;
      display: inline-block;
    }
    .icon-1 {
      background-position: -6px -243px;
    }
    .icon-2 {
      background-position: -64px -243px;
    }
  }
  .section-5 {
    .item {
      cursor: pointer;
    }
    .icon {
      display: block;
      height: 30px;
      width: 35px;
      margin: 0 auto;
    }
    p {
      font-size: 18px;
      margin-top: 30px;
    }
    .icon-1 {
      background-position: -10px -128px;
    }
    .icon-2 {
      width: 40px;
      background-position: -45px -128px;
    }
    .icon-3 {
      background-position: -83px -128px;
    }
    .item:hover {
      .icon-1 {
        background-position: -10px -161px;
      }
      .icon-2 {
        background-position: -45px -161px;
      }
      .icon-3 {
        background-position: -83px -161px;
      }
    }
  }
  .section-6 {
    .item {
      cursor: pointer;
    }
    .icon {
      width: 60px;
      height: 60px;
    }
    .icon-1 {
      background-position: -123px -243px;
    }
    .icon-2 {
      background-position: -184px -243px;
    }
  }
}
// mobile 样式
.help-center.help-center__mobile {
  .ssq-button-primary {
    width: 164px;
    background: linear-gradient(90deg, #019b89, #06b9a5);
  }
  .section {
    padding: 0;
    .section-headline {
      color: #383838;
      font-size: 20px;
      font-weight: 400;
      text-align: center;
    }
  }
  .section-1 {
    > .container {
      .input-wrap {
        text-align: center;
        .article-headline {
          font-size: 28px;
          color: #383838;
          font-weight: normal;
          margin-bottom: 30px;
          margin-top: 50px;
        }
      }
      .img-wrap {
        width: 232px;
        height: 176px;
        margin: 50px auto;
        img {
          width: 100%;
        }
      }
    }
  }
  .section-2 {
    .container {
      height: 273px;
      background-color: #fafbfc;
      overflow: hidden;
      .section-headline {
        margin: 50px 0 40px 0;
      }
      .nav-wrap {
        display: flex;
        flex-direction: row;
        justify-content: center;
        .nav-item {
          text-align: center;
          &:first-child {
            margin-right: 56px;
          }
          p {
            margin: 20px 0 15px 0;
            color: #323232;
            font-size: 14px;
          }
          .img {
            width: 58px;
            height: 58px;
            margin: 0 auto;
            background-image: url(~assets/images/help/<EMAIL>);
            transform: scale(0.64); // 37/58
          }
          .img-1 {
            background-position: -14px -11px;
          }
          .img-2 {
            background-position: -72px -11px;
          }
          &:hover {
            .img-1 {
              background-position: -14px -69px;
            }
            .img-2 {
              background-position: -72px -69px;
            }
          }
        }
      }
    }
  }
  .section-3 {
    padding: 53px 30px;
    height: 350px;
    .container {
      .section-headline {
        margin-bottom: 61px;
      }
      .content {
        .item {
          color: #323232;
          font-size: 14px;
          display: inline-block;
          width: 49%;
          height: 37px;
          span {
            height: 37px;
            vertical-align: top;
            display: inline-block;
            line-height: 37px;
          }
          &:hover {
            span {
              color: #00a664;
            }
          }
          i {
            width: 37px;
            height: 37px;
            display: inline-block;
            margin-right: 10px;
            background-image: url(~assets/images/help/<EMAIL>);
            transform: scale(0.64); // 24/37
          }
          .icon-1 {
            background-position: -10px -197px;
          }
          .icon-2 {
            background-position: -49px -197px;
          }
          .icon-3 {
            background-position: -88px -197px;
          }
          .icon-4 {
            background-position: -127px -197px;
          }
          .icon-5 {
            background-position: -166px -197px;
          }
          .icon-6 {
            background-position: -205px -197px;
          }
          .icon-7 {
            background-position: -244px -197px;
          }
        }
      }
    }
  }
  .section-4 {
    background: #fafbfc;
    text-align: center;
    padding: 52px 0 50px 0;
    .content {
      margin-top: 47px;
      .item {
        color: #323232;
        font-size: 14px;
        display: flex;
        align-items: center;
        padding-left: 25vw;
        .icon {
          background-image: url(~assets/images/help/<EMAIL>);
        }
        .icon {
          width: 58px;
          height: 58px;
          margin-right: 30px;
          display: inline-block;
          transform: scale(0.64); // 37/58
        }
        .icon-1 {
          background-position: -6px -243px;
        }
        .icon-2 {
          background-position: -64px -243px;
        }
      }
    }
  }
  .section-5 {
    padding: 46px 0 52px 0;
    .content {
      display: flex;
      flex-direction: row;
      justify-content: center;
      .item {
        margin: 0 30px;
        margin-top: 58px;
        .icon {
          display: block;
          height: 30px;
          width: 35px;
          margin: 0 auto;
          background-image: url(~assets/images/help/<EMAIL>);
          transform: scale(0.64); // 37/58
        }
        p {
          font-size: 14px;
          margin-top: 30px;
        }
        .icon-1 {
          background-position: -10px -128px;
        }
        .icon-2 {
          width: 40px;
          background-position: -45px -128px;
        }
        .icon-3 {
          background-position: -83px -128px;
        }
        .item:hover {
          .icon-1 {
            background-position: -10px -161px;
          }
          .icon-2 {
            background-position: -45px -161px;
          }
          .icon-3 {
            background-position: -83px -161px;
          }
        }
      }
    }
  }
  .section-6 {
    border-top: 1px solid #cecece;
    padding: 46px 0;
    .content {
      display: flex;
      flex-direction: row;
      justify-content: center;
      margin-top: 43px;
      .item {
        display: flex;
        justify-content: center;
        align-items: center;
        &:first-child {
          margin-right: 81px;
        }
        .icon {
          width: 60px;
          height: 60px;
          display: inline-block;
          background-image: url(~assets/images/help/<EMAIL>);
          transform: scale(0.57); // 34/60
        }
        .icon-1 {
          background-position: -123px -243px;
        }
        .icon-2 {
          background-position: -184px -243px;
        }
      }
    }
  }
}
</style>
