<template>
  <article class="help-guide" :class="{'help-guide__mobile' : isMobile, 'help-guide__pc' : !isMobile}">
    <!-- <section class="section section-1" v-if="!isMobile">
      <img src="@/assets/images/help/<EMAIL>">
    </section> -->
    <!-- <section class="section section-2">
      <div class="container">
        <div class="nav-wrap">
          <div
            :class="{'is-active': active === 'starter'}"
            class="nav-item"
            @click="handleTab('starter')">
            <div class="img img-1"></div>
            <p>我刚开始使用上上签</p>
          </div>
        </div>
        <div class="nav-wrap">
          <div
            :class="{'is-active': active === 'deeper'}"
            class="nav-item"
            @click="handleTab('deeper')">
            <div class="img img-2"></div>
            <p>我想更深入的使用</p>
          </div>
        </div>
      </div>
    </section> -->
    <section class="section section-3">
      <div class="container">
        <div class="para para-title">
          <p class="title">{{ content.para1.title }}</p>
          <p class="desc">{{ content.para1.desc }}</p>
        </div>
        <div class="video-wrap">
          <template v-if="!isMobile">
            <div class="mask">
              <img
                src="~/assets/images/icon/<EMAIL>"
                @click="handleShowVideo">
            </div>
            <img src="https://static.bestsign.cn/e0d7540bdba6af16d25d67a68595a25b6c54e702.png">
          </template>
          <video
            v-else
            :src="content.url"
            ref="video"
            controls
          ></video>
        </div>
        <div class="para">
          <p class="title">{{ content.para2.title }}</p>
          <p class="desc">{{ content.para2.desc }}</p>
        </div>
        <div class="para">
          <p class="title">{{ content.para3.title }}</p>
          <p class="desc">{{ content.para3.desc }}</p>
        </div>
        <div class="para">
          <p class="title">{{ content.para4.title }}</p>
          <p class="desc">{{ content.para4.desc }}</p>
        </div>
      </div>
    </section>
    <v-video
      :visible="dialogVisible"
      :videoUrl="content.url"
      @close="dialogVisible = false"></v-video>
  </article>
</template>

<script>
import Video from '~/components/Video.vue';
import { mapState } from 'vuex';
export default {
  name: 'HelpGuide',
  props: {
    active: {
      type: String,
      default: '',
    },
  },
  head() {
    const step = this.$route.query.step || 'starter';
    const seo = {
      starter: {
        title: '新手入门服务_上上签电子签约云平台',
        keywords: '第一次用电子签约平台,签署合同',
        description:
          '从上传合同到完成签署，教你如何第一次使用上上签电子签约平台，开始签署你的第一份合同',
      },
      deeper: {
        title: '深入了解服务_上上签电子签约云平台',
        keywords: '深入了解电子签约平台,管理合同',
        description:
          '从批量合同处理到印章把控，教你如何高效签署合同、管理合同，开启效率飞跃、解放人力的畅快之旅',
      },
    };
    return {
      title: seo[step].title,
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: seo[step].keywords,
        },
        {
          hid: 'description',
          name: 'description',
          content: seo[step].description,
        },
      ],
    };
  },
  data() {
    return {
      //   active: 'starter',
      dialogVisible: false,
    };
  },
  components: {
    'v-video': Video,
  },
  computed: {
    ...mapState(['isMobile']),
    content() {
      const content = {
        starter: {
          para1: {
            title: '新手入门',
            desc: '欢迎使用上上签电子签约平台！三步就能开始签署你的第一份合同',
          },
          para2: {
            title: '1.上传合同',
            desc:
              '轻点鼠标，上传本地文件即可开始体验。支持上传多个文件，填写合同数据，更便于后续合同管理。',
          },
          para3: {
            title: '2.添加签约方',
            desc:
              '可与企业签，也可以与个人签。还可以支持一次添加多个签约方。为了保障你的权益，企业方需要完成实名认证，才能签署合同。',
          },
          para4: {
            title: '3.发送合同，完成签署',
            desc:
              '点击发送按钮，即可发送合同。签署方会收到短信、邮件提醒，完成签约，即合同完成签署，开始生效。',
          },
          url:
            'https://static.bestsign.cn/16e49e553c56d4b399fa2ce344c3de5ac3ed2fca.mp4',
        },
        deeper: {
          para1: {
            title: '高手进阶',
            desc: '高效签署合同、管理合同，开启效率飞跃、解放人力的畅快之旅。',
          },
          para2: {
            title: '1.批量处理合同',
            desc:
              '可向收件人一次发送多份合同，也可以向多个收件人一次发类似合同，还可以批量签署、审批、提醒、撤销、下载合同，极大的节省你的时间。',
          },
          para3: {
            title: '2.合同自动归档、一键查询',
            desc:
              '设置标签后，即可对所有合同自动进行归档，还可以多状态、多条件查询合同，管理从此无忧。',
          },
          para4: {
            title: '3.账号分级，印章把控',
            desc:
              '企业账号分级管理，印章使用全流程记录在按，并严格把控。在统一管理的同时，又能做到安全放心。',
          },
          url:
            'https://static.bestsign.cn/3834aeb93c8864cf82c84dbe34e8cdaf3b370668.mp4',
        },
      };
      return content[this.active];
    },
  },
  created() {
    // console.log(this.active);
    this.active = this.$route.query.step || 'starter';
    this.$store.commit('changePath', { path: 'help' });
  },
  methods: {
    handleTab(step) {
      this.active = step;
    },
    handleShowVideo() {
      this.dialogVisible = true;
    },
  },
};
</script>

<style scoped lang="scss">
.help-guide__pc {
  a {
    color: #323232;
  }
  .section-1 {
    padding: 0;
    img {
      width: 100%;
    }
  }
  .section-2 {
    padding: 0;
    height: 90px;
    border-bottom: 1px solid #bfbfbf;
    .container {
      width: 90%;
      height: 100%;
      margin: 0 auto;
      display: flex;
      .nav-wrap {
        height: 100%;
        flex: 1;
        display: flex;
        justify-content: center;
      }
      .nav-item {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        height: calc(100% - 1px);
        position: relative;
        bottom: -2px;
        .img {
          width: 42px;
          height: 42px;
          background-image: url(~assets/images/help/<EMAIL>);
          background-size: 220px;
        }
        p {
          margin-left: 24px;
          font-size: 18px;
        }
        .img-1 {
          background-position: -10px -7px;
        }
        .img-2 {
          background-position: -53px -7px;
        }
        &:hover,
        &.is-active {
          .img-1 {
            background-position: -10px -50px;
          }
          .img-2 {
            background-position: -53px -50px;
          }
          p {
            color: #00a664;
          }
          border-bottom: 1px solid #00a664;
        }
      }
    }
  }
  .section-3 {
    .container {
      width: 90%;
      margin: 0 auto;
      .para {
        margin-bottom: 70px;
        .title {
          font-size: 18px;
        }
        .desc {
          color: #888;
          margin-top: 40px;
        }
      }
      .para-title {
        .title {
          text-align: center;
          cursor: pointer;
          text-align: center;
          margin: 0 auto;
          color: #090909;
          font-size: 2rem;
          line-height: 2.5rem;
          font-weight: 400;
        }
      }
      .video-wrap {
        width: 100%;
        height: auto;
        margin-top: 80px;
        margin-bottom: 150px;
        box-shadow: 0 0 1px 0 #00000033;
        position: relative;
        .mask {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
          background-color: rgba(0, 0, 0, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          img {
            width: 46px;
            height: 46px;
          }
        }
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
.help-guide__mobile {
  font-size: 14px;
  text-align: center;
  .section {
    padding: 0;
  }
  .section-2 {
    text-align: center;
    .nav-wrap {
      .nav-item {
        height: 54px;
        line-height: 54px;
        background-color: #eeeeee;
        color: #323232;
      }
      .is-active {
        background-color: #07c3ab !important;
        color: #ffffff;
      }
    }
  }
  .section-3 {
    margin-top: 0px;
    padding: 0 17px;
    padding-bottom: 88px;
    .para.para-title {
      color: #888888;
      padding: 0 13px;
      text-align: center;
      .title {
        font-size: 20px;
        color: #323232;
        margin-bottom: 30px;
      }
    }
    .video-wrap {
      border-radius: 5px;
      width: 100%;
      height: 218px;
      position: relative;
      margin: 39px 0 54px 0;
      overflow: hidden;
      video {
        width: 100%;
        border-radius: 5px;
        position: absolute;
        top: 0;
        left: 0;
        background-color: #a0a0a0;
      }
    }
    .para {
      color: #707070;
      padding: 0 2px;
      text-align: left;
      .title {
        font-size: 14px;
        color: #232323;
        margin: 20px 0 10px 0;
      }
      .desc {
        font-size: 12px;
        line-height: 1.6;
      }
    }
  }
}
</style>
