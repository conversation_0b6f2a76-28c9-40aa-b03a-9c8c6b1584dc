<template>
  <div class="search-result">
	  <document-header></document-header>
    <div class="container">
      <div class="result-title">{{`关于'${$route.query.content}'的${list.length}条结果`}}</div>
      <div v-for="item in list" :key="item.id" class="block">
        <h3 slot="header" v-html="item.docTitle" @click="handleView(item.id)"></h3>
        <div v-html="item.content"></div>
        <div class="more">
			<a href="javascript: void (0)" @click="handleView(item.id)">
				查看详情
				<!-- <i class="iconfont icon-xiangy<PERSON><PERSON><PERSON><PERSON>"></i> -->
				</a></div>
      </div>
    </div>
  </div>
</template>

<script>
import DocumentHeader from '@/components/DocumentHeader';

const format = (keyword, array) => {
  return array.filter(o => o.docTitle.indexOf(keyword) > -1).map(o => {
    // console.log(...o);
    return {
      ...o,
      docTitle: o.docTitle.replace(
        keyword,
        `<span class="highlight">${keyword}</span>`
      ),
    };
  });
};

export default {
  name: 'FAQSearch',
  components: {
    DocumentHeader,
  },
  async asyncData({ route, app, query }) {
    const search = query.content;
    const res = await app.$axios.get('www/api/web/getHelpDoc');
    const filterRes = res;
    const list = format(search, filterRes);
    return {
      list,
      res: filterRes,
    };
  },
  watch: {
    $route: {
      handler(val, oldVal) {
        const keyword = val.query.content;
        this.list = format(keyword, this.res);
      },
    },
  },
  methods: {
    handleView(id) {
      this.$router.push(`/help/FAQ/${id}`);
    },
  },
};
</script>

<style>
.highlight {
  /* background-color: rgba(255, 0, 0, 0.4);  */
  color: #00aa64;
}
</style>
<style scoped lang="scss">
.search-result {
  .container {
    margin-top: 40px;
  }
  width: 100%;
  padding-bottom: 8rem;
  min-height: 100vh;
  .result-title {
    padding-bottom: 2rem;
  }
  .block {
    border-bottom: 1px solid #dadada;
    padding-left: 2rem;
    margin-top: 1rem;
    padding-bottom: 1rem;
    h3 {
      height: 3rem;
      line-height: 3rem;
      cursor: pointer;
    }
    .more {
      //   margin-top: 2rem;
      a {
        color: #00aa64;
        font-size: 0.75rem;
      }
      .iconfont {
        align-items: center;
      }
    }
  }
}
@media (max-width: 768px) {
  .search-result {
    .block {
      .more {
        margin-top: 40px;
      }
    }
  }
}
</style>
