<template>
  <article>
    <bread-nav :menu="menu"></bread-nav>
    <section class="section section-1">
      <div class="container">
        <div class="article">
          <!-- <document-header :searchShow="false"></document-header> -->
          <h1>{{ article.docTitle }}</h1>
          <div
            id="content"
            class="braft-output-content"
            v-html="article.content"></div>
            <nav>
            <div class="nav-item">
              <nuxt-link
                v-if="article.prevId"
                class="nav-item"
                :to="`/help/FAQ/${article.prevId}`"
              >上一篇：{{ article.prevTitle }}</nuxt-link>
            </div>
            <div class="nav-item">
              <nuxt-link
                v-if="article.nextId"
                class="nav-item"
                :to="`/help/FAQ/${article.nextId}`"
              >下一篇：{{ article.nextTitle }}</nuxt-link>
            </div>
            <!-- <div
              class="back"
              @click.stop.prevent="handlePage()">
              <i class="el-icon-arrow-left"></i>
              返回
            </div> -->
          </nav>
        </div>
         <aside class="article-side" v-if="!isLandMobile">
			<div class="icon-box">
				<i class="iconfont icon-weixin">
					<img
					src="@/assets/images/qr-wechat.png"
					alt="二维码">
				</i>
				<a :href="shareUrl" target="_blank"><i class="iconfont icon-weibo"></i></a>
				
				<i class="iconfont icon-fanhui" @click.stop.prevent="handlePage()"></i>
				
			</div>
        </aside>
      </div>
    </section>
  </article>  
</template>

<script>
import DocumentHeader from '@/components/DocumentHeader';
export default {
  name: 'FAQDetail',
  head() {
    return {
      title: this.tdkT || '上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keyword',
          content: this.tdkK || '电子合同,电子合同签署,电子签名,电子签章',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            this.tdkD ||
            '上上签是业内领先的在线电子合同签署，电子签名，电子签章的云服务平台，提供多种行业专属解决方案。',
        },
      ],
    };
  },
  async asyncData({ route, app, redirect }) {
    const id = route.params.id;
    const res = await app.$axios.get(`www/api/web/getHelpDoc/${id}`);

    // if (res.length === 0 || res[0].column != 'helpDoc') {
    //   redirect('/404');
    // }
    return {
      article: res,
      // seo: res[0].summary ? JSON.parse(res[0].summary) : {},
      tdkT: res.tdkT,
      tdkD: res.tdkD,
      tdkK: res.tdkK,
    };
  },
  components: {
    DocumentHeader,
  },
  data() {
    return {
      menu: [
        {
          name: '帮助中心',
          to: '/help/FAQ',
        },
        {
          name: '详情',
          to: '',
        },
      ],
      shareUrl: '',
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  mounted() {
    this.shareUrl =
      'http://service.weibo.com/share/share.php?url=' +
      window.location.href +
      '&sharesource=weibo&title=' +
      this.article.docTitle;
  },
  methods: {
    handlePage(id) {
      this.$router.go(-1);
    },
  },
};
</script>
<style>
#content .video-wrap,
#content video {
  max-width: 100%;
}
#content {
  padding-bottom: 45px;
  border-bottom: 1px solid #e5e5e5;
}
</style>
<style scoped lang="scss">
.section-1 {
  background-color: #fafbfc;
  padding: 30px 0;
  .container {
    display: flex;
    .article {
      padding: 2.5rem 3rem 4rem;
      background-color: #fafbfc;
      //   border: 1px solid #e5e5e5;
      border-radius: 5px;
      flex: 1;
    }
    h1 {
      font-size: 30px;
      margin-bottom: 2.5rem;
      color: #090909;
    }
  }
  aside {
    .icon-box {
      width: auto;
      margin-left: 16px;
      display: flex;
      flex-direction: column;
      text-align: center;
      .iconfont {
        font-size: 20px;
        cursor: pointer;
        color: #bfbfbf;
        margin: 5px 0;
        &:hover {
          color: #00a664;
        }
      }
      .icon-weixin {
        position: relative;
        img {
          position: absolute;
          width: 100px;
          right: 40px;
          top: -20px;
          display: none;
        }
        &:hover img {
          display: block;
        }
      }
    }

    .item-wrap {
      background-color: #fff;
      border: 1px solid #e5e5e5;
      border-radius: 5px;
      padding: 24px 18px;
      // margin-bottom: 15px;
      .img-wrap {
        width: 100%;
        text-align: center;
        img {
          width: 100%;
        }
      }
      h4 {
        margin: 36px auto;
        font-size: 18px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e5e5e5;
      }
      .item {
        margin-bottom: 25px;
        cursor: pointer;
        color: #888;
        &:hover {
          color: #00a664;
        }
      }
    }
    .qrcode {
      min-width: 200px;
      margin-top: 20px;
      display: flex;
      justify-content: space-around;
      img {
        width: 80px;
        height: 80px;
      }
      p {
        // margin-top: 0.25rem;
        font-size: 12px;
        // margin-left: 0.3rem;
      }
    }
  }
  nav {
    // border-top: 1px solid #e5e5e5;
    margin-top: 25px;
    position: relative;
    .nav-item {
      cursor: pointer;
      padding: 10px 0;
      color: #515151;
      &:hover {
        color: #00a664;
      }
    }
    .back {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 10px;
      .el-icon-arrow-left {
        padding-right: 5px;
        font-size: 22px;
        position: relative;
        top: 2px;
      }
      &:hover {
        color: #00a664;
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .section-1 {
    .container {
      .article {
        padding: 30px 20px 50px;
      }
      h3 {
        font-size: 24px;
        margin-bottom: 40px;
      }
      nav {
        .back {
          cursor: pointer;
          left: 0;
          bottom: -35px;
          top: auto;
        }
      }
    }
    img {
      max-width: 100%;
    }
  }
}
</style>
