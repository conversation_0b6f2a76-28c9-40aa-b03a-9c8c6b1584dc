<template>
  <!-- <article class="help-center" :class="{'help-center__mobile' : isMobile, 'help-center__pc' : !isMobile}"> -->
  <article class="help-center">
    <section class="section section-1 banner">
      <div class="container">
        <div class="input-wrap">
          <h1 class="article-headline">请问需要什么帮助？</h1>
          <div class="help-search">
			<el-input v-model="searchValue" placeholder="请输入内容" @keyup.enter.native="searchDoc">
				 <el-button slot="append" icon="el-icon-search" @click="searchDoc"></el-button>
			</el-input>
          </div>
        </div>
      
      </div>
    </section>
    <section class="section section-2">
      <div class="container">
		    <el-row class="nav-wrap" :gutter='20'>
				<el-col :xs="24" :sm="8">
					<div class="nav-item">
						<div class="wrap-top">
							<i class="iconfont icon icon-yonghuzhinan"></i>
							<p class="wrap-title">用户指南</p>
							<p class="wrap-desc">让你更快速上手</p>
						</div>
						<div class="wrap-bottom">
							<!-- <nuxt-link  to="/help/guide?step=starter"> -->
								<p @click="helpDialog('guide','starter')">我刚开始使用上上签
									<span class="el-icon-arrow-right"></span>
								</p>
							<!-- </nuxt-link> -->
							<!-- <nuxt-link  to="/help/guide?step=deeper"> -->
								<p @click="helpDialog('guide','deeper')">我想更深入的使用
									<span class="el-icon-arrow-right"></span>
								</p>
							<!-- </nuxt-link> -->
						</div>
					</div>
				</el-col>
				<el-col :xs="24" :sm="8">
					<div class="nav-item">
						<div class="wrap-top">
							<i class="iconfont icon icon-fuwuliucheng"></i>
							<p class="wrap-title">标准化服务流程</p>
							<p class="wrap-desc">为你快速响应</p>
						</div>
						<div class="wrap-bottom">
							  <!-- <nuxt-link
								to="/help/service?page=human"
								class="item"> -->
								<p @click="helpDialog('service','human')">人工服务	<span class="el-icon-arrow-right"></span></p>
							<!-- </nuxt-link> -->
							<!-- <nuxt-link
								to="/help/service?page=process"
								class="item"> -->
								<p @click="helpDialog('service','process')">服务流程 <span class="el-icon-arrow-right"></span></p>
							<!-- </nuxt-link> -->
							<!-- <nuxt-link
								to="/help/service?page=content"
								class="item"> -->
								<p @click="helpDialog('service','content')">服务内容	<span class="el-icon-arrow-right"></span></p>
							<!-- </nuxt-link> -->
						</div>
					</div>
				</el-col>
				<el-col :xs="24" :sm="8">
					<div class="nav-item">
						<div class="wrap-top">
							
							<i class="iconfont icon icon-bangzhufangshi"></i>
							<p class="wrap-title">更多帮助方式</p>
							<p class="wrap-desc">你还可以直接反馈</p>
						</div>
						<div class="wrap-bottom">
								<p>咨询热线：400-993-6665 </p>
								<p>企业邮箱：<EMAIL>  </p>
						</div>
					</div>
				</el-col>
		    </el-row>
      </div>
    </section>
    <section class="section section-3">
      <div class="container">
          <el-row class="common-problem">
			  	<el-col :xs="24" :sm="8" class="problem-item" v-for="option in options" :key="option.code">
					<div class="problem-icon">
						<i :class="['iconfont',option.icon]"></i>
					</div>
					<h3 class="problem-title">
						{{option.codeValue}}
					</h3>
					<div class="problem-content">
						<nuxt-link :to="`/help/FAQ/${item.id}`" v-for="item in list.filter(o => o.docType === option.code)" :name="item.id" :key="item.id" class="more">
							<p>{{item.docTitle}}</p>	
						</nuxt-link>
						
					</div>
				</el-col>
          </el-row>
      </div>
    </section>
	<el-dialog
		title=""
		:visible.sync="helpVisibile"
		:width="isMobile?'90%':'60%'"
		center>
		<Guide v-if="dialogType == 'guide'"   :active=pageSection></Guide>
		<Service v-if="dialogType=='service'" :page=pageSection></Service>
		
		</el-dialog>
  </article>
</template>

<script>
import { mapState } from 'vuex';
import Guide from '../guide.vue';
import Service from '../service.vue';

export default {
  name: 'HelpIndex',
  components: {
    Guide,
    Service,
  },
  head() {
    return {
      title: '服务中心_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content:
            '电子合同使用帮助,电子合同使用问题,电子合同使用方法,电子合同使用介绍',
        },
        {
          hid: 'description',
          name: 'description',
          content: '服务中心帮你快速解决电子合同使用中的问题。',
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.bestsign.cn/help/FAQ',
        },
      ],
    };
  },
  data() {
    return {
      searchValue: '',
      helpVisibile: false,
      dialogType: '', //弹框类型
      pageSection: '', //页面显示部分
    };
  },
  async asyncData({ route, app }) {
    const res0 = await app.$axios.get('/www/api/web/category/helpDoc');
    const res = await app.$axios.get('www/api/web/getHelpDoc');
    res0.forEach(item => {
      switch (item.code) {
        case 'common':
          item.icon = 'icon-changjianwenti';
          break;
        case 'register':
          item.icon = 'icon-zhucedenglu';
          break;
        case 'authentication':
          item.icon = 'icon-shimingrenzheng';
          break;
        case 'contractSign':
          item.icon = 'icon-hetongfaqiqianshu';
          break;
        case 'manage':
          item.icon = 'icon-hetongguanli';
          break;
        case 'law':
          item.icon = 'icon-falvfagui';
          break;
        case 'price':
          item.icon = 'icon-chanpinfeiyong';
          break;
        case 'interface':
          item.icon = 'icon-jiekoulei';
          break;
        default:
          item.icon = '';
      }
    });
    return {
      list: res,
      options: res0,
    };
  },
  computed: mapState(['isMobile']),
  mounted() {
    this.$store.commit('changePath', { path: 'resource' });
  },
  methods: {
    helpDialog(type, page) {
      console.log(type, page);
      this.dialogType = type;
      this.pageSection = page;
      this.helpVisibile = true;
    },
    searchDoc() {
      this.$router.push({
        path: '/help/FAQ/search',
        query: {
          content: this.searchValue,
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.help-center {
  text-align: center;
  a {
    color: #323232;
  }
  .banner {
    width: 100%;
    height: 25vw;
    background-size: cover;
    background-image: url(~assets/images/help/help-banner.jpg);
    background-position: 0 75%;
    background-repeat: no-repeat;
    font-size: 1.25vw;
  }
  .section-3,
  .section-5 {
    background-color: #fafbfc;
  }
  .section-1 {
    padding: 0;
    > .container {
      display: flex;
      align-items: center;
      justify-content: center;
      .help-search {
        min-width: 600px;
        /deep/ .el-button {
          background: #fff;
        }
        /deep/ .el-input__inner {
          border-right: none;
        }
        /deep/ .el-input__inner:focus {
          border-color: #dcdfe6;
        }
      }
      .input-wrap {
        margin-top: 4rem;
        text-align: left;
      }
      .article-headline {
        margin-bottom: 4.375rem;
        text-align: center;
        color: #fff;
        font-size: 2.875rem;
        font-weight: 500;
      }
      .ssq-input {
        width: 335px;
      }
      .img-wrap {
        width: 400px;
        img {
          width: 100%;
        }
      }
    }
  }
  .section-2 {
    border-bottom: 1px solid #ebeef5;
    .nav-wrap {
      margin: 75px auto 0;
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      .nav-item {
        padding: 30px 25px;
        border: 1px solid #ebeef5;
        flex: 1;
        border-radius: 2px;
        cursor: pointer;
        .iconfont {
          font-size: 4rem;
          color: #00aa64;
        }
        // &:hover .iconfont {
        //   color: #00aa64;
        // }
        .wrap-top {
          padding: 0 0 20px;
          border-bottom: 1px solid #ebeef5;
          .wrap-title {
            font-size: 20px;
            padding: 25px 0 10px;
            color: #1d1d1f;
            font-weight: 400;
          }
          .wrap-desc {
            font-size: 14px;
            line-height: 1.5;
            color: #86868b;
          }
        }
        .wrap-bottom {
          padding: 20px 0 30px;
          min-height: 178px;
          p {
            font-size: 14px;
            padding: 10px;
            margin-bottom: 5px;
            span {
              float: right;
              visibility: hidden;
            }
          }
          p:hover {
            background: #f4f4f4;
            border-radius: 20px;
            span {
              visibility: visible;
            }
          }
        }
        &:hover {
          box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.4);
        }
      }
    }
  }
  .section-3 {
    background: #fff;
    padding: 0 2rem 5rem 2rem;
    border-bottom: 1px solid #ebeef5;
    .common-problem {
      display: flex;
      flex-wrap: wrap;
      .problem-item {
        text-align: left;
        padding: 50px 20px;
        border-bottom: 1px solid #eee;
        padding-bottom: 2rem;
        .iconfont {
          color: #00aa64;
          font-size: 3rem;
        }
      }
      .problem-icon {
        .icon {
          font-size: 40px;
          margin: 0 15px;
        }
      }
      h3 {
        font-size: 20px;
        margin: 40px 0 30px;
      }
      .problem-content {
        p {
          font-size: 14px;
          color: #86868b;
          line-height: 1.4;
          margin-bottom: 15px;
        }
        p:hover {
          // color: #333;
          color: #00aa64;
        }
      }
    }
  }
}
@media (max-width: 768px) {
  .help-center {
    .banner {
      height: 100vw;
      background-position: 20% 100%;
    }
    .section-1 {
      .container {
        .input-wrap {
          margin-top: 11rem;
        }
        .help-search {
          min-width: auto;
        }
        .article-headline {
          margin-bottom: 2rem;
          font-size: 28px;
        }
      }
    }
    .section-2 {
      .nav-wrap {
        .nav-item {
          margin-bottom: 2rem;
          .wrap-bottom {
            min-height: auto;
            padding: 20px 0 0;
            p {
              font-size: 14px;
              color: #86868b;
            }
          }
        }
      }
    }
    .section-3 {
      .common-problem {
        .problem-item {
          padding: 50px 10px;
        }
        .problem-item:last-child {
          border-bottom: none;
        }
      }
    }
  }
}
</style>
