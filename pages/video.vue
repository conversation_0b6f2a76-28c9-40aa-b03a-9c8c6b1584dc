<template>
  <article class="help-video">
	<section class="banner">
		<div class="container">
			<h1 class="article-headline">观看操作视频<br>了解如何使用SaaS电子合同</h1>
			<!-- <p  class="article-text">注册成为上上签官方微信会员，快速跻身电子签名专家。</p> -->
		</div>
	</section>
     <section class="section section-1 is-tiny">
      <div class="container">
        <el-row>
          <template v-for="(item, index) in list">
             <el-col
              :key="index"
              :md="12"
              :lg="8"
              :sm="8"
            >
              <div class="card">
                <div
                  class="card-image"
                  @click="handleShowVideo(formatUrl(item.videoUrl),item.id)">
                  <div class="mask">
                    <img src="~/assets/images/icon/icon@play_b.png">
                  </div>
                  <img :src="formatUrl(item.imgUrl)" class="video-img">
                </div>
                <div class="card-content">
                  <h4 class="header">{{ item.videoTitle }}</h4>
                  <!-- <p class="body">{{ item.videoDescription }}</p> -->
                </div>
              </div>
            </el-col>
          </template>
        </el-row>
        <!-- <div class="button-wrap">
          <el-pagination
		  background
            :page-size="12"
            :current-page="current"
            :total="total"
            @current-change="handleCurrentChange"
            layout="prev, pager, next">
			</el-pagination>
        </div> -->
      </div>
    </section>
	<v-video
      :visible="dialogVisible"
      :videoUrl="videoUrl"
      @close="dialogVisible = false"></v-video>
  </article>
</template>

<script>
import Video from '@/components/Video.vue';
export default {
  name: 'HelpVideo',
  components: {
    'v-video': Video,
  },
  head() {
    return {
      title: '电子签约相关视频_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子签约介绍视频，合同操作介绍视频',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '通过视频, 快速了解上上签电子签约平台主要功能，包括个人和企业实名认证，合同签署操作以及如何发起合同',
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.bestsign.cn/video',
        },
      ],
    };
  },
  data() {
    return {
      dialogVisible: false,
      videoUrl: '',
    };
  },
  async asyncData({ route, app }) {
    const res = await app.$axios.get('www/api/web/getOperateVideo');
    return {
      list: res,
    };
  },
  mounted() {
    this.$store.commit('changePath', { path: 'resource' });
  },
  methods: {
    handleShowVideo(videoUrl, id) {
      window._hmt &&
        window._hmt.push(['_trackEvent', 'help-video', 'click', videoUrl]);
      this.dialogVisible = true;
      this.videoUrl = videoUrl;
      //调用此接口的目的是为了做播放次数统计因为播放的内容已经在list接口拿到了
      this.$axios.get(`www/api/web/getOperateVideo/${id}`).then(res => {});
    },
    formatUrl(url) {
      const arr = url && url.split('?');
      return url && arr[0];
    },
  },
};
</script>

<style scoped lang="scss">
.help-video {
  background: #f9f9f9;
}
.article-headline {
  margin: 90px 0 50px;
  text-align: center;
  line-height: 1.7;
  color: #090909;
  font-size: 2rem;
  line-height: 2.5rem;
  font-weight: 400;
}
.article-text {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.7;
  font-size: 18px;
}
// .banner {
//   background-color: #f2f2f4;
//   position: relative;
//   padding: 0;
//   width: 100%;
//   color: #fff;
//   height: 41.67vw;
//   background-size: 100% 100%;
//   background-image: url('https://static.bestsign.cn/a1c00a4fffb55d2c5adf1f90a4fada1d650df998.jpg');
//   padding-top: 12rem;
// }
.section-1 {
  text-align: center;
  a {
    color: #323232;
  }
  .container {
    max-width: 70.75rem;
  }
  .card {
    // height: 265px;
    .card-image {
      position: relative;
      .mask {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background-color: rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 28;
        img {
          width: 48px;
        }
      }
      img {
        width: 100%;
      }
      .video-img {
        height: 100%;
      }
    }
    .card-content {
      padding: 36px 20px;
      p {
        color: #888;
      }
    }
  }
  .video-wrap {
    width: 690px;
    height: 370px;
    margin: 80px auto 150px;
    box-shadow: 0 0 80px 0 rgba(0, 0, 0, 0.2);
    position: relative;
    background-color: #fff;
    .mask {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background-color: rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 28;
      img {
        width: 46px;
        height: auto;
      }
    }
  }
}

@media (max-width: 1024px) {
  .help-video .section-1 .container {
    top: 40px;
    .video-wrap {
      width: 500px;
      height: 250px;
    }
  }
}
@media screen and (max-width: 767px) {
  .help-video {
    .container {
      .card {
        // height: 310px;
        .card-content {
          padding: 25px 20px;
          height: auto;
          .body {
            line-height: 2.2rem;
          }
        }
      }
    }
    .article-headline {
      font-size: 2.2rem;
      margin-bottom: 30px;
      font-weight: 500;
      color: #090909;
      margin: 50px 0 0;
      line-height: 1.4;
    }
    .section-1 {
      padding-top: 0;
      .container {
        top: 0;
      }
    }
  }
}
</style>
