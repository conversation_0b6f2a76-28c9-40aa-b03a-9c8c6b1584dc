import moment from 'moment';
import { Message } from 'element-ui';
import axios from 'axios';
function checkStatus() {
  const query = this.$route.query;
  const entId = query.entId;
  const devId = query.devId;
  const code = query.code;
  return {
    entId,
    devId,
    code,
  };
}
const $axios = axios.create({
  baseURL: process.env.baseUrl,
});
async function sendCount(operation) {
  const url = `/www/api/web/annual/bill/stats?operation=${operation}`;
  $axios.get(url);
}
/**
 * 异步获取数据的复用函数，作为组件的methods使用
 * @return {Promise<boolean>}
 */
async function getData() {
  let url = '';
  if (this.entId !== undefined) {
    url = `/www/api/web/annual/bill/ent?entId=${this.entId}&code=${this.code}`;
  } else if (this.devId !== undefined) {
    url = `/www/api/web/annual/bill/dev?devId=${this.devId}&code=${this.code}`;
  }
  // url = 'https://www.easy-mock.com/mock/5c1766976ccaa7461ef01eda/annual/data' // 开发用mock
  const response = await $axios.get(url);
  const date = response.headers.date || new Date().getTime();
  const { data: resData } = response;
  const data = resData.data;
  if (resData.data === null) {
    Message.error(resData.message);
    return false;
  }
  data.currentDate = moment(date).valueOf();
  Object.assign(this.textData, parseData(data));
  if (
    typeof data.maxEnterpriseName !== 'string' ||
    data.maxEnterpriseName.length === 0
  ) {
    document.querySelector('.maxEnterpriseName').style.visibility = 'hidden';
  }
}

/**
 * 解析并拼装数据
 * @param data
 */
function parseData(data) {
  const {
    firstUseDay,
    sendCount,
    finishCount,
    myEnterpriseCount,
    myPersonalCount,
    maxEnterpriseName,
    maxCountDay,
    maxCount,
    contractPages,
    protectTrees,
    reduceEmissions,
    savingTims,
    savingCost,
    currentDate,
  } = data;
  const textData = {};
  let totalDay = Math.floor(
    (currentDate - moment(firstUseDay).valueOf()) / 86400000
  );
  let maxCountDayFormat = moment(maxCountDay);
  textData.firstUseDay = [
    '初识上上签，在',
    moment(firstUseDay).format('YYYY.M.D'),
  ];
  textData.totalDay = [
    '此后，',
    normalize(totalDay)[0],
    normalize(totalDay)[1],
    '天',
  ];
  textData.sendCount = [
    '可以装满',
    sendCountNormalize(sendCount)[0],
    sendCountNormalize(sendCount)[1],
    '个文件柜',
  ];
  textData.finishCount = [
    '你签署了',
    normalize(finishCount)[0],
    normalize(finishCount)[1],
    '份',
  ];
  textData.myEnterpriseCount = [normalize(myEnterpriseCount).join('')];
  textData.myPersonalCount = [normalize(myPersonalCount).join('')];
  textData.maxEnterpriseName = ['其中和', maxEnterpriseName, '往来最多'];
  textData.maxCountDay = [
    maxCountDayFormat.format('YYYY'),
    '年',
    maxCountDayFormat.format('M'),
    '月',
    maxCountDayFormat.format('D'),
    '日',
  ];
  textData.maxCount = [
    '一共签署了',
    normalize(maxCount)[0],
    normalize(maxCount)[1],
    '份',
  ];
  textData.contractPages = [
    '你一共节约了',
    normalize(contractPages)[0],
    normalize(contractPages)[1],
    '张纸',
  ];
  textData.protectTrees = [
    '保护了',
    normalize(protectTrees)[0],
    normalize(protectTrees)[1],
    '棵树木',
  ];
  textData.reduceEmissions = [
    '减少了',
    normalize(reduceEmissions)[0],
    normalize(reduceEmissions)[1],
    '吨碳排放',
  ];
  textData.savingTims = [
    '上上签为你节约了',
    hourToDay(savingTims)[0],
    hourToDay(savingTims)[1],
    hourToDay(savingTims)[2],
  ];
  textData.savingCost = [
    '帮你省了快递费',
    normalize(savingCost)[0],
    normalize(savingCost)[1],
    '元',
  ];
  textData.today = [
    '这是上上签陪伴你的第',
    normalize(totalDay + 1)[0],
    normalize(totalDay + 1)[1],
    '天',
  ];
  return textData;
}
/* 添加缓存处理 */
const normalize = (function() {
  const cache = {};
  /**
   * 多位数字的处理方法: 最多4位，逢10000进位，保留一位小数
   * @param int
   * @return {array}
   */
  return function(int) {
    const cacheValue = cache[`${int}`];
    if (cacheValue !== undefined) return cacheValue;
    const length = `${int}`.length;
    let unit = '';
    if (length > 12) {
      unit = '万亿';
      int = (+int / 1000000000000).toFixed(1);
    } else if (length > 8) {
      unit = '亿';
      int = (+int / 100000000).toFixed(1);
    } else if (length > 4) {
      unit = '万';
      int = (+int / 10000).toFixed(1);
    } else {
      unit = '';
      int = (+int).toFixed(0);
    }
    let result = [int, unit];
    cache[`${int}`] = result;
    return result;
  };
})();
function hourToDay(hour) {
  hour = +hour;
  let result = [];
  if (hour > 1000000) {
    hour = Math.round(hour / 24 / 365);
    result = result.concat(normalize(hour));
    result.push('年');
  } else {
    result = result.concat(normalize(hour));
    result.push('个小时');
  }
  return result;
}
function sendCountNormalize(int) {
  int = +int;
  let result = [];
  if (int < 200) {
    int = 1;
  } else {
    int = Math.floor((int - 200) / 100) + 1;
  }
  result = result.concat(normalize(int));
  return result;
}
export { getData, parseData, normalize, sendCount, checkStatus };
