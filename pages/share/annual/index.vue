<template>
  <v-touch tag="article" class="annual-box"
           ref="annual-box"
           @swipeup="onSwipe($event, 'up')"
           @swipedown="onSwipe($event, 'down')" :style="`height: ${innerHeight}px;`">
    <Pictorial class="annual-pictorial-mobile"
               :srcArray="src"
               :textSettings="textSettings"
               :textData="textData"
               :showPages="[page]"
               :width="innerWidth"
               :height="innerHeight"
               :backImage="backImage"
               :scale="scale"
    ></Pictorial>
  </v-touch>
</template>

<script>
import anime from 'animejs';
import Pictorial from '@/components/Pictorial';
import {
  portrait_back,
  portrait1,
  portrait2,
  portrait3,
  portrait4,
  portrait5,
  portrait6,
} from '@/assets/images/share/annual';
import { getData, checkStatus } from '../utils';
const IS_EXPIRED = true; // 配置项，活动是否已过期
export default {
  name: 'annualIndex',
  layout: 'layout_share',
  head: {
    title: '2018年企业年度账单',
    meta: [
      {
        hid: 'viewport',
        name: 'viewport',
        content:
          'width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no',
      },
    ],
    script: !IS_EXPIRED
      ? [
          {
            src: 'https://hm.baidu.com/hm.js?aed153eaa05dfe7e3eba517f3ceb5010',
          },
        ]
      : [], // 百度统计
  },
  data() {
    const imageNaturalHeight = 1334;
    const imageNaturalWidth = 750;
    const { entId, devId, code } = this.checkStatus();
    return {
      entId,
      devId,
      code,
      page: 1,
      frameTimer: null,
      innerHeight: 1,
      innerWidth: 1,
      imageNaturalHeight: imageNaturalHeight,
      imageNaturalWidth: imageNaturalWidth,
      src: IS_EXPIRED
        ? [portrait6]
        : [portrait1, portrait2, portrait3, portrait4, portrait5, portrait6],
      backImage: portrait_back,
      textSettings: IS_EXPIRED
        ? {
            page_description1: {
              x: 0,
              y: 419,
              page: 1,
              width: imageNaturalWidth + 'px',
              class: 'page6',
              content: [{ fontSize: 32, class: 'description' }],
            },
            page_description2: {
              x: 0,
              y: 484,
              page: 1,
              width: imageNaturalWidth + 'px',
              class: 'page6',
              content: [{ fontSize: 32, class: 'description' }],
            },
          }
        : {
            page1_description1: {
              x: 78,
              y: 100,
              page: 1,
              content: [{ fontSize: 32, class: 'description' }],
            },
            page1_description2: {
              x: 78,
              y: 140,
              page: 1,
              content: [{ fontSize: 32, class: 'description' }],
            },
            page1_description3: {
              x: 78,
              y: 200,
              page: 1,
              content: [{ fontSize: 72, class: 'data' }],
            },
            page1_description4: {
              x: 78,
              y: 286,
              page: 1,
              content: [{ fontSize: 72, class: 'data' }],
            },
            page1_description5: {
              x: 78,
              y: 372,
              page: 1,
              content: [{ fontSize: 72, class: 'data' }],
            },
            page2_description1: {
              x: 77,
              y: 100,
              page: 2,
              content: [{ fontSize: 72, class: 'data big' }],
            },
            firstUseDay: {
              x: 79,
              y: 210,
              page: 2,
              content: [
                { fontSize: 32, class: 'description' },
                { fontSize: 36, class: 'data' },
              ],
            },
            page2_description2: {
              x: 79,
              y: 265,
              page: 2,
              content: [{ fontSize: 32, class: 'description' }],
            },
            totalDay: {
              x: 79,
              y: 325,
              page: 2,
              content: [
                { fontSize: 32, class: 'description' },
                { fontSize: 100, class: 'data big' },
                { fontSize: 32, class: 'data' },
                { fontSize: 32, class: 'description' },
              ],
            },
            page2_description3: {
              x: 79,
              y: 430,
              page: 2,
              content: [{ fontSize: 32, class: 'description' }],
            },
            page3_description1: {
              x: 77,
              y: 100,
              page: 3,
              content: [{ fontSize: 72, class: 'data big' }],
            },
            page3_description2: {
              x: 79,
              y: 200,
              page: 3,
              content: [{ fontSize: 32, class: 'description' }],
            },
            page3_description23: {
              x: 79,
              y: 250,
              page: 3,
              content: [{ fontSize: 32, class: 'description' }],
            },
            sendCount: {
              x: 79,
              y: 290,
              page: 3,
              content: [
                { fontSize: 32, class: 'description' },
                { fontSize: 40, class: 'data' },
                { fontSize: 32, class: 'data' },
                { fontSize: 32, class: 'description' },
              ],
            },
            // finishCount: {
            //   x: 79,
            //   y: 290,
            //   page: 3,
            //   content: [
            //     { fontSize: 32, class: 'description' },
            //     { fontSize: 40, class: 'data' },
            //     { fontSize: 32, class: 'data' },
            //     { fontSize: 32, class: 'description' }
            //   ]
            // },
            page3_description3: {
              x: 85,
              y: 398,
              page: 3,
              content: [{ fontSize: 24, class: 'description' }],
            },
            page3_description4: {
              x: 86,
              y: 367,
              page: 3,
              content: [{ fontSize: 24, class: 'description horizontal' }],
            },
            myEnterpriseCount: {
              x: 85,
              y: 435,
              page: 3,
              content: [{ fontSize: 100, class: 'data big' }],
            },
            myPersonalCount: {
              x: 353,
              y: 435,
              page: 3,
              content: [{ fontSize: 100, class: 'data big' }],
            },
            maxEnterpriseName: {
              x: 88,
              y: 548,
              page: 3,
              class: 'maxEnterpriseName',
              content: [
                {
                  fontSize: 36,
                  class: 'description maxEnterpriseName',
                },
                { fontSize: 36, class: 'data maxEnterpriseName' },
                {
                  fontSize: 36,
                  class: 'description maxEnterpriseName',
                },
              ],
            },
            page4_description1: {
              x: 77,
              y: 100,
              page: 4,
              content: [{ fontSize: 72, class: 'big data' }],
            },
            maxCountDay: {
              x: 79,
              y: 202,
              page: 4,
              content: [
                { fontSize: 32, class: 'data' },
                { fontSize: 32, class: 'description' },
                { fontSize: 32, class: 'data' },
                { fontSize: 32, class: 'description' },
                { fontSize: 32, class: 'data' },
                { fontSize: 32, class: 'description' },
              ],
            },
            page4_description2: {
              x: 79,
              y: 247,
              page: 4,
              content: [{ fontSize: 32, class: 'description' }],
            },
            maxCount: {
              x: 79,
              y: 300,
              page: 4,
              content: [
                { fontSize: 32, class: 'description' },
                { fontSize: 100, class: 'data big' },
                { fontSize: 32, class: 'data' },
                { fontSize: 32, class: 'description' },
              ],
            },
            page4_description3: {
              x: 79,
              y: 409,
              page: 4,
              content: [{ fontSize: 32, class: 'description' }],
            },
            page5_description1: {
              x: 77,
              y: 100,
              page: 5,
              content: [{ fontSize: 72, class: 'big data' }],
            },
            page5_description2: {
              x: 79,
              y: 202,
              page: 5,
              content: [{ fontSize: 32, class: 'description' }],
            },
            contractPages: {
              x: 79,
              y: 255,
              page: 5,
              content: [
                { fontSize: 33, class: 'description' },
                { fontSize: 100, class: 'data big' },
                { fontSize: 33, class: 'data' },
                { fontSize: 33, class: 'description' },
              ],
            },
            protectTrees: {
              x: 79,
              y: 365,
              page: 5,
              content: [
                { fontSize: 33, class: 'description' },
                { fontSize: 33, class: 'data' },
                { fontSize: 33, class: 'data' },
                { fontSize: 33, class: 'description' },
              ],
            },
            reduceEmissions: {
              x: 79,
              y: 415,
              page: 5,
              content: [
                { fontSize: 33, class: 'description' },
                { fontSize: 33, class: 'data' },
                { fontSize: 33, class: 'data' },
                { fontSize: 33, class: 'description' },
              ],
            },
            page5_description3: {
              x: 79,
              y: 465,
              page: 5,
              content: [{ fontSize: 32, class: 'description' }],
            },
            page6_description1: {
              x: 0,
              y: 130,
              page: 6,
              width: imageNaturalWidth + 'px',
              class: 'page6',
              content: [{ fontSize: 32, class: 'description' }],
            },
            savingTims: {
              x: 0,
              y: 181,
              page: 6,
              width: imageNaturalWidth + 'px',
              class: 'page6',
              content: [
                { fontSize: 32, class: 'description' },
                { fontSize: 72, class: 'data' },
                { fontSize: 32, class: 'data' },
                { fontSize: 32, class: 'description' },
              ],
            },
            savingCost: {
              x: 0,
              y: 259,
              page: 6,
              width: imageNaturalWidth + 'px',
              class: 'page6',
              content: [
                { fontSize: 32, class: 'description' },
                { fontSize: 72, class: 'data' },
                { fontSize: 32, class: 'data' },
                { fontSize: 32, class: 'description' },
              ],
            },
            today: {
              x: 0,
              y: 354,
              page: 6,
              width: imageNaturalWidth + 'px',
              class: 'page6',
              content: [
                { fontSize: 32, class: 'description' },
                { fontSize: 33, class: 'data' },
                { fontSize: 32, class: 'data' },
                { fontSize: 32, class: 'description' },
              ],
            },
            page6_description2: {
              x: 0,
              y: 419,
              page: 6,
              width: imageNaturalWidth + 'px',
              class: 'page6',
              content: [{ fontSize: 32, class: 'description' }],
            },
            page6_description3: {
              x: 0,
              y: 484,
              page: 6,
              width: imageNaturalWidth + 'px',
              class: 'page6',
              content: [{ fontSize: 32, class: 'description' }],
            },
            page6_description4: {
              x: 0,
              y: 564,
              page: 6,
              width: imageNaturalWidth + 'px',
              class: 'page6 flex-center',
              content: [
                {
                  fontSize: 32,
                  class: 'description horizontal-center horizontal',
                },
              ],
            },
            page6_description5: {
              x: 0,
              y: 614,
              page: 6,
              width: imageNaturalWidth + 'px',
              class: 'page6',
              content: [{ fontSize: 32, class: 'data goodbye2018' }],
            },
            page6_description6: {
              x: 0,
              y: 679,
              page: 6,
              width: imageNaturalWidth + 'px',
              class: 'page6',
              content: [{ fontSize: 32, class: 'data hello2019' }],
            },
          },
      textData: IS_EXPIRED
        ? {
            page_description1: ['哇哦！你来晚了'],
            page_description2: ['2018年度账单已经下线了'],
          }
        : {
            page1_description1: ['时间总是偷偷流逝'],
            page1_description2: ['转眼间2018已经过去了'],
            page1_description3: ['在过去的一年里'],
            page1_description4: ['很高兴'],
            page1_description5: ['在上上签遇见你'],
            page2_description1: ['很高兴认识你'],
            firstUseDay: [''],
            page2_description2: ['在那天你在上上签签了第一份合同'],
            totalDay: ['', '天'],
            page2_description3: ['为你的合同＂保驾护航＂'],
            page3_description1: ['签约小达人'],
            page3_description2: ['2018年'],
            page3_description23: ['与你有关的合同文件'],
            sendCount: ['可以装满', '', '个文件柜'],
            // finishCount: ['', '份'],
            page3_description3: ['合同往来企业数　　　　合同往来个人'],
            page3_description4: [''],
            myEnterpriseCount: [''],
            myPersonalCount: [''],
            maxEnterpriseName: ['其中和', '', '往来最多'],
            page4_description1: ['忙碌的一天'],
            maxCountDay: ['', '年', '', '月', '', '日'],
            page4_description2: ['这一天你签的合同最多'],
            maxCount: ['', '份'],
            page4_description3: ['工作总是充实又快乐着'],
            page5_description1: ['低碳楷模'],
            page5_description2: ['你知道吗，在2018年'],
            contractPages: ['', '张纸'],
            protectTrees: ['', '棵树木'],
            page5_description3: ['哇塞，你简直就是低碳办公的楷模'],
            page6_description1: ['在陪伴你的一年里'],
            reduceEmissions: ['', '吨碳排放'],
            savingTims: ['', '个小时'],
            savingCost: ['', '元'],
            today: ['', '天'],
            page6_description2: ['在新的一年里'],
            page6_description3: ['上上签继续陪伴你左右'],
            page6_description4: [''],
            page6_description5: ['再见，2018'],
            page6_description6: ['你好，2019'],
          },
    };
  },
  mounted() {
    const self = this;
    if (process.browser) {
      // todo 可优化？
      function getInnerHeight() {
        /* 监控窗口innerHeight */
        if (self.innerHeight !== window.innerHeight) {
          self.innerHeight = window.innerHeight;
          /* innerHeight变化时 */
        }
        if (self.innerWidth !== window.innerWidth) {
          self.innerWidth = window.innerWidth;
          /* innerHeight变化时 */
        }
        window.requestAnimationFrame(getInnerHeight);
      }
      this.frameTimer = window.requestAnimationFrame(getInnerHeight);
    }
    !IS_EXPIRED && this.getData();
  },
  destroyed() {
    if (process.browser) {
      window.cancelAnimationFrame(this.frameTimer);
    }
  },
  computed: {
    scale() {
      return this.innerWidth / this.imageNaturalWidth;
    },
    totalPage() {
      return this.src.length;
    },
  },
  methods: {
    checkStatus,
    getData,
    onSwipe(e, direction) {
      const oldPage = this.page;
      if (direction === 'down') {
        this.page > 1 && this.page--;
      } else {
        this.page < this.totalPage && this.page++;
      }
      /* 翻页到顶或底的时候，不做处理 */
      if (this.page === oldPage) return;
      const target = this.$refs['annual-box'].$el;
      anime({
        targets: target,
        scrollTop: { value: (this.page - 1) * this.innerHeight },
        duration: 300,
        loop: false,
        easing: 'easeInOutQuart',
      });
      if (this.page === 6) {
        const basicTimeline = anime.timeline();
        const horizontalCenter = document.querySelector(
          '.page6 .horizontal-center'
        );
        const goodbye2018 = document.querySelector('.page6 .goodbye2018');
        const hello2019 = document.querySelector('.page6 .hello2019');
        horizontalCenter.style.opacity = 0;
        goodbye2018.style.opacity = 0;
        hello2019.style.opacity = 0;
        basicTimeline
          .add({
            targets: horizontalCenter,
            opacity: [{ value: 0 }, { value: 1 }],
            easing: 'linear',
          })
          .add({
            targets: goodbye2018,
            opacity: [{ value: 0 }, { value: 1 }],
            easing: 'linear',
            offset: 0,
          })
          .add({
            targets: hello2019,
            opacity: [{ value: 0 }, { value: 1 }],
            easing: 'linear',
          });
      }
    },
  },
  components: {
    Pictorial,
  },
};
</script>

<style scoped lang="scss">
.annual-box {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  /*display: flex;*/
  /*justify-content: center;*/
  .annual-pictorial-mobile {
    align-self: center;
    overflow: hidden;
    position: absolute;
  }
}
</style>
<style lang="scss">
.annual-box {
  .maxEnterpriseName {
    white-space: normal;
    width: 610px;
    overflow-wrap: break-word;
    line-height: 1.2;
    /*font-family: PingFangSC-Semibold;*/
  }
  .page6 {
    width: 100%;
    text-align: center;
    &.flex-center {
      display: flex;
      justify-content: center;
    }
  }
  span {
    &.maxEnterpriseName {
      font-weight: bolder;
    }
    &.data {
      color: #42abac;
      font-weight: bolder;
      /*font-family: DINAlternate-Bold;*/
      &.maxEnterpriseName {
        /*font-family: PingFangSC-Semibold;*/
      }
      &.big {
        vertical-align: sub;
      }
    }
    &.description {
      color: #4a4f55;
      font-size: 40px;
      &.maxEnterpriseName {
        &:last-child {
          display: block;
        }
        text-indent: 0;
        line-height: 1.5;
      }
    }
    &.horizontal {
      display: block;
      width: 412px;
      height: 0;
      border-bottom: 1px solid #979797;
    }
    &.horizontal-center {
      width: 70%;
      border-color: #dddddd;
      opacity: 0;
    }
    &.goodbye2018 {
      opacity: 0;
    }
    &.hello2019 {
      opacity: 0;
    }
  }
}
</style>
