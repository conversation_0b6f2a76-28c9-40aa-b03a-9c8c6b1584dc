<template>
  <div class="annual-box">
    <div class="max-height" :style="`max-height: ${src.length * imageNaturalHeight * scale}px`">
      <Pictorial
        class="pictorial"
        :needAnimate="false"
        :width="imageNaturalWidth"
        :height="imageNaturalHeight"
        :textSettings="textSettings"
        :textData="textData"
        :showPages="[1,2,3,4,5,6]"
        :srcArray="src"
        :style="`transform: scale(${scale});transform-origin: 0 0;`"
        :backImage="backImage"
      ></Pictorial>
      <div class="hidden">
        <Pictorial
          class="pictorial_to_canvas"
          :needAnimate="false"
          :width="imageNaturalWidth"
          :height="imageNaturalHeight"
          :textSettings="textSettings"
          :textData="textData"
          :showPages="[1,2,3,4,5,6]"
          :srcArray="src"
          :backImage="backImage"
        ></Pictorial>
      </div>
    </div>
    <el-button @click="download" class="dl-btn" type="primary" icon="el-icon-download" circle size="large"></el-button>
  </div>
</template>
<script>
/* jspdf 在客户端引用时会有无法正常使用的状况，此段代码无效 */
// let jsPDF
// if (process.browser) {
//   jsPDF = require('jspdf')
// }
import Pictorial from '@/components/Pictorial';
import { getData, checkStatus, sendCount } from '../utils';
import html2canvas from 'html2canvas';
import {
  landscape1_front,
  landscape2_front,
  landscape3_front,
  landscape4_front,
  landscape5_front,
  landscape6_front,
  landscape_back,
  // landscape1,
  // landscape2,
  // landscape3,
  // landscape4,
  // landscape5,
  // landscape6
} from '@/assets/images/share/annual';
const IS_EXPIRED = true; // 配置是否已过期
export default {
  name: 'annualPdfIndex',
  layout: 'layout_share',
  head: {
    title: '2018年企业年度账单',
    // script: [{ src: '/js/pdfjs.js' }] // 试了一万种方法引入插件，然并卵，故将其加入静态文件中，用经典的script解决
    script: [{ src: 'https://cdn.bootcss.com/jspdf/1.5.0/jspdf.min.js' }], // 项目目录静态文件加载有问题，只能用cdn了
  },
  data() {
    const { entId, devId, code } = this.checkStatus();
    return {
      entId,
      devId,
      code,
      page: 1,
      innerWidth: 1,
      imageNaturalHeight: 1080,
      imageNaturalWidth: 1920,
      src: IS_EXPIRED
        ? [landscape6_front]
        : [
            landscape1_front,
            landscape2_front,
            landscape3_front,
            landscape4_front,
            landscape5_front,
            landscape6_front,
            // landscape1,
            // landscape2,
            // landscape3,
            // landscape4,
            // landscape5,
            // landscape6
          ],
      backImage: landscape_back,
      textSettings: IS_EXPIRED
        ? {
            page_description1: {
              x: 120,
              y: 413,
              page: 1,
              content: [{ fontSize: 48, class: 'description' }],
            },
            page_description2: {
              x: 120,
              y: 490,
              page: 1,
              content: [{ fontSize: 48, class: 'description' }],
            },
          }
        : {
            page1_description1: {
              x: 120,
              y: 240,
              page: 1,
              content: [{ fontSize: 48, class: 'description' }],
            },
            page1_description2: {
              x: 120,
              y: 310,
              page: 1,
              content: [{ fontSize: 48, class: 'description' }],
            },
            page1_description3: {
              x: 120,
              y: 385,
              page: 1,
              content: [{ fontSize: 110, class: 'data big' }],
            },
            page1_description4: {
              x: 120,
              y: 505,
              page: 1,
              content: [{ fontSize: 110, class: 'data big' }],
            },
            page1_description5: {
              x: 120,
              y: 630,
              page: 1,
              content: [{ fontSize: 110, class: 'data big' }],
            },
            page2_description1: {
              x: 120,
              y: 150,
              page: 2,
              content: [{ fontSize: 100, class: 'data big' }],
            },
            firstUseDay: {
              x: 120,
              y: 345,
              page: 2,
              content: [
                { fontSize: 48, class: 'description' },
                { fontSize: 48, class: 'data' },
              ],
            },
            page2_description2: {
              x: 120,
              y: 420,
              page: 2,
              content: [{ fontSize: 48, class: 'description' }],
            },
            totalDay: {
              x: 120,
              y: 500,
              page: 2,
              content: [
                { fontSize: 48, class: 'description' },
                { fontSize: 90, class: 'data big' },
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'description' },
              ],
            },
            page2_description3: {
              x: 120,
              y: 605,
              page: 2,
              content: [{ fontSize: 48, class: 'description' }],
            },
            page3_description1: {
              x: 120,
              y: 150,
              page: 3,
              content: [{ fontSize: 100, class: 'data big' }],
            },
            page3_description2: {
              x: 120,
              y: 300,
              page: 3,
              content: [{ fontSize: 48, class: 'description' }],
            },
            page3_description23: {
              x: 120,
              y: 370,
              page: 3,
              content: [{ fontSize: 46, class: 'description' }],
            },
            sendCount: {
              x: 120,
              y: 435,
              page: 3,
              content: [
                { fontSize: 46, class: 'description' },
                { fontSize: 46, class: 'data' },
                { fontSize: 46, class: 'data' },
                { fontSize: 46, class: 'description' },
              ],
            },
            // finishCount: {
            //   x: 120,
            //   y: 435,
            //   page: 3,
            //   content: [
            //     { fontSize: 46, class: 'description' },
            //     { fontSize: 46, class: 'data' },
            //     { fontSize: 46, class: 'data' },
            //     { fontSize: 46, class: 'description' }
            //   ]
            // },
            page3_description3: {
              x: 120,
              y: 580,
              page: 3,
              content: [{ fontSize: 30, class: 'description' }],
            },
            myEnterpriseCount: {
              x: 120,
              y: 625,
              page: 3,
              content: [{ fontSize: 100, class: 'data big' }],
            },
            myPersonalCount: {
              x: 374,
              y: 625,
              page: 3,
              content: [{ fontSize: 100, class: 'data big' }],
            },
            maxEnterpriseName: {
              x: 120,
              y: 725,
              page: 3,
              class: 'maxEnterpriseName',
              content: [
                {
                  fontSize: 48,
                  class: 'description maxEnterpriseName',
                },
                { fontSize: 48, class: 'data maxEnterpriseName' },
                {
                  fontSize: 48,
                  class: 'description maxEnterpriseName',
                },
              ],
            },
            page4_description1: {
              x: 120,
              y: 150,
              page: 4,
              content: [{ fontSize: 100, class: 'big data' }],
            },
            maxCountDay: {
              x: 120,
              y: 340,
              page: 4,
              content: [
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'description' },
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'description' },
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'description' },
              ],
            },
            page4_description2: {
              x: 120,
              y: 440,
              page: 4,
              content: [{ fontSize: 48, class: 'description' }],
            },
            maxCount: {
              x: 120,
              y: 523,
              page: 4,
              content: [
                { fontSize: 48, class: 'description' },
                { fontSize: 70, class: 'data big' },
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'description' },
              ],
            },
            page4_description3: {
              x: 120,
              y: 640,
              page: 4,
              content: [{ fontSize: 48, class: 'description' }],
            },
            page5_description1: {
              x: 120,
              y: 150,
              page: 5,
              content: [{ fontSize: 100, class: 'big data' }],
            },
            page5_description2: {
              x: 120,
              y: 320,
              page: 5,
              content: [{ fontSize: 48, class: 'description' }],
            },
            contractPages: {
              x: 120,
              y: 375,
              page: 5,
              content: [
                { fontSize: 48, class: 'description' },
                { fontSize: 90, class: 'data big' },
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'description' },
              ],
            },
            protectTrees: {
              x: 120,
              y: 500,
              page: 5,
              content: [
                { fontSize: 48, class: 'description' },
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'description' },
              ],
            },
            reduceEmissions: {
              x: 120,
              y: 590,
              page: 5,
              content: [
                { fontSize: 48, class: 'description' },
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'description' },
              ],
            },
            page5_description3: {
              x: 120,
              y: 675,
              page: 5,
              content: [{ fontSize: 48, class: 'description' }],
            },
            page6_description1: {
              x: 120,
              y: 150,
              page: 6,
              content: [{ fontSize: 100, class: 'big data' }],
            },
            savingTims: {
              x: 120,
              y: 364,
              page: 6,
              content: [
                { fontSize: 48, class: 'description' },
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'description' },
              ],
            },
            savingCost: {
              x: 120,
              y: 451,
              page: 6,
              content: [
                { fontSize: 48, class: 'description' },
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'description' },
              ],
            },
            today: {
              x: 120,
              y: 532,
              page: 6,
              content: [
                { fontSize: 48, class: 'description' },
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'data' },
                { fontSize: 48, class: 'description' },
              ],
            },
            page6_description2: {
              x: 120,
              y: 613,
              page: 6,
              content: [{ fontSize: 48, class: 'description' }],
            },
            page6_description3: {
              x: 120,
              y: 690,
              page: 6,
              content: [{ fontSize: 48, class: 'description' }],
            },
          },
      textData: IS_EXPIRED
        ? {
            page_description1: ['哇哦！你来晚了'],
            page_description2: ['2018年度账单已经下线了'],
          }
        : {
            page1_description1: ['时间总是偷偷流逝'],
            page1_description2: ['转眼间2018已经过去了'],
            page1_description3: ['在过去的一年里'],
            page1_description4: ['很高兴'],
            page1_description5: ['在上上签遇见你'],
            page2_description1: ['很高兴认识你'],
            firstUseDay: [''],
            page2_description2: ['在那天你在上上签签了第一份合同'],
            totalDay: ['', '天'],
            page2_description3: ['为你的合同＂保驾护航＂'],
            page3_description1: ['签约小达人'],
            page3_description2: ['2018年'],
            page3_description23: ['与你有关的合同文件'],
            sendCount: ['可以装满', '', '个文件柜'],
            finishCount: ['', '份'],
            page3_description3: ['合同往来企业数　　合同往来个人'],
            page3_description4: [''],
            myEnterpriseCount: [''],
            myPersonalCount: [''],
            maxEnterpriseName: ['其中和', '', '往来最多'],
            page4_description1: ['忙碌的一天'],
            maxCountDay: ['', '年', '', '月', '', '日'],
            page4_description2: ['这一天你签的合同最多'],
            maxCount: ['', '份'],
            page4_description3: ['工作总是充实又快乐着'],
            page5_description1: ['低碳楷模'],
            page5_description2: ['你知道吗，在2018年'],
            contractPages: ['', '张纸'],
            protectTrees: ['', '棵树木'],
            page5_description3: ['哇塞，你简直就是低碳办公的楷模'],
            page6_description1: ['在陪伴你的一年里'],
            reduceEmissions: ['', '吨碳排放'],
            savingTims: ['', '个小时'],
            savingCost: ['', '元'],
            today: ['', '天'],
            page6_description2: ['在新的一年里'],
            page6_description3: ['上上签继续陪伴你左右'],
          },
    };
  },
  mounted() {
    // if (process.browser) {
    this.innerWidth = window.innerWidth;
    !IS_EXPIRED && this.getData();
    IS_EXPIRED && (this.imageNaturalHeight = window.innerHeight * 2);
    // }
  },
  computed: {
    scale() {
      return (this.innerWidth / this.imageNaturalWidth) * 0.708;
    },
    totalPage() {
      return this.src.length;
    },
  },
  methods: {
    sendCount,
    checkStatus,
    getData,
    async download() {
      const node = document.querySelector('.pictorial_to_canvas');
      const canvas = await html2canvas(node, {
        width: 1920,
        height: 1080 * this.totalPage,
        scale: 1,
        windowWidth: 1920,
        windowHeight: 1080 * this.totalPage,
      });
      const ctx = canvas.getContext('2d');
      const canvasList = new Array(this.totalPage).fill('').map((i, index) => {
        const _canvas = document.createElement('canvas');
        _canvas.width = 1920;
        _canvas.height = 1080;
        const _ctx = _canvas.getContext('2d');
        const imageData = ctx.getImageData(0, index * 1080, 1920, 1080);
        _ctx.putImageData(imageData, 0, 0);
        return _canvas;
      });
      const doc = new window.jsPDF('l', 'pt', [1920, 1080]);
      for (let i = 0; i < this.totalPage; i++) {
        doc.addImage(canvasList[i], 'JPEG', 0, 0, 1920, 1080);
        i !== this.totalPage - 1 && doc.addPage();
      }
      doc.save('2018企业年度账单.pdf');
      this.count();
    },
    count() {
      let operation;
      if (this.entId !== undefined) {
        operation = 'pdfDownloadWeb';
      } else if (this.devId !== undefined) {
        operation = 'pdfDownloadApi';
      } else {
        return false;
      }
      this.sendCount(operation);
    },
  },
  components: {
    Pictorial,
  },
};
</script>

<style scoped lang="scss">
.annual-box {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 100vw;
  background-color: #303030;
  padding: 0 14.6%;
  overflow: auto;
  .pictorial {
    box-shadow: 0 0 10px 10px #353535;
  }
  .hidden {
    top: 0;
    /*visibility: hidden;*/
    position: fixed;
    z-index: -1;
  }
  .dl-btn {
    position: fixed;
    right: 7%;
    bottom: 70px;
    font-size: 30px;
  }
}
</style>
<style lang="scss">
.annual-box {
  .maxEnterpriseName {
    white-space: normal;
    width: 610px;
    overflow-wrap: break-word;
    line-height: 1.2;
    font-family: 'PingFang SC';
    font-weight: bolder;
  }
  span {
    &.data {
      color: #42abac;
      font-family: DINAlternate-Bold;
      /*vertical-align: sub;*/
      /*&.maxEnterpriseName {*/
      /*font-family: 'PingFang SC';*/
      /*font-weight: bolder;*/
      /*}*/
      &.big {
        /*vertical-align: sub;*/
        font-weight: bolder;
      }
    }
    &.description {
      opacity: 0.92;
      color: #4a4f55;
      font-size: 40px;
      font-family: DINAlternate-Bold;
      &.maxEnterpriseName {
        &:last-child {
          display: block;
        }
        text-indent: 0;
        line-height: 1.5;
      }
    }
  }
}
</style>
