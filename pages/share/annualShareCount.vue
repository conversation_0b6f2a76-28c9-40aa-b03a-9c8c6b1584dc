<template>
  <div></div>
</template>

<script>
import { sendCount, checkStatus } from './utils';
export default {
  name: 'annualShareCount',
  layout: 'layout_share',
  mounted() {
    const { entId, devId, code } = this.checkStatus();
    let url, operation;
    if (entId !== undefined) {
      url = `/share/annual?entId=${entId}&code=${code}`;
      operation = 'QRCodeWeb';
    } else {
      url = `/share/annual?devId=${devId}&code=${code}`;
      operation = 'QRCodeApi';
    }
    this.sendCount(operation);
    this.$router.replace(url);
  },
  methods: {
    sendCount,
    checkStatus,
  },
};
</script>

<style scoped>
</style>
