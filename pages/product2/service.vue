<template>
    <section class="manageSeries" :class="isMobile && 'manageSeries-mobile'"> 
        <el-row class="manageSeries-banner">
            <el-col :span="isMobile ? 24 :12" class="manageSeries-banner-tip" :class="{'manageSeries-banner-tipBigEn':isEN}">
                <h1>{{$t('service.header.title')}}</h1>
                <p>{{$t('service.header.description')}}</p>
                <el-button @click="toEntRegister" v-if="!isEN">{{$t('service.header.tryToUse')}}</el-button>
                <el-button @click="toReg" v-if="isEN">{{$t('header.material')}}</el-button>
            </el-col>
            <el-col :span="isMobile ? 24 :12">
                <img :src="isMobile ? 'https://static.bestsign.cn:443/e4221cf114f715ace1ef1b7cb9cabb44799e06e7.jpg' : 'https://static.bestsign.cn:443/47cb52a42780a6c86acea81dc1509c2e00d0ce56.jpg'" alt="">
            </el-col>
        </el-row>
        <section class="manageSeries-service">
            <h4 class="sub-title" :class="{'sub-title-en':isEN}">{{$t('service.value.title')}}</h4>
            <h5>{{$t('service.value.description')}}</h5>
            <el-row class="manageSeries-service-row-1">
                <el-col :span="isMobile? 24: 6">
                   <i class="iconfont icon-a-1"></i>
                   <h6>{{$t('service.value.1')}}</h6>
                   <p>{{$t('service.value.1tip')}}</p>
                </el-col>
                <el-col :span="isMobile? 24: 6">
                    <i class="iconfont icon-a-2"></i>
                    <h6>{{$t('service.value.2')}}</h6>
                   <p>{{$t('service.value.2tip')}}</p>
                </el-col>
                <el-col :span="isMobile? 24: 6">
                    <i class="iconfont icon-a-3"></i>
                    <h6>{{$t('service.value.3')}}</h6>
                   <p>{{$t('service.value.3tip')}}</p>
                </el-col>
                <el-col :span="isMobile? 24: 6">
                    <i class="iconfont icon-a-4"></i>
                    <h6>{{$t('service.value.4')}}</h6>
                    <p>{{$t('service.value.4tip')}}</p>
                </el-col>
            </el-row>
        </section>
        <!-- //todo -->
        <section class="manageSeries-service manageSeries-service_2">
            <h4 class="sub-title" :class="{'sub-title-en':isEN}">{{$t('service.feature.title')}}</h4>
            <el-row class="manageSeries-service-row-2" :class="{'manageSeries-service-row-2-en':isEN}">
                <el-col :span="isMobile? 24: 8">
                    <img src="https://static.bestsign.cn:443/c6cf5dabe0f599027ac07f184b8f74e28c5a6be3.jpg" alt="" v-if="!isEN">
                    <img src="https://static.bestsign.cn:443/d3bc59bfbbd08109f6deed117986d578feeda806.jpg" alt="" v-if="isEN">
                    <div :class="{'en-h6':isEN}">
                        <h6>{{$t('service.feature.f1.1')}}</h6>
                        <h5>{{$t('service.feature.f1.2')}}</h5>
                        <p>{{$t('service.feature.f1.3')}}</p>
                        <p>{{$t('service.feature.f1.4')}}</p>
                        <p>{{$t('service.feature.f1.5')}}</p>
                        <p>{{$t('service.feature.f1.6')}}</p>
                        <p>{{$t('service.feature.f1.7')}}</p>
                        <p>{{$t('service.feature.f1.8')}}</p>
                        <p>{{$t('service.feature.f1.9')}}</p>
                        <p>{{$t('service.feature.f1.10')}}</p>
                    </div>
                </el-col>
                <el-col :span="isMobile? 24: 8">
                    <img src="https://static.bestsign.cn:443/39203c08eb89a540d59bf270a30e7e59a7806de3.jpg" alt="" v-if="!isEN">
                    <img src="https://static.bestsign.cn:443/15e9dfdc15e87da9e24edc81b2ee9c4684ad3ff1.jpg" alt="" v-if="isEN">
                    <div>
                        <h6>{{$t('service.feature.f2.1')}}</h6>
                        <h5>{{$t('service.feature.f2.2')}}</h5>
                        <p>{{$t('service.feature.f2.3')}}</p>
                        <p>{{$t('service.feature.f2.4')}}</p>
                        <p>{{$t('service.feature.f2.5')}}</p>
                        <p>{{$t('service.feature.f2.6')}}</p>
                        <p>{{$t('service.feature.f2.7')}}</p>
                        <p>{{$t('service.feature.f2.8')}}</p>
                    </div>
                </el-col>
                <el-col :span="isMobile? 24: 8">
                    <img src="https://static.bestsign.cn:443/2c4b4283d5530cc20b77df694009e003c0105cbf.jpg" alt="" v-if="!isEN">
                    <img src="https://static.bestsign.cn:443/602ad554840e7f975d0b26e109f7c9ae9c0980f1.jpg" alt="" v-if="isEN">
                    <div>
                        <h6>{{$t('service.feature.f3.1')}}</h6>
                        <h5>{{$t('service.feature.f3.2')}}</h5>
                        <p>{{$t('service.feature.f3.3')}}</p>
                        <p>{{$t('service.feature.f3.4')}}</p>
                        <p>{{$t('service.feature.f3.5')}}</p>
                        <p>{{$t('service.feature.f3.6')}}</p>
                    </div>
                </el-col>
            </el-row>
        </section>
        <safety-approve></safety-approve>
        <!-- <section class="manageSeries-security">
            <h4 class="sub-title">{{$t('service.security.title')}}</h4>
            <div class="swiper-container" v-swiper:securitySwiper="isMobile?securitySwiperOptions:{}" >
                <el-row class="manageSeries-security-1" :class="isMobile &&'swiper-wrapper'" type="flex" >
                    <el-col :span="8" :class="isMobile &&'swiper-slide'">
                        <i class="iconfont icon-a-5"></i>
                        <h5>{{$t('service.security.f1.1')}}</h5>
                        <p>{{$t('service.security.f1.2')}}</p>
                    </el-col>
                    <el-col :span="8" :class="isMobile &&'swiper-slide'">
                        <i class="iconfont icon-a-6"></i>
                        <h5>{{$t('service.security.f2.1')}}</h5>
                        <p>{{$t('service.security.f2.2')}}</p>
                    </el-col>
                    <el-col :span="8" :class="isMobile &&'swiper-slide'">
                        <i class="iconfont icon-a-7"></i>
                        <h5>{{$t('service.security.f3.1')}}</h5>
                        <p>{{$t('service.security.f3.2')}}</p>
                    </el-col>
                </el-row>
            </div>
            <h2>{{$t('service.security.1')}}<i v-if="!isMobile" class="el-icon-bottom-right"></i></h2>
            <el-row class="manageSeries-security-2" type="flex">
                <el-col :span="isMobile ? 24: 6">
                    <img src="https://static.bestsign.cn:443/e0d436174481210e37a0d916ef32a343d6deabb5.png" alt="">
                    <div>
                        <p>{{$t('service.security.2')}}</p>
                        <p>{{$t('service.security.3')}}</p>
                    </div>
                </el-col>
                <el-col :span="isMobile ? 24:6">
                    <img src="https://static.bestsign.cn/1bc4e4c2fd7f38f4dece0ce97fb8275677e27d4c.png" alt="">
                    <div>
                        <p>{{$t('service.security.4')}}</p>
                        <p>{{$t('service.security.5')}}</p>
                    </div>
                </el-col>
                <el-col :span="isMobile ? 24:6">
                    <img src="https://static.bestsign.cn:443/57e6d887f0ab72e5cad816ceb5353b687032d7ee.png" alt="">
                    <div>
                        <p>{{$t('service.security.6')}}</p>
                        <p>{{$t('service.security.7')}}</p>
                    </div>
                </el-col>
                <el-col :span="isMobile ? 24:6">
                    <img src="https://static.bestsign.cn:443/aa0703c64fdac67b34de17f82965a55dda42330b.png" alt="">
                    <div>
                        <p>{{$t('service.security.8')}}</p>
                        <p>{{$t('service.security.9')}}</p>
                    </div>
                </el-col>
            </el-row>
            <h2>{{$t('service.security.10')}}<i v-if="!isMobile" class="el-icon-bottom-right"></i></h2>
            <el-row class="manageSeries-security-3" type="flex">
                <el-col :span="isMobile ? 24: 6">
                    <h6>99.99%</h6>
                    <p>{{$t('service.security.11')}}</p>
                </el-col>
                <el-col :span="isMobile ? 24: 6">
                    <h6>3,118<span>万回</span></h6>
                    <p>{{$t('service.security.12')}}</p>
                </el-col>
                <el-col :span="isMobile ? 24: 6"
                    <h6>2,200<span>日以上</span></h6>
                    <p>{{$t('service.security.13')}}</p>
                </el-col>
            </el-row>
        </section> -->
        <section class="manageSeries-return">
            <h4 class="sub-title" :class="{'sub-title-en':isEN}">{{$t('service.return.title')}}</h4>
            <el-row type="flex">
                <el-col :span="isMobile? 24: 12">
                    <i class="iconfont icon-a-8"></i>
                    <h2>95%</h2>
                    <h5>{{$t('service.return.4')}}</h5>
                    <h6>{{$t('service.return.1')}}</h6>
                    <p><span>{{$t('service.return.2-t')}} </span>{{$t('service.return.2')}}</p>
                    <p><span>{{$t('service.return.3-t')}} </span>{{$t('service.return.3')}}</p>
                    <el-button @click="toEntRegister" v-if="!isEN">{{$t('service.header.tryToUse')}}</el-button>
                </el-col>
                <el-col :span="isMobile? 24:12">
                    <i class="iconfont icon-a-9"></i>
                    <h2>60%</h2>
                    <h5>{{$t('service.return.8')}}</h5>
                    <h6>{{$t('service.return.5')}}</h6>
                    <p><span>{{$t('service.return.6-t')}} </span>{{$t('service.return.6')}}</p>
                    <p><span>{{$t('service.return.7-t')}} </span>{{$t('service.return.7')}}</p>
                    <el-button @click="toEntRegister" v-if="!isEN">{{$t('service.header.tryToUse')}}</el-button>
                </el-col>
            </el-row>
        </section>
        <el-row class="manageSeries-team">
            <el-col :span="isMobile? 24: 12" class="manageSeries-team-left" :class="{'manageSeries-team-left-en':isEN}">
                <h4>{{$t('service.team.title')}}</h4>
                <p>{{$t('service.team.description')}}</p>
                <!-- <div v-if="!isMobile" class="swiper-pagination swiper-pagination_safety"></div> -->
            </el-col>
            <el-col :span="isMobile? 24: 12">
                <div class="swiper-container" v-swiper:teamSwiper="isMobile?swiperOptionMobile:swiperOption" >
                    <div class="swiper-wrapper">
                        <div class="swiper-slide">
                            <i class="iconfont icon-a-10"></i>
                            <h4>{{$t('service.team.1')}}</h4>
                            <p>{{$t('service.team.2')}}</p>
                            <p>{{$t('service.team.3')}}</p>
                            <p>{{$t('service.team.4')}}</p>
                            <p>{{$t('service.team.5')}}</p>
                        </div>
                        <div class="swiper-slide">
                            <i class="iconfont icon-a-11"></i>
                            <h4>{{$t('service.team.6')}}</h4>
                            <p>{{$t('service.team.7')}}</p>
                            <p>{{$t('service.team.8')}}</p>
                            <p>{{$t('service.team.9')}}</p>
                        </div>
                        <!-- <div class="swiper-slide">
                            <i class="iconfont icon-a-12"></i>
                            <h4>{{$t('service.team.10')}}</h4>
                            <p>{{$t('service.team.11')}}</p>
                            <p>{{$t('service.team.12')}}</p>
                            <p>{{$t('service.team.13')}}</p>
                            <p>{{$t('service.team.14')}}</p>
                            <p>{{$t('service.team.15')}}</p>
                        </div> -->
                    </div>
                </div>
                <div v-if="isMobile" class="swiper-pagination swiper-pagination_safety"></div>
            </el-col>
        </el-row>
    </section>
</template>

<script>
import { mapState } from 'vuex';
import './common.scss';
import { toEntRegister } from '@/assets/utils/index.js';
import SafetyApprove from '../advantage/components/safetyApprove1.vue';

export default {
  components: {
    SafetyApprove,
  },
  data() {
    return {
      swiperOption: {
        autoplay: true, // 自动轮播
        direction: 'vertical',
        loop: true,
        initialSlide: 1,
        // centeredSlides: true,
        speed: 700,
        slidesPerView: '1',
        // loopedSlides: 3,
        spaceBetween: 20,
        navigation: {
          nextEl: '.ssq-button-next-b',
          prevEl: '.ssq-button-prev-b',
        },
        pagination: {
          el: '.swiper-pagination.swiper-pagination_safety',
        },
      },
      swiperOptionMobile: {
        direction: 'horizontal',
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        spaceBetween: 20,
        centeredSlides: true,
        slidesPerView: 'auto',
        pagination: {
          el: '.swiper-pagination.swiper-pagination_safety',
        },
      },
      securitySwiperOptions: {
        direction: 'horizontal',
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        spaceBetween: 20,
        centeredSlides: true,
        slidesPerView: 'auto',
      },
      isEN: false,
    };
  },
  head() {
    return {
      title: this.$t('service.TDK.title'),
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.$t('service.TDK.keywords'),
        },
        {
          hid: 'description',
          name: 'description',
          content: this.$t('service.TDK.description'),
        },
      ],
    };
  },
  computed: {
    ...mapState(['isMobile']),
    language() {
      return this.$store.state.locale;
    },
  },
  methods: {
    toEntRegister,
    toReg() {
      this.$router.push({
        path: `/${this.language}/material`,
      });
    },
  },
  mounted() {
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
};
</script>

<style lang="scss">
</style>
