<template>
    <section class="manageSeries function" :class="isMobile && 'manageSeries-mobile function-mobile'">
        <el-row class="manageSeries-banner">
            <el-col :span="isMobile ? 24 :12" class="manageSeries-banner-tip" :class="{'manageSeries-banner-tipEn':isEN}">
                <h1>{{$t('manageSeriesProduction.header.title')}}</h1>
                <p>{{$t('manageSeriesProduction.header.tip')}}</p>
                <el-button @click="toEntRegister" v-if="!isEN">{{$t('manageSeriesProduction.tryToUse')}}</el-button>
                <el-button @click="toReg" v-if="isEN">{{$t('header.material')}}</el-button>
            </el-col>
            <el-col :span="isMobile ? 24 : 12" v-if="!isEN">
                <img :src="isMobile ? 'https://static.bestsign.cn:443/71f45198ef2540750c262057f5e5447212365870.jpg': 'https://static.bestsign.cn:443/c6dce4acf2acec5ca34ce99d7185aef13c9c5910.jpg'" alt="">
            </el-col>
            <el-col :span="isMobile ? 24 : 12" v-if="isEN">
                <img :src="isMobile ? 'https://static.bestsign.cn:443/adbbcdda53a21e3f09ce55df8f7aadb3263807b1.jpg': 'https://static.bestsign.cn:443/17e3ee1b830595096bfa72f3e18ce4d39462ec54.jpg'" alt="">
            </el-col>
        </el-row>
        <section class="function-base function-common" :class="{'function-baseEn':isEN}">
            <h4 class="sub-title" :class="{'sub-title-en':isEN}">{{$t('manageSeriesProduction.base.title')}}</h4>
            <section class="function-base-1">
              <p class="order" v-if="!isMobile">01</p>
              <h5><span v-if="isMobile">01</span>{{$t('manageSeriesProduction.base.f1.1')}}</h5>
              <img :src="isMobile ? 'https://static.bestsign.cn:443/8fd4739d5c46819009489f429c03a8c5152a38f2.png' : 'https://static.bestsign.cn:443/ff2b992e36531d63c9068ae62b03baddde56cb22.png'" alt="" v-if="!isEN">
              <img :src="isMobile ? 'https://static.bestsign.cn:443/f8b6a5cd7b2ed4f6d85ea814aaee79b3180e923.png' : 'https://static.bestsign.cn:443/797fdbbf357360619de1a01f7355cdc6fe15cda3.png'" alt="" v-if="isEN">
            </section>
            <!-- <el-row class="function-base-2">
              <el-col :span="isMobile ? 24 : 12">
                <p v-if="!isMobile" class="order">02</p>
                <h5><span v-if="isMobile">02</span>{{$t('manageSeriesProduction.base.f2.1')}}</h5>
                <p>{{$t('manageSeriesProduction.base.f2.2')}}</p>
              </el-col>
              <el-col :span="isMobile ? 24 : 11" :offset="isMobile ? 0 : 1">
                <img src="https://static.bestsign.cn:443/1a572ef3088d04fb9e990f30e188667081e80cb2.png" alt="">
              </el-col>
            </el-row> -->
            <el-row class="function-base-3">
              <el-col :span="isMobile ? 24 : 12">
                <p v-if="!isMobile" class="order">02</p>
                <h5><span v-if="isMobile">02</span>{{$t('manageSeriesProduction.base.f3.1')}}</h5>
                <p>{{$t('manageSeriesProduction.base.f3.2')}}</p>
              </el-col>
              <el-col :span="isMobile ? 24 : 11" :offset="isMobile ? 0 : 1">
                <img src="https://static.bestsign.cn:443/53ad5132f7d653ecc8ea94ee201cf29dab9d6d8d.png" alt="" v-if="!isEN">
                <img src="https://static.bestsign.cn:443/88571c29fca1742ce099b15ee1730ab4d104180e.png" alt="" v-if="isEN">
              </el-col>
            </el-row>
        </section>
        <section class="function-contract function-common">
            <h4 class="sub-title" :class="{'sub-title-en':isEN}">{{$t('manageSeriesProduction.contract.title')}}</h4>
            <el-row class="function-contract-1">
              <el-col :span="isMobile ? 24 : 12">
                <p v-if="!isMobile" class="order">01</p>
                <h5><span v-if="isMobile">01</span>{{$t('manageSeriesProduction.contract.f1.1')}}</h5>
                <p class="title-2" :class="{'title-2EN':isEN}">{{$t('manageSeriesProduction.contract.f1.2')}}</p>
                <p>{{$t('manageSeriesProduction.contract.f1.3')}}</p>
                <p>{{$t('manageSeriesProduction.contract.f1.4')}}</p>
                <p>{{$t('manageSeriesProduction.contract.f1.5')}}</p>
              </el-col>
              <el-col :span="isMobile ? 24 : 11" :offset="isMobile ? 0: 1">
                <img src="https://static.bestsign.cn:443/5495ae16480986eac3a0408ee7f4c295ed9804d9.png" alt="" v-if="!isEN">
                <img src="https://static.bestsign.cn:443/7f11d8f06be60caffeb03d57f8b77e3c7ceab48.png" alt="" v-if="isEN">
              </el-col>
            </el-row>
            <el-row class="function-contract-2">
              <el-col :span="isMobile ? 24 : 12">
                <p v-if="!isMobile" class="order">02</p>
                <h5><span v-if="isMobile">02</span>{{$t('manageSeriesProduction.contract.f2.1')}}</h5>
                <p class="title-2" :class="{'title-2EN':isEN}">{{$t('manageSeriesProduction.contract.f2.2')}}</p>
                <p>{{$t('manageSeriesProduction.contract.f2.3')}}</p>
                <p>{{$t('manageSeriesProduction.contract.f2.4')}}</p>
              </el-col>
              <el-col :span="isMobile ? 24 : 11" :offset="isMobile ? 0:1">
                <img src="https://static.bestsign.cn:443/53fcc53a39013b83a949d5746becae5a4c4b97c7.png" alt="" v-if="!isEN">
                <img src="https://static.bestsign.cn:443/d3566c2ecb98d1f01ede168b0b51d9b4664387f9.png" alt="" v-if="isEN">
              </el-col>
            </el-row>
            <el-row class="function-contract-3">
              <el-col :span="isMobile ? 24 : 12">
                <p v-if="!isMobile" class="order">03</p>
                <h5><span v-if="isMobile">03</span>{{$t('manageSeriesProduction.contract.f3.1')}}</h5>
                <p class="title-2" :class="{'title-2EN':isEN}">{{$t('manageSeriesProduction.contract.f3.2')}}</p>
                <p>{{$t('manageSeriesProduction.contract.f3.3')}}</p>
                <p>{{$t('manageSeriesProduction.contract.f3.4')}}</p>
                <p>{{$t('manageSeriesProduction.contract.f3.5')}}</p>
              </el-col>
              <el-col :span="isMobile ? 24 : 11" :offset="isMobile ? 0:1">
                <img src="https://static.bestsign.cn:443/698998f54702587c0eb5cb3e5a06cdf18978dd62.png" alt="" v-if="!isEN">
                <img src="https://static.bestsign.cn:443/7937aab3bbad596dcebb1500ad6dc33257ed939c.png" alt="" v-if="isEN">
              </el-col>
            </el-row>
        </section>
        <section class="function-list function-common"  :class="{'function-common-en':isEN}">
            <h4 class="sub-title" :class="{'sub-title-en':isEN}">{{$t('manageSeriesProduction.list.title')}}</h4>
            <el-row>
                <el-col :span="isMobile ? 24 : 12" v-for="(item, index) in list" :key="index">
                  <div class="function-list-item-left">
                    <i :class="`iconfont icon-${item.icon}`"></i>
                  </div>
                  <div class="function-list-item-right">
                    <p class="title">{{item.title}}</p>
                    <p class="tip">{{item.tip}}</p>
                  </div>
                </el-col>
            </el-row>
        </section>
    </section>
</template>

<script>
import { mapState } from 'vuex';
import './common.scss';
import { toEntRegister } from '@/assets/utils/index.js';

const ICON_MAP = {
  '0': 'danganguanli',
  '1': 'zhinengmoban',
  '2': 'qianfa',
  '3': 'qianshu1',
  '4': 'shenpi',
  '5': 'hetongguanli2',
  '6': 'tongzhi',
  '7': 'quanxianguanli',
  '8': 'hegui',
  '9': 'hangyebao',
};
export default {
  data() {
    const obj = this.$t('manageSeriesProduction.list.list');
    const list = [];
    const data = Object.keys(obj).map(key => obj[key]);
    data.forEach((e, index) => {
      if (index % 2 === 0) {
        list.push({
          icon: ICON_MAP[index / 2],
          title: e,
          tip: data[index + 1],
        });
      }
    });
    console.log(list);
    return {
      list,
      isEN: false,
    };
  },
  head() {
    return {
      title: this.$t('manageSeriesProduction.TDK.title'),
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.$t('manageSeriesProduction.TDK.keywords'),
        },
        {
          hid: 'description',
          name: 'description',
          content: this.$t('manageSeriesProduction.TDK.description'),
        },
      ],
    };
  },
  computed: {
    ...mapState(['isMobile']),
    language() {
      return this.$store.state.locale;
    },
  },
  methods: {
    toEntRegister,
    toReg() {
      this.$router.push({
        path: `/${this.language}/material`,
      });
    },
  },
  mounted() {
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
};
</script>

<style lang="scss">
.function {
  img {
    width: 100%;
  }
  .function-common {
    padding: 1.5rem 15% 5rem 15%;
    p.order {
      color: $-color-main;
      font-size: 1.4rem;
      font-weight: 500;
    }
    h5 {
      color: #111;
      font-size: 1.4rem;
      margin-top: 1rem;
      line-height: 1.5;
    }
    .el-row {
      .el-col:first-child {
        padding-right: 10%;
      }
    }
  }
  .function-common-en {
    .el-row {
      .el-col {
        height: 240px;
        &:first-child {
          padding-right: 5%;
        }
        .function-list-item-right {
          .title {
            font-weight: 600;
          }
        }
      }
    }
  }
  &-base {
    .function-base-1 {
      border-bottom: 1px solid #e8e8e8;
      padding-bottom: 5rem;
      img {
        margin-top: 2rem;
      }
    }
    .function-base-2 {
      border-bottom: 1px solid #e8e8e8;
    }
    .el-row {
      // border-bottom: 1px solid #e9e8e8;
      padding: 5rem 0;
      p {
        color: #666;
        margin-top: 10px;
      }
    }
  }
  &-baseEn {
    .function-base-1 {
      h5 {
        font-weight: 600;
      }
    }
    .el-row {
      h5 {
        font-weight: 600;
      }
    }
  }
  &-contract {
    background: $-color-white;
    h5 {
      color: $-color-main !important;
    }
    .el-row {
      border-bottom: 1px solid #f5f5f5;
      padding: 5rem 0;
      &:last-child {
        border-bottom: 0;
      }
      &.function-contract-1 {
        padding-top: 0;
      }
      p {
        color: #666;
        margin-top: 10px;
        &.title-2 {
          color: #111;
          font-size: 1.3rem;
          margin-bottom: 1.5rem;
          font-weight: 500;
        }
        &.title-2EN {
          font-weight: 600;
        }
      }
    }
  }
  &-list {
    .el-col {
      display: flex;
      height: 160px;
      // padding-right: 10%;
      &:nth-child(2n + 1) {
        padding-right: 5%;
      }
      &:nth-child(2n) {
        padding-left: 5%;
      }
    }
    &-item-left {
      height: 80px;
      width: 80px;
      background: #ffffff;
      border-radius: 10px;
      margin-right: 5%;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      i.iconfont {
        margin: 0;
        font-size: 3rem;
      }
    }
    &-item-right {
      .title {
        color: #111;
      }
      .tip {
        color: #666;
      }
      p:first-child {
        font-weight: 500;
        margin-bottom: 10px;
        font-size: 1.3rem;
      }
    }
  }
}
.function.function-mobile {
  .function-common {
    padding: 1.5rem 5% 5rem 5%;
    h5 {
      padding-left: 3rem;
      position: relative;
      span {
        color: $-color-main;
        font-size: 1.6rem;
        width: 3rem;
        position: absolute;
        top: 0;
        left: 0;
      }
    }
    p {
      padding-left: 3rem;
    }
    .el-row .el-col {
      padding: 0 !important;
    }
    &.function-contract {
      .el-row {
        border-bottom: none;
        padding-top: 0;
      }
      img {
        margin-top: 4rem;
      }
    }
    &.function-base {
      img {
        margin-top: 4rem;
      }
    }
  }
  .function-common.function-list .el-col {
    height: auto;
    padding: 2rem !important;
    background: $-color-white;
    margin-bottom: 1rem;
    border-radius: 10px;
    .iconfont {
      font-size: 2.5rem;
    }
    .function-list-item-left {
      width: auto;
      background: #ffffff;
      border-radius: 8px;
      margin-right: 5%;
      flex-shrink: 0;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      margin-right: 2rem;
    }
  }
}
</style>
