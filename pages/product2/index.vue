<template>
    <section class="manageSeries" :class="isMobile && 'manageSeries-mobile'">
        <el-row class="manageSeries-banner">
            <el-col :span="isMobile ? 24 :12" class="manageSeries-banner-tip" :class="{'manageSeries-banner-tipBigEn':isEN}">
                <h1>{{$t('manageSeries.header.title')}}</h1>
                <p>{{$t('manageSeries.header.tip')}}</p>
                <el-button @click="toEntRegister" v-if="!isEN">{{$t('manageSeries.tryToUse')}}</el-button>
                <el-button @click="toReg" v-if="isEN">{{$t('header.material')}}</el-button>
            </el-col>
            <el-col :span="isMobile ? 24 : 12" v-if="!isEN">
                <img :src="isMobile ? 'https://static.bestsign.cn:443/2c4f098524ef2d1f87b7718f02572ea3c6d08cd8.jpg': 'https://static.bestsign.cn/205c3eca6ecae094e2ecfcd67726abd64d379a98.jpg'" alt="">
            </el-col>
            <el-col :span="isMobile ? 24 : 12" v-if="isEN">
                <img :src="isMobile ? 'https://static.bestsign.cn:443/d5229781c55917cf35add665ece9a7b0a488b86e.jpg': 'https://static.bestsign.cn:443/9e7f5e476750c16c1448decaea1357fbe94d3bae.jpg'" alt="">
            </el-col>
        </el-row>
        <section class="manageSeries-feature" :class="{'manageSeries-featureEn':isEN}">
            <h4 class="sub-title" :class="{'sub-title-en':isEN}">{{$t('manageSeries.header.production')}}</h4>
            <div class="swiper-container"  v-swiper:featureSwiper="isMobile?swiperOptions:{}">
              <el-row :class="isMobile &&'swiper-wrapper'" type="flex">
                  <el-col :span="8" :class="isMobile &&'swiper-slide'">
                      <i class="iconfont icon-a-xingzhuang638"></i>
                      <h6>{{$t('manageSeries.header.feature1')}}</h6>
                      <p>{{$t('manageSeries.header.feature1Tip')}}</p>
                  </el-col>
                  <el-col :span="8" :class="isMobile &&'swiper-slide'">
                      <i class="iconfont icon-a-xingzhuang640"></i>
                      <h6>{{$t('manageSeries.header.feature2')}}</h6>
                      <p>{{$t('manageSeries.header.feature2Tip')}}</p>
                  </el-col>
                  <el-col :span="8" :class="isMobile &&'swiper-slide'">
                    <i class="iconfont icon-a-xingzhuang637"></i>
                      <h6>{{$t('manageSeries.header.feature3')}}</h6>
                      <p>{{$t('manageSeries.header.feature3Tip')}}</p>
                  </el-col>
              </el-row>
            </div>
        </section>
        <el-row class="manageSeries-type" :class="{'manageSeries-typeEn':isEN}">
            <el-col class="manageSeries-type-title" :span="isMobile ? 24 : 8" :class="{'manageSeries-type-titleEn':isEN}">
                <el-col><h5 class="title">{{$t('manageSeries.type.title')}}</h5></el-col>
                <el-collapse v-if="!isMobile" v-model="activeType" accordion>
                  <el-collapse-item :title="$t('manageSeries.type.1')" name="1">
                    <h4>{{$t('manageSeries.type.1tip')}}</h4>
                    <p>{{$t('manageSeries.type.1tipMore')}}</p>
                    <el-button @click="toEntRegister" v-if="!isEN">{{$t('manageSeries.tryToUse')}}</el-button>
                  </el-collapse-item>
                  <el-collapse-item :title="$t('manageSeries.type.2')" name="2">
                    <h4>{{$t('manageSeries.type.2tip')}}</h4>
                    <p>{{$t('manageSeries.type.2tipMore')}}</p>
                    <el-button @click="toEntRegister" v-if="!isEN">{{$t('manageSeries.tryToUse')}}</el-button>
                  </el-collapse-item>
                  <el-collapse-item :title="$t('manageSeries.type.3')" name="3">
                    <h4>{{$t('manageSeries.type.3tip')}}</h4>
                    <p>{{$t('manageSeries.type.3tipMore')}}</p>
                    <el-button @click="toEntRegister" v-if="!isEN">{{$t('manageSeries.tryToUse')}}</el-button>
                  </el-collapse-item>
                </el-collapse>
                <div class="type-title-switch" v-else>
                  <el-row type="flex">
                    <el-col :span="8" :class="{
                      'type-title-switch-active': activeType === '1',
                      'type-title-switch-active-1-2': activeType === '2',
                    }" @click.native="activeType = '1'">
                        <div>
                          <i class="iconfont icon-biaozhunban" />
                        </div>
                    </el-col>
                    <el-col :span="8" :class="{
                      'type-title-switch-active': activeType === '2',
                      'type-title-switch-active-2-3': activeType === '3',
                    }" @click.native="activeType = '2'">
                        <div>
                          <i class="iconfont icon-qijianban" />
                        </div>
                    </el-col>
                    <el-col :span="8" :class="{
                      'type-title-switch-active': activeType === '3'
                    }" @click.native="activeType = '3'">
                      <div>
                        <i class="iconfont icon-zhuanyeban" />
                      </div>
                    </el-col>
                  </el-row>
                  <div class="type-title-switch-content">
                    <template v-if="activeType === '1'">
                      <h3>{{$t('manageSeries.type.1')}}</h3>
                      <h4>{{$t('manageSeries.type.1tip')}}</h4>
                      <p>{{$t('manageSeries.type.1tipMore')}}</p>
                    </template>
                    <template v-else-if="activeType === '2'">
                      <h3>{{$t('manageSeries.type.2')}}</h3>
                      <h4>{{$t('manageSeries.type.2tip')}}</h4>
                      <p>{{$t('manageSeries.type.2tipMore')}}</p>
                    </template>
                    <template v-else>
                      <h3>{{$t('manageSeries.type.3')}}</h3>
                      <h4>{{$t('manageSeries.type.3tip')}}</h4>
                      <p>{{$t('manageSeries.type.3tipMore')}}</p>
                    </template>
                    <el-button @click="toEntRegister" v-if="!isEN">{{$t('manageSeries.tryToUse')}}</el-button>
                  </div>
                </div>
            </el-col>
            <el-col class="manageSeries-type-advantage" :span="isMobile ? 12 : 8" :class="{'manageSeries-type-advantageEn':isEN}">
                <h4>{{$t('manageSeries.type.advantage')}}</h4>
                <p v-for="(item, index) in typeAdvantage" :key="item" :class="index ===0 && 'first'">
                  <span v-if="index%2===0 && !isMobile" >{{index/2+1}}</span>{{item}}
                </p>
            </el-col>
            <el-col class="manageSeries-type-feature" :span="isMobile ? 12 : 8" :class="{'manageSeries-type-featureEn':isEN}">
                <h4>{{typeTitle}}</h4>
                <p v-for="(item) in typeFeature" :key="item">
                    {{item}}
                </p>
            </el-col>
        </el-row>
        <IndexQuestion v-if="!isEN"/>
        <process v-if="isEN" style="background:#fff"></process>
    </section>
</template>

<script>
import { mapState } from 'vuex';
import IndexQuestion from '../cost/components/answersBox.vue';
import './common.scss';
import { toEntRegister } from '@/assets/utils/index.js';
import Process from '../cost/components/process.vue';
export default {
  components: {
    IndexQuestion,
    Process,
  },
  data() {
    return {
      activeType: '1',
      swiperOptions: {
        direction: 'horizontal',
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        spaceBetween: 20,
        centeredSlides: true,
        slidesPerView: 'auto',
      },
      isEN: false,
    };
  },
  head() {
    return {
      title: this.$t('manageSeries.TDK.title'),
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.$t('manageSeries.TDK.keywords'),
        },
        {
          hid: 'description',
          name: 'description',
          content: this.$t('manageSeries.TDK.description'),
        },
      ],
    };
  },
  methods: {
    toEntRegister,
    getList(name) {
      const obj = this.$t(`manageSeries.${name}`);
      return Object.keys(obj).map(key => obj[key]);
    },
    toReg() {
      this.$router.push({
        path: `/${this.language}/material`,
      });
    },
  },
  computed: {
    ...mapState(['isMobile']),
    typeTitle() {
      return this.$t(`manageSeries.type.feature${this.activeType}`);
    },
    typeAdvantage() {
      if (this.activeType === '1') {
        return this.getList('type1Advantage');
      }
      if (this.activeType === '2') {
        return this.getList('type2Advantage');
      }
      return this.getList('type3Advantage');
    },
    typeFeature() {
      if (this.activeType === '1') {
        return this.getList('type1Feature');
      }
      if (this.activeType === '2') {
        return this.getList('type2Feature');
      }
      return this.getList('type3Feature');
    },
    language() {
      return this.$store.state.locale;
    },
  },
  mounted() {
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
    // console.log(this.$t('manageSeries.type3Feature'));
  },
};
</script>

<style lang="scss">
</style>
