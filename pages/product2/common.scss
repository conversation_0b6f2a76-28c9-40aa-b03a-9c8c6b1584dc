.manageSeries {
  background: $-color-bg;
  .autoRecharge-answersBox {
    // padding-top:80px;
    background-color: $-color-white;
  }
  p {
      line-height: 1.6;
  }
  h4.sub-title {
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 450;
    padding: 4.5rem 0;
    color: #090909;
    text-align: center;
  }
  h4.sub-title-en {
    font-weight: 600;
    font-size: 2.5rem;
    line-height: 1.5;
  }
  .el-button {
    color: $-color-white;
    background: $-color-main;
    border: 1px solid $-color-main;
    border-radius: 3px;
    font-weight: 600;
    &:hover {
      // color: $-color-main;
      // background: $-color-white;
    }
  }
  .iconfont {
    color: $-color-main;
    font-size: 2.4rem;
    display: block;
    text-align: center;
    margin-bottom: 20px;
  }
  &-banner {
    background: $-color-white;
    min-height: 300px;
    .el-button {
      margin-top: 40px;
      font-size: 18px;
    }
    &-tip {
      // padding: 80px 120px;
      padding: 5% 10% 0 10%;
      h1 {
        color: $-font-color-main;
        font-size: 3rem;
        margin-bottom: 0.5rem;
      }
      p {
        font-size: 1rem;
        line-height: 1.6;
      }
    }
    &-tipEn{
      padding: 5% 5% 0 5%;
      h1{
        font-size: 3rem;
        font-weight: 600;
        margin-bottom: 40px;
      }
      p {
        font-size: 1.2rem;
        line-height: 1.5;
      }
    }
    &-tipBigEn{
      padding: 5% 5% 0 5%;
      h1{
        font-size: 3rem;
        font-weight: 600;
        margin-bottom: 40px;
      }
      p {
        font-size: 1.2rem;
        line-height: 1.5;
      }
    }
    img {
      width: 100%;
      vertical-align: middle;
    }
  }
  &-feature {
    padding: 1.5rem 15%  5rem 15%;
    h4 {
      text-align: center;
      padding: 6rem 0 3rem 0;
      font-size: 2rem;
      color: $-font-color-main;
    }
    h5 {
      padding: 10px 30%;
      text-align: center;
      line-height: 1.6;
    }
    .el-row {
      max-width: 1000px;
      margin: 0 auto;
      display: flex;
      justify-content: center;
    }
    .el-col {
      // width: 200px;
      // height: 280px;
      min-height: 300px;
      background: $-color-white;
      margin: 10px;
      border-radius: 10px;
      // padding: 50px 30px;
      padding: 5% 5%;
      margin: 1%;
      h6 {
        // font-size: 16px;
        font-size: 1.2rem;
        text-align: center;
        color: $-font-color-main;
      }
      p {
        margin-top: 20px;
        // font-size: 12px;
      }
    }
  }
  &-featureEn{
    .el-col {
      h6 {
        font-weight: 600;
      }
      p {
        margin-top: 20px;
      }
    }
  }
  &-type {
    height: 760px;
    &-title {
      height: 100%;
      background: $-color-main;
      .el-collapse-item {
        background: $-color-main;
        color: $-color-white;
        h4 {
          color: $-color-white;
          font-size: 1.8rem;
          margin-top: -10px;
        }
        p {
          color: $-color-sub;
          line-height: 1.6;
          margin: 10px 0;
        }
        .el-collapse-item__arrow {
          display: none !important;
        }
        .el-collapse-item__wrap,
        .el-collapse-item__header {
          padding: 0 80px;
          background: $-color-main;
          color: $-font-color-main;
          border-bottom: 0;
        }
        .el-collapse-item__header {
          font-size: 16px;
          padding: 40px 80px;
          line-height: 1.2;
        }
        &.is-active {
          background: $-color-deep;
          .el-collapse-item__wrap,
          .el-collapse-item__header {
            background: $-color-deep;
          }
          .el-collapse-item__header {
            color: $-color-main;
          }
        }
      }
      .el-col {
        padding: 0 80px;
        background: $-color-main;
        h5 {
          color: $-font-color-main;
          line-height: 80px;
          height: 80px;
          font-size: 16px;
        }
      }
    }
    &-titleEn{
      .el-collapse-item{
        h4 {
          font-weight: 600;
        }
        p {
          color: $-color-sub;
          line-height: 1.6;
          margin: 10px 0;
          font-size: 1.1rem;
        }
        .el-collapse-item__header {
          font-size: 1.1rem;
        }
        &.is-active {
          .el-collapse-item__wrap,
          .el-collapse-item__header {
            font-size: 1.1rem;
          }
          .el-collapse-item__header {
            color: $-color-main;
            font-size: 1.1rem;
          }
        }
      }
      .el-col {
        h5 {
          font-size: 1.1rem;
        }
      }
    }
    &-advantage {
      height: 100%;
      padding: 0 80px;
      h4 {
        color: $-color-main;
        font-size: 16px;
        line-height: 80px;
      }
      background: #273243;
      p {
        color: $-color-sub;
        line-height: 1.4;
        margin-top: 10px;
        &:nth-child(2n) {
          color: $-color-white;
          font-size: 1.1rem;
          margin-top: 30px;
          font-weight: 500;
        }
        &:nth-child(2n+1) {
          padding-left: 30px;
          font-size: 1rem;
        }
        span {
          width: 30px;
          display: inline-block;
          color: $-color-main;
        }
      }
    }
    &-advantageEn{
      h4 {
        font-size: 1.1rem;
        font-weight: 600;
      }
      p{
        &:nth-child(2n) {
          font-weight: 600;
        }
        &:nth-child(2n+1) {
          font-size: 1.1rem;
        }
      }
    }
    &-feature {
      height: 100%;
      padding: 0 80px;
      h4 {
        color: $-color-main;
        font-size: 16px;
        padding: 30px 0;
        line-height: 1.4;
      }
      background: $-color-deep;
      p {
        line-height: 1.5;
        color: $-color-sub;
        font-size: 1rem;
        position: relative;
        padding-left: 8px;
        margin: 10px 0;
        &:after {
          content: "";
          position: absolute;
          top: 12px;
          left: 0;
          width: 2px;
          height: 2px;
          border-radius: 100%;
          background-color: $-color-main;
        }
      }
    }
    &-featureEn {
      h4 {
        font-size: 1.1rem;
        font-weight: 600;
      }
      p{
        font-size: 1.1rem;
      }
    }
  }
  &-typeEn{
    height: 1100px;
  }
  &-service {
      padding: 1.5rem 10%  0  10%;
      h5 {
          padding: 0 20%;
          text-align: center;
          margin-bottom: 30px;
          line-height: 1.6;
          color: #333333;
          font-weight: 450;
          font-size: 1.1rem;
      }
      .el-row {
          display: flex;
          justify-content: center;
          max-width: 1200px;
          margin: 0 auto;;
      }
      .el-col {
          margin: 1%;
          background: $-color-white;
          border-radius: 10px;
          overflow: hidden;
          img {
              width: 100%;
              min-height: 100px;
          }
          h6 {
            // font-size: 18px;
            font-size: 1.4rem;
            text-align: center;
            color: $-font-color-main;
          }
          p {
            margin-top: 20px;
            line-height: 1.6;
            // font-size: 12px;
          }
      }
      &-row-1 {
          .el-col {
              padding: 6% 4%;
              min-height: 300px;
              h6 {
                  margin-top: 40px;
              }
          }
      }
      &-row-2 {
          .el-col {
              height: auto;
              div {
                  padding: 10%;
              }
              h5 {
                  text-align: left;
                  margin-bottom: 20;
                  padding: 0;
                  color: $-font-color-main;
              }
              h6 {
                  text-align: left;
                  // font-size: 16px;
                  font-size: 1.2rem;
                  margin-bottom: 10px;
              }
              p:nth-child(2n+1) {
                  position: relative;
                  padding-left: 10px;
                  margin-top: 6%;
                  margin-bottom: 2%;
                  color: $-font-color-main;
                  font-weight: 600;
                  font-size: 1.1rem;
                  &:after {
                      content: "";
                      position: absolute;
                      top: 8px;
                      left: 0;
                      width: 4px;
                      height: 4px;
                      border-radius: 100%;
                      background-color: $-color-main;
                  }
              }
              p:nth-child(2n) {
                  margin: 0;
              }
          }
          
      }
      &-row-2-en{
        .el-col{
          h6{
            font-weight: 600;
          }
        }
      }
  }
  &-security {
      background-color: $-color-main;
      padding: 1.5rem 20% 60px 20%;
      margin-top: 5rem;
      .el-col {
        margin: 1% 0;
      }
      h2 {
          margin: 30px 0;
          color: $-font-color-main;
      }
      &-1 {
          .el-col {
              margin: 2%;
              margin-left: 0;
              background: $-color-white;
              border-radius: 10px;
              padding: 40px;
              i.iconfont {
                font-size: 3rem;
              }
              h5 {
                  margin-bottom: 20px;
                  text-align: center;
                  color: $-font-color-main;
                  font-size: 1.4rem;;
              }
          }
      }
      &-2 {
          display: flex;
          border-bottom: 2px solid $-font-color-main;
          padding-bottom: 30px;
          p {
              // font-size: 12px;
              margin-right: 10%;
          }
          img {
            width: 50%;
            margin-bottom: 20px;
          }
      }
      &-3 {
          display: flex;
          border-bottom: 2px solid $-font-color-main;
          padding-bottom: 30px;
          p {
              // font-size: 12px;
          }
          h6 {
              font-size: 2.5rem;
              color: $-font-color-main;
              span {
                  font-size: 12px;
                  vertical-align: super;
              }
          }
      }
  }
  &-return {
      padding: 1.5rem 10% 0 10%;
      .el-row {
          max-width: 1200px;
          margin: 0 auto;
          .el-col {
              margin: 1%;
              background-color: $-color-white;
              border-radius: 10px;
              padding: 80px;
              h2 {
                  font-size: 3.5rem;
                  color: $-font-color-main;
              }
              h5 {
                  color: $-font-color-main;
                  margin-bottom: 40px;;
                  font-size: 1.2rem;
                  margin-top: 1rem;
              }
              h6 {
                  color: $-color-main;
                  margin-bottom: 20px;
                  font-size: 1rem;
                  line-height: 1.5;
              }
              p {
                  margin-bottom: 10px;
                  // font-size: 12px;
                  color: $-font-color-main;
                  span {
                    font-weight: 500;
                  }
              }
              .el-button {
                  // border-radius: 19px;
                  margin-top: 20px;
              }
              .iconfont {
                text-align: left;
                font-size: 3rem;
              }
          }
      }
  }
  &-team {
      margin-top: 5rem;
      background-color: #273243;
      padding-top: 32%;
      padding-bottom: 80px;
      background-image: url(https://static.bestsign.cn:443/d5acd6073566714c13e67c4c8f83c861ef113dfa.jpg);
      background-size: 100%;
      background-repeat: no-repeat;
      .swiper-container {
          width: 70%;
          height: 380px;
          margin: 0;
      }
      .swiper-slide {
          padding: 5% 10%;
          background: $-color-main;
          width: 100%;
          height: 380px;
          border-radius: 10px;
          h4 {
              color: $-font-color-main;
              font-size: 20px;
              margin: 20px 0;
          }
          p {
              // font-size: 12px;
          }
          .iconfont {
            text-align: left;
            color: $-color-white;
            font-size: 3rem;
            margin-top: 20px;
          }
      }
      .swiper-pagination {
          margin: 20px 0 ;
          span {
              display: inline-block;
              margin: 0 4px;
              // background-color: #767e88;
              background-color: #fff;
          }
          .swiper-pagination-bullet-active {
              background-color: $-color-main;
          }
      }
      &-left {
          padding: 0 10%;
          h4 {
              color: $-color-white;
              font-size: 2rem;
              margin-bottom: 20px;
          }
          p {
              color: $-color-white;
              // font-size: 12px;
          }
      }
      &-left-en {
        padding: 0 10%;
        h4 {
            color: $-color-white;
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 600;
            line-height:1.5;
        }
        p {
            color: $-color-white;
            // font-size: 12px;
        }
    }
  }
}
// 移动端样式覆盖
.manageSeries.manageSeries-mobile {
  .iconfont {
    font-size: 4rem;
  }
  p {
    font-size: 1.3rem;
  }
  h6 {
    font-size: 1.5rem;
    font-weight: 600;
  }
  .manageSeries-banner {
    position: relative;
    .manageSeries-banner-tip {
      padding: 40px 60px;
      text-align: center;
      background:  $-color-white;
      p {
        font-size: 1.3rem;
      }
    }
  }
  .manageSeries-feature {
      background: $-color-white;
      padding:  1.5rem 0 4.5rem 0;
      margin: 0;
      .swiper-container {
        height: 370px;
      }
      .el-row {
          justify-content: flex-start;
          .el-col {
              width: 70%;
              background: $-color-bg;
              margin: 0;
              padding: 15% 10%;
              height: 100%;
              p {
                font-size: 1.3rem;
              }
              h6 {
                font-size: 18px;
              }
          }
      }
  }
  .manageSeries-type {
    height: auto;
  }
  .manageSeries-type-advantage,
  .manageSeries-type-feature {
    padding: 0 20px;
    height: 960px;
  }
  .manageSeries-type-advantageEn,
  .manageSeries-type-featureEn{
    height: 1320px;
  }
  .manageSeries-type-advantage {
    p {
      padding-left: 0;
    }
    p.first {
      margin-top: 0;
    }
  }
  .manageSeries-type-feature p {
    color: $-color-white;
  }
  .manageSeries-type-title {
    background:$-color-white;
    height: auto;;
    .el-col {
      background:$-color-white;
    }
    .title {
      font-size: 2rem;
      text-align: center;
      margin-bottom: 4.5rem;
      margin-top: 1.5rem;
      line-height: 1;
      height: 2rem;
      font-weight: 450;
    }
    .type-title-switch {
      .el-row {
        overflow: hidden;
        border-radius: 20px 20px 0 0;
        height: 60px;
        width: 100%;
        background: $-color-main;
        .el-col {
          cursor: pointer;
          padding: 0;
          background: $-color-main;
          color: $-color-deep;
          // display: flex;
          // align-items: center;
          // justify-content: center;
          div {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: $-color-main;
            .iconfont {
              font-size: 2rem;
              margin: 0;
              display: inline;
              color: $-color-deep;
            }
          }
          // .outer {
          //   background: $-color-deep;
          // }
          &.type-title-switch-active {
            // background: $-color-deep;
            border-radius: 20px 20px 0 0;
            position: relative;
            &:after {
              content: "";
              position: absolute;
              width: 40px;
              height: 4px;
              border-radius: 2px;
              background-color: $-color-main;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
              z-index: 101;
            }
            div {
              background: $-color-deep;
              border-radius: 20px 20px 0 0;
              height: 62px;
            }
            .iconfont {
              color: $-color-main;
            }
            &+.el-col {
              background: $-color-deep; 
              div {
                background: $-color-main;
                border-radius: 0  0 0 20px;
              }
            }
          }
          &.type-title-switch-active-1-2 {
            background: $-color-deep;
            div {
              border-radius: 20px 0 20px 0;
            }
          }
          &.type-title-switch-active-2-3 {
            background: $-color-deep;
            div {
              border-radius: 0 0 20px 0;
            }
          }
        }
      }
      .type-title-switch-content {
        background-color: $-color-deep;
        padding: 10% 10%;
        text-align: center;
        position: relative;
        &:before {
          // mobile 多了一线 hack
          content: "";
          position: absolute;
          width: 100%;
          height: 2px;
          top: -1px;
          left: 0%;
          background: $-color-deep;
          z-index: 100;
        }
        h3 {
          color: $-color-main;
          font-size: 16px;;
        }
        h4 {
          color: $-color-white;
          font-size: 18px;
          margin: 20px 0;
        }
        .el-button {
          margin: 20px;
          border-radius: 3px;
        }
        p {
          color: #bbbbbd;
        }
      }
    }
  }
  .manageSeries-service {
      padding: 1.5rem 5% 5rem 5%;
      h5 {
          padding: 0 20px;
          font-size: 1.3rem;
          margin-bottom: 4rem;
          // text-align: justify;
      }
      .el-row {
          flex-direction: column;
          .el-col {
            margin: 0 0 20px 0;
            &:last-child {
              margin-bottom: 0;
            }
          }
      }
      .manageSeries-service-row-1 .el-col {
        padding: 15% 10%;
      }
      .manageSeries-service-row-2 h5 {
        padding: 0;
      }
      .manageSeries-service-row-2 .el-col p:nth-child(2n+1) {
        font-size: 1.3rem;
    }
  }
  .manageSeries-service_2 {
    background: $-color-white;
    .el-col  {
      background: $-color-bg;
    }
  }
  .manageSeries-security {
    margin-top: 0;
    padding: 40px 0;
    h2 {
      padding: 0 20px;
      margin: 50px 0 10px 0;
      &:first-child {
        margin: 80px 0 10px 0;
      }
    }
    .swiper-slide {
      width: 70%;
      background-color: $-color-white;
      margin: 0;
      height: 300px;
    }
    .manageSeries-security-2 {
      margin: 0 20px;
      border-top: 2px solid $-font-color-main;
      border-bottom: 0;
      flex-direction: column;
      .el-col  {
        display: flex;
        flex-direction: row;
        img {
          width: 30%;
          margin: 20px 40px 10px 0;
        }
        div {
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding-top: 10px;
          p {
            color: $-font-color-main;
            margin-right: 0;
            &:last-child {
              line-height: 1.4;
            }
          }
        }
      }
    }
    .manageSeries-security-3 {
      margin: 0 20px;
      border-top: 2px solid $-font-color-main;
      border-bottom: 0;
      flex-direction: column;
      .el-col {
        padding: 20px 0;
        border-bottom: 1px solid $-font-color-main;
        display: flex;
        justify-content: space-between;
        h6 {
          font-size: 2.5rem;
        }
        p {
          color: $-font-color-main;
          line-height: 2.5;
        }
        span {
          font-size: 2rem;
          vertical-align: middle;
        }
      }
    }
  }
  .manageSeries-return  {
      padding: 1.5rem 5%;
      .el-row {
        flex-direction: column;
        h6 {
          font-size: 1.3rem;
        }
        .el-col {
          padding: 40px;
          margin: 0 0 20px 0;
        }
      }
  }
  .manageSeries-team {
      .manageSeries-team-left {
          text-align: center;
          margin-bottom: 40px;
      }
      .swiper-container {
          width: 100%;
          margin: 0 auto;
          height: 45rem;
      }
      .swiper-slide {
          background: $-color-main;
          width: 70%;
          height: 45rem;
      }
      .swiper-pagination {
          left: 50%;
          transform: translateX(-50%);
      }
      .swiper-slide {
          h4 {
              color: $-font-color-main;
              font-size: 20px;
              margin: 20px 0;
          }
          p {
              font-size: 1.3rem;
          }
      }
  }
}