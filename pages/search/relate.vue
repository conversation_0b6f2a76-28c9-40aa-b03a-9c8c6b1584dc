<!-- 组件说明 -->
<template>
<div class="all">
  <div class="search-in">
    <el-input v-model="inputSearch" placeholder="请输入内容"></el-input>
    <i class="iconfont icon-sousuo1" @click="handleSearch"></i>
  </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
  <el-tab-pane
    :key="item.name"
    v-for="item in searchList"
    :label="`${item.columnName}(${item.count})`"
    :name="item.columnType"
  >
    <a class="dsc" :href="`/${editableTabs.contentType}/${editableTabs.contentId}`">
    <div class="left">
      <img :src="editableTabs.imgUrl" alt="">
    </div>
    <div class="right">
      <div class="right-title">{{editableTabs.title}}</div>
      <!-- <div class="right-content">{{relate1.tdkD}}<span @click="toDetail(666)">{{'[查看详情]'}}</span></div> -->
      <div class="right-content">{{editableTabs.description}}<nuxt-link to="/wiki/detail/701"><span>{{'[查看详情]'}}</span></nuxt-link></div>
      <div class="right-time"><span style="margin-right:10px">上上签电子签约</span>{{editableTabs.releaseTime}}</div>
    </div>
  </a>
  </el-tab-pane>
</el-tabs>
  <!-- <div class="title"><span class="title-dsc1">电子签名相关知识</span><span class="title-dsc2">—  安全、便捷的电子签约云平台</span></div> -->
  
  <!-- <div class="dsc-bottom-container">
    <div class="dsc-bottom">
    <div class="dsc-bottom-left" @click="toDetail(661)">{{relate2.wikiTitle}}</div>
    <nuxt-link to="/wiki/detail/601"><div class="dsc-bottom-left">{{relate2.wikiTitle}}</div></nuxt-link>
    <div class="dsc-bottom-right" v-if="!isMobile"><span style="margin-right:10px">上上签电子签约</span>{{relate2.releaseTime}}</div>
  </div>
  <div class="dsc-bottom">
    <div class="dsc-bottom-left" @click="toDetail(623)">{{relate3.wikiTitle}}</div>
    <nuxt-link to="/wiki/detail/587"><div class="dsc-bottom-left">{{relate3.wikiTitle}}</div></nuxt-link>
    <div class="dsc-bottom-right" v-if="!isMobile"><span style="margin-right:10px">上上签电子签约</span>{{relate3.releaseTime}}</div>
  </div>
  </div>
  <div class="more">查看更多<i class="iconfont icon-xiangxiajiantou" style="margin-left:3px"></i></div>
  <nuxt-link to="/wiki"><div class="more">查看更多<i class="iconfont icon-xiangxiajiantou" style="margin-left:3px"></i></div></nuxt-link> -->
</div>
</template>

<script>
//import x from ''
import Qs from 'qs';
export default {
  name: 'relate',
  props: {
    searchList: {
      type: Array,
      default: () => [],
    },
    editableTabs: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      inputSearch: '',
      activeName: '1',
      tabIndex: 2,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {
    // toDetail(id) {
    //   const query = sessionStorage.getItem('query');
    //   this.$router.push({
    //     path: `/wiki/detail/${id}`,
    //     query: {
    //       id: Qs.parse(query).id,
    //       utm_source: Qs.parse(query).utm_source,
    //     },
    //   });
    // },
    // toList() {
    //   const query = sessionStorage.getItem('query');
    //   this.$router.push({
    //     path: '/wiki',
    //     query: {
    //       id: Qs.parse(query).id,
    //       utm_source: Qs.parse(query).utm_source,
    //     },
    //   });
    // },
    handleSearch() {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/search',
        query: {
          ...Qs.parse(query),
          inputContent: this.inputSearch,
        },
      });
    },
    handleClick(tab, event) {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/search',
        query: {
          ...Qs.parse(query),
          inputContent: this.inputSearch,
          searchType: tab.name,
        },
      });
    },
  },
  mounted() {},
};
</script>

<style lang='scss' scoped>
.all {
  margin: 20px 0;
  // background-color: #fff;
  // border: 1px solid #e5e5e5;
  // border-radius: 5px;
  .title {
    font-size: 1.6rem;
    margin: 30px;
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 15px;
    .title-dsc1 {
      width: 200px;
      margin-right: 20px;
      padding-bottom: 13px;
      border-bottom: 1px solid #00aa64;
    }
    .title-dsc2 {
      width: 200px;
      padding-bottom: 11px;
      color: #999999;
    }
  }
  .el-input {
    width: 50%;
  }
  .dsc {
    margin: 30px;
    font-size: 15px;
    line-height: 25px;
    color: #666666;
    display: flex;
    flex-wrap: nowrap;
    padding-bottom: 30px;
    border-bottom: 1px solid #e5e5e5;
    .left {
      margin-right: 10px;
    }
    .right {
      margin-left: 10px;
      .right-title {
        font-size: 1.2rem;
        margin-bottom: 10px;
      }
      .right-content {
        font-size: 1rem;
        span {
          color: red;
          cursor: pointer;
        }
      }
      .right-time {
        margin-top: 10px;
        font-size: 0.9rem;
      }
    }
  }
  .dsc-bottom-container {
    margin: 30px;
    border-bottom: 1px solid #e5e5e5;
    .dsc-bottom {
      margin: 20px 0px;
      font-size: 15px;
      line-height: 25px;
      color: #666666;
      display: flex;
      justify-content: space-between;
      .dsc-bottom-left {
        color: #666666;
        cursor: pointer;
        &:hover {
          color: #00aa64;
        }
      }
    }
  }
  .more {
    color: #666666;
    margin: 20px;
    text-align: center;
    cursor: pointer;
    &:hover {
      color: #00aa64;
    }
  }
}
@media (max-width: 768px) {
  .all {
    margin: 20px 0;
    background-color: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 5px;
    .title {
      font-size: 1.6rem;
      margin: 30px;
      border-bottom: 1px solid #e5e5e5;
      padding-bottom: 15px;
      .title-dsc1 {
        width: 200px;
        margin-right: 20px;
        padding-bottom: 11px;
        border-bottom: 0px solid #00aa64;
        display: block;
      }
      .title-dsc2 {
        width: 200px;
        padding-bottom: 11px;
        color: #999999;
        font-size: 14px;
      }
    }
    .dsc {
      margin: 10px;
      font-size: 15px;
      line-height: 25px;
      color: #666666;
      display: flex;
      flex-wrap: wrap;
      padding-bottom: 30px;
      border-bottom: 1px solid #e5e5e5;
      .left {
        margin: 0 auto;
        img {
          width: 100%;
        }
      }
      .right {
        margin-left: 10px;
        .right-title {
          font-size: 1.2rem;
          // margin-bottom: 10px;
          margin: 10px 0;
          line-height: 30px;
        }
        .right-content {
          font-size: 1rem;
          span {
            color: red;
            cursor: pointer;
          }
        }
        .right-time {
          margin-top: 10px;
          font-size: 10px;
          float: right;
          font-size: 0.9rem;
        }
      }
    }
    .dsc-bottom-container {
      margin: 0px;
      border-bottom: 1px solid #e5e5e5;
      .dsc-bottom {
        margin: 20px 10px;
        font-size: 1.2rem;
        line-height: 25px;
        color: #666666;
        display: flex;
        justify-content: space-between;
        .dsc-bottom-left {
          color: #666666;
          cursor: pointer;
          &:hover {
            color: #00aa64;
          }
        }
      }
    }
    .more {
      color: #666666;
      margin: 20px;
      text-align: center;
      cursor: pointer;
      &:hover {
        color: #00aa64;
      }
    }
  }
}
//@import url()
</style>
