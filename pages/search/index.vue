<template>
  <article>
    <section class="section section-1">
      <div class="container">
        <div class="article" v-if="!isMobile">
          <div class="all" >
          <div class="search-in">
            <el-input v-model="inputSearch" placeholder="请输入内容" @keyup.enter.native="searchEnterFun"></el-input>
            <div class="search-btn">
              <i class="iconfont icon-sousuo1" @click="handleSearch"></i>
            </div>
            <!-- <el-button type="primary" icon="el-icon-search">搜索</el-button> -->
          </div>
          <el-tabs v-model="activeName" @tab-click="handleClick" v-if="isSearch">
            <!-- <el-tab-pane :label="`全部(${totalNum})`" name="all">
              <div v-for="itemList in editableTabs" :key="itemList.contentId">
                <a class="dsc" :href="goDetail(itemList)">
                  <div class="left" v-if="!isMobile">
                    <img :src="itemList.imgUrl" alt="">
                  </div>
                  <div class="right">
                    <div class="right-title">{{itemList.title}}</div>
                    <div class="right-descript">{{itemList.description}}</div>
                    <div class="right-time"><i class="iconfont icon-gerentouxiang"></i><span style="margin-right:10px">上上签</span>{{releaseTime(itemList.releaseTime)}}</div>
                  </div>
                </a>
          </div>
          <div class="searchResult" v-if="!totalNum">暂无搜索结果</div>
        </el-tab-pane> -->
        <el-tab-pane
          :key="item.name"
          v-for="item in searchList"
          :label="`${item.columnName}(${item.count})`"
          :name="item.columnType"
        >
        <div v-for="itemList in editableTabs" :key="itemList.contentId">
          <a class="dsc" :href="goDetail(itemList)">
          <div class="left">
            <img :src="itemList.imgUrl" alt="">
          </div>
          <div class="right">
            <div class="right-title">{{itemList.title}}</div>
            <div class="right-descript">{{itemList.description}}</div>
            <div class="right-time"><i class="iconfont icon-gerentouxiang"></i><span style="margin-right:10px">上上签</span>{{releaseTime(itemList.releaseTime)}}</div>
          </div>
        </a>
        </div>
        <div class="searchResult" v-if="!totalNum">暂无搜索结果</div>
        </el-tab-pane>
      </el-tabs>
      <el-tabs v-model="activeName"  v-if="!isSearch">
        <!-- <el-tab-pane label="全部(0)" name="all">
          <div class="searchResult">暂无搜索结果</div>
        </el-tab-pane> -->
        <el-tab-pane
          :key="item.name"
          v-for="item in searchList"
          :label="`${item.columnName}(0)`"
          :name="item.columnType"
        >
        <div class="searchResult">暂无搜索结果</div>
        </el-tab-pane>
      </el-tabs>
      </div>
        </div>
        <div class="article" v-if="isMobile">
          <div class="all" >
          <div class="search-in">
            <el-input v-model="inputSearch" placeholder="请输入内容" @keyup.enter.native="searchEnterFun"></el-input>
            <div class="search-btn">
              <i class="iconfont icon-sousuo1" @click="handleSearch"></i>
            </div>
            <!-- <el-button type="primary" icon="el-icon-search">搜索</el-button> -->
          </div>
          <nav v-if="isSearch">
            <div class="abstract industry">
              <ul class="">
                <li class="active" :class="{ oldStyle:control}" @click="getcontentList">所有({{allCount}})</li>
                <template v-for="item in searchList">
                  <li :key="item.columnType" @click="handleWapClick(item)" v-if="!(item.columnType==='all')">{{ item.columnName }}({{item.count}})</li>
                </template>
              </ul>
            </div>
          </nav>
          <div v-for="itemList in editableTabs" :key="itemList.contentId" >
            <a class="dsc" :href="goDetail(itemList)" v-if="isSearch">
            <div class="left" v-if="!isMobile">
              <img :src="itemList.imgUrl" alt="">
            </div>
            <div class="right">
              <div class="right-title">{{itemList.title}}</div>
              <div class="right-descript-wap">{{itemList.description}}</div>
              <div class="right-time"><i class="iconfont icon-gerentouxiang"></i><span style="margin-right:10px">上上签</span>{{releaseTime(itemList.releaseTime)}}</div>
            </div>
          </a>
        </div>
        <div class="searchResult" v-if="!totalNum">暂无搜索结果</div>
          <nav v-if="!isSearch">
            <div class="abstract industry">
              <ul class="">
                <!-- 由于样式的问题所有分开写了 -->
                <li class="active" :class="{ oldStyle:control}">所有(0)</li>
                <template v-for="item in searchList">
                  <li :key="item.columnType" @click="handleWapClick(item)" v-if="!(item.columnType==='all')">{{ item.columnName }}({{0}})</li>
                </template>
              </ul>
            </div>
          </nav>
          <div v-if="!isSearch" class="searchResult">暂无搜索结果</div>
          
        </div>
        </div>
        <!-- <aside class="article-side" v-if="!isMobile">
          <div class="item-wrap">
            <h4>电子签约的政策解读</h4>
            <div><i class="iconfont icon-weibiaoti-1"></i>{{time}}</div>
            <nuxt-link to="/wiki/detail/463"><div class="policyTitle">{{policy.wikiTitle}}</div></nuxt-link>
          </div>
          <div class="item-wrap">
            <h4>最新资讯</h4>
            <template v-for="item in newsInformation.data">
              <div class="item" :key="item.id">
                <nuxt-link :to="`/news/detail/${item.id}`">{{ item.dynamicTitle }}</nuxt-link>
              </div>
            </template>
          </div>
          <div class="item-wrap">
            <h4>推荐词条</h4>
            <div class="item-container">
              <nuxt-link to="/dianziqianyue"><div class="item1">电子签约</div></nuxt-link>
            <nuxt-link to="/dianzihetong"><div class="item2">电子合同</div></nuxt-link>
            <nuxt-link to="/dianziqianzhang"><div class="item3">电子签章</div></nuxt-link>
            <nuxt-link to="/dianziyinzhang"><div class="item4">电子印章</div></nuxt-link>
            </div>
          </div>
          <div class="qrcode">
              <img
                src="@/assets/images/qr-wechat.png"
                alt="二维码">
              <p style="line-height: 25px;color:#808080;">扫码加小签-电子签约顾问<br>领取行业资料<br>观看行业公开课</p>
            </div>
        </aside> -->
      </div>
      <div class="pagination-btn" v-if="isSearch">
        <el-pagination
          background
          :page-size="10"
          :current-page="current"
          @current-change="handleCurrentChange"
          layout="prev, pager, next"
          :total="totalNum"
          :pager-count="5">
        </el-pagination>
      </div>
      
    </section>
  </article>
</template>

<script>
import Qs from 'qs';
import moment from 'moment';
import find from 'lodash/find';
import Relate from './relate';
export default {
  name: 'dianziqianyue',
  components: {
    Relate,
  },
  head() {
    return {
      title: this.tdkT || '上上签站内搜索-上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keyword',
          content: this.tdkK || '电子签约,电子合同,电子签名,电子签章,电子印章',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            this.tdkD ||
            '上上签电子签约云平台站内搜索板块为企业提供关于电子签约、电子合同、电子签名、电子签章、电子印章等专业信息，满足企业的搜索需求。',
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.bestsign.cn/dianziqianming',
        },
      ],
    };
  },
  // async asyncData({ route, app, redirect }) {
  //   const id = route.params.id;
  //   const res = await app.$axios.get(
  //     // `/www/api/web/customerCase?customerCaseId=${id}`
  //     `www/api/web/getCustomerCase/${id}`
  //   );
  //   if (res.length === 0) {
  //     redirect('/404');
  //   }
  //   return {
  //     article: res,
  //     // releaseTime: moment(res.releaseTime).format('YYYY-MM-DD'),
  //     releaseTime: res.releaseTime,
  //     // seo: res[0].summary ? JSON.parse(res[0].summary) : {},
  //     tdkT: res.tdkT,
  //     tdkD: res.tdkD,
  //     tdkK: res.tdkK,
  //   };
  // },
  data() {
    return {
      article: {},
      time: moment().format('YYYY-MM-DD'),
      inputSearch: '',
      isSearch: '',
      activeName: 'all',
      tabIndex: 2,
      editableTabs: [],
      searchList: [],
      totalNum: 0,
      control: true,
      current: 1,
      wapTab: '',
      allCount: 0,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    // handlePage(id) {
    //   this.$router.push(`/case${id ? `/${id}` : ''}`);
    // },
    // getDate() {
    //   var data = new Date();
    //   return moment(data);
    // },
    releaseTime(time) {
      return moment(time).format('YYYY-MM-DD');
    },
    //跳到去详情页逻辑
    goDetail(type) {
      if (type.contentType === 'customerCase') {
        return `case/detail/${type.contentId}`;
      } else if (type.contentType === 'dynamic') {
        return `news/detail/${type.contentId}`;
      } else if (type.contentType === 'wiki') {
        return `wiki/detail/${type.contentId}`;
      } else {
        return `${type.contentType}/${type.contentId}`;
      }
    },
    // 分页逻辑
    handleCurrentChange(val) {
      this.current = val;
      if (!this.isMobile) {
        this.$axios({
          url: `www/api/web/search?pageNum=${
            this.current
          }&pageSize=10&keyword=${this.inputSearch}&partition=${
            this.activeName
          }`,
          method: 'get',
        }).then(res => {
          this.editableTabs = res.data;
          this.totalNum = res.totalNum;
        });
      } else {
        this.control = false;
        this.getTypeList();
        this.$axios({
          url: `www/api/web/search?pageNum=${
            this.current
          }&pageSize=10&keyword=${this.inputSearch}&partition=${this.wapTab}`,
          method: 'get',
        }).then(res => {
          this.editableTabs = res.data;
          this.totalNum = res.totalNum;
        });
      }
    },
    // 回车搜索
    searchEnterFun(e) {
      var keyCode = window.event ? e.keyCode : e.which;
      // console.log('回车搜索',keyCode,e);
      if (keyCode == 13 && this.inputSearch) {
        // this.$router.push({ path: '/Share?keywords=' + this.input });
        this.handleSearch();
      }
    },
    // tab栏目以及数量统计
    getTypeList() {
      this.$axios({
        url: `/www/api/web/search/statistics?keyword=${this.inputSearch}`,
        method: 'get',
      }).then(res => {
        this.searchList = res.result;
        this.allCount = res.result.filter(
          (item, index) => item.columnType === 'all'
        )[0].count;
      });
    },
    // 点击全部拿到搜索列表
    getcontentList() {
      this.current = 1;
      this.$axios({
        url: `www/api/web/search?pageNum=${this.current}&pageSize=10&keyword=${
          this.inputSearch
        }&partition=`,
        method: 'get',
      }).then(res => {
        this.editableTabs = res.data;
        this.totalNum = res.totalNum;
      });
    },
    //pc端搜索逻辑
    handleSearch() {
      this.activeName = 'all';
      this.control = true;
      this.isSearch = this.inputSearch;
      this.getTypeList();
      this.getcontentList();
    },
    // pc 端切换tab逻辑
    handleClick(tab, event) {
      this.getTypeList();
      this.tabName = tab.name; //分页用到
      this.current = 1;
      this.$axios({
        url: `www/api/web/search?pageNum=${this.current}&pageSize=10&keyword=${
          this.inputSearch
        }&partition=${tab.name}`,
        method: 'get',
      }).then(res => {
        this.editableTabs = res.data;
        this.totalNum = res.totalNum;
      });
    },
    //移动端切换tab逻辑
    handleWapClick(item) {
      this.wapTab = item.columnType; //分页用到
      this.control = false;
      this.current = 1;
      this.getTypeList();
      if (!(item.columnType === 'all')) {
        this.$axios({
          url: `www/api/web/search?pageNum=${
            this.current
          }&pageSize=10&keyword=${this.inputSearch}&partition=${
            item.columnType
          }`,
          method: 'get',
        }).then(res => {
          this.editableTabs = res.data;
          this.totalNum = res.totalNum;
        });
      } else {
        this.$axios({
          url: `www/api/web/search?pageNum=${
            this.current
          }&pageSize=10&keyword=${this.inputSearch}&partition=`,
          method: 'get',
        }).then(res => {
          this.editableTabs = res.data;
          this.totalNum = res.totalNum;
        });
      }
    },
  },
  mounted() {
    this.getTypeList();
  },
};
</script>

<style scoped lang="scss">
.section-1 {
  background-color: #fafbfc;
  padding: 16px 0;
  a {
    color: #888;
    &:hover {
      color: #00aa64;
    }
  }
  .pagination-btn {
    text-align: center;
  }
  .container {
    display: flex;
    .article {
      flex: 1;
      padding: 30px 20px 80px;
      // background-color: #fff;
      // border: 1px solid #e5e5e5;
      // border-radius: 5px;
      .all {
        margin: 20px 0;
        // background-color: #fff;
        // border: 1px solid #e5e5e5;
        // border-radius: 5px;
        .searchResult {
          text-align: center;
          margin-top: 30px;
          font-size: 20px;
        }
        /deep/ .el-tabs__active-bar {
          background-color: #00aa64;
        }
        /deep/ .el-tabs__item.is-active {
          color: #00aa64;
        }
        /deep/ .el-tabs__item:hover {
          color: #00aa64;
          cursor: pointer;
        }
        /deep/ .el-input__inner:focus {
          border-color: #00aa64;
          outline: 0;
        }
        .search-in {
          .el-input {
            width: 50%;
          }
          .search-btn {
            position: relative;
            top: -28px;
            left: calc(50% - 30px);
            cursor: pointer;
          }
        }
        .title {
          font-size: 1.6rem;
          margin: 30px;
          border-bottom: 1px solid #e5e5e5;
          padding-bottom: 15px;
          .title-dsc1 {
            width: 200px;
            margin-right: 20px;
            padding-bottom: 13px;
            border-bottom: 1px solid #00aa64;
          }
          .title-dsc2 {
            width: 200px;
            padding-bottom: 11px;
            color: #999999;
          }
        }

        .dsc {
          margin: 30px;
          font-size: 15px;
          line-height: 25px;
          color: #666666;
          display: flex;
          flex-wrap: nowrap;
          padding-bottom: 30px;
          border-bottom: 1px solid #e5e5e5;
          .left {
            margin-right: 10px;
            width: 20rem;
            img {
              width: 20rem !important;
            }
          }
          .right {
            margin-left: 10px;
            .right-title {
              font-size: 1.2rem;
              margin-bottom: 10px;
              color: #090909;
              font-weight: 500;
            }
            .right-content {
              font-size: 1rem;
              span {
                color: red;
                cursor: pointer;
              }
            }
            .right-time {
              margin-top: 10px;
              font-size: 0.9rem;
              display: flex;
              align-items: center;
              .iconfont {
                font-size: 30px;
              }
            }
          }
        }
        .dsc-bottom-container {
          margin: 30px;
          border-bottom: 1px solid #e5e5e5;
          .dsc-bottom {
            margin: 20px 0px;
            font-size: 15px;
            line-height: 25px;
            color: #666666;
            display: flex;
            justify-content: space-between;
            .dsc-bottom-left {
              color: #666666;
              cursor: pointer;
              &:hover {
                color: #00aa64;
              }
            }
          }
        }
        .more {
          color: #666666;
          margin: 20px;
          text-align: center;
          cursor: pointer;
          &:hover {
            color: #00aa64;
          }
        }
      }
    }
    #content {
      padding-bottom: 45px;
      border-bottom: 1px solid #e5e5e5;
    }
    aside {
      width: 25%;
      margin-left: 16px;
      display: flex;
      flex-direction: column;
      padding: 30px 0;
      .item-wrap {
        background-color: #fff;
        border: 1px solid #e5e5e5;
        border-radius: 5px;
        padding: 24px 18px;
        margin-bottom: 20px;
        .img-wrap {
          width: 100%;
          text-align: center;
          img {
            width: 100%;
          }
        }
        h4 {
          margin: 0 auto 26px;
          font-size: 18px;
          padding-bottom: 15px;
          border-bottom: 1px solid #e5e5e5;
        }
        .item {
          font-size: 14px;
          margin-bottom: 25px;
          cursor: pointer;
          color: #888;
          line-height: 25px;
          &:hover {
            color: #00a664;
          }
        }
        .item-container {
          display: flex;
          flex-wrap: wrap;
          .item1 {
            width: 101%;
            font-size: 14px;
            cursor: pointer;
            color: #383838;
            line-height: 25px;
            text-align: center;
            line-height: 34px;
            background: #e8eff9;
            border: 1px solid #e8eff9;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            width: 80px;
            &:hover {
              color: #00a664;
            }
          }
          .item2 {
            width: 101%;
            font-size: 14px;
            cursor: pointer;
            color: #383838;
            line-height: 25px;
            text-align: center;
            line-height: 34px;
            background: #f4f1f0;
            border: 1px solid #f4f1f0;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            width: 80px;
            &:hover {
              color: #00a664;
            }
          }
          .item3 {
            width: 101%;
            font-size: 14px;
            cursor: pointer;
            color: #383838;
            line-height: 25px;
            text-align: center;
            line-height: 34px;
            background: #f4e8f6;
            border: 1px solid #f4e8f6;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            width: 80px;
            &:hover {
              color: #00a664;
            }
          }
          .item4 {
            width: 101%;
            font-size: 14px;
            cursor: pointer;
            color: #383838;
            line-height: 25px;
            text-align: center;
            line-height: 34px;
            background: #eaf7ec;
            border: 1px solid #eaf7ec;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            width: 80px;
            &:hover {
              color: #00a664;
            }
          }
        }

        .policyTitle {
          font-size: 14px;
          color: #888;
          margin: 5px 0;
          line-height: 25px;
          &:hover {
            color: #00a664;
          }
        }
        .iconfont {
          color: #00aa64;
        }
      }
      .qrcode {
        min-width: 200px;
        margin-top: 20px;
        display: flex;
        justify-content: space-around;
        img {
          width: 80px;
          height: 80px;
        }
        p {
          // margin-top: 0.25rem;
          font-size: 12px;
          // margin-left: 0.3rem;
        }
      }
    }
    h1 {
      font-size: 30px;
      margin-bottom: 60px;
    }
    .desc {
      text-align: right;
      font-size: 14px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e5e5e5;
      margin-bottom: 45px;
    }
  }
  nav {
    margin-top: 25px;
    position: relative;
    .nav-item {
      cursor: pointer;
      padding: 10px 0;
      &:hover {
        color: #00a664;
      }
    }
    .back {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 10px;
      .el-icon-arrow-left {
        padding-right: 5px;
        font-size: 22px;
        position: relative;
        top: 2px;
      }
      &:hover {
        color: #00a664;
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .section-1 {
    .container {
      .article {
        padding: 0;
        width: 100%;
        .all {
          margin: 20px 0;
          // background-color: #fff;
          // border: 1px solid #e5e5e5;
          // border-radius: 5px;
          .search-in {
            .el-input {
              width: 100%;
            }
            .search-btn {
              position: relative;
              top: -28px;
              left: calc(100% - 30px);
              cursor: pointer;
            }
          }
          .searchResult {
            text-align: center;
            margin-top: 30px;
            font-size: 20px;
          }
          .abstract {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            margin: 0 auto;
            max-width: 1200px;
            border-bottom: 1px solid #eee;
            width: 100%;
            font-size: 14px;
            margin-left: auto;
            margin-right: auto;
            flex-wrap: wrap;
            .oldStyle {
              border-bottom: 2px solid #00aa64;
              color: #00aa64;
            }
            a:hover {
              border-bottom: 2px solid #00aa64;
              color: #00aa64;
            }
            ul {
              display: flex;
              flex-wrap: wrap;
              align-items: center;
              justify-content: space-around;
            }
            li {
              cursor: pointer;
              padding: 20px 8px;
              color: #86868b;
              font-size: 14px;
              text-align: center;
              &:hover {
                color: #00a664;
                border-bottom: 2px solid #00aa64;
              }
            }
            //   li:last-child {
            //     padding-right: 0;
            //   }
            .nuxt-link-exact-active {
              border-bottom: 2px solid #00aa64;
              li {
                color: #00aa64;
              }
            }
          }
          .title {
            font-size: 1.6rem;
            margin: 30px;
            border-bottom: 1px solid #e5e5e5;
            padding-bottom: 15px;
            .title-dsc1 {
              width: 200px;
              margin-right: 20px;
              padding-bottom: 11px;
              border-bottom: 0px solid #00aa64;
              display: block;
            }
            .title-dsc2 {
              width: 200px;
              padding-bottom: 11px;
              color: #999999;
              font-size: 14px;
            }
          }
          .dsc {
            margin: 10px;
            font-size: 15px;
            line-height: 25px;
            color: #666666;
            display: flex;
            flex-wrap: wrap;
            padding-bottom: 30px;
            border-bottom: 1px solid #e5e5e5;
            .left {
              margin: 0 auto;
              img {
                width: 100%;
              }
            }
            .right {
              margin-left: 10px;
              .right-title {
                font-size: 1.5rem;
                // margin-bottom: 10px;
                margin: 10px 0;
                line-height: 30px;
              }
              .right-content {
                font-size: 1rem;
                span {
                  color: red;
                  cursor: pointer;
                }
              }
              .right-descript-wap {
                margin-top: 24px;
                font-size: 14px;
                letter-spacing: 0;
                line-height: 20px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: normal;
                /* autoprefixer: off */
                -webkit-box-orient: vertical;
                display: -webkit-box;
                /* autoprefixer: on */
                -webkit-line-clamp: 2;
                max-width: 460px;
                color: #86868b;
                height: 40px;
              }
              .right-time {
                margin-top: 10px;
                font-size: 10px;
                // float: right;
                font-size: 1.2rem;
              }
            }
          }
          .dsc-bottom-container {
            margin: 0px;
            border-bottom: 1px solid #e5e5e5;
            .dsc-bottom {
              margin: 20px 10px;
              font-size: 1.2rem;
              line-height: 25px;
              color: #666666;
              display: flex;
              justify-content: space-between;
              .dsc-bottom-left {
                color: #666666;
                cursor: pointer;
                &:hover {
                  color: #00aa64;
                }
              }
            }
          }
          .more {
            color: #666666;
            margin: 20px;
            text-align: center;
            cursor: pointer;
            &:hover {
              color: #00aa64;
            }
          }
        }
      }
      // padding: 30px 20px 50px;
      h1 {
        font-size: 24px;
        color: #515151;
        font-weight: 200;
        margin-bottom: 30px;
        line-height: 35px;
      }
      h3 {
        font-size: 24px;
        margin-bottom: 40px;
      }
      nav {
        .back {
          cursor: pointer;
          left: 0;
          bottom: -35px;
          top: auto;
        }
      }
    }
    img {
      max-width: 100%;
    }
  }
}
</style>
