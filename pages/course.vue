<template>
  <article class="public-class" :class="{'public-class__mobile': isMobile}">
    <section class="section section-1 is-tiny">
      <div class="container">
        <h1 class="article-headline headline--large">
          <template v-if="!isMobile">在这里你可以与大咖面对面交流，了解最前沿的行业新知。</template>
          <template v-else>在这里你可以与<br>大咖面对面交流，<br>了解最前沿的行业新知。</template>
        </h1>
        <p class="article-subtitle">注册成为上上签官方微信会员，快速跻身电子签名专家。</p>
        <div class="card-wrapper">
          <el-row>
            <template v-for="(item, index) in shownList">
              <el-col
                :key="index"
                :xs="24"
                :md="12"
                :lg="8">
                <div class="card">
                  <div
                    class="card-image"
                    @click="handleShowVideo(formatUrl(item.videoUrl))">
                    <div class="mask">
                      <img src="~/assets/images/icon/icon@play_b.png" alt="通过上上签线上公开课，你可以与行业大咖当面交流，了解电子合同、电子签名领域最前沿的行业知识">
                    </div>
                    <img :src="formatUrl(item.pictureUrl)">
                  </div>
                  <div class="card-content">
                    <p>{{ item.knowledgeTitle }}</p>
                  </div>
                </div>
              </el-col>
            </template>
          </el-row>
        </div>
        <div class="button-wrap" v-if="total > 6">
          <el-pagination
            :page-size="6"
            :current-page.sync="current"
            :total="total"
            layout="prev, pager, next"></el-pagination>
        </div>
      </div>
    </section>
    <v-video
      :visible="dialogVisible"
      :videoUrl="videoUrl"
      @close="dialogVisible = false"></v-video>
  </article>
</template>

<script>
import { mapState } from 'vuex';
import Video from '~/components/Video.vue';
export default {
  name: 'CollegeCourse',
  layout: 'default',
  components: {
    'v-video': Video,
  },
  head() {
    return {
      title: '线上公开课_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同视频,电子合同公开课、电子签名公开课',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '通过上上签线上公开课，你可以与行业大咖当面交流，了解电子合同、电子签名领域最前沿的行业知识',
        },
      ],
    };
  },
  data() {
    return {
      dialogVisible: false,
      videoUrl: '',
      current: 1,
    };
  },
  async asyncData({ route, app }) {
    const res = await app.$axios.get(
      '/www/api/web/college2?status=0&column=course&pageNum=1&pageSize=99'
    );
    return {
      list: res,
    };
  },
  computed: {
    total() {
      return this.list.length;
    },
    shownList() {
      return this.list.slice((this.current - 1) * 6, 6 * this.current);
    },
    ...mapState(['isMobile']),
  },
  mounted() {
    this.$store.commit('changePath', { path: 'resource' });
  },
  methods: {
    handleShowVideo(videoUrl) {
      window._hmt &&
        window._hmt.push(['_trackEvent', 'college-video', 'click', videoUrl]);
      this.dialogVisible = true;
      this.videoUrl = videoUrl;
    },
    formatUrl(url) {
      const arr = url && url.split('?');
      return url && arr[0];
    },
  },
};
</script>

<style scoped lang="scss">
.section-1 {
  text-align: center;
  .article-headline {
    font-size: 2.5rem;
  }
  .article-subtitle {
    margin-bottom: 110px;
  }
  .nav-wrap {
    margin-bottom: 40px;
    .el-col {
      cursor: pointer;
      margin-bottom: 40px;
      font-size: 18px;
      &:hover {
        color: #00a664;
      }
    }
  }
  .image {
    width: 100%;
  }
  .card {
    height: 265px;
    .card-image {
      position: relative;
      height: 160px;
      .mask {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background-color: rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 28;
        img {
          width: 48px;
          height: 48px;
        }
      }
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .card-content {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .button-wrap {
    margin-top: 120px;
    text-align: center;
  }
}
.public-class__mobile {
  .article-headline {
    font-size: 24px;
    font-weight: normal;
    color: #323232;
    line-height: 1.6;
  }
  .article-subtitle {
    font-size: 12px;
  }
}
</style>
