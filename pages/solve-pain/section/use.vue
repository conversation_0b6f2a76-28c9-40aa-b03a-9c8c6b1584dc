<template>
  <div class="index-use section">
    <div class="container">
      <h2 class="section-headline headline--main" style="color:#fff;" v-if="!isMobile">他们都在用上上签</h2>
      <h2 class="section-headline headline--main" style="color:#000;" v-else >他们都在用上上签</h2>
      <div class="content" >
        <img src="https://static.bestsign.cn:443/5469bc16c6a19b77da0854126bf1556901d7a19a.png" v-if="!isMobile">
        <img src="https://static.bestsign.cn:443/cf17ebee05106a542e624a96dbc2f736b5e481fd.png" v-else>
        <div style="padding:50px 0 0;">
          <button class="ssq-button-primary" @click="toRegister">免费试用</button>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import Qs from 'qs';
export default {
  name: 'ssq-use',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeName: 'HR',
      activeIndex: 2,
      swiperOption: {
        initialSlide: 2,
        centeredSlides: true,
        speed: 700,
        slidesPerView: 5,
        navigation: {
          nextEl: '.ssq-button-next',
          prevEl: '.ssq-button-prev',
        },
      },
    };
  },
  computed: {
    feature() {
      const item = this.data[this.activeIndex];
      return item && item.features;
    },
    isMobile() {
      return this.$store.state.isMobile;
    },
    gutter() {
      return this.isMobile ? 10 : 20;
    },
  },
  methods: {
    toRegister() {
      this.$emit('toDemo', 'true');
    },
    handleChange(cur) {
      const activeIndex = cur.activeIndex;
      this.activeIndex = activeIndex;
    },
    slideTo(index) {
      // this.mySwiper.slideTo(index);
      const url = `/solution/${this.data[index].name}`;
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: url,
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
          hp_topnav: '',
        },
      });
    },
    toSolution(item) {
      if (item.name != 'more') {
        this.$router.push(`/solution/${item.name}`);
      } else {
        const query = sessionStorage.getItem('query');
        this.$router.push({
          path: '/demo',
          query: {
            id: Qs.parse(query).id,
            utm_source: Qs.parse(query).utm_source,
            hp_solution: '',
          },
        });
      }
    },
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const query = sessionStorage.getItem('query');
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          ...Qs.parse(query),
        },
      });
    },
    toDemoQJ() {
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          index_qijian: '',
        },
      });
    },
  },
  mounted() {
    const _this = this;
    // this.mySwiper.on('slideChange', function() {
    //   _this.handleChange(this);
    // });
  },
};
</script>

<style scoped lang="scss">
.index-use {
  background: url(https://static.bestsign.cn:443/a6f8050f31090a0f1239fc31b12640f0a67bbe73.jpg)
    no-repeat;
  background-size: 100% 100%;
  .section-headline {
    color: #090909;
  }
  text-align: center;
  .content {
    img {
      width: 80%;
    }
    .ssq-button-primary {
      width: 200px;
    }
  }
}
@media screen and (max-width: 767px) {
  .index-use {
    background: #fff;
    .content {
      margin: 0 20px;
      img {
        width: 100%;
      }
    }
  }
}
</style>
