<template>
  <article class="how-solve-pain">
    <section class="section section-1 is-tiny">
      <div class="container">
        <!-- <h2 class="article-headline headline--large">选择适合你的方式，</h2>
        <h2 class="article-headline headline--large">免费试用。</h2> -->
        <h2 class="section-headline headline--main">如何有效解决业务痛点？</h2>
      <!-- <div class="min-title">STANDARD</div> -->
        <!-- <p class="article-subtitle">可由企业内评估上线电子签约项目的决策者如总经理、法务负责人、技术负责人、采购负责人、风控负责人，或使用电子签约的业务部门如产品运营负责人、资源负责人等，提交企业使用上上签产品的项目情况及成效。</p> -->
        <div class="content" >
          <div class="plan-version">
			<div class="plan-icon plan-standard">
				<img src="../images/left.png" alt="">
			</div>
            <div class="plan-header">
              <h4>对不同合同主体<br>实现统一管控</h4>
              <!-- <p>SaaS</p> -->
            </div>
            <div class="plan-price">
              <div class="price">
                <div class="comment">
                    <li>合同签署人实名认证信息预收集并管控</li>
                    <li>不同合同主体的合同模板智能管控</li>
                    <li>合同内容权限统一管控</li>
                    <li>合同审批可视化管控</li>
                    <li>总部与子公司印章统一管控</li>

			          </div>
              </div>
            </div>
            
          </div>
          <div class="plan-version">
			<div class="plan-icon plan-profession">
					<img src="../images/center.png" alt="">
			</div>
            <div class="plan-header">
              <h4>对所有合同数据<br>实现统一管理</h4>
              <!-- <p>SaaS</p> -->
            </div>
           
            <div class="plan-price">
              <div class="price">
				 <div class="comment">
					<li>多业务线合同数据统一管控</li>
          <li>线上线下合同数据协同管理</li>
          <li>业务信息、签署信息、管理信息、权限信息统一管理</li>
				</div>
              </div>
            </div>
           
          </div>
          <div class="plan-version">
			<div class="plan-icon plan-power">
					<img src="../images/right.png" alt="">
			</div>
            <div class="plan-header">
              <h4>对所有合同关联关系<br>实现统一管理</h4>
              <!-- <p>SaaS+API</p> -->
            </div>
           
            <div class="plan-price">
              <div class="price">
				 <div class="comment">
           <li>业务流转方合同协同管理</li>
           <li>上下游关联方合同协同管理</li>
				</div>
              </div>
            </div>
           
          </div>
        </div>
      </div>
    </section>
  </article>
</template>

<script>
import Qs from 'qs';
import Usage from '@/components/Index/Usage';
export default {
  name: 'rule',
  components: {
    Usage,
  },
  data() {
    return {
      activeNames: ['1'],
    };
  },
  mounted() {
    this.$store.commit('changePath', { path: 'price' });
  },
  computed: {
    isMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    handleActiveTab(element) {
      this.$scrollTo(element, 500, {
        offset: this.isMobile ? -80 : -120,
      });
      this.activeTab = element;
    },
    headerStyle({ row, rowIndex }) {
      if (rowIndex == 0) {
        return 'background:#333;color:#fff;font-size:16px;border-right:none';
      } else {
        return '';
      }
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 0) {
        return 'background:#fafafa';
      }
    },
    toReg(param) {
      const query = sessionStorage.getItem('query');
      const url =
        window.location.host.indexOf('cn') > -1
          ? `https://ent.bestsign.cn/register?price_buy&${query}&${param}`
          : `https://ent.bestsign.info/register?price_buy&${query}&${param}`;
      window.open(url);
    },
    toDemo(param) {
      const query = sessionStorage.getItem('query');
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          ...Qs.parse(query),
          [param]: '',
          utm_source: Qs.parse(query).utm_source,
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.how-solve-pain {
  background: #f9f9f9;
  text-align: center;
  .section-1 {
    padding: 5rem 0;
    .section-headline {
      color: #090909;
      font-size: 2rem;
      line-height: 2.5rem;
      font-weight: 400;
      padding: 0 0 0.5rem;
    }
    .min-title {
      text-align: center;
      padding-bottom: 2.5rem;
    }
    .article-headline {
      text-align: left;
      padding-left: 10%;
      font-size: 2.2rem;
      font-weight: 500;
      color: #090909;
    }
    .article-subtitle {
      margin: 1.75rem 4.6rem;
      text-align: left;
      max-width: 100%;
      color: #86868b;
      // width: 570px;
      // margin-left: 10%;
    }
    .content {
      margin: 6rem auto 0;
      display: flex;
      justify-content: space-around;
      flex-wrap: wrap;
      padding: 0 4.6rem;
    }
    .plan-version {
      position: relative;
      display: inline-block;
      width: 100%;
      min-width: 205px;
      cursor: pointer;
      box-shadow: 0 8px 15px 0 rgba(82, 94, 102, 0.15);
      border-top: none;
      background: #fff;
      padding: 0 50px 80px;
      text-align: left;
      flex: 1;
      &:nth-child(2) {
        margin: 0 20px 0;
      }
      &:hover {
        box-shadow: 5px 8px 24px 10px rgba(82, 94, 102, 0.15);
      }
      .tag {
        padding: 3px 5px;
        color: #fff;
        background-color: #00a664;
        border-radius: 10px;
        &.less {
          padding-left: 0;
          padding-right: 0;
        }
      }
      .plan-icon {
        width: 80px;
        height: 80px;
        border-radius: 25px;
        margin-top: 52px;
        position: relative;
        img {
          width: 70%;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-41%, -36%);
        }
      }
      .plan-header {
        position: relative;
        display: flex;
        align-items: baseline;
        justify-content: flex-start;
        flex-direction: column;
        margin: 2.75rem 0;
        h4 {
          font-size: 1.5rem;
          margin: 0;
          font-weight: 400;
          color: #1d1d1f;
          line-height: 1.5;
        }
        p {
          padding: 5px 10px;
          border: 1px solid #7e7777;
          border-radius: 3px;
          color: #7e7777;
          margin-top: 20px;
        }
      }
      // .plan-standard {
      //   background-image: linear-gradient(135deg, #67e4d4, #24d3a8);
      // }
      // .plan-profession {
      //   background-image: linear-gradient(135deg, #b5b0e3, #9491c2);
      // }
      // .plan-power {
      //   background-image: linear-gradient(135deg, #fdd89d, #f8c16b);
      // }
      .plan-price {
        .top-comment {
          text-align: left;
          margin-top: 20px;
        }
        p {
          //   margin: 24px auto 32px;
          font-size: 14px;
          color: #86868b;
          line-height: 1.7;
        }
        .currency {
          position: relative;
          top: 8px;
          left: -4px;
          font-size: 20px;
          vertical-align: top;
        }
        .price-header {
          border-bottom: 1px solid #dcdcdc;
          text-align: left;
          padding-bottom: 40px;
          .money {
            font-size: 20px;
            color: #1d1d1f;
            font-weight: 500;
            &.line-through {
              text-decoration-line: line-through;
              text-decoration-color: #00aa64;
            }
          }
        }
        .ssq-button-primary {
          margin-top: 50px;
          border: 1px solid #00aa64;
          width: 12rem;
        }

        .comment {
          // margin-top: 30px;
          line-height: 25px;
          // border-top: 1px solid #dcdcdc;
          // padding-top: 2rem;
          font-size: 14px;
          li {
            &:before {
              content: '·';
            }
            font-size: 14px;
          }
          ul li:before {
            content: '';
            display: inline-block;
            width: 5px;
            height: 5px;
            background-color: #333;
            border-radius: 50%;
            margin-right: 10px;
            vertical-align: middle;
          }
        }
        .contract {
          font-size: 14px;
          margin: 15px auto;
          line-height: 22px;
          padding-left: 80px;
          text-align: left;
        }
      }
      .plan-list {
        text-align: left;
        margin: 0 1.5rem;
        padding: 20px 0;
        min-height: 375px;
        &.bordered {
          border-bottom: 1px solid #eee;
          min-height: 550px;
        }
        ul {
          margin: 0 auto;
          h4 {
            margin: 0 0 0 16px;
            padding: 32px 0;
            color: #a6a6a6;
          }
          li {
            position: relative;
            font-size: 16px;
            margin-bottom: 20px;
            line-height: 1.5;
            padding-left: 30px;
            &.none {
              padding-left: 0;
            }
            .icon-gou-img {
              position: absolute;
              display: inline-block;
              left: 0;
              color: #00a664;
            }
          }
        }
      }
    }
  }
  .help {
    font-size: 22px;
    color: rgb(96, 96, 96);
    line-height: 42px;
  }
}
.section-2 {
  padding: 0rem 1.5rem 6rem;
  .container {
    padding: 0 4.6rem;
  }
  /deep/ .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: #ffffff !important;
  }
}

@media screen and (max-width: 767px) {
  /deep/ .el-collapse-item__content {
    padding-bottom: 0;
  }
  .how-solve-pain {
    background: #f9f9f9;
    .container {
      max-width: 100%;
      width: 100%;
    }
    .section-1 {
      .content {
        padding: 0;
      }
      .article-headline {
        font-size: 2rem;
        text-align: center;
      }
      .article-subtitle {
        font-size: 14px;
        color: #86868b;
        padding: 0 1rem;
        margin: 2rem auto;
        text-align: center;
        width: 100%;
      }
      .plan-header {
        height: auto;
        margin-bottom: 0;
      }
      .plan-version {
        width: auto;
        flex: auto;
        margin: 0 0 20px;
        min-width: auto;
        .plan-price {
          .ssq-button-primary {
            width: 200px;
            font-size: 16px;
          }
        }
        &:nth-child(2) {
          margin: 0 0 20px;
        }
        &:hover {
          box-shadow: 0 8px 15px 0 rgba(82, 94, 102, 0.4);
        }
      }
    }
    .section-2 {
      .container {
        padding: 0;
        /deep/ .el-collapse-item {
          border: 1px solid #adb0b8;
          .el-collapse-item__header {
            padding: 0 1rem;
            border-bottom: 1px solid #adb0b8;
            margin-bottom: -1px;
          }

          .el-collapse-item__wrap {
            border-top: 1px solid #adb0b8;
          }
          .table-item:last-child {
            border-top: solid 1px #dfe1e6;
          }
          &:nth-child(2) {
            border-top: none;
            border-bottom: none;
          }
        }
        .table-item {
          //   border-top: solid 1px #dfe1e6;
          display: table;
          //   width: calc(100% - 30px);
          width: 100%;
          //   padding: 15px 0;
          //   margin: 0 14px;
          .table-item-title {
            display: table-cell;
            vertical-align: middle;
            border-right: 1px solid #dfe1e6;
            width: 95px;
            font-weight: 700;
            color: #252b3a;
          }
          .table-item-desc {
            display: table-cell;
            vertical-align: middle;
            color: #575d6c;
            li {
              border-bottom: 1px solid #dfe1e6;
              padding: 10px 0;
            }
            li:last-child {
              border-bottom: none;
            }
          }
        }
      }
    }
    .help {
      font-size: 14px;
      color: rgb(96, 96, 96);
      line-height: 42px;
    }
  }
}
@media screen and (min-width: 767px) and (max-width: 1024px) {
  .how-solve-pain .section-1 .plan-version .plan-price .top-comment {
    margin-top: 0;
    height: 48px;
  }
  .how-solve-pain .section-1 .content {
    margin: 6rem auto 0;
    display: flex;
    justify-content: space-around;
    flex-wrap: nowrap;
    padding: 0 4.6rem;
  }
}
</style>
