<template>
  <section class="index-reason section">
    <div class="container">
      <h2 class="section-headline headline--main" v-if="reasonTitle">上上签电子签约云平台优势<br>提供最佳电子签约解决方案</h2>
      <div class="content" v-if="!isLandMobile">
        <div class="block" v-for="(item, index) in data" :key="index">
          <div class="ad-img"><img :src="item.img"></div>
          <div class="law-dsc">
            <div style="font-size:1rem; color:#86868b;">{{item.name}}</div>
            <h4 v-html="item.title"></h4>
            <div style="font-size:1rem; line-height:1.5; color:#86868b;">{{item.content}}</div>
          </div>
        </div>
      </div>
      <div class="container" v-else>
      <div class="content_wrap">
        <div class="swiper-container" v-swiper:mySwiper="swiperOptionMobile">
          <div class="swiper-wrapper">
				<div class="swiper-slide" v-for="(item, index) in data" :key="item.name">
					<div class="item">
						   <div class="top">
								<div :class="['default-avatar', activeIndex === index? 'active-avatar':'']">
                  <div class="ad-img"><img :src="item.img"></div>
                  <div class="law-dsc">
                    <div class="use-name" style="font-size:14px;">{{item.name}}</div>
                    <h4 v-html="item.title"></h4>
                    <div style="font-size:14px; color:#86868b;">{{item.content}}</div>
                  </div>
								</div>
						   </div>
					</div>
				</div>
          </div>
        </div>
      </div>
      
	    <div class="swiper-pagination swiper-pagination__reason" slot="pagination"></div>
      </div>
    </div>
  </section>
</template>

<script>
import Qs from 'qs';
const data = [
  {
    img:
      'https://static.bestsign.cn:443/62fded6ddd2b12fa0efc7ea8ed8d8be5745991a9.jpg',
    name: '好丽友',
    title: ' 方案成熟<br>解决深层次业务问题',
    content: '以签约为契机，实现经销商基础数据动态管理。',
  },
  {
    img:
      'https://static.bestsign.cn:443/12695f1c49e7f62c2e16ace057020732f41dd44a.jpg',
    name: '外企德科',
    title: '500强集聚<br>迭代支撑未来业务',
    content: '需求敏捷迭代，打造HRO行业电子签约最佳实践。',
  },
  {
    img:
      'https://static.bestsign.cn:443/3e7e383b60d17c65b0282a03b3e903b357c49bb1.jpg',
    name: '菜鸟物流',
    title: '集成高效<br>拥有总成本更低',
    content: '快速无缝集成，业务侧低成本改造，不占用开发资源。',
  },
  {
    img:
      'https://static.bestsign.cn:443/981939f73f780b8111576908b79ff387224f013e.jpg',
    name: '海尔集团',
    title: '服务可视<br>携手后续运营推广',
    content: '多维联合培训推广，保障上万家经销商签署时效。',
  },
  {
    img:
      'https://static.bestsign.cn:443/54bbb5d28e01822b0768918a598fc5cd81a1f8e8.jpg',
    name: '赛诺菲',
    title: '安全合规<br>严守业务风控要求',
    content: '独立第三方专业机构全面评估安全及隐私保护能力。',
  },
  {
    img:
      'https://static.bestsign.cn:443/474866c8f86aae412a487d9c59d82536dc3a7628.jpg',
    name: '上海电气',
    title: '生态共赢<br>上下游伙伴同步享有',
    content: '供应商便捷地享有核心企业也在使用的电子签约应用。',
  },
  {
    img:
      'https://static.bestsign.cn:443/a327620aca5abecb117c21ced53cc813c2a346e9.jpg',
    name: '海信',
    title: '网络效应<br>加速带来极简体验',
    content: '经销商无需重新认证，提升相对方的合同签署体验。',
  },
  {
    img:
      'https://static.bestsign.cn:443/e3315aa3d665abd8ba19cf5ecc436e326d0bdc9b.jpg',
    name: '英国石油',
    title: '项目铁三角<br>高配置的专家团队',
    content: '以客户为中心的专业服务，打造数字化项目里程碑。',
  },
];
export default {
  name: 'law',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
    reasonUse: {
      type: Boolean,
      default: true,
    },
    reasonTitle: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      activeIndex: 0,
      data,
      swiperOptionMobile: {
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        pagination: {
          el: '.swiper-pagination.swiper-pagination__reason',
        },
      },
      partnerInfo: {
        content: '',
        name: '',
        desc: '',
      },
      activeClass: 'iconfont',
    };
  },
  computed: {
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          index_youshi: '',
        },
      });
    },
    handleChange(cur) {
      const activeIndex = cur.realIndex;
      this.activeIndex = activeIndex;
    },
    advantage(page) {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/advantage/' + (page + 1),
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
        },
      });
    },
  },
  mounted() {
    const _this = this;
    if (this.isLandMobile) {
      this.mySwiper.on('slideChange', function() {
        _this.handleChange(this);
      });
    }
  },
};
</script>

<style scoped lang="scss">
.index-reason {
  //   padding-top: 5rem;
  //   padding-bottom: 5rem;
  background-color: #f9f9f9;
  .section-headline {
    color: #090909;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
  }
  .content {
    display: flex;
    // margin-top: 3.5rem;
    flex-wrap: wrap;
    padding: 0 2rem;
    box-sizing: border-box;
    .el-button--text {
      color: #00aa64;
    }
    .icon {
      font-size: 3rem;
    }
    .block {
      // padding: 60px 50px;
      width: 24%;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      // border: 2px solid #e8e8e8;
      margin: 0.5%;
      border-radius: 2px;
      background: #fff;
      .ad-img {
        // background: #f4f4f4;
        color: #1d1d1f;
        // height: 66px;
        text-align: center;
        // font-size: 18px;
        font-size: 1.125rem;
        // line-height: 70px;
        // border-bottom: 2px solid #e8e8e8;
        padding-bottom: 1rem;
        img {
          width: 100%;
        }
      }
      .law-dsc {
        padding: 10px 10px 50px;
        text-align: left;
        h4 {
          margin: 20px 0;
          // font-size: 14px;
          font-size: 1.25rem;
          font-weight: 400;
          color: #1d1d1f;
          line-height: 1.5;
        }
      }

      // &.bor-r-b {
      //   margin-right: 1%;
      //   margin-bottom: 1%;
      //   border: 1px solid #515151;
      //   .ad-img {
      //     height: 30%;
      //     background: red;
      //   }
      // }
      // &.bor-b {
      //   // border-bottom: 1px solid #ddd;
      //   background: #fff;
      //   margin-bottom: 1%;
      // }
      // &.bor-r {
      //   // border-right: 1px solid #ddd;
      //   background: #fff;
      //   margin-right: 1%;
      // }
      &:hover {
        background-color: #fff;
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.4);
      }
      > .iconfont {
        color: #00aa64;
        font-size: 3rem;
        padding-bottom: 2rem;
        display: block;
      }
      img {
        width: 60px;
      }
      h3 {
        font-size: 20px;
        line-height: 1.5;
        margin: 0.75rem auto 1.75rem;
        color: #1d1d1f;
        font-weight: 400;
      }
      p {
        line-height: 1.5;
        color: #86868b;
        text-align: center;
      }
    }
  }
  .ssq-button-primary {
    margin: 3rem auto;
  }
}
@media screen and (max-width: 1280px) {
  .index-reason {
    .content {
      .block {
        // padding: 60px 50px;
        width: 24%;
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .index-reason {
    padding: 0 0 80px;
    background-color: #f9f9f9;
    .section-headline {
      color: #090909;
      font-size: 1.9rem;
      line-height: 2.5rem;
      font-weight: 400;
    }
    .content_wrap {
      text-align: center;
      // border: 2px solid #e8e8e8;
      .main_icon {
        margin: 0 0 3rem;
      }
      .ad-img {
        img {
          width: 100%;
        }
      }
      .law-dsc {
        text-align: left;
        margin: 20px;
        .use-name {
          margin-top: 5px;
          color: #86868b;
        }
        h4 {
          margin: 15px 0;
          font-size: 1.5rem;
          color: #1d1d1f;
          font-weight: normal;
          line-height: 1.5;
        }
      }
      .iconfont {
        font-size: 4rem;
        text-align: center;
        margin-top: 5rem;
        color: #00aa64;
      }
      .main-content {
        // background: #f4f4f4;
        // height: 66px;
        text-align: center;
        /* vertical-align: middle; */
        padding: 20px;
        // font-size: 18px;
        font-size: 1.36rem;
        color: #090909;
        font-size: 1.4rem;
        line-height: 2.5rem;
        font-weight: 400;
        // border-bottom: 2px solid #e8e8e8;
      }
      .name {
        padding: 10px;
        p {
          margin: 20px 0;
          font-size: 14px;
          color: #86868b;
          font-weight: normal;
          line-height: 25px;
          text-align: left;
          // font-size: 0.875rem;
        }
      }
      .ssq-button-more {
        margin-top: 2rem;
        .iconfont {
          font-size: 14px;
        }
      }
    }
    .swiper-pagination {
      width: 100%;
      left: 50%;
      transform: translateX(-50%);
      margin-top: 20px;
      /deep/ .swiper-pagination-bullet {
        margin: 0 10px;
      }
      /deep/ .swiper-pagination-bullet-active {
        background: #62686f;
      }
    }
    .ssq-button-primary {
      width: 130px;
      height: 44px;
      line-height: 44px;
    }
  }
}
</style>
