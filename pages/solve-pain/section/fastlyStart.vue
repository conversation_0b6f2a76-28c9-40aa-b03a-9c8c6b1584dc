<template>
  <section class="index-reason section">
    <div class="container">
      <h2 class="section-headline headline--main" v-if="reasonTitle">让业务更快地跑起来</h2>
      <!-- <div v-if="reasonTitle" class="min-title">BRIEF</div> -->
      <div class="content-container">
        <div>
          <div class="content">
        <div class="left"><img src="../images/top-icon.png"></div>
        <div class="right">
          <div class="top-desc">产品持续迭代升级，享有头部标杆企业带来的应用经验</div>
          <div class="bottom-desc">月均迭代产品特性10+，SaaS服务超过100家500强的经验赋能</div>
        </div>
      </div>
      <div class="content">
        <div class="left"><img src="../images/middle-icon.png"></div>
        <div class="right">
          <div class="top-desc">新增业务场景、新增公司架构的快速适配</div>
          <div class="bottom-desc">新增业务线、新增子公司都可以快速对接，节约重复开发</div>
        </div>
      </div>
      <div class="content">
        <div class="left"><img src="../images/bottom-icon.png"></div>
        <div class="right">
          <div class="top-desc">关联企业同步便捷享有</div>
          <div class="bottom-desc">关联企业同步总部电子签使用经验</div>
        </div>
      </div>
        </div>
        
      </div>
      
      <div style="padding:50px 0 0;">
          <button class="ssq-button-primary" @click="toRegister">免费试用</button>
        </div>
    </div>
  </section>
</template>

<script>
import Qs from 'qs';
export default {
  name: 'description',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
    reasonUse: {
      type: Boolean,
      default: true,
    },
    reasonTitle: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      activeIndex: 0,
      activeClass: 'iconfont',
    };
  },
  computed: {
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toRegister() {
      this.$emit('toDemo', 'true');
    },
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          index_youshi: '',
        },
      });
    },
    advantage(page) {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: '/advantage/' + (page + 1),
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
        },
      });
    },
    openAdvantage(item) {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: `/advantage/${item}`,
        query: {
          id: Qs.parse(query).id,
          utm_source: Qs.parse(query).utm_source,
          hp_productnav: '',
        },
      });
    },
  },
  mounted() {},
};
</script>

<style scoped lang="scss">
.index-reason {
  //   padding-top: 5rem;
  //   padding-bottom: 5rem;
  .container {
    text-align: center;
  }
  padding: 5rem 0 0 !important;
  // background-color: #f9f9f9;
  background-color: #fff;
  .section-headline {
    color: #090909;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
    padding: 0 0 0.5rem;
  }
  .min-title {
    text-align: center;
    padding-bottom: 2.5rem;
  }
  .content-container {
    display: flex;
    align-items: center;
    justify-content: center;
    .content {
      text-align: center;
      font-size: 1rem;
      line-height: 2rem;
      margin: 4rem auto 0;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      // width: 50%;
      .left {
        margin-right: 50px;
        img {
          width: 3rem;
        }
      }
      .right {
        text-align: left;
        .top-desc {
          font-size: 1.25rem;
          color: #1d1d1f;
        }
        .bottom-desc {
          font-size: 1rem;
          color: #86868b;
        }
      }
    }
  }

  .ssq-button-primary {
    margin: 3rem auto;
    width: 200px;
  }
}
@media screen and (max-width: 1280px) {
  .index-reason {
    .content {
    }
  }
}
@media screen and (max-width: 767px) {
  .index-reason {
    padding: 0 0 80px;
    background-color: #fff;
    .section-headline {
      color: #090909;
      font-size: 1.9rem;
      line-height: 2.5rem;
      font-weight: 400;
    }
    .content-wap {
      text-align: left;
      margin: 0 20px;
      font-size: 14px;
      line-height: 25px;
    }
    .content-container {
      .content {
        width: 90%;
        margin: 3rem auto 0;
        .left {
          margin: 0 20px 0 0;
        }
      }
    }

    .ssq-button-primary {
      width: 200px;
      height: 44px;
      line-height: 44px;
    }
  }
}
</style>
