<template>
  <section class="section section-1">
    <div class="container">
      <div class="content flex bc">
       <div class="leader">
		   <div class="main-content">
			<h1 class="main-title">中国电子签约云平台领跑者</h1>
			<div class="leader-desc">
				<p>为企业提供「合同起草」、「实名认证」、「在线审批」、「签约管理」、「合同管理」、「履约提醒」、「法律支持」、「证据链保存」等合同全生命周期的智能管理服务。企业可借助电脑、手机、平板等设备，随时随地完成电子合同的实时签署与线上智能管理，安全合规、不可篡改。</p>
			</div>
			<div class="littleTitle">
				<div class="item">
				<div class="top">1273+<span style="color:#090909; font-size:1rem;margin-left:10px">万家</span></div>
				<div class="bottom">服务企业</div>
				</div>
        <div class="space" v-if="!isMobile"></div>
				<div class="item">
				<div class="top">161<span style="color:#090909; font-size:1rem;margin-left:10px">亿次</span></div>
				<div class="bottom">合同累计签署量</div>
				</div>
        <div class="space" v-if="!isMobile"></div>
				<div class="item">
				<div class="top">6000+<span style="color:#090909; font-size:1rem;margin-left:10px">次</span></div>
				<div class="bottom">每秒处理签章</div>
				</div>
        <div class="space" v-if="!isMobile"></div>
        <div class="item">
				<div class="top">1900+<span style="color:#090909; font-size:1rem;margin-left:10px">天</span></div>
				<div class="bottom">安全运行记录</div>
				</div>
        <div class="space" v-if="!isMobile"></div>
        <div class="item">
				<div class="top">99.99<span style="color:#090909; font-size:1rem;margin-left:10px">%</span></div>
				<div class="bottom">系统可用性</div>
				</div>
			</div>
    	</div>
	   </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'leader',
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
};
</script>

<style scoped lang="scss">
@import './leaderUs';
.section-1 {
  .leader {
    .main-title {
      padding: 2rem 0 2rem 0;
      text-align: center;
      font-size: 2rem;
      line-height: 2.5rem;
      font-weight: 400;
      color: #090909;
    }

    .leader-desc {
      width: 60%;
      line-height: 1.7;
      text-align: center;
      font-size: 16px;
      margin: 0 auto;
    }
    .littleTitle {
      display: flex;
      flex-wrap: nowrap;
      margin: 2rem 5%;
      justify-content: space-around;
      flex-wrap: wrap;
      .item {
        margin: 1rem;
        text-align: left;
        .top {
          font-size: 2rem;
          color: #00aa64;
        }
        .bottom {
          font-size: 14px;
          line-height: 2rem;
          max-width: 300px;
          margin-top: 10px;
          color: #86868b;
        }
      }
      .space {
        background-color: #bfbfbf;
        height: 50px;
        width: 1px;
        margin: auto 0;
      }
    }
  }
}
@media only screen and (max-width: 767px) {
  .section-1 {
    padding-top: 0;
    .leader {
      .main-title {
        color: #090909;
        font-size: 2rem;
        line-height: 2.5rem;
        font-weight: 400;
        padding: 4.375rem 0 2.5rem;
        margin: 0;
      }

      .leader-desc {
        width: 100%;
        line-height: 1.7;
        text-align: center;
        font-size: 14px;
        margin: 0 auto;
        p {
          font-size: 14px;
          color: #86868b;
        }
      }
      .littleTitle {
        display: flex;
        flex-wrap: nowrap;
        margin: 2rem 0;
        justify-content: flex-start;
        flex-wrap: wrap;
        .item {
          margin: 1%;
          text-align: left;
          width: 31%;
          .top {
            font-size: 2rem;
            color: #00aa64;
            margin-top: 1rem;
          }
          .bottom {
            font-size: 14px;
            line-height: 2rem;
            max-width: 300px;
            margin-top: 0;
            color: #86868b;
          }
        }
      }
    }
  }
}
</style>
