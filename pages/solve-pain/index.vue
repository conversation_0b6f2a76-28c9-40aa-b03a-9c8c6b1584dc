<template>
  <div class="solvePain" :class="{mobile: isMobile}">
    <header class="container-pain-wap" v-if="isMobile">
      <nuxt-link to="/">
        <img src="@/assets/images/little-logo.png" class="logo" width="120" alt="上上签logo，电子合同">
      </nuxt-link>
      <div class="contact">
        <button v-if="isMobile" style="width: 100px;height: 30px;padding: 0;line-height: 30px" class="ssq-button-primary noform" @click="toDemo">免费试用</button>
      </div>
    </header>
    <header class="container-pain-pc" v-if="!isMobile">
      <nuxt-link to="/">
        <img src="@/assets/images/little-logo.png" class="logo" width="120" alt="上上签logo，电子合同">
      </nuxt-link>
      <div class="contact">
        <span style="font-size: 17px;color: #00aa64;width: 140px; margin-right: 10px">************</span>
        <button v-if="!isMobile" style="width: 120px;height: 40px;padding: 0 15px;line-height: 30px" class="ssq-button-primary noform" @click="toDemo">免费试用</button>
      </div>
    </header>
    <div v-if="!isMobile" class="banner-pc">
      <div class="banner">
      <div class="container banner-wrapper">
        <div class="banner-container-solve">
        <h1>上上签电子签约云平台</h1>
        <div class="title-desc">全行业、各场景皆可快速对接，让业务更快地跑起来，<br>SaaS服务超过100家500强企业，848万+标杆企业的选择。</div>
        <button class="ssq-button-primary" @click="toDemo">免费试用</button>
        <img src="./images/pic-banner.png">
        </div>
      </div>
    </div>
    </div>
    <div v-else class="banner-wap">
      <div v-if="isMobile" class="industry-wrap">
          <h3>上上签电子签约云平台</h3>
          <h4>全行业、各场景皆可快速对接，<br>让业务更快地跑起来，<br>SaaS服务超过100家500强企业，<br>848万+标杆企业的选择。</h4>
          <button class="ssq-button-primary" @click="toDemo">免费试用</button>
          <div><img src="./images/pic-banner.png"></div>
    </div>
    </div>
    <leader></leader>
    <how-pain></how-pain>
    <pain @toDemo="toDemo"></pain>
    <fastly-start @toDemo="toDemo"></fastly-start>
    <solution-advantage></solution-advantage>
    <ssq-use @toDemo="toDemo"></ssq-use>
    <v-footer :h5="true"></v-footer>
    <float-help></float-help>
    <el-dialog
  :visible.sync="centerDialogVisible"
  width="500px"
  center
  v-if="!isMobile">
  <register class="register"></register>
</el-dialog>
<el-dialog
  :visible.sync="centerDialogVisible"
  width=100%
  center
  v-if="isMobile">
  <register class="register"></register>
</el-dialog>
  </div>
</template>

<script>
import Footer from '@/components/Footer.vue';
import FloatHelp from '@/components/FloatHelp.vue';
import Register from '../demo/RegisterNoTitleFoot.vue';
import Leader from './section/leader.vue';
import HowPain from './section/howpain.vue';
import Pain from './section/pain.vue';
import SolutionAdvantage from './section/solutionAdvantage.vue';
import SsqUse from './section/use.vue';
import Throttle from '@/assets/utils/throttle';
import FastlyStart from './section/fastlyStart.vue';
export default {
  name: 'solvePain',
  layout: 'blank',
  components: {
    FloatHelp,
    'v-footer': Footer,
    Register,
    Leader,
    HowPain,
    Pain,
    SolutionAdvantage,
    SsqUse,
    FastlyStart,
  },
  head() {
    return {
      title: '【电子签约】_行业解决方案_如何解决业务痛点-上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '在线签约,电子合同,电子签名,电子签章,上上签电子签约云平台',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签电子签约云平台全行业各场景皆可快速对接，让业务更快地跑起来，SaaS服务超过100家500强企业，848万+标杆企业的选择。',
        },
      ],
    };
  },
  data: () => ({
    fixedTab: false,
    centerDialogVisible: false,
  }),
  computed: {
    form() {
      return this.$route.query.form || '1';
    },
    isMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toDemo() {
      this.centerDialogVisible = true;
    },
    openScroll() {
      const scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      if (scrollTop < 100) {
        this.fixedTab = false;
      } else {
        this.fixedTab = true;
      }
    },
  },
  mounted() {
    window.addEventListener('scroll', Throttle(this.openScroll, 50));
  },
};
</script>

<style lang="scss">
$base-color: #00aa64;
@function pxToVw($fz) {
  @return ($fz / 1920) * 100vw;
}
.container-pain-pc {
  position: fixed;
  padding: 0 1%;
  top: 0;
  left: 0;
  height: 76px;
  background-color: #f9f9f9;
  z-index: 999;
  width: 100%;
  .logo {
    width: 100px !important;
  }
}
.container-fluid {
  width: 100%;
}
.banner-pc {
  background-color: #f9f9f9;
  position: relative;
  padding: 0;
  width: 100%;
  background-size: cover;
  min-height: 48vw;
  background-position-x: 40%;
}
.logo-user {
  img {
    width: 90%;
    margin: 10px 5%;
  }
}

.container {
  width: 100%;
}
.color-text {
  color: $base-color;
}
.solvePain {
  .ssq-button-primary {
    background: #00aa64;
  }
  .solution-con {
    text-align: center;
  }
}
.solvePain.mobile {
  .container-pain-wap {
    position: fixed;
    padding: 0 10px;
    top: 0;
    left: 0;
    background-color: #f9f9f9;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    width: 100%;
    .logo {
      width: 100px !important;
    }
  }
  .banner {
    height: auto;
    padding-top: 60px;
  }
}
.container-pain-pc {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: $base-color;
  height: 60px;
  .contact {
    color: $base-color;
    display: flex;
    align-items: center;
    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: $base-color;
      width: 0;
      height: 0;
      border: 0.5rem solid #00aa64;
      border-radius: 100%;
      margin-right: 12px;
      img {
        width: 20px;
      }
    }
  }
}
.mobile {
  .banner {
    background-size: 100% 50%;
    background-repeat: no-repeat;
    background-position: bottom;
  }
}
.banner {
  width: 100%;
  background-size: cover;
  font-size: pxToVw(24);
  .register {
    width: 28rem;
    margin: 9rem 0 7rem;
    position: relative;
    /deep/.text-little {
      font-size: 1rem;
    }
  }
  .banner-wrapper-mobile {
    text-align: center;
    h1 {
      font-size: 20px;
      line-height: 30px;
      margin: 0;
      padding-top: 34px;
      padding-bottom: 16px;
    }
    p {
      font-size: 13px;
      line-height: 18px;
    }
    .land-form {
      padding: 20px 20px 50px;
    }
    .noform.ssq-button-primary {
      margin: 40px auto;
      font-size: 16px;
      width: 150px;
      height: 37px;
      padding: 0;
    }
  }
  .banner-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 60px;
    .banner-container-solve {
      text-align: center;
      h1 {
        color: #090909;
        font-size: 2rem;
        line-height: 2.5rem;
        font-weight: 400;
        padding: 2rem 0;
      }
      .title-desc {
        color: #4e4e4e;
        padding-bottom: 2rem;
        font-size: 1.2rem;
        line-height: 30px;
      }
      .ssq-button-primary {
        margin: 0 auto;
      }
      img {
        width: 50%;
      }
    }
    .banner-left {
      img {
        width: 160px;
      }
      h1 {
        font-size: 2.875rem;
        font-weight: normal;
        line-height: 54px;
        color: #fff;
      }
      h4 {
        font-size: 1.2rem;
        line-height: 36px;
        font-weight: 400;
        color: #fff;
      }
    }
  }
  p {
    line-height: 42px;
    font-size: 16px;
  }

  .feature {
    margin-top: pxToVw(70);
    &-item {
      display: block;
      line-height: 32px;
      font-size: 16px;
      i {
        color: $base-color;
        margin-right: 8px;
      }
    }
  }
  .no-form {
    margin-top: pxToVw(70);
    img {
      margin-top: 40px;
    }
  }
  .ssq-button-primary {
    display: block;
    background-color: #00aa64;
    width: 200px;
  }
}
.solvePain.mobile {
  .headline {
    font-size: 20px;
  }
  .paragraph {
    .section-button {
      margin-top: 30px;
      width: 150px;
      height: 37px;
      padding: 0 12px;
      font-size: 16px;
    }
    .logo-img {
      width: 100px;
    }
    .feature {
      display: block;
      &-text {
        padding-left: 10px;
        .title {
          font-size: 16px;
        }
        .desc {
          padding-top: 10px;
        }
      }
      &-img {
        width: 100%;
      }
    }
    .service {
      display: block;
      &-item {
        margin: 0 auto 20px;
        height: auto;
      }
      .top {
        font-size: 16px;
      }
      .body {
        .title {
          font-size: 14px;
        }
        .desc {
          font-size: 12px;
        }
      }
    }
  }
  .section-4 .hover-content {
    display: block;
    width: 100%;
    margin-top: 40px;
    .text {
      .text-title {
        font-size: 16px;
      }
      .text-para {
        margin-bottom: 20px;
      }
    }
    .img {
      width: 100%;
      height: auto;
      margin-left: 0;
    }
  }
  .footer {
    padding: 20px 0 70px;
    .container {
      display: flex;
      align-items: center;
      .right {
        margin-left: 20px;
      }
    }
  }
}

@media only screen and (max-width: 767px) {
  .container {
    margin: 0px 0px;
  }
  .solvePain {
    font-size: 12px;
  }
  .container {
    padding: 0 10px;
  }
  .banner-wap {
    background-color: #f9f9f9;
    .industry-wrap {
      text-align: center;
      padding-top: 5rem;
      top: 3rem;
      img {
        width: 80%;
        margin-top: 20px;
      }
      h3 {
        padding-top: 30px;
        // color: #fff;
        font-size: 28px;
        font-weight: 500;
      }
      h4 {
        // color: #fff;
        font-size: 16px;
        line-height: 32px;
        margin-top: 20px;
        font-weight: 500;
      }
      .ssq-button-primary {
        margin: 20px auto 0;
        width: 200px;
      }
    }
  }
  .register {
    margin: 0 1.5rem;
    padding: 0 !important;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .container {
    padding: 0 10px;
  }
  .banner-pc {
    background-color: #f9f9f9;
    position: relative;
    padding: 0;
    width: 100%;
    background-size: cover;
    min-height: 48vw;
    background-position-x: 40%;
    .banner-wrapper {
      position: relative;
      left: -12%;
    }
  }
  .banner {
  }
  .about {
    margin: 20px 6%;
    .aboutTitle {
      text-align: center;
      font-size: 2rem;
      line-height: 2rem;
      border-top: #fff solid 4rem;
    }
    .hr {
      width: 20px;
      height: 3px;
      background-color: #00aa64;
      margin: 1.5rem auto;
    }
    .aboutContent {
      display: flex;
    }
    .content-left {
      line-height: 1rem;
      color: #424242;
      margin-right: 2rem;
    }
    .video-wrapper {
      width: 200%;
      height: 100%;
      video {
        width: 100%;
        border: 1px solid #eee;
        margin: 3rem 0;
      }
    }
  }
}
@media only screen and (min-width: 992px) {
  .solvePain {
    font-size: 14px;
  }
  .banner-pc {
    background-color: #f9f9f9;
    position: relative;
    padding: 0;
    width: 100%;
    background-size: cover;
    min-height: 48vw;
    background-position-x: 40%;
  }
}

.solvePain {
  .section-headline {
    text-align: center;
    color: #515151;
  }
  .ssq-button-primary {
    text-align: center;
  }
}
</style>
