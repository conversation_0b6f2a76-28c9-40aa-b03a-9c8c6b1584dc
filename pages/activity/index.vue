<template>
  <article class="about-news">
	<section class="banner">
		<div class="container">
			<!-- <h1 class="article-headline">上上签电子签约，助力更多企业业务效率升级</h1> -->
			<h1 class="article-headline">品牌活动</h1>
			<!-- <p  class="article-text">在这里，你可以看到专业的上上签市场活动</p> -->
		</div>
	</section>
     <section class="section section-1 is-tiny">
      <div class="container">
        <el-row>
          <template v-for="(item, index) in shownList">
            <el-col
              :key="index"
              :md="8"
              :lg="8"
              :sm="8"
            >
             <div class="card">
                  <!-- <nuxt-link :to="{path: `/activity/${item.id}`, query: {}}"> -->
                  <!-- 在上一层页码刷新当下页面后，这里用nuxt-link会把那个页码带过来，所以用a便签实现 -->
                  <!-- <a :href="`/activity/${item.id}`"> -->
                  <a :href="goUrl(item)">
                    <img
                     :key="item.imgUrl"
                      v-lazy="item.imgUrl"
                      class="card-image">
                    <div class="card-content">
                      <p class="body">{{ item.activityTitle }}</p>
                      <p class="footer">
                        <!-- <span style="float: left">{{ categories[item.speaker] }}</span> -->
                        <span class="time">{{ releaseTime(item.releaseTime) }}</span>
                      </p>
                    </div>
                  </a>
                  <!-- </nuxt-link> -->
                </div>
            </el-col>
          </template>
        </el-row>
        <div class="button-wrap">
          <el-pagination
		  background
            :page-size="12"
            :current-page="pageNum"
            :total="totalNum"
            @current-change="handlePageChange"
            :pager-count="5"
            layout="prev, pager, next">
			</el-pagination>
        </div>
        <!-- seo优化，由于分页是客户端渲染，源码里无法拿到路由 -->
        <!-- <div style="display:none" v-for="item in pageNums" :key="item">
          <a v-if="item === 1" href="/case">{{item}}</a>
          <a v-else :href="`/case-p${item}`">{{item}}</a>
        </div> -->
      </div>
    </section>
  </article>
</template>

<script>
import { mapActions } from 'vuex';
import moment from 'moment';
export default {
  name: 'News',
  layout: 'default',
  head() {
    return {
      title: '行业新闻动态_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同行业新闻,电子合同行业资讯,电子合同行业事件',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签新闻资讯提供最新企业动态，电子合同行业内最新资讯以及电子签约领域热门事件和专业解读。',
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.bestsign.cn/activity',
        },
      ],
    };
  },
  async asyncData({ app, params, route }) {
    const page = route.query.page || 1;
    const res0 = await app.$axios.get('/www/api/web/category/activity');
    // const res = await app.$axios.get(
    //   '/www/api/web/college2?status=0&column=activity&pageNum=1&pageSize=999'
    // );
    //支持服务端渲染（分页是客户端和服务端同时渲染实现的），实现当前页面刷新实现
    const res1 = await app.$axios.get(
      `/www/api/web/getActivity?pageNum=${page}&pageSize=12`
    );
    const res2 = await app.$axios.get(
      `/www/api/web/getActivity?pageNum=1&pageSize=1000`
    );
    let xmlList = [
      {
        loc: 'https://www.bestsign.cn/activity',
        priority: '1',
        lastmod: moment().format('YYYY-MM-DD'),
        changefreq: 'daily',
      },
    ];
    res2.data.forEach(it => {
      let xmlIt = {};
      xmlIt.loc = 'https://www.bestsign.cn/activity/' + it.id;
      xmlIt.priority = '1';
      xmlIt.lastmod = moment().format('YYYY-MM-DD');
      xmlIt.changefreq = 'daily';
      xmlList.push(xmlIt);
    });

    app.$axios
      .post('/www-api/updateMapText', {
        list: xmlList,
        column: 'activity',
      })
      .then(function(response) {
        console.log(response);
      })
      .catch(function(error) {
        // console.log(error);
      });
    console.log('wwwwwww');
    return {
      type: res0[0].codeValue,
      shownList: res1.data,
      current: +page,
      pageNums: res1.totalPageNum,
      totalNum: res1.totalNum,
      pageNum: res1.pageNum,
    };
  },
  data() {
    return {
      // current: Number(this.$route.query.page) || 1,
      current: 1,
      category: Number(this.$route.query.category) || 0,
      categories: {
        1: '市场活动',
        2: '品牌活动',
      },
      // total: 0,
      list: [],
    };
  },
  computed: {
    text() {
      const Text = {
        0: '品牌活动和市场活动',
        1: '市场活动',
        2: '品牌活动',
      };
      return Text[this.category];
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: 'resource' });
    // this.init();
  },
  methods: {
    goUrl(item) {
      if (item.linkUrl) {
        return item.linkUrl;
      } else {
        return `/activity/${item.id}`;
      }
    },
    formatUrl(url) {
      const arr = url && url.split('?');
      return url && arr[0];
    },
    ...mapActions(['setViewTimes']),
    init() {
      const { page, category } = this.$route.query;
      this.category = category;
      this.current = page;
      this.getData(page, category);
    },
    // handleClick(id) {
    //   this.setViewTimes({
    //     id,
    //     type: '2',
    //   });
    // },
    handleFilter(index) {
      this.category = index.toString();
      this.current = '1';
      this.getData('1', index.toString());
    },
    // getData(page = '1', category = '0') {
    //   if (category === '0') {
    //     this.count = this.list.length;
    //     this.shownList = this.list.slice(
    //       (parseInt(page) - 1) * 12,
    //       12 * parseInt(page)
    //     );
    //   } else {
    //     this.count = this.list.filter(o => o.speaker === category).length;
    //     this.shownList = this.list
    //       .filter(o => o.speaker === category)
    //       .slice((parseInt(page) - 1) * 12, 12 * parseInt(page));
    //   }
    //   this.$router.push({
    //     query: {
    //       page,
    //       category,
    //     },
    //   });
    // },
    getData(page) {
      // this.shownList = this.list.slice(
      //   (parseInt(page) - 1) * 12,
      //   12 * parseInt(page)
      // );
      //支持客户端渲染（分页是客户端和服务端同时渲染实现的），实现点击页码跳转正确
      this.$axios
        .get(`/www/api/web/getActivity?pageNum=${page}&pageSize=12`)
        .then(res => {
          this.shownList = res.data;
        });
      if (+page === 1) {
        //此处不用路由跳转的原因是无论在那一页刷新页面后，点击第一页就调转到前面刷新的页码的路由，但是list渲染的数据是正确的
        // this.$router.push(this.$route.fullPath.replace(/page=\d+/, ''));
        location.href = this.$route.fullPath.replace(/\?page=\d+/, '');
      } else {
        this.$router.push({
          query: {
            page,
          },
        });
      }
    },
    handlePageChange(page) {
      this.current = page;
      // const { category } = this.$route.query;
      // this.getData(page, category);
      this.getData(page);
    },
    releaseTime(time) {
      return moment(time).format('YYYY-MM-DD');
    },
  },
};
</script>

<style scoped lang="scss">
.article-headline {
  margin: 90px 0 50px;
  text-align: center;
  color: #090909;
  font-weight: 500;
  font-size: 2.2rem;
}
.container {
  max-width: 70.75rem;
}
.article-text {
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.7;
  font-size: 18px;
}
/deep/ .el-pagination.is-background .el-pager li:not(.disabled).active {
  background: #00aa64;
}
.section-1 {
  text-align: center;
  //   background: #eee;
  a {
    color: #323232;
  }
  .card {
    box-sizing: content-box;
    width: 21.42rem;
    height: 22rem;
    margin: 15px auto;
    border: 1px solid #ebeef5;
    background-color: #fff;
    color: #323232;
    transition: 0.3s;
    border-radius: 2px;
    overflow: hidden;
    box-shadow: 0 2px 5px 0 #cccbcb;
    display: flex;
    flex-direction: column;

    .card-image {
      width: 100%;
      height: 11.45rem;
    }
  }

  .card-content {
    text-align: center;
    height: auto;
    padding-bottom: 60px;
    .header {
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      font-size: 1.4rem;
      line-height: 2.25rem;
      margin: 4rem 0 1rem;
    }
    .body {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      text-align: justify;
      word-break: break-all;
      line-height: 1.25rem;
      padding: 0 30px;
    }
    .footer {
      align-self: flex-end;
      a {
        color: #00a664;
      }
    }
  }
}
.about-news {
  background: #f9f9f9;
  .card-content {
    height: 135px;
    padding: 24px 20px;
    .body {
      text-align: justify;
    }
    .text-content {
      margin: 24px auto 0;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      text-align: center;
      word-break: break-all;
    }
  }
}
.button-wrap {
  margin-top: 60px;
  text-align: center;
}
.filter {
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #00a664;
  margin-bottom: 32px;
  .item {
    line-height: 32px;
    font-size: 1rem;
    padding: 0 1.75rem;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    &.is-active {
      color: #00a664;
      border-bottom-color: #00a664;
    }
  }
}
@media screen and (max-width: 767px) {
  .section {
    padding: 30 10px 40px;
  }
  .section-1 {
    .card {
      width: 100%;
      height: auto;
      .card-image {
        height: auto;
      }
    }
  }
  .article-headline {
    font-size: 2.2rem;
    margin-bottom: 30px;
    font-weight: 500;
    color: #090909;
    margin: 50px 0 0;
  }
  .article-text {
    width: 80%;
  }
  .card {
    .card-content {
      .body {
        line-height: 2.2rem;
        font-size: 14px;
        color: #86868b;
        padding: 0;
      }
    }
  }
  .article-subtitle {
    margin: 28px auto 0px;
  }
  .section-1 {
    .container {
      padding: 0 2%;
    }
  }
}
</style>
