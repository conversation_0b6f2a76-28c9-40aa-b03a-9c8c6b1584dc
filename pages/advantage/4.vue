<template>
<div>
  <article class="advantage4">
    <index-banner 
      bigTitle="灵活部署，<br>更快交付。"
      littleTitle="支持标准化接口方式对接，提供完整dome示例，0开发对接，最快可做到2天交付。"
     :pcImgUrl="bannerPc"
      :mobileImgUrl="bannerWap">
      </index-banner>
    <lowPay></lowPay>
    <bgc></bgc>
    <kind></kind>
  </article>
</div>
</template>

<script>
import IndexBanner from './banner/banner.vue';
import LowPay from '../advantage/components/lowPay.vue';
import Bgc from '../advantage/components/bgc.vue';
import Kind from '../advantage/components/kind.vue';
export default {
  name: 'homepage',
  components: {
    IndexBanner,
    LowPay,
    Bgc,
    Kind,
  },
  data() {
    return {
      bannerPc: require('@/assets/images/advantage/pc_ad4.jpg'),
      bannerWap: require('@/assets/images/advantage/wap_ad4.jpg'),
    };
  },
  head() {
    return {
      title: '电子合同_公有云部署_本地化部署_交付时间短_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同,电子签约,电子签名,电子签章,上上签电子签约云平台',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签电子签约实现合同秒发秒签,助力企业实现降本增效.适配各种终端,线上线下无缝切换。',
        },
      ],
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
};
</script>
<style scoped lang="scss">
.advantage4 {
  text-align: center;
}

@media screen and (max-width: 767px) {
  .homepage {
    // padding-top: 56px;
    .headline--main {
      font-size: 1.75rem;
    }
    .section-2 {
      padding-top: 10px;
      .image-link {
        flex: 1;
        width: 33.3%;
        padding: 0 5px;
      }
    }
  }
}
</style>
