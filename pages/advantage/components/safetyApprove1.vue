<!-- 组件说明 -->
<template>
  <div class=''>
    <section class="function-list function-common" :class="{'function-common-en':isEN}">
            <h4 class="sub-title">{{$t('manageSeriesProduction.list1.title')}}</h4>
            <el-row>
                <el-col :span="isMobile ? 24 : 12" v-for="(item, index) in list" :key="index">
                  <div class="function-list-item-left">
                    <div style="width:80%;padding:10%">
                      <img :src="item.icon" alt="" width="100%">
                    </div>
                  </div>
                  <div class="function-list-item-right">
                    <p class="title">{{item.title}}</p>
                    <p class="tip">{{item.tip}}</p>
                  </div>
                </el-col>
            </el-row>
        </section>
  </div>
</template>

<script>
//import x from ''
import { mapState } from 'vuex';
const ICON_MAP = {
  '0':
    'https://static.bestsign.cn:443/821b4d9003d3e90b75f22260787ecbebbef0ee54.png',
  '1':
    'https://static.bestsign.cn:443/1c7d83f901a5e9f4796f3f08035c44a4e00abd5c.png',
  '2':
    'https://static.bestsign.cn:443/a83104cbe8278671485f1629ec877fb41d21ec72.png',
  '3':
    'https://static.bestsign.cn:443/a750c574699aba666c1f181378dbc1c6702f1063.png',
  '4':
    'https://static.bestsign.cn:443/c36c9712bd88eda5da605529b39bf2ebff185938.png',
  '5':
    'https://static.bestsign.cn:443/ba22273c478f47935fb8587db05b792e67f2721c.png',
  '6':
    'https://static.bestsign.cn:443/ab7ccb194bea440921c010d191ea750c61b534ec.png',
};
export default {
  components: {},
  data() {
    const obj = this.$t('manageSeriesProduction.list1.list');
    const list = [];
    const data = Object.keys(obj).map(key => obj[key]);
    data.forEach((e, index) => {
      if (index % 2 === 0) {
        list.push({
          icon: ICON_MAP[index / 2],
          title: e,
          tip: data[index + 1],
        });
      }
    });
    console.log(list);
    return {
      list,
      isEN: false,
    };
  },
  computed: {
    ...mapState(['isMobile']),
    language() {
      return this.$store.state.locale;
    },
  },
  methods: {},
  mounted() {
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>

<style lang='scss' scoped>
.function-common {
  padding: 1.5rem 15% 0rem 15%;
  p.order {
    color: $-color-main;
    font-size: 1.4rem;
    font-weight: 500;
  }
  h5 {
    color: #111;
    font-size: 1.4rem;
    margin-top: 1rem;
    line-height: 1.5;
  }
  .el-row {
    .el-col:first-child {
      padding-right: 10%;
    }
  }
}
.function-list {
  .el-col {
    display: flex;
    height: 160px;
    // padding-right: 10%;
    &:nth-child(2n + 1) {
      padding-right: 5%;
    }
    &:nth-child(2n) {
      padding-left: 5%;
    }
  }
  &-item-left {
    height: 80px;
    width: 80px;
    background: #ffffff;
    border-radius: 10px;
    margin-right: 5%;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    i.iconfont {
      margin: 0;
      font-size: 3rem;
    }
  }
  &-item-right {
    .title {
      color: #111;
    }
    .tip {
      color: #666;
    }
    p:first-child {
      font-weight: 500;
      margin-bottom: 10px;
      font-size: 1.2rem;
    }
  }
}
.function-common-en {
  .sub-title {
    font-weight: 600;
    font-size: 2.5rem;
    line-height: 1.5;
  }
  .el-row {
    .el-col {
      height: 210px;
      &:first-child {
        padding-right: 5%;
      }
      .function-list-item-right {
        .title {
          font-weight: 600;
        }
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .function-common {
    padding: 1.5rem 5% 5rem 5%;
    h5 {
      padding-left: 0;
      position: relative;
      span {
        color: $-color-main;
        font-size: 1.6rem;
        width: 3rem;
        position: absolute;
        top: 0;
        left: 0;
      }
    }
    p {
      padding-left: 0;
    }
    .el-row .el-col {
      padding: 0 !important;
    }
    &.function-contract {
      .el-row {
        border-bottom: none;
        padding-top: 0;
      }
      img {
        margin-top: 4rem;
      }
    }
    &.function-base {
      img {
        margin-top: 4rem;
      }
    }
  }
  .function-common.function-list .el-col {
    height: auto;
    padding: 2rem !important;
    background: $-color-white;
    margin-bottom: 1rem;
    border-radius: 10px;
    .iconfont {
      font-size: 2.5rem;
    }
    .function-list-item-left {
      background: #ffffff;
      border-radius: 8px;
      margin-right: 5%;
      flex-shrink: 0;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      margin-right: 2rem;
    }
  }
}
//@import url()
</style>
