<template>
  <section class="index-reason section">
    <div class="container">
      <h2 class="section-headline headline--main" v-if="reasonTitle">合同秒发秒签，<br>助力企业实现降本增效。</h2>
      <div class="content" v-if="!isLandMobile">
        <div class="block bor-r-b">
          <h3>签约周期缩短</h3>
          <h1>95%<sup>+</sup></h1>
          <p>10步变2步，效率提升看得见</p>
          <img src="../img/left.png" alt="">
        </div>
        <div class="block bor-b">
          <h3>合同费用下降</h3>
          <h1>60%<sup>+</sup></h1>
          <p>消除5大基本费用，签得多省得多</p>
          <img src="../img/right.png" alt="">
        </div>
      </div>
      <div class="content_wap" v-else>
      <div class="explain">
        <div class="t1">95%+</div>
        <div class="t2">签约周期缩短95%以上</div>
        <div class="t3">10步变2步，效率提升看得见</div>
      </div>
      <div class="paper-sign">
        <img src="../img/left.png" width="100%" alt="">
      </div>
      <div class="explain">
        <div class="t1">60%+</div>
        <div class="t2">合同费用下降60%以上</div>
        <div class="t3">消除5大基本费用，签得多省得多</div>
      </div>
      <div class="paper-sign">
        <img src="../img/right.png" width="100%" alt="">
      </div>
    </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'lowPay',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
    reasonUse: {
      type: Boolean,
      default: true,
    },
    reasonTitle: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      activeIndex: 0,
      swiperOptionMobile: {
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        pagination: {
          el: '.swiper-pagination.swiper-pagination__reason',
        },
      },
      partnerInfo: {
        content: '',
        name: '',
        desc: '',
      },
      activeClass: 'iconfont',
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          index_youshi: '',
        },
      });
    },
    handleChange(cur) {
      const activeIndex = cur.realIndex;
      this.activeIndex = activeIndex;
    },
  },
};
</script>

<style scoped lang="scss">
.index-reason {
  padding-bottom: 5rem;
  background-color: #fff;
  .section-headline {
    color: #090909;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
  }
  .content {
    display: flex;
    flex-wrap: wrap;
    .el-button--text {
      color: #00aa64;
    }
    .icon {
      font-size: 3rem;
    }
    .block {
      padding: 30px;
      width: 49%;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fafafa;
      &.bor-r-b {
        // border-right: 1px solid #ddd;
        // border-bottom: 1px solid #ddd;
        background: #fafafa;
        margin-right: 1%;
        margin-bottom: 1%;
      }
      &.bor-b {
        // border-bottom: 1px solid #ddd;
        background: #fafafa;
        margin-bottom: 1%;
      }
      &:hover {
        background-color: #fafafa;
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.4);
      }
      img {
        width: 80%;
      }
      h3 {
        font-size: 1.6rem;
        font-weight: 400;
        color: #1d1d1f;
        margin: 0.75rem auto 1.75rem;
        color: #1d1d1f;
      }
      h1 {
        font-size: 3rem;
        color: #1c1c1e;
      }
      p {
        line-height: 1.5;
        color: #86868b;
        text-align: center;
      }
    }
  }
  .ssq-button-primary {
    margin: 3rem auto;
  }
}
@media screen and (max-width: 767px) {
  .index-reason {
    padding: 50px 0;
    background-color: #fff;
    .section-headline {
      color: #090909;
      font-size: 1.9rem;
      line-height: 2.5rem;
      font-weight: 400;
    }
    .content_wap {
      margin: 2rem;
      .howSignTitle {
        font-size: 1.9rem;
        line-height: 2.5rem;
        font-weight: 400;
        border-top: #fff solid 2rem;
        color: #090909;
        // margin: 0 3rem;
      }
      .explain {
        text-align: left;
        .t1 {
          font-size: 3rem;
          line-height: 4rem;
          margin-top: 2rem;
          font-weight: 500;
          color: #00aa64;
        }
        .t2 {
          font-size: 1.4rem;
          line-height: 2rem;
          font-weight: 500;
        }
        .t3 {
          font-size: 1.2rem;
          line-height: 2rem;
        }
      }
      .paper-sign {
        margin-top: 2rem;
      }
    }
    .swiper-pagination {
      width: 100%;
      left: 50%;
      transform: translateX(-50%);
      margin-top: 20px;
      /deep/ .swiper-pagination-bullet {
        margin: 0 10px;
      }
      /deep/ .swiper-pagination-bullet-active {
        background: #62686f;
      }
    }
    .ssq-button-primary {
      width: 130px;
      height: 44px;
      line-height: 44px;
    }
  }
}
</style>
