<template>
  <section class="index-reason section">
    <div class="container">
      <h2 class="section-headline headline--main" v-if="reasonTitle">智能法务系统</h2>
      <div class="little-title">
        为了提高企业法务的工作效率，上上签电子签约2020年推出智能法务系统。该系统提供在线实时公证、多渠道存证、在线仲裁、区块链全程溯源等一站式服务，适用于金融、B2B供应链、人力资源、物流等各种场景，能够极大的降低法律纠纷和维权成本。
      </div>
      <div class="content">
        <div class="block bor-r-b">
          <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-quanzhouqishishigongzheng"></use>
            </svg> -->
			<i class="iconfont icon-falv"></i>
          <h3>全周期实时公证</h3>
          <p>对用户注册、实名、申请证书、合同起草、合同签署等一系列行为进行全周期实时公证并产生证据报告。</p>
        </div>
        <div class="block bor-r-b">
          <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-duotongdaocunzheng"></use>
            </svg> -->
			<i class="iconfont icon-duotongdaocunzheng"></i>
          <h3>多通道存证</h3>
          <p>对接公证处、司法鉴定中心、互联网法院、互联网仲裁委等多通道，进行存证。</p>
        </div>
        <div class="block bor-b">
          <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-zaixiancaijue"></use>
            </svg> -->
			<i class="iconfont icon-zaixiancaijue"></i>
          <h3>在线裁决</h3>
          <p>对接互联网法院、互联网仲裁委，提供一键诉讼的高效快捷服务。</p>
        </div>
        <div class="block bor-r">
          <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-qukuailianzhuisu"></use>
            </svg> -->
			<i class="iconfont icon-qukuailianzhuisu"></i>
          <h3>区块链追溯</h3>
          <p>用户注册、实名、合同发送、合同签署等结构化业务数据都将在哈希处理后分散化储存在区块链中，由多方节点实时记录、跟踪和审计，全程可溯源。</p>
        </div>
        <div class="block bor-r">
          <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-hetongAIfenxi"></use>
            </svg> -->
			<i class="iconfont icon-hetongAIfenxi"></i>
          <h3>合同AI分析</h3>
          <p>智能分析合同文本内容，快速定位、提取合同关键词，并结合机器视觉，在合同模板编辑、合同发起、合同签署等多个环节，对合同表单、签章位置等迅速锚定。</p>
        </div>
        <div class="block">
          <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-hetongAIzhinengjiansuo"></use>
            </svg> -->
			<i class="iconfont icon-hetongAIzhinengjiansuo"></i>
          <h3>合同AI智能检索</h3>
          <p>单份合同智能化高效检索，在海量的数据查询请求中可实现秒级响应。</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
const data = [
  {
    icon: 'icon-xingzhuang51',

    title: '合同全生命周期智能管理2.0，全新定义电子签名',
    content:
      '涵盖智能档案、智能模板、智能起草、智能收发、智能签署、智能审批、智能管理、合规管理、权限管理等合同管理的所有环节，让企业管理如臂使指。',
  },
  {
    icon: 'icon-zhinengfawuxitong',
    title: '智能法务系统，全面提升法务工作效率',
    content:
      '提供全周期实时公证、多通道存证、在线裁决、区块链追溯、合同AI分析、合同AI智能检索等服务，多次得到司法机构事实认证，提升企业维权效率。',
  },
  {
    icon: 'icon-hetongzhongshenbaoanquan',
    title: '合同终身保安全，更高的安全保障',
    content:
      '7大全球顶级安全资质认证、6大全球领先安全科技，世界500强、大型银行广泛使用，系统可用性不低于99.99%。超过1700天的安全运行记录。',
  },
  {
    icon: 'icon-linghuobushu',
    title: '灵活部署，更快交付',
    content:
      '全面兼容各大企业内部管理系统，PaaS中台技术支撑， API标准化产品最快可做到7天交付。',
  },
  {
    icon: 'icon-xingzhuang10',
    title: '终身服务团队，实时解决客户问题',
    content:
      '咨询服务、培训服务、技术对接指导服务、巡检服务、项目管理服务，多维度实时解决客户安装使用问题。',
  },
  {
    icon: 'icon-xingzhuang12',
    title: '一线投资机构领投，规模行业领先',
    content:
      '2018年8月31日，上上签率先完成电子签约行业首个C轮融资3.58亿，融资规模行业领先。',
  },
];
export default {
  name: 'laws',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
    reasonUse: {
      type: Boolean,
      default: true,
    },
    reasonTitle: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      activeIndex: 0,
      data,
      swiperOptionMobile: {
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        pagination: {
          el: '.swiper-pagination.swiper-pagination__reason',
        },
      },
      partnerInfo: {
        content: '',
        name: '',
        desc: '',
      },
      activeClass: 'iconfont',
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          index_youshi: '',
        },
      });
    },
    handleChange(cur) {
      const activeIndex = cur.realIndex;
      this.activeIndex = activeIndex;
    },
  },
  mounted() {
    // const _this = this;
    // if (this.isLandMobile) {
    //   this.mySwiper.on('slideChange', function() {
    //     _this.handleChange(this);
    //   });
    // }
  },
};
</script>

<style scoped lang="scss">
.index-reason {
  padding-bottom: 5rem;
  background-color: #fff;
  .section-headline {
    color: #090909;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
  }
  .little-title {
    margin: 0px 10% 20px;
    line-height: 2rem;
  }
  .content {
    display: flex;
    flex-wrap: wrap;
    padding: 0 5rem;
    .el-button--text {
      color: #00aa64;
    }
    .icon {
      font-size: 3rem;
    }

    .block {
      padding: 3.75rem 3.125rem;
      width: 32.66%;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #f2f2f2;
      &.bor-r-b {
        background: #f2f2f2;
        margin-right: 1%;
        margin-bottom: 1%;
      }
      &.bor-b {
        background: #f2f2f2;
        margin-bottom: 1%;
      }
      &.bor-r {
        // border-right: 1px solid #ddd;
        background: #f2f2f2;
        margin-right: 1%;
      }
      &:hover .icon {
        color: #00aa64;
      }
      img {
        width: 60px;
      }
      h3 {
        font-size: 20px;
        line-height: 1.5;
        margin: 0.75rem auto 1.75rem;
        color: #1d1d1f;
        font-weight: 400;
      }
      p {
        line-height: 1.5;
        color: #86868b;
        text-align: center;
      }
      .iconfont {
        font-size: 3rem;
        color: #00aa64;
        padding-bottom: 2rem;
        display: block;
      }
    }
  }
  .ssq-button-primary {
    margin: 3rem auto;
  }
}
@media screen and (max-width: 767px) {
  .index-reason {
    padding: 0 0 50px 0;
    background-color: #fff;
    .section-headline {
      color: #090909;
      font-size: 1.9rem;
      line-height: 2.5rem;
      font-weight: 400;
    }
    .little-title {
      margin: 0px 5px 20px;
    }
    .content {
      display: block;
      margin-top: 0;
      padding: 0rem;
      .el-button--text {
        color: #00aa64;
      }
      .icon {
        font-size: 3rem;
      }
      .block {
        padding: 30px;
        width: 100%;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #f2f2f2;
        margin-bottom: 10px;
        border-radius: 2px;
        &.bor-r-b {
          // border-right: 1px solid #ddd;
          // border-bottom: 1px solid #ddd;
          //   background: #fff;
          margin-right: 0;
          margin-bottom: 0;
          background: #f2f2f2;
          border-radius: 2px;
          margin-bottom: 10px;
        }
        &.bor-b {
          // border-bottom: 1px solid #ddd;
          //   background: #fff;
          margin-bottom: 10px;
          background: #f2f2f2;
          border-radius: 2px;
        }
        &.bor-r {
          // border-right: 1px solid #ddd;
          background: #f2f2f2;
          margin-right: 0;
          border-radius: 2px;
        }
        img {
          width: 60px;
        }
        h3 {
          font-size: 20px;
          font-weight: normal;
          line-height: 1.5;
          margin: 0.75rem auto 1.75rem;
          color: #272828;
        }
        p {
          line-height: 24px;
          text-align: justify;
          font-size: 14px;
          color: #86868b;
        }
      }
    }
    .content_wrap {
      text-align: center;
      .main_icon {
        margin: 3rem;
      }
      .iconfont {
        font-size: 4rem;
        text-align: center;
        margin-top: 5rem;
      }
      .main-content {
        color: #090909;
        font-size: 1.4rem;
        line-height: 2.5rem;
        font-weight: 400;
      }
      .name {
        margin-top: 2rem;
        line-height: 1.5;
        color: #444447;
        font-size: 1.1rem;
      }
      .ssq-button-more {
        margin-top: 2rem;
      }
    }
    .swiper-pagination {
      width: 100%;
      left: 50%;
      transform: translateX(-50%);
      margin-top: 20px;
      /deep/ .swiper-pagination-bullet {
        margin: 0 10px;
      }
      /deep/ .swiper-pagination-bullet-active {
        background: #62686f;
      }
    }
    .ssq-button-primary {
      width: 130px;
      height: 44px;
      line-height: 44px;
    }
  }
}
</style>
