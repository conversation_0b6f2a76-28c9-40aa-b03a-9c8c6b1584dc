<template>
  <section class="index-reason section">
    <div class="container">
      <div class="content">
        <div class="block bor-r-b">
            <img src="../img/ad5_3.png" alt="">
          <h3>项目管理服务包 </h3>
          <p>制定项目计划、把控项目进度、资源协调及项目交付</p>
        </div>
        <div class="block bor-r-b">
          <img src="../img/ad5_4.png" alt="">
          <h3>咨询服务包</h3>
          <p>需求调研、业务梳理及技术方案规划</p>
        </div>
        <div class="block bor-b">
            <img src="../img/ad5_2.png" alt="">
          <h3>培训服务包</h3>
          <p>提供在线或现场培训，课件开发、课程体系搭建</p>
        </div>
        <div class="block bor-r">
         <img src="../img/ad5_1.png" alt="">
          <h3>技术对接指导服务包 </h3>
          <p>现场提供技术对接指导、故障排除、常见技术问题汇总及优化</p>
        </div>
        <div class="block bor-r">
          <img src="../img/ad5_5.png" alt="">
          <h3>巡检服务包 </h3>
          <p>系统使用情况例行检查</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
const data = [
  {
    icon: 'icon-xingzhuang51',

    title: '合同全生命周期智能管理2.0，全新定义电子签名',
    content:
      '涵盖智能档案、智能模板、智能起草、智能收发、智能签署、智能审批、智能管理、合规管理、权限管理等合同管理的所有环节，让企业管理如臂使指。',
  },
  {
    icon: 'icon-zhinengfawuxitong',
    title: '智能法务系统，全面提升法务工作效率',
    content:
      '提供全周期实时公证、多通道存证、在线裁决、区块链追溯、合同AI分析、合同AI智能检索等服务，多次得到司法机构事实认证，提升企业维权效率。',
  },
  {
    icon: 'icon-hetongzhongshenbaoanquan',
    title: '合同终身保安全，更高的安全保障',
    content:
      '7大全球顶级安全资质认证、6大全球领先安全科技，世界500强、大型银行广泛使用，系统可用性不低于99.99%。超过1700天的安全运行记录。',
  },
  {
    icon: 'icon-linghuobushu',
    title: '灵活部署，更快交付',
    content:
      '全面兼容各大企业内部管理系统，PaaS中台技术支撑， API标准化产品最快可做到7天交付。',
  },
  {
    icon: 'icon-xingzhuang10',
    title: '终身服务团队，实时解决客户问题',
    content:
      '咨询服务、培训服务、技术对接指导服务、巡检服务、项目管理服务，多维度实时解决客户安装使用问题。',
  },
  // {
  //   icon: 'icon-xingzhuang12',
  //   title: '一线投资机构领投，规模行业领先',
  //   content:
  //     '2018年8月31日，上上签率先完成电子签约行业首个C轮融资3.58亿，融资规模行业领先。',
  // },
];
export default {
  name: 'box',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
    reasonUse: {
      type: Boolean,
      default: true,
    },
    reasonTitle: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      activeIndex: 0,
      data,
      swiperOptionMobile: {
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        pagination: {
          el: '.swiper-pagination.swiper-pagination__reason',
        },
      },
      partnerInfo: {
        content: '',
        name: '',
        desc: '',
      },
      activeClass: 'iconfont',
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          index_youshi: '',
        },
      });
    },
    handleChange(cur) {
      const activeIndex = cur.realIndex;
      this.activeIndex = activeIndex;
    },
  },
  // mounted() {
  //   const _this = this;
  //   if (this.isLandMobile) {
  //     this.mySwiper.on('slideChange', function() {
  //       _this.handleChange(this);
  //     });
  //   }
  // },
};
</script>

<style scoped lang="scss">
.index-reason {
  padding-top: 5rem;
  padding-bottom: 5rem;
  // background-color: #f2f2f2;
  .section-headline {
    color: #090909;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
  }
  .content {
    display: flex;
    margin-top: 3.5rem;
    flex-wrap: wrap;
    .el-button--text {
      color: #00aa64;
    }
    .icon {
      font-size: 3rem;
    }
    .block {
      background-color: #fafafa;
      padding: 60px;
      width: 32%;
      height: 20rem;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      // background: #fff;
      &.bor-r-b {
        // border-right: 1px solid #ddd;
        // border-bottom: 1px solid #ddd;
        background: #fafafa;
        margin-right: 2%;
        margin-bottom: 2%;
      }
      &.bor-b {
        // border-bottom: 1px solid #ddd;
        background: #fafafa;
        margin-bottom: 2%;
      }
      &.bor-r {
        // border-right: 1px solid #ddd;
        background: #fafafa;
        margin-right: 2%;
      }
      &:hover {
        background-color: #fafafa;
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.4);
      }
      img {
        width: 60px;
        padding-bottom: 2rem;
      }
      h3 {
        font-size: 20px;
        line-height: 1.5;
        margin: 0.75rem auto 1.75rem;
        text-align: center;
        color: #1d1d1f;
        font-weight: 400;
      }
      p {
        line-height: 1.5;
        color: #86868b;
        text-align: center;
      }
    }
  }
  .ssq-button-primary {
    margin: 3rem auto;
  }
}
@media screen and (max-width: 767px) {
  .index-reason {
    padding: 50px 0;
    background-color: #fff;
    .section-headline {
      color: #090909;
      font-size: 1.9rem;
      line-height: 2.5rem;
      font-weight: 400;
    }
    .content {
      display: block;
      margin-top: 3.5rem;
      .el-button--text {
        color: #00aa64;
      }
      .icon {
        font-size: 3rem;
      }
      .block {
        padding: 30px 30px 0;
        width: 100%;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #fff;
        height: 200px;
        &.bor-r-b {
          // border-right: 1px solid #ddd;
          // border-bottom: 1px solid #ddd;
          background: #fff;
          margin-right: 0;
          margin-bottom: 0;
        }
        &.bor-b {
          // border-bottom: 1px solid #ddd;
          background: #fff;
          margin-bottom: 0;
        }
        &.bor-r {
          // border-right: 1px solid #ddd;
          background: #fff;
          margin-right: 0;
        }
        img {
          width: 40px;
        }
        h3 {
          font-size: 20px;
          font-weight: normal;
          line-height: 1.5;
          margin: 0.75rem auto 1.75rem;
          color: #272828;
        }
        p {
          line-height: 1.5;
          color: #444447;
          text-align: center;
        }
      }
    }
    .content_wrap {
      text-align: center;
      .main_icon {
        margin: 3rem;
      }
      .iconfont {
        font-size: 4rem;
        text-align: center;
        margin-top: 5rem;
      }
      .main-content {
        color: #090909;
        font-size: 1.4rem;
        line-height: 2.5rem;
        font-weight: 400;
      }
      .name {
        margin-top: 2rem;
        line-height: 1.5;
        color: #444447;
        font-size: 1.1rem;
      }
      .ssq-button-more {
        margin-top: 2rem;
      }
    }
    .swiper-pagination {
      width: 100%;
      left: 50%;
      transform: translateX(-50%);
      margin-top: 20px;
      /deep/ .swiper-pagination-bullet {
        margin: 0 10px;
      }
      /deep/ .swiper-pagination-bullet-active {
        background: #62686f;
      }
    }
    .ssq-button-primary {
      width: 130px;
      height: 44px;
      line-height: 44px;
    }
  }
}
</style>
