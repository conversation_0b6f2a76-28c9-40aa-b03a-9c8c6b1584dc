<template>
  <section class="section section-1">
    <div class="container">
      <h1 class="section-headline" style="margin:0">安全运营纪录</h1>
      <img src="../img/operation.jpg" alt="">
      <div class="content">
        <div class="left">
          <h1>6000<sup>+</sup></h1>
          <h3>高并发</h3>
          <p>每秒可处理超过6000次签署</p>
        </div>
        <div class="left">
          <h1>99.99<sup>%</sup></h1>
          <h3>高可用</h3>
          <p>系统可用性不低于99.99%</p>
        </div>
        <div class="left">
          <h1>1900<sup>+</sup></h1>
          <h3>高可靠</h3>
          <p>超过1900天的安全运行记录</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'operation',
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
};
</script>

<style scoped lang="scss">
.section-1 {
  .container {
    img {
      width: 100%;
    }
    .content {
      display: flex;
      margin: 3rem 10%;
      justify-content: space-between;
      h1 {
        color: #050505;
      }
      sup {
        font-size: 2rem;
        top: 0;
      }
      h3 {
        color: #282828;
        margin: 35px 0 15px 0;
      }
      p {
        line-height: 1.5;
        color: #86868b;
        text-align: center;
      }
    }
  }
}
@media only screen and (max-width: 767px) {
  .section-1 {
    .container {
      img {
        width: 100%;
      }
      .content {
        display: flex;
        margin: 3rem 0;
        justify-content: space-between;
        h1 {
          color: #050505;
        }
        sup {
          font-size: 2rem;
          top: 0;
        }
        h3 {
          color: #282828;
        }
        p {
          line-height: 1.5rem;
        }
      }
    }
  }
}
</style>
