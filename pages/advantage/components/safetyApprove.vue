<template>
  <div class="index-evaluate section">
    <div class="container">
      <h2 class="section-headline headline--main">{{$t('aboutSafety.title')}}</h2>
      <div class="safe_logo" v-if="!isMobile">
        <div class="block" :class="{active: activeIndex === 0}" @click="activeIndex=0">
          <img src="https://static.bestsign.cn/d3cf3b1b9d2d91194306e50201c1872591b120ba.png" alt="" style="height:3rem;">
          <p>ISO/IEC 27018</p>
        </div>
        <div class="block" :class="{active: activeIndex === 1}" @click="activeIndex=1">
         <img src="https://static.bestsign.cn/2d38dc4a5b0bc2df75f037bb04ce7dc1447a8376.png" alt="" style="height:3rem;">
          <p>ISO/IEC 27001</p>
        </div>
        <div class="block" :class="{active: activeIndex === 2}" @click="activeIndex=2">
          <img src="https://static.bestsign.cn/8d017300c2971aaf0f22c8078dbf19d0200e18a.png" alt="" style="height:3rem;">
          <p>ISO 38505-1:2017</p>
        </div>
        <div class="block" :class="{active: activeIndex === 3}" @click="activeIndex=3">
          <img src="https://static.bestsign.cn/2a081bbeba00135ce1c695f04cd6f89df24eaa9a.png" alt="" style="height:3rem;">
          <p>ISO 22301:2019</p>
        </div>
      </div>
	  <div class="safe_logo_line" v-if="!isMobile"></div>
      <div class="safe_logo_wap" v-if="isMobile">
        <div class="block" :class="{active: activeIndex === 0}" @click="activeIndex=0">
          <!-- <img src="https://static.bestsign.cn/d3cf3b1b9d2d91194306e50201c1872591b120ba.png" alt="" style="height:3rem;"> -->
          <img src="@/assets/images/about/27018.png" alt="" style="height:3rem;">
          <p>ISO/IEC 27018</p>
        </div>
        <div class="block" :class="{active: activeIndex === 1}" @click="activeIndex=1">
         <!-- <img src="https://static.bestsign.cn/2d38dc4a5b0bc2df75f037bb04ce7dc1447a8376.png" alt="" style="height:3rem;"> -->
		 <img src="@/assets/images/about/27001.png" alt="" style="height:3rem;">
          <p>ISO/IEC 27001</p>
        </div>
        <div class="block" :class="{active: activeIndex === 2}" @click="activeIndex=2">
          <!-- <img src="https://static.bestsign.cn/8d017300c2971aaf0f22c8078dbf19d0200e18a.png" alt="" style="height:3rem;"> -->
		  	 <img src="@/assets/images/about/2017.png" alt="" style="height:3rem;">
          <p>ISO 38505-1:2017</p>
        </div>
      </div>
      <div class="safe_logo_wap_1" v-if="isMobile">
        <div class="block" :class="{active: activeIndex === 3}" @click="activeIndex=3">
          <img src="https://static.bestsign.cn/2a081bbeba00135ce1c695f04cd6f89df24eaa9a.png" alt="" style="height:3rem;">
          <p>ISO 22301:2019</p>
        </div>
      </div>
      <div class="content">
        <div class="swiper-container" v-swiper:mySwiper="isMobile?swiperOptionMobile:swiperOption">
          <div class="swiper-wrapper">
				<div class="swiper-slide" v-for="(item, index) in list" :key="index">
					<div class="item">
						   <div class="top">
								<!-- <div :class="['default-avatar', activeIndex === index? 'active-avatar':'']" :style="{backgroundImage: `url(${item.logo})`}"> -->
								<div :class="['default-avatar', activeIndex === index? 'active-avatar':'']">
									<img :src="item.logo" alt="" width="100%" height="100%" style="width:235px">
                  <!-- <div v-if="activeIndex === index" class="desc">{{item.desc}}</div> -->
								</div>
						   </div>
					</div>
					<div v-if="isMobile">
            <div class="name">
							<p class="label">{{ partnerInfo.desc }}</p>
						</div>
						<!-- <div class="main-content">{{ partnerInfo.content }}</div> -->
					</div>
				</div>
          </div>
        </div>
        <div class="arrow" v-if="!isMobile">
          <div class="ssq-button-prev-b">
            <i class="el-icon-arrow-left"></i>
          </div>
          <div class="ssq-button-next-b">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
      <div class="name" v-if="!isMobile">
			<p class="label">{{ partnerInfo.desc }}</p>
		</div>
	 	<!-- <div class="main-content" v-if="!isMobile">{{ partnerInfo.content }}</div> -->
	<div v-if="isMobile" class="swiper-pagination swiper-pagination_safety" slot="pagination"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'safetyApprove',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      activeIndex: 0,
      list: [
        {
          logo:
            'https://static.bestsign.cn:443/543c6726cb6de691f0613562d724756c1bb2a23c.png',
          desc: this.$t('aboutSafety.certificate1'),
        },
        {
          logo:
            'https://static.bestsign.cn:443/8231976d1cf538815c02d11159db6065ea2062fd.png',
          desc: this.$t('aboutSafety.certificate2'),
        },
        {
          logo:
            'https://static.bestsign.cn:443/d5ce10b54bb9ee81e5ade72c9b39e563623f9669.png',
          desc: this.$t('aboutSafety.certificate3'),
        },
        {
          logo:
            'https://static.bestsign.cn/fc2f1f6d1d29f6b1654fac62ffdb363b714212dd.jpg',
          desc: this.$t('aboutSafety.certificate4'),
        },
      ],
      swiperOption: {
        loop: true,
        initialSlide: 0,
        centeredSlides: true,
        speed: 700,
        slidesPerView: 3,
        loopedSlides: 5,
        navigation: {
          nextEl: '.ssq-button-next-b',
          prevEl: '.ssq-button-prev-b',
        },
      },
      swiperOptionMobile: {
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        pagination: {
          el: '.swiper-pagination.swiper-pagination_safety',
        },
      },
      partnerInfo: {
        content: '',
        name: '',
        desc: '',
      },
      isInSliding: false,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toCase() {
      this.$router.push('/evaluate');
    },
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const query = sessionStorage.getItem('query');
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          ...Qs.parse(query),
        },
      });
    },
    async handleChange(cur) {
      this.isInSliding = true;
      const activeIndex = cur.realIndex;
      this.activeIndex = activeIndex;
      // debugger;
      await this.$nextTick();
      this.isInSliding = false;
      this.getInfo();
      // console.log(111);
    },
    getInfo() {
      this.$nextTick(() => {
        this.partnerInfo = this.list.filter(
          (item, index) => index == this.activeIndex
        )[0];
        console.log(this.partnerInfo);
      });
    },
    slideTo(index) {
      this.mySwiper.slideToLoop(index);
    },
  },
  mounted() {
    const _this = this;
    this.mySwiper.on('slideChange', function() {
      _this.handleChange(this);
    });
    this.getInfo();
  },
  watch: {
    activeIndex: {
      // immediate: true,
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          !this.isInSliding && this.slideTo(newVal);
        }
      },
    },
  },
};
</script>

<style scoped lang="scss">
.index-evaluate {
  //   padding-top: 2rem;
  padding-bottom: 2rem;
  background-color: #fff;
  text-align: center;
  .little-title {
    line-height: 1.7;
    width: 70%;
    margin: 0 auto;
  }
  .safe_logo {
    margin-top: 50px;
    padding: 50px auto;
    // padding-bottom: 50px;
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-around;
    // border-bottom: 1px solid #dddddd;
    .block {
      // margin: 50px auto 0;
      padding-bottom: 30px;
      cursor: pointer;
      // border-bottom: 1px solid #dddddd;
      // &:active {
      //   border-bottom: 1px solid #dddddd;
      // }
      img {
        margin-bottom: 30px;
      }
    }
    .active {
      border-bottom: 1px solid #747167;
    }
  }
  .safe_logo_line {
    width: 100%;
    height: 1px;
    background: #dddddd;
    margin-top: -1px;
  }
  .content {
    width: 70%;
    position: relative;
    margin: 0 auto;
    // .swiper-container1 {
    //   margin: 0 auto;
    //   position: relative;
    //   overflow: hidden;
    //   list-style: none;
    //   padding: 0;
    //   z-index: 1;
    // }
    .swiper-container {
      margin-top: 2rem;
    }
    .item {
      width: 100%;
      background-color: #fff;
      padding: 20px 0;
      .top {
        display: flex;
        align-items: center;
        // img {
        // width: 500px;
        // margin-right: 30px;
        // border-radius: 50%;
        // border: 1px solid #eee;
        // }
        .default-avatar {
          margin: 70px auto;
          img {
            // height: 90px;
            // border-radius: 50%;
            width: 75%;
          }
          .desc {
            margin-top: 100px;
          }
        }

        .name {
          text-align: left;
          font-size: 20px;
          line-height: 1.3;
        }
      }
    }
    .swiper-slide-active .default-avatar img {
      -ms-transform: scale(1.4); /* IE 9 */
      -webkit-transform: scale(1.4); /* Safari */
      transform: scale(1.4); /* 标准语法 */
      transition: transfrom 0.4s;
    }
    .swiper-slide-prev {
      .item {
        padding: 20px 0;
        .default-avatar {
          margin-left: 0;
        }
      }
    }
    .swiper-slide-next {
      .item {
        padding: 20px 0px;
        transition: all 0.5s;
        .default-avatar {
          margin-right: 0;
          transition: all 0.5s;
        }
      }
    }
  }
  .main-content {
    max-width: 40rem;
    text-align: center;
    text-align: justify;
    line-height: 1.75;
    margin: 0 auto;
    // margin-top: 1.5rem;
    // color: #717171;
    // min-height: 8.75rem;
  }
  .name {
    margin-top: 2.5rem;
    .title {
      font-size: 20px;
      font-weight: 600;
    }
    .label {
      font-size: 14px;
      margin: 1rem 0 2rem 0;
    }
  }
  .ssq-button-more {
    color: #00aa64;
  }
  .ssq-button-prev-b,
  .ssq-button-next-b {
    position: absolute;
    top: 50%;
    transform: translate3d(0, -50%, 0);
    background: transparent;
    cursor: pointer;
    > i {
      font-size: 20px;
      color: rgba(35, 24, 21, 0.5);
      outline: none;
      background: #eee;
      border-radius: 50%;
      padding: 5px;
      &:hover {
        color: rgba(35, 24, 21, 1);
      }
      &:focus {
        outline: none;
      }
    }
    &:focus {
      outline: none;
    }
  }
  .ssq-button-prev-b {
    left: -60px;
  }
  .ssq-button-next-b {
    right: -60px;
  }
  .ssq-button-primary {
    display: inline-block;
  }
}
.swiper-pagination {
  width: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 20px;
  /deep/ .swiper-pagination-bullet {
    margin: 0 10px;
  }
  /deep/ .swiper-pagination-bullet-active {
    background: #62686f;
  }
}
@media screen and (max-width: 767px) {
  .index-evaluate {
    background-color: #f2f2f2;
    padding-bottom: 50px;
    padding-top: 0;
    .little-title {
      line-height: 3rem;
      font-size: 1.5rem;
    }
    .safe_logo_wap {
      margin-top: 10px;
      padding: 50px auto;
      // padding-bottom: 50px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-around;
      border-bottom: 1px solid #dddddd;
      .block {
        // margin: 50px auto 0;
        padding-bottom: 20px;
        cursor: pointer;
        // border-bottom: 1px solid #dddddd;
        // &:active {
        //   border-bottom: 1px solid #dddddd;
        // }
        p {
          margin-top: 20px;
        }
      }
      .active {
        border-bottom: 1px solid #747167;
      }
    }
    .safe_logo_wap_1 {
      margin-top: 10px;
      padding: 50px auto;
      // padding-bottom: 50px;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      border-bottom: 1px solid #dddddd;
      margin-left: 10px;
      .block {
        // margin: 50px auto 0;
        padding-bottom: 20px;
        cursor: pointer;
        // border-bottom: 1px solid #dddddd;
        // &:active {
        //   border-bottom: 1px solid #dddddd;
        // }
        p {
          margin-top: 20px;
        }
      }
      .active {
        border-bottom: 1px solid #747167;
      }
    }
    .content {
      position: relative;
      width: 100%;
      padding: 0 10px;
      margin: 0 auto;
      .swiper-slide-active .default-avatar img {
        -ms-transform: scale(1); /* IE 9 */
        -webkit-transform: scale(1); /* Safari */
        transform: scale(1); /* 标准语法 */
        transition: transfrom 0.4s;
      }

      .item {
        width: 100%;
        padding: 20px 15px;
        background-color: #f2f2f2;
        .top {
          img {
            width: 60px;
            margin-right: 0;
            // border-radius: 50%;
            // border: 1px solid #eee;
          }
          .default-avatar {
            margin: 0 auto;
            .desc {
              margin-top: 0;
            }
          }
          .name {
            text-align: left;
            font-size: 16px;
            line-height: 1.3;
          }
        }
        .main-content {
          text-align: justify;
          line-height: 1.75;
          margin-top: 1.5rem;
          color: #717171;
        }
      }
    }

    .ssq-button-prev-b,
    .ssq-button-next-b {
      > i {
        font-size: 22px;
      }
    }
    .ssq-button-prev-b {
      left: -12px;
    }
    .ssq-button-next-b {
      right: -12px;
    }
  }
}
</style>
