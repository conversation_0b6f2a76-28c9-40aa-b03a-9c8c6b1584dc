<template>
  <section class="index-leader section">
    <div class="container">
      <h2 class="section-headline headline--main">参与多项行业标准制定</h2>
      <p class="little-title">率先入选工信部“可信区块链联盟”理事单位。</p>
      <div class="content">
        <div class="block">
          <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-zukaifangpingtai"></use>
            </svg> -->
          <img src="../img/ad2_2.png" alt="">
          <div class="dsc1">
            <h4>2017</h4>
          <h3>中国互联网金融协会</h3>
          <p>《互联网金融个体网络借贷电子合同安全规范》</p>
          </div>
        </div>
        <div class="block">
          <img src="../img/ad2_1.png" alt="">
          <div class="dsc2">
            <h4>2018</h4>
          <h3>中国信息通信研究院</h3>
          <p>《可信云企业级SaaS评估方法（电子合同服务平台类）》</p>
          <p>《可信区块链推进计划-可信区块链测评方法》</p>
          <!-- <p>行业率先入选工信部“可信区块链联盟”理事单位</p> -->
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'standard',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toReg() {
      const query = sessionStorage.getItem('query');
      const href = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push([
            '_trackEvent',
            'click_register_mobile',
            'click',
            href,
          ]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_register_pc', 'click', href]);
      }
      const url =
        window.location.host.indexOf('cn') > -1
          ? 'https://ent.bestsign.cn/register'
          : 'https://ent.bestsign.info/register';
      window.open(url + '?' + 'index_saas&' + query);
    },
    toDemo() {
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          index_api: '',
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.index-leader {
  background-color: #f2f2f2;
  padding-bottom: 0;
  .section-headline {
    color: #090909;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
  }
  .little-title {
    line-height: 3rem;
    color: #86868b;
    font-size: 14px;
  }
  .content {
    display: flex;
    flex-wrap: nowrap;
    margin-top: 3.5rem;
    justify-content: space-between;
    padding-bottom: 5rem;
    .block {
      flex: 1;
      text-align: left;
      background-color: #fff;
      margin: 0 10px 0;
      padding: 30px 50px;
      .icon {
        font-size: 5rem;
      }
      img {
        height: 100px;
      }
      .dsc1 {
        margin-left: 30px;
        h3 {
          font-size: 1.6rem;
          font-weight: normal;
          margin: 1.5rem auto;
          color: #1d1d1f;
          font-weight: 400;
        }
        p {
          line-height: 1.5;
          color: #86868b;
        }
        margin-top: 20px;
      }
      .dsc2 {
        margin-left: 15px;
        h3 {
          font-size: 1.6rem;
          font-weight: normal;
          margin: 1.5rem auto;
          color: #1d1d1f;
          font-weight: 400;
        }
        p {
          line-height: 1.5;
          color: #86868b;
        }
        h4 {
          margin-top: 20px;
        }
      }
      .tips {
        font-size: 14px;
        color: #00aa64;
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .index-leader {
    // padding-top: 210px;
    .section-headline {
      color: #090909;
      font-size: 1.9rem;
      line-height: 2.5rem;
      font-weight: 400;
    }
    .content {
      display: flex;
      flex-wrap: wrap;
      margin-top: 3.5rem;
      // border-bottom: 1px solid #ddd;
      justify-content: space-between;
      .block {
        flex: auto;
        text-align: left;
        background-color: #fff;
        margin: 10px 0;
        padding: 30px 20px;
        .icon {
          font-size: 5rem;
        }
        img {
          height: 100px;
        }
        .dsc1 {
          margin-left: 30px;
          h3 {
            font-size: 1.6rem;
            font-weight: normal;
            margin: 1.5rem auto;
            color: #1d1d1f;
            font-weight: 400;
          }
          p {
            line-height: 24px;
            font-size: 14px;
            color: #86868b;
          }
          margin-top: 20px;
        }
        .dsc2 {
          margin-left: 15px;
          h3 {
            font-size: 1.6rem;
            font-weight: normal;
            margin: 1.5rem auto;
            color: #1d1d1f;
            font-weight: 400;
          }
          p {
            line-height: 1.5;
            color: #86868b;
            font-size: 14px;
          }
          h4 {
            margin-top: 20px;
          }
        }
        .tips {
          font-size: 14px;
          color: #00aa64;
        }
      }
    }
  }
}
</style>
