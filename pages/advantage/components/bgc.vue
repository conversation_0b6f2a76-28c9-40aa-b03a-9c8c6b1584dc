<template>
<div>
  <div class="vision" v-if="!isMobile">
    <div class="container form-wrapper">
      <div class="vision-text">
        <div class="title">适配各种终端，<br>线上线下无缝切换。</div>
      </div>
    </div>
  </div>
  <div class="vision" v-else>
    <div class="container form-wrapper">
      <div class="vision-text">
        <div class="title">适配各种终端，<br>线上线下无缝切换。</div>
      </div>
    </div>
  </div>
</div>
  
</template>

<script>
export default {
  name: 'bgc',
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {},
};
</script>

<style scoped lang="scss">
.vision {
  width: 100%;
  height: 20vw;
  background-size: 100% 160%;
  background-image: url(../img/type.jpg);
  background-position: 0 75%;
  background-repeat: no-repeat;
  font-size: pxToVw(24);
  position: relative;
  .ssq-button-primary {
    display: inline-block;
    margin-top: 40px;
    font-size: 16px;
    width: 120px;
    height: 37px;
    line-height: 37px;
    padding: 0;
  }
  .form-wrapper {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
  .title {
    font-size: 36px;
    font-weight: 400;
    line-height: 54px;
    color: #fff;
  }
  .feature {
    margin-top: pxToVw(70);
    &-item {
      display: block;
      line-height: 32px;
      font-size: 14px;
      i {
        margin-right: 8px;
      }
    }
  }
  &::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }
}

@media only screen and (max-width: 767px) {
  .vision {
    background-image: url(../img/type.png);
    background-size: cover;
    height: 20vh;
    background-position: 44% 75%;
    position: relative;
    .form-wrapper {
      display: flex;
      text-align: center;
      justify-content: center;
      .title {
        font-size: 20px;
        line-height: 30px;
        padding-top: 0px;
        margin-bottom: 15px;
        color: #fff;
        font-weight: 500;
      }
      .vision-text {
        padding: 0 16px;
      }
    }
    .feature {
      width: 65%;
      margin-left: auto;
      margin-right: auto;
      text-align: left;
      .feature-item {
        font-size: 12px;
        line-height: 20px;
      }
    }
    .ssq-button-primary {
      margin-top: 25px;
    }
    &::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      background-color: rgba(0, 0, 0, 0.5);
    }
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vision {
    padding: 0 32px;
    .feature {
      margin-top: 16px;
    }
    .title {
      font-size: 20px;
      line-height: 30px;
      color: #fff;
    }
  }
}
@media only screen and (min-width: 991px) and (max-width: 1024px) {
  .vision {
    padding: 0 32px;
    .feature {
      margin-top: 16px;
    }
    .title {
      font-size: 20px;
      line-height: 30px;
      padding-top: 33px;
      margin-bottom: 15px;
      color: #fff;
    }
  }
}
</style>
