<template>
  <section class="index-reason section">
    <div class="container">
      <h2 class="section-headline headline--main" v-if="reasonTitle">安全核心科技</h2>
      <div class="content">
        <div class="block bor-r-b">
          <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-xingzhuang51"></use>
            </svg> -->
			<i class="iconfont icon-dianziqianmingzhaiyaosuanfajishu"></i>
          <h3>电子签名摘要算法技术</h3>
          <p>用户仅提供数据摘要（Hash值）而不提供原文信息的情况下，完成快速签名，最终达到“文档不出门”的效果，有效增强了文件的信息安全。</p>
        </div>
        <div class="block bor-r-b">
          <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-sanjimichiguanlijishu"></use>
            </svg> -->
				<i class="iconfont icon-sanjimichiguanlijishu"></i>
          <h3>三级密钥管理技术</h3>
          <p>基于三级密钥体系和硬件密码机自主研发的密钥管理中心系统，实现系统内所需全部密钥的注入、生成、导出、备份、恢复、更新、销毁等全生命周期的安全防护及管理。</p>
        </div>
        <div class="block bor-b">
          <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-xingzhuang6"></use>
            </svg> -->
				<i class="iconfont icon-zhengjulianshishigongzhengbaoquanjishu"></i>
          <h3>证据链实时公证保全技术</h3>
          <p>指用户注册、实名、申请证书、合同发送、合同签署等一系列行为进行全周期实时公证并产生证据报告。并使用多通道存证确保证据的高可用，签约过程的可追溯、不可篡改。</p>
        </div>
        <div class="block bor-r">
          <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-shuzizhengshuduotongdaozhinengguanlijishu"></use>
            </svg> -->
				<i class="iconfont icon-shuzizhengshuduotongdaozhinengguanlijishu"></i>
          <h3>数字证书多通道智能管理技术</h3>
          <p>对接了国内多家优质CA机构，并通过智能路由切换技术，选择当前响应最快的CA，并对故障CA智能下线，自动恢复，实现证书服务的稳定、高可用和故障智能切换。</p>
        </div>
        <div class="block bor-r">
          <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-duojiagouhunheyunbushujishu"></use>
            </svg> -->
				<i class="iconfont icon-duojiagouhunheyunbushujishu"></i>
          <h3>多架构混合云部署技术</h3>
          <p>运用灵活的分布式部署技术方案，实现了文档本地化等多种混合云部署方案。</p>
        </div>
        <div class="block">
          <!-- <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-duoyunzhinengcunchujishu"></use>
            </svg> -->
				<i class="iconfont icon-duoyunzhinengcunchujishu"></i>
          <h3>多云智能存储技术</h3>
          <p>完全自主研发的一套可结合多云多活、多云容灾备份、多云流量智能管控、文档智能分类存档等特性的分布式存储服务中间件技术，最大限度地减少极端灾难事件或重大事故造成的各类损失，全方位保护用户数据的高可用、高可靠。</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
const data = [
  {
    icon: 'icon-xingzhuang51',

    title: '合同全生命周期智能管理2.0，全新定义电子签名',
    content:
      '涵盖智能档案、智能模板、智能起草、智能收发、智能签署、智能审批、智能管理、合规管理、权限管理等合同管理的所有环节，让企业管理如臂使指。',
  },
  {
    icon: 'icon-zhinengfawuxitong',
    title: '智能法务系统，全面提升法务工作效率',
    content:
      '提供全周期实时公证、多通道存证、在线裁决、区块链追溯、合同AI分析、合同AI智能检索等服务，多次得到司法机构事实认证，提升企业维权效率。',
  },
  {
    icon: 'icon-hetongzhongshenbaoanquan',
    title: '合同终身保安全，更高的安全保障',
    content:
      '7大全球顶级安全资质认证、6大全球领先安全科技，世界500强、大型银行广泛使用，系统可用性不低于99.99%。超过1700天的安全运行记录。',
  },
  {
    icon: 'icon-linghuobushu',
    title: '灵活部署，更快交付',
    content:
      '全面兼容各大企业内部管理系统，PaaS中台技术支撑， API标准化产品最快可做到7天交付。',
  },
  {
    icon: 'icon-xingzhuang10',
    title: '终身服务团队，实时解决客户问题',
    content:
      '咨询服务、培训服务、技术对接指导服务、巡检服务、项目管理服务，多维度实时解决客户安装使用问题。',
  },
  {
    icon: 'icon-xingzhuang12',
    title: '一线投资机构领投，规模行业领先',
    content:
      '2018年8月31日，上上签率先完成电子签约行业首个C轮融资3.58亿，融资规模行业领先。',
  },
];
export default {
  name: 'technology',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
    reasonUse: {
      type: Boolean,
      default: true,
    },
    reasonTitle: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      activeIndex: 0,
      data,
      swiperOptionMobile: {
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        pagination: {
          el: '.swiper-pagination.swiper-pagination__reason',
        },
      },
      partnerInfo: {
        content: '',
        name: '',
        desc: '',
      },
      activeClass: 'iconfont',
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          index_youshi: '',
        },
      });
    },
    handleChange(cur) {
      const activeIndex = cur.realIndex;
      this.activeIndex = activeIndex;
    },
  },
};
</script>

<style scoped lang="scss">
.index-reason {
  padding-bottom: 5rem;
  background-color: #f5f5f7;
  .section-headline {
    color: #090909;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
  }
  .content {
    display: flex;
    flex-wrap: wrap;
    padding: 0 2rem;
    .el-button--text {
      color: #00aa64;
    }
    .icon {
      font-size: 3rem;
    }
    .block {
      padding: 30px;
      width: 32.66%;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fff;
      padding: 60px 50px;
      .iconfont {
        color: #00aa64;
        font-size: 3rem;
        padding-bottom: 2rem;
        display: block;
      }
      &.bor-r-b {
        // border-right: 1px solid #ddd;
        // border-bottom: 1px solid #ddd;
        background: #fff;
        margin-right: 1%;
        margin-bottom: 1%;
      }
      &.bor-b {
        // border-bottom: 1px solid #ddd;
        background: #fff;
        margin-bottom: 1%;
      }
      &.bor-r {
        // border-right: 1px solid #ddd;
        background: #fff;
        margin-right: 1%;
      }
      img {
        width: 60px;
      }
      h3 {
        font-size: 20px;
        line-height: 1.5;
        margin: 0.75rem auto 1.75rem;
        color: #1d1d1f;
        font-weight: 400;
      }
      p {
        line-height: 1.5;
        color: #86868b;
        text-align: center;
      }
    }
  }
  .ssq-button-primary {
    margin: 3rem auto;
  }
}
@media screen and (max-width: 767px) {
  .index-reason {
    padding: 0 0 50px 0;
    background-color: #f5f5f7;
    .section-headline {
      color: #090909;
      font-size: 1.9rem;
      line-height: 2.5rem;
      font-weight: 400;
    }
    .content {
      display: block;
      margin-top: 0;
      .el-button--text {
        color: #00aa64;
      }
      .icon {
        font-size: 3rem;
      }
      .block {
        padding: 30px;
        width: 100%;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #f5f5f7;
        &.bor-r-b {
          // border-right: 1px solid #ddd;
          // border-bottom: 1px solid #ddd;
          background: #f5f5f7;
          margin-right: 0;
          margin-bottom: 0;
        }
        &.bor-b {
          // border-bottom: 1px solid #ddd;
          background: #f5f5f7;
          margin-bottom: 0;
        }
        &.bor-r {
          // border-right: 1px solid #ddd;
          background: #f5f5f7;
          margin-right: 0;
        }
        img {
          width: 60px;
        }
        h3 {
          font-size: 20px;
          font-weight: normal;
          line-height: 1.5;
          margin: 0.75rem auto 1.75rem;
          color: #272828;
        }
        p {
          line-height: 1.5;
          color: #444447;
          text-align: justify;
        }
      }
    }
    .content_wrap {
      text-align: center;
      .main_icon {
        margin: 3rem;
      }
      .iconfont {
        font-size: 4rem;
        text-align: center;
        margin-top: 5rem;
      }
      .main-content {
        color: #090909;
        font-size: 1.4rem;
        line-height: 2.5rem;
        font-weight: 400;
      }
      .name {
        margin-top: 2rem;
        line-height: 1.5;
        color: #444447;
        font-size: 1.1rem;
      }
      .ssq-button-more {
        margin-top: 2rem;
      }
    }
    .swiper-pagination {
      width: 100%;
      left: 50%;
      transform: translateX(-50%);
      margin-top: 20px;
      /deep/ .swiper-pagination-bullet {
        margin: 0 10px;
      }
      /deep/ .swiper-pagination-bullet-active {
        background: #62686f;
      }
    }
    .ssq-button-primary {
      width: 130px;
      height: 44px;
      line-height: 44px;
    }
  }
}
</style>
