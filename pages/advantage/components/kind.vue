<template>
  <section class="index-reason section">
    <div class="container">
      <div class="content">
        <div class="block bor-r-b">
          <!-- <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-SaaSpingtai"></use>
          </svg> -->
      <i class="iconfont icon-qijian"></i>
          <h3>签约管理系列</h3>
          <p>0开发成本，即充即用，合同秒发秒签，满足各类合同工作流管理，适应各终端（Web、APP、H5、小程序等）与平台（OA、HR、CRM等）。</p>
          <div class="type_content">
            <div class="type_left">专业版</div>
            <div class="type_right">满足合同签署基本需求。</div>
          </div>
          <div class="type_content">
            <div class="type_left">标准版</div>
            <div class="type_right">满足企业内部签署管理需求。</div>
          </div>
          <div class="type_content">
            <div class="type_left">旗舰版</div>
            <div class="type_right">满足多元业务签署协同及管控需求。</div>
          </div>
        </div>
        <div class="block bor-b">
          <!-- <svg class="icon" aria-hidden="true">
            <use xlink:href="#icon-APIkaifangpingtai"></use>
          </svg> -->
      <i class="iconfont icon-API1"></i>
          <h3>签约工具系列</h3>
          <p>提供丰富的API接口嵌入，可根据业务深度与使用习惯深度自定义。</p>
          <div class="type_content">
            <div class="type_left">工具版</div>
            <div class="type_right">满足客户灵活构建签署能力需求。</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'kind',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
    reasonUse: {
      type: Boolean,
      default: true,
    },
    reasonTitle: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      activeIndex: 0,
      swiperOptionMobile: {
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        pagination: {
          el: '.swiper-pagination.swiper-pagination__reason',
        },
      },
      partnerInfo: {
        content: '',
        name: '',
        desc: '',
      },
      activeClass: 'iconfont',
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          index_youshi: '',
        },
      });
    },
    handleChange(cur) {
      const activeIndex = cur.realIndex;
      this.activeIndex = activeIndex;
    },
  },
};
</script>

<style scoped lang="scss">
.index-reason {
  padding-bottom: 5rem;
  background-color: #fff;
  .section-headline {
    color: #090909;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
  }
  .content {
    display: flex;
    margin-top: 3.5rem;
    flex-wrap: wrap;
    .el-button--text {
      color: #00aa64;
    }
    .icon {
      font-size: 3rem;
    }
    .block {
      padding: 6rem 3rem;
      width: 49%;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fafafa;
      .iconfont {
        font-size: 6rem;
        color: #00aa64;
      }
      &.bor-r-b {
        // border-right: 1px solid #ddd;
        // border-bottom: 1px solid #ddd;
        background: #fafafa;
        margin-right: 1%;
        margin-bottom: 1%;
      }
      &.bor-b {
        // border-bottom: 1px solid #ddd;
        background: #fafafa;
        margin-bottom: 1%;
      }
      &:hover {
        background-color: #fafafa;
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.4);
      }
      img {
        width: 80%;
      }
      .icon {
        font-size: 5rem;
      }
      h3 {
        font-size: 1.6rem;
        color: #1d1d1f;
        font-weight: 400;
        margin: 1.75rem auto 1.75rem;
      }
      p {
        line-height: 1.5;
        color: #86868b;
        text-align: center;
      }
      .type_content {
        display: flex;
        margin-top: 3rem;
        .type_left {
          margin-right: 10%;
          width: 20%;
          border-top: 1px solid #00aa64;
          padding-top: 2rem;
          font-size: 1.5rem;
          color: #1c1c1e;
          font-weight: 500;
          white-space: nowrap;
        }
        .type_right {
          margin-left: 5%;
          width: 65%;
          text-align: left;
          border-top: 1px solid #cbcbcb;
          padding-top: 2rem;
          line-height: 2rem;
          color: #86868b;
        }
      }
    }
  }
  .ssq-button-primary {
    margin: 3rem auto;
  }
}
@media screen and (max-width: 767px) {
  .index-reason {
    padding: 0;
    background-color: #fff;
    .section-headline {
      color: #090909;
      font-size: 1.9rem;
      line-height: 2.5rem;
      font-weight: 400;
    }

    .content {
      display: flex;
      margin-top: 3.5rem;
      flex-wrap: wrap;
      .el-button--text {
        color: #00aa64;
      }
      .icon {
        font-size: 3rem;
      }
      .block {
        padding: 6rem 3rem;
        width: 100%;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #fafafa;
        &.bor-r-b {
          // border-right: 1px solid #ddd;
          // border-bottom: 1px solid #ddd;
          background: #fafafa;
          margin-right: 1%;
          margin-bottom: 1%;
        }
        &.bor-b {
          // border-bottom: 1px solid #ddd;
          background: #fafafa;
          margin-bottom: 1%;
        }
        &:hover {
          background-color: #fafafa;
          box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.4);
        }
        img {
          width: 80%;
        }
        .icon {
          font-size: 5rem;
        }
        h3 {
          font-size: 1.8rem;
          font-weight: normal;
          line-height: 1.5;
          margin: 1.75rem auto 1.75rem;
          color: #1c1c1e;
          font-weight: 500;
        }
        p {
          line-height: 1.5;
          color: #454547;
          text-align: center;
        }
        .type_content {
          display: flex;
          margin-top: 3rem;
          .type_left {
            margin-right: 10%;
            width: 20%;
            border-top: 1px solid #00aa64;
            padding-top: 2rem;
            font-size: 1.5rem;
            color: #1c1c1e;
            font-weight: 500;
          }
          .type_right {
            margin-left: 5%;
            width: 65%;
            text-align: left;
            border-top: 1px solid #cbcbcb;
            padding-top: 2rem;
            line-height: 2rem;
          }
        }
      }
    }
    .ssq-button-primary {
      width: 130px;
      height: 44px;
      line-height: 44px;
    }
  }
}
</style>
