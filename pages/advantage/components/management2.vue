<template>
  <section class="section section-1">
    <div class="container">
      <div class="content">
        <div :class="['block_top',fixedTab?'fixed-bar':'']" v-if="!isMobile" id="childNav">
			<div class="nav-box"> 
				 <div class="icon_container">
					<div class="title" :class="{ newStyle:1===number}" @mouseenter="check(1)">智能档案</div>
				</div>
				<div class="icon_container">
					<div class="title" :class="{ newStyle:2===number}" @mouseenter="check(2)">智能模板</div>
				</div>
				<div class="icon_container">
					<div class="title" :class="{ newStyle:3===number}" @mouseenter="check(3)">智能起草</div>
				</div>
				<div class="icon_container">
					<div class="title" :class="{ newStyle:4===number}" @mouseenter="check(4)">智能收发</div>
				</div>
				<div class="icon_container">
					<div class="title" :class="{ newStyle:5===number}" @mouseenter="check(5)">智能签署</div>
				</div>
				<div class="icon_container">
					<div class="title" :class="{ newStyle:6===number}" @mouseenter="check(6)">智能审批</div>
				</div>
				<div class="icon_container">
					<div class="title" :class="{ newStyle:7===number}" @mouseenter="check(7)">智能管理</div>
				</div>
				<div class="icon_container">
					<div class="title" :class="{ newStyle:8===number}" @mouseenter="check(8)">合规管理</div>
				</div>
				<div class="icon_container">
					<div class="title" :class="{ newStyle:9===number}" @mouseenter="check(9)">权限管理</div>
				</div>
				<div class="icon_container">
					<div class="title" :class="{ newStyle:10===number}" @mouseenter="check(10)">灵活接口</div>
				</div>
				<div class="icon_container">
					<div class="title" :class="{ newStyle:11===number}" @mouseenter="check(11)">行业包配置</div>
				</div>
			</div>
        </div>
        <div class="block_bottom_container" v-if="!isMobile">
          <div class="block_bottom" v-if="number==1">
         <h1>智能档案</h1>
         <p>预先收集所有与合同所需的签署信息（如学历证书、职业证书等）。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
         <!-- <div class="mac">
          <img src="https://static.bestsign.cn/e54a971ad1948aba31418d6001cd8132b4784cf0.png" alt="">
          <div class="mac_content">
           <img src="https://static.bestsign.cn/591a13f08867cab7a730a8354572b2dec534c848.png" alt="">
         </div>
         </div> -->
         <img src="https://static.bestsign.cn/d59980f9242a5a98888f1101ab4878da1c6863d4.png" alt="">
        </div>
        <div class="block_bottom" v-if="number==2">
         <h1>智能模板</h1>
         <p>将变量字段导入合同模板，一键生成多份合同。合同模板智能匹配签署人信息生成相应合同。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
         <img src="https://static.bestsign.cn/58f9bcfd73a87d7d50e5552e9ec4d0d6ecde5fe5.png" alt="">
        </div>
        <div class="block_bottom" v-if="number==3">
         <h1>智能起草</h1>
         <p>多人、多方共同协作编辑合同内容。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
         <img src="https://static.bestsign.cn/894f40da3658e255bd5e65ec96a210bf43d9617c.png" alt="">
        </div>
        <div class="block_bottom" v-if="number==4">
         <h1>智能收发</h1>
         <p>多份文件一次发送一次签署、多方签署一次发送等功能，一次搞定合同发送。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
         <img src="https://static.bestsign.cn/8de33fba8327a2fafff9b1983aa27df1bf896cca.png" alt="">
        </div>
        <div class="block_bottom" v-if="number==5">
         <h1>智能签署</h1>
         <p>批量签署、自动签署、授权签署等多种方式。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
         <img src="https://static.bestsign.cn/c188e53cb99f551433b0195daea35374b28b08f6.png" alt="">
        </div>
        <div class="block_bottom" v-if="number==6">
         <h1>智能审批</h1>
         <p>建立合同智能审批流程，添加多个审批人，审批智能提醒，审批完成后自能发起签署。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
         <img src="https://static.bestsign.cn/3fdf37b94ae9da5f9994a70fd03bde2b9013acdd.png" alt="">
        </div>
        <div class="block_bottom" v-if="number==7">
         <h1>智能管理</h1>
         <p>合同状态管理、签署到期提醒、履约提醒、归档管理、导出下载、统计报表等功能，合同数据管理可视化。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
         <img src="https://static.bestsign.cn/c107c7e5e24b234713d948dcaa714263c5cc9a4b.png" alt="">
        </div>
        <div class="block_bottom" v-if="number==8">
         <h1>合规管理</h1>
         <p>在实名认证、意愿验证、时间戳防篡改等基础之上，通过签署人身份强一致性效验、刷脸效验、设定手写签名、强制阅读合同内容等功能，加强签署流程合规。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
         <img src="https://static.bestsign.cn/400ce5f55d88500bd04ad20dc90a3b9d489e109c.png" alt="">
        </div>
        <div class="block_bottom" v-if="number==9">
         <h1>权限管理</h1>
         <p>模板权限管理、角色管理、印章管理等功能，建立合同分级授权管理体系。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
         <img src="https://static.bestsign.cn/61b56d50f993197465757309bcae805a7c45af4a.png" alt="">
        </div>
        <div class="block_bottom" v-if="number==10">
         <h1>灵活接口</h1>
         <p>全面兼容企业各种内部系统（ERP、CRM、OA、EHR等），与业务数据全面打通。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
         <img src="https://static.bestsign.cn/72d6d966e5e0f8bfe1d0dda4ae475f0d8c3a14eb.png" alt="">
        </div>
        <div class="block_bottom" v-if="number==11">
         <h1>行业包配置</h1>
         <p>不同行业不同行业包配置，更适合具体业务场景。</p>
         <div class="ssq-button-primary" @click="toDemo">免费试用</div>
         <img src="https://static.bestsign.cn/16d575aed2908b58d39d196c7e9ac01da37172c7.png" alt="">
        </div>
        </div>
        
      </div>
    </div>
    <div class="container" v-for="(item, index) in list" :key="index" v-if="isMobile" :style="{background:item.color}">
          <div class="content_wap">
            <div class="content_img">
          <img :src="item.mobileImg" alt="">
          </div>
          <div class="text_wap">
            <h2>{{item.title}}</h2>
            <li>{{item.content}}</li>
          </div>
          </div>
        </div>
  </section>
</template>

<script>
const list = [
  {
    mobileImg:
      'https://static.bestsign.cn/53d29b3fc2d8049c313ad5624f85cd6343bb6678.png',
    pcImg:
      'https://static.bestsign.cn/d59980f9242a5a98888f1101ab4878da1c6863d4.png',
    title: '智能档案',
    content: '预先收集所有与合同所需的签署信息（如学历证书、职业证书等）。',
    color: '#fff',
  },
  {
    mobileImg:
      'https://static.bestsign.cn/7a80f7eae016d9d88a067836db31912e2c565966.png',
    pcImg:
      'https://static.bestsign.cn/58f9bcfd73a87d7d50e5552e9ec4d0d6ecde5fe5.png',
    title: '智能模板',
    content:
      '将变量字段导入合同模板，一键生成多份合同。合同模板智能匹配签署人信息生成相应合同。',
    color: '#f2f2f2',
  },
  {
    mobileImg:
      'https://static.bestsign.cn/17198587d2f0e79c8423c86781a1e894ab760133.png',
    pcImg:
      'https://static.bestsign.cn/894f40da3658e255bd5e65ec96a210bf43d9617c.png',
    title: '智能起草',
    content: '多人、多方共同协作编辑合同内容。',
    color: '#fff',
  },
  {
    mobileImg:
      'https://static.bestsign.cn/f84961cd65ccd9d146278cf917cc48fa8be069df.png',
    pcImg:
      'https://static.bestsign.cn/8de33fba8327a2fafff9b1983aa27df1bf896cca.png',
    title: '智能收发',
    content:
      '多份文件一次发送一次签署、多方签署一次发送等功能，一次搞定合同发送。',
    color: '#f2f2f2',
  },
  {
    mobileImg:
      'https://static.bestsign.cn/d5ad87b6987edd7200bd0dbdbcda9131c7c9d5e1.png',
    pcImg:
      'https://static.bestsign.cn/c188e53cb99f551433b0195daea35374b28b08f6.png',
    title: '智能签署',
    content: '批量签署、自动签署、授权签署等多种方式。',
    color: '#fff',
  },
  {
    mobileImg:
      'https://static.bestsign.cn/17a2c1f2efd5ed9d9c21cb5cf62a241c98ba9c6c.png',
    pcImg:
      'https://static.bestsign.cn/3fdf37b94ae9da5f9994a70fd03bde2b9013acdd.png',
    title: '智能审批',
    content:
      '建立合同智能审批流程，添加多个审批人，审批智能提醒，审批完成后自能发起签署。',
    color: '#f2f2f2',
  },
  {
    mobileImg:
      'https://static.bestsign.cn/c617530d73e1b91e6e827ed037c4fa3ecfc93961.png',
    pcImg:
      'https://static.bestsign.cn/c107c7e5e24b234713d948dcaa714263c5cc9a4b.png',
    title: '智能管理',
    content:
      '合同状态管理、签署到期提醒、履约提醒、归档管理、导出下载、统计报表等功能，合同数据管理可视化。',
    color: '#fff',
  },
  {
    mobileImg:
      'https://static.bestsign.cn/46e966d321c877f85c4f3557bbbae1c99c11ef47.png',
    pcImg:
      'https://static.bestsign.cn/400ce5f55d88500bd04ad20dc90a3b9d489e109c.png',
    title: '合规管理',
    content:
      '在实名认证、意愿验证、时间戳防篡改等基础之上，通过签署人身份强一致性效验、刷脸效验、设定手写签名、强制阅读合同内容等功能，加强签署流程合规。',
    color: '#f2f2f2',
  },
  {
    mobileImg:
      'https://static.bestsign.cn/5025c515e33e8e1536ca6eb4ce286573cae94dbe.png',
    pcImg:
      'https://static.bestsign.cn/61b56d50f993197465757309bcae805a7c45af4a.png',
    title: '权限管理',
    content:
      '模板权限管理、角色管理、印章管理等功能，建立合同分级授权管理体系。',
    color: '#fff',
  },
  {
    mobileImg:
      'https://static.bestsign.cn/4535f632b120925531a564e27537dccb062acd03.png',
    pcImg:
      'https://static.bestsign.cn/72d6d966e5e0f8bfe1d0dda4ae475f0d8c3a14eb.png',
    title: '灵活接口',
    content:
      '全面兼容企业各种内部系统（ERP、CRM、OA、EHR等），与业务数据全面打通。',
    color: '#f2f2f2',
  },
  {
    mobileImg:
      'https://static.bestsign.cn/3557ee05d6bc0688917470aa7dfe639bf021e47e.png',
    pcImg:
      'https://static.bestsign.cn/16d575aed2908b58d39d196c7e9ac01da37172c7.png',
    title: '行业包配置',
    content: '不同行业不同行业包配置，更适合具体业务场景。',
    color: '#fff',
  },
];
import Throttle from '@/assets/utils/throttle';
export default {
  name: 'management2',
  data() {
    return {
      list,
      number: 1,
      tabsHeight: '',
      fixedTab: false,
    };
  },
  methods: {
    check(num) {
      this.number = num;
    },
    toDemo() {
      const url = location.href;
      if (this.isMobile) {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        window._hmt &&
          window._hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          index_api: '',
        },
      });
    },
    handleScroll() {
      let scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      if (scrollTop + 80 > this.tabsHeight) {
        this.fixedTab = true;
      } else {
        this.fixedTab = false;
      }
    },
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  mounted() {
    if (!this.isMobile) {
      this.tabsHeight = document
        .querySelector('#childNav')
        .getBoundingClientRect().top;
      window.addEventListener('scroll', Throttle(this.handleScroll, 100));
    }
  },
  destroyed() {
    window.removeEventListener('scroll', this.handleScroll);
  },
};
</script>

<style scoped lang="scss">
.section-1 {
  .container {
    .block_top {
      display: flex;
      justify-content: center;
      border-bottom: 1px solid #f3f3f3;
      .title {
        padding-bottom: 2rem;
        padding: 2rem 0;
        cursor: pointer;
        color: #898989;
        font-size: 1rem;
      }
      .nav-box {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #f3f3f3;
        .icon_container {
          padding: 0 10px;
        }
      }
      .newStyle {
        padding: 2rem 0;
        border-bottom: 2px solid #282828;
        cursor: pointer;
        color: #282828;
      }
    }
    .fixed-bar {
      position: fixed;
      z-index: 20;
      top: 76px;
      background: #fff;
      box-shadow: 0 2px 20px 0 rgba(0, 39, 123, 0.13);
      width: 100%;
      padding: 0;
      left: 0;
      transition: all 0.2s ease;
      ul li {
        border-bottom: none;
      }
      .nav-box {
        display: flex;
        max-width: 1344px;
        margin: 0 auto;
        .icon_container {
          padding: 0 10px;
        }
      }
    }
    .block_bottom {
      .ssq-button-primary {
        margin: 1rem auto;
      }
      h1 {
        padding: 70px 0 40px;
        margin: 0;
      }
    }
    // .mac {
    //   position: relative;
    //   .mac_content {
    //     position: absolute;
    //   }
    // }
    img {
      width: 100%;
    }
    .content {
      // display: flex;
      margin: 0 5%;
      justify-content: space-between;
      h1 {
        color: #050505;
      }
      h3 {
        color: #282828;
      }
      p {
        line-height: 2rem;
      }
    }
  }
}
@media only screen and (max-width: 767px) {
  .section-1 {
    padding: 50px 0;
    .container {
      margin: 0;
      .content_img {
        width: 80%;
        margin: 0 auto;
        padding-top: 30px;
      }
      .content_wap {
        // box-shadow: 0 8px 24px 0 rgba(82, 94, 102, 0.15);
        // border: 1px solid #dcdcdc;
        padding: 30px 0;
        .text_wap {
          margin: 0 20px;
          h2 {
            color: #050505;
            font-size: 20px;
            line-height: 40px;
            margin-top: 10px;
            font-weight: 600;
          }
          li {
            line-height: 1.4;
            padding-bottom: 10px;
            font-size: 14px;
            color: #86868b;
          }
        }
      }
    }
    // .content.flex {
    //   display: block;
    // }
    // .left {
    //   line-height: 24px;
    //   font-size: 12px;
    //   p {
    //     margin-bottom: 20px;
    //   }
    // }
    // .top {
    //   margin-top: 20px;
    // }
  }
}
</style>
