<template>
  <div class="index-evaluate section">
    <div class="container">
      <h2 class="section-headline headline--main">多次得到司法机构事实认证</h2>
      <p class="little-title">行业标准参与制定者。</p>
      <div class="content">
        <div class="swiper-container" v-swiper:mySwiper="isMobile?swiperOptionMobile:swiperOption">
          <div class="swiper-wrapper">
				<div class="swiper-slide" v-for="(item, index) in data" :key="item.desc">
					<div class="item">
						   <div class="top">
								<!-- <div :class="['default-avatar', activeIndex === index? 'active-avatar':'']" :style="{backgroundImage: `url(${item.logo})`}"> -->
								<div :class="['default-avatar', activeIndex === index? 'active-avatar':'']">
									<img :src="item.logo" alt="" width="100%" height="100%">

								</div>
						   </div>
					</div>
					<div v-if="isMobile">
            <div class="name">
							<p class="label_wap">{{ partnerInfo.desc }}</p>
						</div>
						<!-- <div class="main-content">{{ partnerInfo.content }}</div> -->
					</div>
				</div>
          </div>
        </div>
        <div class="arrow" v-if="!isMobile">
          <div class="ssq-button-prev-a">
            <i class="el-icon-arrow-left"></i>
          </div>
          <div class="ssq-button-next-a">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
      <div class="name" v-if="!isMobile">
			<p class="label">{{ partnerInfo.desc }}</p>
		</div>
	 	<!-- <div class="main-content" v-if="!isMobile">{{ partnerInfo.content }}</div> -->
      <!-- <div v-if="!demo" class="ssq-button-more transparent" @click="toCase">进一步了解我们的合作伙伴 <i class="iconfont icon-xiangyoujiantou"></i></div> -->
      <!-- <div v-if="demo" class="ssq-button-more" style="color: #fff" @click="toDemo">免费试用 ></div> -->
	<div v-if="isMobile" class="swiper-pagination swiper-pagination_judicial" slot="pagination"></div>
  <div class="law_content" v-if="!isMobile" >2017.06 ——  浙江省衢州市仲裁委员会认可当事人通过上上签电子签约平台签订合同等的法律效力。<br>

        2018.05 ——  广东省珠海市仲裁委员会认可通过上上签电子签约平台签订合同等的法律效力。<br>

        2018.06 ——  四川省成都市高新区人民法院依据上上签平台服务协议、资质证明等材料，认可客户通过上上签电子签约平台签订合同等的法律效力。<br>

        2018.11 ——  浙江省杭州市余杭区人民法院采信上上签为当事人提供的电子签约证明书，认可通过上上签电子签约平台签订合同等的法律效力。<br>

        2018.12 ——  云南省玉溪市红塔区人民法院依据上上签平台服务协议、主体资格证明等材料，认可通过上上签电子签约平台签订合同等的法律效力。<br>

        2019.03 ——  深圳市罗湖区人民法院认可通过上上签电子签约平台签订合同等的法律效力。<br>

        2019.05 ——  湖北省武汉东湖新技术开发区人民法院采信上上签为当事人提供的电子签约上上签电子签约数据证据报告，认可通过上上签电子签约平台签订合同等的法律效力。<br>

        2019.08 ——  上海市闵行区人民法院认可通过上上签电子签约平台签订合同等的法律效力。<br>

        2019.09 ——  广东省深圳前海合作区人民法院采信上上签为当事人提供的电子签约数据证据报告，认可通过上上签电子签约平台签订合同等的法律效力。<br>

        2019.11 ——  重庆市沙坪坝区人民法院采信上上签为当事人提供的资质证明材料、公证书，认可通过上上签电子签约平台签订合同等的法律效力。</div>
    </div>
  </div>
</template>

<script>
const data = [
  {
    logo:
      'https://static.bestsign.cn/a65b67f3d0cc1cb724b50a84712cd96a9c7abc0a.jpg',
    desc: '上海市闵行区人民法院民事判决书',
  },
  {
    logo:
      'https://static.bestsign.cn/662304fa140b10e1e8d5cad4aa2812e05a6b4e17.jpg',
    desc: '杭州市余杭区人民法院民事判决书',
  },
  {
    logo:
      'https://static.bestsign.cn/a597193328efd2112d9aea0a4e0b3859c4d23b08.jpg',
    desc: '广东省深圳市罗湖区人民法院民事判决书',
  },
  {
    logo:
      'https://static.bestsign.cn/d17ac721efce36da353e7310dea906f5f3e618bd.jpg',
    desc: '湖北省武汉东湖新技术开发区人民法院民事判决书',
  },
  {
    logo:
      'https://static.bestsign.cn/6c25317e287d998c7dd184fbf2cd1ea0227c0c2c.jpg',
    desc: '重庆市沙坪坝区人民法院民事判决书',
  },
  {
    logo:
      'https://static.bestsign.cn/a9328d6417fd2a684f3aee73ac3773d92aac0ab0.jpg',
    desc: '四川省成都市高新技术产业开发区人民法院民事判决书',
  },
  {
    logo:
      'https://static.bestsign.cn/fea192193beff307d2508b0a5bf8b911196b8ffb.jpg',
    desc: '湖北省武汉新技术产业开发区人民法院民事判决书',
  },
  {
    logo:
      'https://static.bestsign.cn/8de43abf2f4a5ef8eed410ff038e22a0ed5a686a.jpg',
    desc: '广东省深圳前海合作区人民法院民事判决书',
  },
  {
    logo:
      'https://static.bestsign.cn/8b19dc28d819c753c36f4cf9ee450a98e7d8c180.jpg',
    desc: '云南省玉溪市红塔区人民法院民事判决书',
  },
];
export default {
  name: 'judicialApprove',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeIndex: 2,
      data,
      swiperOption: {
        loop: true,
        initialSlide: 2,
        centeredSlides: true,
        speed: 700,
        slidesPerView: 3,
        navigation: {
          nextEl: '.ssq-button-next-a',
          prevEl: '.ssq-button-prev-a',
        },
      },
      swiperOptionMobile: {
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        pagination: {
          el: '.swiper-pagination.swiper-pagination_judicial',
        },
      },
      partnerInfo: {
        content: '',
        name: '',
        desc: '',
      },
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toCase() {
      this.$router.push('/evaluate');
    },
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const query = sessionStorage.getItem('query');
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          ...Qs.parse(query),
        },
      });
    },
    handleChange(cur) {
      const activeIndex = cur.realIndex;
      this.activeIndex = activeIndex;
      this.getInfo();
      // console.log(111);
    },
    getInfo() {
      this.$nextTick(() => {
        this.partnerInfo = data.filter(
          (item, index) => index == this.activeIndex
        )[0];
        console.log(this.partnerInfo);
      });
    },
  },
  mounted() {
    const _this = this;
    this.mySwiper.on('slideChange', function() {
      _this.handleChange(this);
    });
    this.getInfo();
  },
};
</script>

<style scoped lang="scss">
.index-evaluate {
  padding-top: 2rem;
  padding-bottom: 2rem;
  background-color: #f2f2f2;
  text-align: center;
  .little-title {
    line-height: 3rem;
    color: #86868b;
    font-size: 14px;
  }
  .content {
    width: 70%;
    position: relative;
    margin: 0 auto;
    .swiper-container {
      margin-top: 2rem;
    }
    .item {
      width: 100%;
      background-color: #f2f2f2;
      padding: 20px 0;
      .top {
        display: flex;
        align-items: center;
        // img {
        // width: 500px;
        // margin-right: 30px;
        // border-radius: 50%;
        // border: 1px solid #eee;
        // }
        .default-avatar {
          margin: 70px auto;
          img {
            // height: 90px;
            // border-radius: 50%;
            width: 75%;
          }
        }

        .name {
          text-align: left;
          font-size: 20px;
          line-height: 1.3;
        }
      }
    }
    .swiper-slide-active .default-avatar img {
      -ms-transform: scale(1.4); /* IE 9 */
      -webkit-transform: scale(1.4); /* Safari */
      transform: scale(1.4); /* 标准语法 */
      transition: transfrom 0.4s;
    }
    .swiper-slide-prev {
      .item {
        padding: 20px 0;
        .default-avatar {
          margin-left: 0;
        }
      }
    }
    .swiper-slide-next {
      .item {
        padding: 20px 0px;
        transition: all 0.5s;
        .default-avatar {
          margin-right: 0;
          transition: all 0.5s;
        }
      }
    }
  }
  .main-content {
    max-width: 40rem;
    text-align: center;
    text-align: justify;
    line-height: 1.75;
    margin: 0 auto;
    // margin-top: 1.5rem;
    // color: #717171;
    // min-height: 8.75rem;
  }
  .name {
    margin-top: 2.5rem;
    .title {
      font-size: 20px;
      font-weight: 600;
    }
    .label {
      font-size: 18px;
      margin: 1rem 0 2rem 0;
    }
  }
  .law_content {
    text-align: left;
    padding: 10px 20%;
    line-height: 40px;
  }
  .ssq-button-more {
    color: #00aa64;
  }
  .ssq-button-prev-a,
  .ssq-button-next-a {
    position: absolute;
    top: 50%;
    transform: translate3d(0, -50%, 0);
    background: transparent;
    cursor: pointer;
    > i {
      font-size: 20px;
      color: rgba(35, 24, 21, 0.5);
      outline: none;
      background: #eee;
      border-radius: 50%;
      padding: 5px;
      &:hover {
        color: rgba(35, 24, 21, 1);
      }
      &:focus {
        outline: none;
      }
    }
    &:focus {
      outline: none;
    }
  }
  .ssq-button-prev-a {
    left: -60px;
  }
  .ssq-button-next-a {
    right: -60px;
  }
  .ssq-button-primary {
    display: inline-block;
  }
}
.swiper-pagination {
  width: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 20px;
  /deep/ .swiper-pagination-bullet {
    margin: 0 10px;
  }
  /deep/ .swiper-pagination-bullet-active {
    background: #62686f;
  }
}
@media screen and (max-width: 767px) {
  .index-evaluate {
    background: #fff;
    padding-bottom: 50px;
    padding-top: 0;
    .little-title {
      line-height: 3rem;
      font-size: 14px;
      color: #86868b;
    }
    .content {
      position: relative;
      width: 100%;
      padding: 0 10px;
      margin: 0 auto;
      .swiper-slide-active .default-avatar img {
        -ms-transform: scale(1); /* IE 9 */
        -webkit-transform: scale(1); /* Safari */
        transform: scale(1); /* 标准语法 */
        transition: transfrom 0.4s;
      }

      .item {
        width: 100%;
        padding: 20px 15px;
        background: #fff;
        .top {
          .default-avatar {
            margin: 0 auto;
          }
          img {
            width: 60px;
            margin-right: 0;
            // border-radius: 50%;
            // border: 1px solid #eee;
          }
          .name {
            text-align: left;
            font-size: 16px;
            line-height: 1.3;
          }
        }
        .main-content {
          text-align: justify;
          line-height: 1.75;
          margin-top: 1.5rem;
          color: #717171;
        }
      }
      .label_wap {
        font-size: 14px;
        margin: 0 !important;
      }
    }

    .ssq-button-prev-a,
    .ssq-button-next-a {
      > i {
        font-size: 22px;
      }
    }
    .ssq-button-prev-a {
      left: -12px;
    }
    .ssq-button-next-a {
      right: -12px;
    }
    .swiper-pagination {
      width: 100%;
      left: 50%;
      transform: translateX(-50%);
      margin-top: 20px;
      /deep/ .swiper-pagination-bullet {
        margin: 0 10px;
      }
      /deep/ .swiper-pagination-bullet-active {
        background: #62686f;
      }
    }
  }
}
</style>
