<template>
<div>
  <div class="vision">
    <div class="container form-wrapper">
      <div class="vision-text">
        <div class="title">客户背书</div>
        <div class="dsc">赛诺菲邀请国际独立的第三方专业安全厂商CyberVadis进行的安全测试中，上上签成为其测评分数最高的供应商。<a v-if="!isMobile" href="https://www.bestsign.cn/news/detail/390 "
          target="_blank" style="color:#00aa64">查看更多 <i class="iconfont icon-xiangyoujiantou"></i></a><br>建设银行、交通银行、汇丰银行、招商银行、民生银行、贵州银行、昆仑银行等多家大型银行使用。</div>
        <div class="logo-container">
        <div class="each-logo" v-for="(item,index) in data" :key="index">
          <img :src="item.logo" alt="">
          <p>{{item.name}}</p>
        </div>
      </div>
      </div>
       <div class="vision-text1" style="padding: 0 16px;">
        <div class="title">权威机构背书</div>
        <!-- <div class="dsc">艾媒咨询《中国电子签名安全专题研究报告》，上上签安全实力行业领先。</div> -->
      </div>
      <div class="dsc" v-if="isMobile">艾媒咨询《中国电子签名安全专题研究报告》，上上签安全实力行业领先。</div>
      <div class="dsc-detail" v-if="!isMobile">
        <div class="detail-left">
          <img src="../img/beishu.jpg" alt="">
        </div>
        <div class="detail-right">各大权威机构，如艾媒咨询、智享会等纷纷出具电子签约相关行业报告，大量报告数据显示，上上签安全实力行业领先。<a href="/report" style="color:#00aa64"><br><br>查看更多 <i class="iconfont icon-xiangyoujiantou"></i></a></div>
      </div>
    </div>
</div>
<!-- <div class="vision" v-else>
    <div class="container form-wrapper">
      <div class="vision-text">
        <div class="title">客户背书</div>
        <div class="dsc">赛诺菲邀请国际独立的第三方专业安全厂商CyberVadis进行的安全测试中，上上签成为其测评分数最高的供应商。<br>建设银行、交通银行、汇丰银行、招商银行、民生银行、贵州银行、昆仑银行等多家大型银行使用。</div>
      </div>
      <div class="vision-text1" style="padding: 0 16px; margin: 4rem 16%;">
        <div class="title">权威机构背书</div>
        <div class="dsc">艾媒咨询《中国电子签名安全专题研究报告》，上上签安全实力行业领先。</div>
      </div>
    </div>
</div> -->
</div>
  
</template>

<script>
const data = [
  {
    logo:
      'https://static.bestsign.cn/6f03caff25b270237096569ca2deb5f16ffa9f69.png',
    name: '建设银行',
  },
  {
    logo:
      'https://static.bestsign.cn/b2318cfe23d44dce53276f7d13e3ec5c4e8bfc99.png',
    name: '交通银行',
  },
  {
    logo:
      'https://static.bestsign.cn/328a3ebe531209f9c4bdeb2a1390ce030eaa02ab.png',
    name: '汇丰银行',
  },
  {
    logo:
      'https://static.bestsign.cn/146f78ea72fe71c06cda8353d40dd4c93f9e3289.png',
    name: '招商银行',
  },
  {
    logo:
      'https://static.bestsign.cn/6a566e3f40a5fbbb068f8b2a8c7853df39c3f668.png',
    name: '民生银行',
  },
  {
    logo:
      'https://static.bestsign.cn/78b2cf5d7dd864b5610ea735e535d174c53b0dd7.png',
    name: '贵州银行',
  },
  {
    logo:
      'https://static.bestsign.cn/b08cf4113c5643e08cbe70fa15ecfca7f3d3c5bd.png',
    name: '昆仑银行',
  },
];
export default {
  name: 'beishu',
  data() {
    return {
      data,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {},
};
</script>

<style scoped lang="scss">
.vision {
  background-color: #f5f5f7;
  padding: 0rem 10%;
  .logo-container {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    margin: 3rem 10% 0;
    border-bottom: 1px solid #c0bfbf;
    padding-bottom: 5rem;
    img {
      width: 5rem;
    }
    p {
      line-height: 3rem;
    }
  }
  .ssq-button-primary {
    display: inline-block;
    margin-top: 40px;
    font-size: 16px;
    width: 120px;
    height: 37px;
    line-height: 37px;
    padding: 0;
  }
  .form-wrapper {
    // width: 100%;
    height: 100%;
    // display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: center;
  }
  .title {
    font-size: 36px;
    font-weight: 400;
    line-height: 54px;
    color: #090909;
    margin: 0 0 40px;
    padding-top: 70px;
  }
  .dsc {
    color: #86868b;
    line-height: 2rem;
  }
  .dsc-detail {
    margin-top: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    .detail-left {
      width: 32%;
      margin-right: 5%;
      img {
        width: 100%;
      }
    }
    .detail-right {
      width: 32%;
      text-align: left;
      line-height: 2;
      font-size: 16px;
    }
  }
  p {
    line-height: 28px;
    font-size: 16px;
    color: #888;
  }
  .feature {
    margin-top: pxToVw(70);
    &-item {
      display: block;
      line-height: 32px;
      font-size: 14px;
      i {
        margin-right: 8px;
      }
    }
  }
}

@media only screen and (max-width: 767px) {
  .vision {
    padding: 0 3% 3rem;
    .logo-container {
      display: flex;
      flex-wrap: wrap;
      // justify-content: space-between;
      margin: 3rem 10% 0;
      border-bottom: 1px solid #c0bfbf;
      padding-bottom: 5rem;
      .each-logo {
        width: 30%;
      }
      img {
        width: 5.5rem;
      }
      p {
        line-height: 3rem;
        font-size: 1.2rem;
      }
    }
    .ssq-button-primary {
      margin-top: 25px;
    }
    .title {
      color: #090909;
      font-size: 1.9rem;
      line-height: 2.5rem;
      font-weight: 400;
    }
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vision {
    padding: 0 32px;
    .feature {
      margin-top: 16px;
    }
    .title {
      font-size: 20px;
      line-height: 30px;
      // margin: 0;
      padding-top: 33px;
      // padding-bottom: 16px;
      margin-bottom: 15px;
      color: #f8f8fa;
    }
    .dsc {
      color: #f8f8fa;
    }
  }
}
@media only screen and (min-width: 991px) and (max-width: 1024px) {
  .vision {
    padding: 0 32px;
    .feature {
      margin-top: 16px;
    }
    .title {
      font-size: 20px;
      line-height: 30px;
      // margin: 0;
      padding-top: 33px;
      // padding-bottom: 16px;
      margin-bottom: 15px;
      color: #f8f8fa;
    }
    .dsc {
      color: #f8f8fa;
    }
  }
}
</style>
