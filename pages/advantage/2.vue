<template>
<div>
  <article class="advantage4">
    <index-banner 
      bigTitle="智能法务系统，<br>全面提升法务工作效率。"
      littleTitle="提供全周期实时公证、多通道存证、在线裁决、区块链追溯、<br>合同AI分析、合同AI智能检索等服务，多次得到司法机构事实认证，提升企业维权效率。"
      :pcImgUrl="bannerPc"
      :mobileImgUrl="bannerWap"></index-banner>
    <laws></laws>
    <standard></standard>
    <judicial-approve></judicial-approve>
    <safety-approve :title="subTitle"></safety-approve>
  </article>
</div>
</template>

<script>
import IndexBanner from './banner/banner.vue';
import Laws from '../advantage/components/laws.vue';
import Standard from '../advantage/components/standard.vue';
import JudicialApprove from '../advantage/components/judicialApprove.vue';
import SafetyApprove from '../advantage/components/safetyApprove.vue';
export default {
  name: 'homepage',
  components: {
    IndexBanner,
    Laws,
    Standard,
    JudicialApprove,
    SafetyApprove,
  },
  data() {
    return {
      subTitle: '可用性不低于99.99%，支持每秒6000+次签署。',
      bannerPc: require('@/assets/images/advantage/pc_ad2.jpg'),
      bannerWap: require('@/assets/images/advantage/wap_ad2.jpg'),
    };
  },
  head() {
    return {
      title: '合同公证_在线裁决_AI分析_智能检索_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同,电子签约,电子签名,电子签章,上上签电子签约云平台',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签电子签约智能法务系统,全面提升法务工作效率.提供全周期实时公证、多通道存证、在线裁决、区块链追溯、合同AI分析、合同AI智能检索等服务,多次得到司法机构事实认证,提升企业维权效率。',
        },
      ],
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
};
</script>
<style scoped lang="scss">
.advantage4 {
  text-align: center;
}

@media screen and (max-width: 767px) {
  .homepage {
    // padding-top: 56px;
    .headline--main {
      font-size: 1.75rem;
    }
    .section-2 {
      padding-top: 10px;
      .image-link {
        flex: 1;
        width: 33.3%;
        padding: 0 5px;
      }
    }
  }
}
</style>
