<template>
<div>
  <article class="advantage5">
    <index-banner 
      bigTitle="终身服务团队，<br>实时解决客户问题。"
      littleTitle="针对系统开发能力较弱的企业客户，上上签提供轻量级部署、快速实施交付<br>的全程服务，保障项目顺利上线，显著提升合同签署与管理体验。"
      :pcImgUrl="bannerPc"
      :mobileImgUrl="bannerWap">
      </index-banner>
    <index-box></index-box>
  </article>
</div>
</template>

<script>
import IndexBox from '../advantage/components/box.vue';
import IndexBanner from './banner/banner.vue';
export default {
  name: 'homepage',
  components: {
    IndexBanner,
    IndexBox,
  },
  data() {
    return {
      bannerPc: require('@/assets/images/advantage/pc_ad5.jpg'),
      bannerWap: require('@/assets/images/advantage/wap_ad5.jpg'),
    };
  },
  head() {
    return {
      title: '电子合同_轻量级部署_交付时间短_服务包支持_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同,电子签约,电子签名,电子签章,上上签电子签约云平台',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签电子签约终身服务团队,实时解决客户问题.针对系统开发能力较弱的客户,上上签提供轻量级部署、快速实施交付的全程服务,保障项目顺利上线,显著提升合同签署与管理体验。',
        },
      ],
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
};
</script>
<style scoped lang="scss">
.advantage5 {
  text-align: center;
}

@media screen and (max-width: 767px) {
  .homepage {
    // padding-top: 56px;
    .headline--main {
      font-size: 1.75rem;
    }
    .section-2 {
      padding-top: 10px;
      .image-link {
        flex: 1;
        width: 33.3%;
        padding: 0 5px;
      }
    }
  }
}
</style>
