<template>
<div>
  <article class="advantage4">
    <index-banner 
      bigTitle="合同全生命周期智能管理2.0，<br>全新定义电子签名。"
      littleTitle="涵盖智能档案、智能模板、智能起草、智能收发、智能签署、智能审批、智能管理、<br>合规管理、权限管理等合同管理的所有环节，让企业管理如臂使指。"
      :pcImgUrl="bannerPc"
      :mobileImgUrl="bannerWap">
      </index-banner>
      <management2></management2>
  </article>
</div>
</template>

<script>
import IndexBanner from './banner/banner.vue';
import Management2 from '../advantage/components/management2.vue';
export default {
  name: 'homepage',
  components: {
    IndexBanner,
    Management2,
  },
  head() {
    return {
      title: '合同管理_模板_起草_收发_审批_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同,电子签约,电子签名,电子签章,上上签电子签约云平台',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签电子签约合同全生命周期智能管理2.0,全新定义电子签名.涵盖智能档案、智能模板、智能起草、智能收发、智能签署、智能审批、智能管理、合规管理、权限管理等合同管理的所有环节,让企业管理如臂使指。',
        },
      ],
    };
  },
  data() {
    return {
      bannerPc: require('@/assets/images/advantage/pc_ad1.jpg'),
      bannerWap: require('@/assets/images/advantage/wap_ad1.jpg'),
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
};
</script>
<style scoped lang="scss">
.advantage4 {
  text-align: center;
}

@media screen and (max-width: 767px) {
  .homepage {
    // padding-top: 56px;
    .headline--main {
      font-size: 1.75rem;
    }
    .section-2 {
      padding-top: 10px;
      .image-link {
        flex: 1;
        width: 33.3%;
        padding: 0 5px;
      }
    }
  }
}
</style>
