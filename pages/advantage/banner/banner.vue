<template>
  <div class="index-banner" :style="{backgroundImage: `url(${isLandMobile ? mobileImgUrl : pcImgUrl}`}">
    <div class="container">
      <div class="main-text">
        <!-- <img v-if="!isLandMobile" class="banner-text" src="./img/banner-text.png" alt=""> -->
        <div class="ad_name" v-if="!isMobile">品牌优势</div>
        <div class="ad_title" v-html="bigTitle"></div>
        <div class="ad_dsc" v-html="littleTitle"></div>
        <button class="ssq-button-primary" @click="toReg">免费试用</button>
        <!-- <button class="ssq-button-primary" @click="toReg">查看详情</button> -->
        <!-- <img v-if="!isLandMobile" class="main-text-img-mp" src="./img/icons.png" alt=""> -->
      </div>
    </div>
    <div class="banner-wrapper">
      <div class="container1">
        <template v-for="item in banner">
          <div class="link-wrapper" :key="item.id">
            <img class="bannerImg" v-if="!isMobile" :src="item.content">
            <a class="image-link" :href="item.pictureUrl">
              <h3>{{ item.knowledgeTitle }}</h3>
              <span v-if="!isMobile">{{ item.videoUrl }}</span>
            </a>
          </div>
        </template>
      </div>
    </div>
  </div>
  
</template>

<script>
export default {
  props: {
    bigTitle: {
      type: String,
      default: '',
    },
    littleTitle: {
      type: String,
      default: '',
    },
    mobileImgUrl: {
      type: String,
      default: '',
    },
    pcImgUrl: {
      type: String,
      default: '',
    },
  },
  data: () => ({
    banner: [],
    headBanner: {},
    bannerButton: {},
  }),
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    toReg() {
      this.$router.push({
        path: '/demo',
        query: {
          id: this.$route.query.id,
          utm_source: this.$route.query.utm_source,
        },
      });
    },
  },
  mounted() {},
};
</script>

<style scoped lang="scss">
.index-banner {
  background-color: #f2f2f4;
  // background: url('../img/advantage6_banner.png') no-repeat;
  position: relative;
  padding: 0;
  width: 100%;
  height: 30vw;
  //   min-height: 450px;
  background-size: 100% 100%;
  .feature {
    margin-bottom: 20px;
    &-item {
      display: block;
      line-height: 1.5;
      color: #626262;
      i {
        color: #00aa64;
        margin-right: 8px;
      }
    }
  }
  .container {
    height: 100%;
    display: flex;
    align-items: center;
    text-align: left;
    // max-width: 1480px;
    &.auto {
      height: auto;
      background-color: rgba(0, 0, 0, 0.5);
    }
    .main-text {
      position: relative;
      top: 0;
      left: 10rem;
      //   margin-top: 150px;
      //   padding-left: 8%;
      .ad_name {
        padding: 3rem 0 1rem 0;
        font-size: 1.2rem;
        color: #fff;
        font-weight: 500;
        line-height: 2rem;
      }
      .ad_title {
        font-size: 2.2rem;
        color: #fff;
        font-weight: 500;
      }
      .ad_dsc {
        margin-top: 2rem;
        font-size: 1.2rem;
        line-height: 1.4;
        font-weight: 400;
        margin-bottom: 1.5rem;
        padding: 6px 0px;
        color: #fff;
      }
      h3 {
        font-size: 52px;
        font-weight: 500;
        margin-bottom: 20px;
      }
      h4 {
        font-size: 20px;
        line-height: 26px;
        max-width: 480px;
        font-weight: 400;
        margin-bottom: 1.5rem;
        border: 1px solid #fff;
        border-radius: 8px;
        display: inline-block;
        padding: 6px 10px;
      }
      .ssq-button-primary {
        background: #00aa64;
        width: 120px;
        height: 40px;
        line-height: 40px;
        display: block;
        padding: 0 15px;
      }
      .main-text-img {
        margin-top: 8rem;
        width: 75%;
      }
      .main-text-img-mp {
        margin-top: 2rem;
        width: 75%;
      }
      .banner-text {
        width: 26vw;
      }
      .tips {
        color: #00aa64;
        font-size: 18px;
        margin-top: 30px;
        font-weight: 400;
      }
    }
  }
  .container1 {
    width: 100%;
    display: flex;
    justify-content: space-evenly;
    margin-top: 16px;
  }
  .ssq-button-primary {
    img {
      margin-left: 5px;
      width: 20px;
      vertical-align: bottom;
    }
  }
  .banner-wrapper {
    // position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    color: #fff;
    .container {
      padding: 12px 0;
      display: flex;
    }
    .link-wrapper {
      flex: 1;
      max-width: 25%;
      border-left: 1px solid #fff;
      border-radius: 4px;
      background-color: #ffffff;
      box-shadow: 0 4px 16px 0 rgba(47, 67, 82, 0.1);
      display: inline-block;
      position: relative;
      margin-left: 5px;
      margin-right: 5px;
      padding-left: 10px;
      &:hover {
        -webkit-box-shadow: #ccc 10px 10px 10px;
        box-shadow: #ccc -0px 10px 10px;
      }
      &:last-child {
        border-right: 1px solid #fff;
      }
    }
    .image-link {
      display: inline-block;
      color: rgb(17, 15, 15);
      padding: 30px 24px;
      line-height: 23px;
      text-align: left;
      vertical-align: middle;
      height: 130px;
      width: 80%;
      h3 {
        margin-bottom: 12px;
        font-size: 16px;
        color: #515151;
        &:hover {
          color: #00aa64;
        }
      }
      span {
        font-size: 14px;
        color: #65697f;
        &:hover {
          color: #00aa64;
        }
      }
    }
    .bannerImg {
      width: 60px;
    }
  }
}
@media screen and (max-width: 767px) {
  .index-banner {
    // background: url('./img/banner-mp1.jpg') no-repeat;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    // padding: 0 16px;
    min-height: 140vw;
    .feature {
      margin-bottom: 30px;
      &-item {
        line-height: 24px;
        &:last-child {
          padding-left: 14px;
        }
      }
    }
    .container1 {
      margin-top: 18px;
      flex-wrap: wrap;
    }
    .container {
      align-items: flex-start !important;
      justify-content: center;
      .main-text {
        text-align: center;
        padding-left: 0 !important;
        margin-top: 30px;
        position: relative;
        left: 0;
        .ad_name {
          text-align: center;
          font-size: 14px;
        }
        .ad_title {
          text-align: center;
          font-size: 28px;
          margin: 50px 0;
        }
        .ad_dsc {
          text-align: center;
          font-size: 16px;
          line-height: 24px;
          padding: 6px;
        }
        .ssq-button-primary {
          font-size: 16px;
          position: absolute;
          left: 50%;
          top: 80vw;
          width: 120px;
          height: 40px;
          line-height: 40px;
          transform: translateX(-50%);
        }
      }
    }
    .banner-wrapper {
      //   bottom: -92px;
      position: relative;
      .container {
        padding: 16px 0;
        display: flex;
      }
      .link-wrapper {
        flex: none;
        width: calc(50% - 10px);
        max-width: none;
        margin-bottom: 10px;
        &:first-child {
          border-left: none;
        }
        &:last-child {
          border-right: none;
        }
      }
      .image-link {
        padding: 0 8px;
        width: 100%;
        margin-top: 10px;
        height: 65px;
        h3 {
          margin: 8px 0px;
          font-size: 14px;
          color: #515151;
          vertical-align: middle;
          line-height: 20px;
        }
        span {
          font-size: 12px;
        }
      }
    }
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .index-banner {
    // background: url('./img/banner1.jpg') no-repeat;
    position: relative;
    height: 65vw;
    .feature {
      margin-bottom: 30px;
      &-item {
        line-height: 24px;
        &:last-child {
          padding-left: 14px;
        }
      }
    }
    .container {
      justify-content: center;
      text-align: center;
      align-items: flex-start;
      .main-text {
        margin-top: 10vw;
      }
      .ssq-button-primary {
        margin: 0 auto;
      }
    }
  }
}
</style>
