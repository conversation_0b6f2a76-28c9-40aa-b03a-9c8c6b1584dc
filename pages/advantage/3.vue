<template>
<div>
  <article class="advantage4">
    <index-banner 
      bigTitle="合同终身保安全，<br>更高的安全保障。"
      littleTitle="7大全球顶级安全资质认证、6大全球领先安全科技，世界500强、大型银行<br>广泛使用，系统可用性不低于99.99%。超过1900天的安全运行记录。"
     :pcImgUrl="bannerPc"
      :mobileImgUrl="bannerWap">
      </index-banner>
    <safety></safety>
    <technology></technology>
    <operation></operation>
    <beishu></beishu>
  </article>
</div>
</template>

<script>
import IndexBanner from './banner/banner.vue';
import Safety from '../advantage/components/safety.vue';
import Technology from '../advantage/components/technology.vue';
import Operation from '../advantage/components/operation.vue';
import Beishu from '../advantage/components/beishu.vue';
export default {
  name: 'homepage',
  components: {
    IndexBanner,
    Safety,
    Technology,
    Operation,
    Beishu,
  },
  data() {
    return {
      bannerPc: require('@/assets/images/advantage/pc_ad3.jpg'),
      bannerWap: require('@/assets/images/advantage/wap_ad3.jpg'),
    };
  },
  head() {
    return {
      title: '电子合同安全_核心技术_安全资质_运营记录_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同,电子签约,电子签名,电子签章,上上签电子签约云平台',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签电子签约提供更高的安全保障.7大全球顶级安全资质认证,6大全球领先安全科技,世界500强、大型银行广泛使用,系统可用性不低于99.99%.超过1900天的安全运行记录。',
        },
      ],
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
};
</script>
<style scoped lang="scss">
.advantage4 {
  text-align: center;
}

@media screen and (max-width: 767px) {
  .homepage {
    // padding-top: 56px;
    .headline--main {
      font-size: 1.75rem;
    }
    .section-2 {
      padding-top: 10px;
      .image-link {
        flex: 1;
        width: 33.3%;
        padding: 0 5px;
      }
    }
  }
}
</style>
