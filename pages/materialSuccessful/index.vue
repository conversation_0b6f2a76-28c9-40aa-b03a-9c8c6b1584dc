<template>
  <div class="material-successful-container">
    <div class="success-title" :class="{'success-title-en':isEN}">{{$t('materialSuccess.1')}}</div>
    <div class="material-successful-await">
      <div class="thankConsult" :class="{'thankConsult-en':isEN}">{{$t('materialSuccess.2')}}</div>
      <div class="await-item">{{$t('materialSuccess.3')}}</div>
      <div class="await-item">{{$t('materialSuccess.4')}}</div>
    </div>
    <div class="gotoHome" v-if="!isEN">
      {{count}} {{$t('materialSuccess.5')}}<div @click="goHome" class="goHome">{{$t('materialSuccess.6')}} ></div>
    </div>
    <div class="gotoHome" v-if="isEN" :class="{gotoHomeEn:isEN}">
      {{$tc('materialSuccess.5',1)}} {{count}} {{$tc('materialSuccess.5',2)}}<div @click="goHome" class="goHome" :class="{'goHome-en':isEN}">{{$t('materialSuccess.6')}} ></div>
    </div>
  </div>
</template>
<script>
export default {
  layout: 'default',
  head() {
    return {
      title: this.$tc('material.t'),
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.$tc('material.k'),
        },
        {
          hid: 'description',
          name: 'description',
          content: this.$tc('material.d'),
        },
      ],
    };
  },
  data() {
    return {
      count: 10,
      timer: null,
      isEN: false,
    };
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // debugger;
      // console.log(from);
      if (from.path !== '/material') {
        vm.$router.push('/');
      }
    });
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  mounted() {
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
  methods: {
    tenGo() {
      const timeCount = 10;
      if (!this.timer) {
        this.count = timeCount;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= timeCount) {
            this.count--;
          } else {
            clearInterval(this.timer);
            this.timer = null;
            this.$router.push('/');
          }
        }, 1000);
      }
    },
    goHome() {
      this.$router.push('/');
    },
  },
  created() {
    this.tenGo();
  },
};
</script>

<style scoped lang="scss">
.material-successful-container {
  text-align: center;
  height: 100vh;
  .success-title {
    background: $-color-main;
    padding: 4rem 0;
    font-size: 2rem;
    color: #fff;
  }
  .success-title-en {
    font-weight: 600;
  }

  .material-successful-await {
    margin: 30px auto;
    color: #000;
    display: inline-block;
    text-align: left;
    .thankConsult {
      color: #000;
      padding: 3rem 0;
      font-size: 1.5rem;
    }
    .thankConsult-en {
      font-weight: 600;
    }
    .await-item {
      margin: 10px 0;
    }
  }
  .gotoHome {
    line-height: 2;
    margin-top: 30px;
    .goHome {
      text-decoration: underline;
      color: #f3c51e;
      font-weight: 700;
      cursor: pointer;
    }
    .goHome-en {
      font-weight: 600;
    }
  }
}
@media screen and (max-width: 767px) {
  .material-successful-container {
    text-align: center;
    height: 80vh;
    .success-title {
      background: $-color-main;
      padding: 4rem 0;
      font-size: 2rem;
      color: #fff;
    }

    .material-successful-await {
      margin: 30px auto;
      color: #000;
      display: inline-block;
      text-align: left;
      padding: 0 3rem;
      line-height: 1.5;
      .thankConsult {
        color: #000;
        padding: 3rem 0;
        font-size: 1.5rem;
      }
      .await-item {
        margin: 10px 0;
        font-size: 1.2rem;
      }
    }
    .gotoHome {
      font-size: 1.2rem;
    }
    .gotoHomeEn {
      margin: 0 3rem;
    }
  }
}
</style>
