<template>
    <div class="invite">
        <section>
            <img class="invite__header" src="@/assets/images/sign-invite/medal_1.png" alt="" style="height: 148px;">
            <h4> 恭喜你！</h4>
            <h4>你已经成为森林守护人</h4>
            <h5>你每签一份合同</h5>
            <div class="invite__des">
                <div>
                    <p>将减少碳排量</p>
                    <p><span>100</span>g</p>
                </div>
                <div>
                    <p>保护树木</p>
                    <p><span>0.01</span>棵</p>
                </div>
            </div>
            <div v-if="type === 'preview'" class="invite__signimg">
                <span>签署人：</span><img :src="signImageBase64" alt="">
            </div>
        </section>
        <button v-if="type === 'sign'" @click="sign">
            <span>确认签署</span><span>成为森林守护人</span>
        </button>
        <button v-else @click="invite" :class="{'preview': type === 'preview' }">
            <span>邀请好友</span><span>成为森林守护人</span>
        </button>
        <img class="invite__footer" src="@/assets/images/sign-invite//bestsign-logo.png"  alt="上上签logo">

        <van-popup
            v-model="popupShow"
            position="bottom"
            :overlay="true"
            class="invite__popup"
        >
            <div @click="invite" v-if="isCanShare">
                分享到XXX
            </div>
            <div v-else>
                <p>点击下方链接可复制，去粘贴给好友吧：</p>
                <p @click="handleCopy" :data-clipboard-text="shareLink" id="copyDom">{{ shareLink }}</p>
            </div>
            <p class="invite__popup__cancel" @click="popupShow = false">取消</p>
        </van-popup>
        <van-popup
            v-model="wxpPopupShow"
            position="top"
            :overlay="true"
            class="invite__tips"
        >
            <img src="@/assets/images/sign-invite/share_1.png" alt="">
            <p>点击右上角分享给好友</p>
        </van-popup>
    </div>
</template>

<script>
import loadScript from '@/assets/utils/loadScript.js';
import { mapState } from 'vuex';

export default {
  name: 'sign-invite-action',
  layout: 'blank',
  data() {
    return {
      popupShow: false,
      wxpPopupShow: false,
      shareLink: '',
      isCanShare: true,
      type: 'invite',
      wxconfig: {},
    };
  },
  computed: {
    ...mapState(['signImageBase64']),
  },
  watch: {
    popupShow(val) {
      if (!val) {
        setTimeout(this.goToSuccess, 500);
      }
    },
  },
  created() {
    this.type = this.$route.params.action;
  },
  async mounted() {
    if (process.client) {
      this.initWxData();
    }
    this.shareLink = `${window.location.origin}/sign-invite/action/sign`;
    document.title = '助力无纸化办公，成为森林守护人';
    await loadScript(
      'https://cdn.jsdelivr.net/npm/clipboard@2/dist/clipboard.min.js'
    );
    let clipboard = new window.ClipboardJS('#copyDom');
    const _this = this;
    clipboard.on('success', function(e) {
      e.clearSelection();
      _this.$MessageToast('已复制到剪切板～');
      _this.popupShow = false;
    });

    clipboard.on('error', function(e) {
      _this.$MessageToast('请长按进行选择复制~');
    });
  },
  methods: {
    handleInvite() {
      this.popupShow = true;
      this.isCanShare = true;
    },
    async invite() {
      const isWechat =
        navigator.userAgent.toLowerCase().match(/MicroMessenger/i) ==
        'micromessenger';

      if (isWechat) {
        this.wxpPopupShow = true;
      }
      await loadScript(
        'https://cdn.jsdelivr.net/npm/nativeshare@2.1.3/NativeShare.min.js'
      );

      // https://github.com/fa-ge/NativeShare
      let nativeShare = new window.NativeShare({
        wechatConfig: {
          appId: '',
          timestamp: '',
          nonceStr: '',
          signature: '',
        },
        syncDescToTag: false,
        syncIconToTag: false,
        syncTitleToTag: false,
      });

      // 设置分享文案
      nativeShare.setShareData({
        icon: `${window.location.origin}/h5-signinvite-share.png`,
        link: this.shareLink,
        desc: '你每签一份电子合同，为森林多添一抹绿色',
        title: '助力无纸化办公，成为森林守护人',
      });
      try {
        nativeShare.call();
        // setTimeout(this.goToSuccess, 3000);
      } catch (err) {
        // 降级处理
        this.isCanShare = false;
        this.popupShow = true;
      }
    },
    sign() {
      this.$router.push('/sign-invite/paint');
    },
    goToSuccess() {
      // this.$router.push('/sign-invite/invite/success');
    },
    initWxData() {
      const wx = require('weixin-js-sdk');
      console.log(wx);
      let apiHost =
        location.host.indexOf('info') > 0
          ? 'https://demo.bestsign.info'
          : 'https://demo.bestsign.cn';
      this.$axios
        .post(
          apiHost + '/demo/weixin/jsticket',
          {
            url: location.href.split('#')[0],
          },
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          }
        )
        .then(res => {
          let { data } = res;
          wx.config({
            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
            appId: data.appId, // 必填，公众号的唯一标识
            timestamp: data.timestamp, // 必填，生成签名的时间戳
            nonceStr: data.nonceStr, // 必填，生成签名的随机串
            signature: data.signature, // 必填，签名
            jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'], // 必填，需要使用的JS接口列表
          });
          wx.error(function(res) {
            // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
            console.log(res);
          });

          /*自定义“分享给朋友”及“分享到QQ”按钮的分享内容*/
          wx.ready(function() {
            //需在用户可能点击分按钮前就先调用
            wx.updateAppMessageShareData({
              title: '助力无纸化办公，成为森林守护人', // 分享标题
              desc: '你每签一份电子合同，为森林多添一抹绿色', // 分享描述
              link: `${window.location.origin}/sign-invite/action/sign`, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
              imgUrl: `${window.location.origin}/h5-signinvite-share.png`, // 分享图标
              success: function(obj) {
                //设置成功
                console.log(obj);
              },
              fail: function(obj) {
                console.log(obj);
              },
            });
            /*自定义“分享到朋友圈”及“分享到QQ空间”按钮的分享内容*/
            wx.updateTimelineShareData({
              title: '助力无纸化办公，成为森林守护人', // 分享标题
              desc: '你每签一份电子合同，为森林多添一抹绿色', // 分享描述
              link: `${window.location.origin}/sign-invite/action/sign`, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
              imgUrl: `${window.location.origin}/h5-signinvite-share.png`, // 分享图标
              success: function(obj) {
                //设置成功
                console.log(obj);
              },
              fail: function(obj) {
                console.log(obj);
              },
            });
          });
        })
        .catch(err => {});
    },
    // fix 移动端复制失败
    handleCopy() {},
  },
};
</script>

<style lang="scss">
html,
body,
#__nuxt,
#__layout {
  height: 100%;
}
.invite {
  position: relative;
  color: #000000;
  min-height: 100%;
  background: #f3f3f3;
  padding-top: 18px;
  section {
    padding-top: 18px;
    background: #ffffff;
    margin: 0 18px;
    border-radius: 12px;
  }
  &__header {
    width: auto;
    height: 148px;
    margin: 22px auto;
    margin-top: 0;
    display: block;
  }
  h4 {
    font-size: 16px;
    text-align: center;
    color: #666666;
  }
  h5 {
    font-size: 16px;
    text-align: center;
    color: #000000;
    font-weight: bold;
    border-top: 1px dashed #c6c6c6;
    margin: 0 18px;
    margin-top: 18px;
    padding-top: 20px;
  }
  &__des {
    padding: 15px 28px;
    color: #000000;
    font-weight: bold;
    div {
      display: inline-block;
      width: 48%;
      &:last-child {
        text-align: right;
      }
      p {
        padding-top: 8px;
        &:last-child {
          span {
            color: #00aa64;
            font-size: 36px;
            font-weight: normal;
          }
          font-weight: bolder;
        }
      }
    }
  }
  button {
    background: linear-gradient(to top, #00d07a, #00aa64);
    border-radius: 45.5px;
    width: 204px;
    height: 45px;
    padding: 4px 10px;
    border: none;
    outline: none;
    display: block;
    margin: 30px auto;
    color: #ffffff;
    &.preview {
      background: #f57323;
    }
    span {
      display: block;
      text-align: center;
      &:first-child {
        font-size: 16px;
        font-weight: bold;
      }
      &:last-child {
        font-size: 12px;
        transform: scale(0.83);
      }
    }
  }
  &__footer {
    width: 70px;
    // height: 42px;
    margin-top: 70px;
    // position: absolute;
    position: relative;
    bottom: 25px;
    left: 50%;
    transform: translateX(-50%);
  }
  &__popup {
    background: #f5f5f5;
    user-select: text;
    &__cancel {
      font-size: 14px;
      text-align: center;
      height: 44px;
      line-height: 44px;
      color: #000000;
      background: #ffffff;
    }
    div {
      padding: 12px 20px;
      p {
        margin-top: 8px;
      }
    }
    #copyDom {
      padding: 8px;
      vertical-align: center;
      border: 1px solid #c5c5c5;
    }
  }
  &__signimg {
    // border-top: 1px solid #CFCFCF;
    color: #000000;
    margin: 0 18px;
    // padding-top: 25px;
    text-align: right;
    line-height: 0;
    span {
      line-height: 60px;
      display: inline-block;
      font-size: 12px;
      vertical-align: middle;
    }
    img {
      width: auto;
      height: 60px;
      vertical-align: middle;
    }
  }
  &__tips {
    background: transparent;
    text-align: right;
    img {
      width: 70px;
      margin-right: 20px;
      margin-top: 20px;
    }
    p {
      color: #ffffff;
      font-size: 12px;
      margin-right: 50px;
      margin-top: 6px;
    }
  }
}
</style>
