<template>
    <div class="invite inivte-sucess" :class="{'inivte-sign-sucess': isSign}">
        <img src="@/assets/images/sign-invite/right-filling.png" alt="" style="height:70px; width: 70px; display: block; margin: 40px auto;">
        <h5 style="text-align: center;">签署成功</h5>
        <p>合同签署状态：<span>合同签署成功</span></p>
        <p>合同签署情况：<span>1人已签署</span></p>
        <button @click="handlePreview">查看签署文件</button>
        <img src="@/assets/images/sign-invite/banner_a_1.png" alt="" @click="goToRegister">
    </div>
</template>

<script>
export default {
  layout: 'blank',
  name: 'sign-invite-index',
  data() {
    return {
      isSign: true,
    };
  },
  methods: {
    handleClick(type) {
      if (type === 'refuse') {
        return this.$MessageToast('提交成功');
      }
      this.$http
        .post('/users/configs/AGREE_RETURN_VISIT', {
          value: true,
          name: 'AGREE_RETURN_VISIT',
        })
        .then(() => {
          this.$MessageToast('提交成功，工作人员稍后会与你联系！');
        });
    },
    handlePreview() {
      this.$router.push('/sign-invite/action/preview');
    },
    goToRegister() {
      location.href = 'https://www.bestsign.cn/h5/newcomer?utm_source=yaoqing';
    },
  },
};
</script>

<style lang="scss">
.inivte-sucess {
  button {
    margin-top: 45px;
    margin-bottom: 15px;
  }
  h5 {
    font-size: 18px;
  }
  p {
    font-size: 16px;
    color: #00aa64;
    text-align: center;
  }
  div {
    height: 150px;
    position: relative;
    i {
      color: #00aa64;
      font-size: 70px;
      position: absolute;
      top: 60px;
      left: 50%;
      transform: translateX(-50%);
      text-align: center;
      transition: all 0.3s;
    }
  }
  &.inivte-sign-sucess {
    background: #ffffff;
    h5 {
      font-size: 16px;
      margin: 20px 0;
      border-top: none;
      padding-top: 0;
    }
    p {
      color: #aaa;
      span {
        color: #000;
      }
    }
    button {
      background: transparent;
      border: 1px solid #00aa64;
      height: 44px;
      border-radius: 0;
      color: #000;
      margin: 45px auto;
      display: block;
      width: 200px;
    }
    img {
      width: 100%;
      //   margin-top: 40px;
    }
  }
}
</style>
