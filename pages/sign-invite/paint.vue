<template>
    <HandPainted
        initialBusiness="sign-demo"
        @write-done="onWriteDone"
        @close-handwrite="handleColose">
    </HandPainted>
</template>
<script>
import HandPainted from '@/components/HandPainted/index.vue';
export default {
  name: 'sign-invite-paint',
  layout: 'blank',
  components: {
    HandPainted,
  },
  data() {
    return {
      componentName: '',
    };
  },
  mounted() {
    // 动态渲染组件
    console.log('paint-process');
    console.log(process);
    if (process.client) {
      this.componentName = HandPainted;
    }
  },
  methods: {
    onWriteDone() {
      this.$router.push('/sign-invite/success');
    },
    handleColose() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss">
</style>
