<template>
  <article class="about-us text-center">
    <banner></banner>
    <cloud-services v-if="isEN"></cloud-services>
    <company-profile v-if="isJa"></company-profile>
    <about></about>
    <scene v-if="!isEN"></scene>
    <en-scene v-if="isEN"></en-scene>
    <partner></partner>
  </article>
</template>

<script>
import Banner from './section/banner.vue';
import CloudServices from './section/cloudServices.vue';
import CompanyProfile from './section/companyProfile.vue';
import About from './section/about.vue';
import Scene from './section/scene.vue';
import Partner from './section/partner';
import EnScene from './section/enScene.vue';
export default {
  name: 'AboutUs',
  // layout: 'layout_about',
  components: {
    Banner,
    CloudServices,
    CompanyProfile,
    About,
    Scene,
    Partner,
    EnScene,
  },
  data() {
    return {
      subTitle:
        '行业率先获得英国标准学会（BSI）ISO/IEC 27001信息安全管理认证、公安部信息系统安全等级保护三级认证、工信部可信云认证，获得BSI协会颁发全球首张ISO 38505-1数据治理安全证书、云计算SaaS服务能力符合性评估（ITSS）三级认证，全球范围内行业率先获得英国BSI协会ISO27018云隐保护认证，并参与制定多项行业技术标准。',
      isEN: false,
      isJa: false,
    };
  },
  head() {
    return {
      title: this.$t('aboutTDK.title'),
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: this.$t('aboutTDK.keyword'),
        },
        {
          hid: 'description',
          name: 'description',
          content: this.$t('aboutTDK.description'),
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.bestsign.cn/about-us',
        },
      ],
    };
  },

  computed: {
    isMobile() {
      return this.$store.state.isLandMobile;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: 'about-us' });
    const hash = this.$route.hash;
    if (hash && process.client) {
      const ele = document.querySelector(hash);
      ele && ele.scrollIntoView();
    }
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
    this.isJa = this.language.indexOf('ja') != -1 ? true : false;
  },
};
</script>
<style lang="scss">
@media (max-width: 768px) {
  .container {
    margin: 0 0;
  }
}
</style>
