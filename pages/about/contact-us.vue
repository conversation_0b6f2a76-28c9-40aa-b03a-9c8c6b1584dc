<template>
  <article class="contract-us">
    <section class="section section-1">
      <div class="container">
        <h1 class="article-headline">你的支持，是我们最大的动力</h1>
      </div>
      <div class="img-wrap">
        <img v-if="!isMobile" src="@/assets/images/about/<EMAIL>">
        <img v-if="isMobile" src="@/assets/images/about/<EMAIL>">
      </div>
    </section>
    <section class="section section-2">
      <div class="container">
        <h3 class="section-headline">总部和各地办公室</h3>
        <div v-if="!isMobile" class="location-wrap">
          <div class="desc">
            <div class="block">
              <p class="title">杭州总部</p>
              <p class="addr">杭州市西湖区万塘路317号华星世纪大楼102、2层、11层</p>
              <p class="tel">24小时服务热线：************</p>
            </div>
          </div>
          <div class="desc flex" >
            <div v-for="item in dist" :key="item.name" class="block">
              <p class="title">{{ item.title }}</p>
              <p>{{ item.address }}</p>
            </div>
          </div>
        </div>
        <div v-if="isMobile" class="mobile-content">
          <el-row class="line">
            <el-col :span="8" class="title">杭州</el-col>
            <el-col :span="16">
              <div class="title">杭州总部</div>
              <div class="desc">杭州市西湖区万塘路317号华星世纪大楼102、2层、11层</div>
              <div class="desc">24小时服务热线：************</div>
            </el-col>
          </el-row>
          <el-row v-for="item in dist" :key="item.name" class="line">
            <el-col :span="8" class="title">{{ item.name }}</el-col>
            <el-col :span="16">
              <div class="title">{{ item.title }}</div>
              <div class="desc">{{ item.address }}</div>
            </el-col>
          </el-row>
        </div>
      </div>
    </section>
  </article>
</template>

<script>
export default {
  name: 'ContractUs',
  // layout: 'layout_about',
  head() {
    return {
      title: '联系我们_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '上上签公司地址',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签总部位于杭州，并在北京，上海，广州，深圳和成都设立办公室',
        },
      ],
    };
  },
  data() {
    return {
      dist: [
        {
          name: '北京',
          title: '北京分公司',
          address: '北京市朝阳区乐成中心B座19层',
          phone: '+86 ************',
          time: '周一至周日0:00～24:00',
        },
        {
          name: '上海',
          title: '上海分公司',
          address: '上海市黄浦区延安东路588号东海商业中心东楼一期2层 WeWork',
          phone: '+86 ************',
          time: '周一至周日0:00～24:00',
        },
        {
          name: '广州',
          title: '广州分公司',
          address:
            '广东省广州市天河区珠江东路28号越秀金融中心大厦WeWork 65-118室',
          phone: '+86 ************',
          time: '周一至周日0:00～24:00',
        },
        {
          name: '深圳',
          title: '深圳分公司',
          address:
            '深圳市南山区海德三道（深圳湾段）166 号航天科技广场 B 座 4 楼 A01 & A03',
          phone: '+86 ************',
          time: '周一至周日0:00～24:00',
        },
        {
          name: '成都',
          title: '成都分公司',
          address: '成都市高新区万象南路669号佳辰国际中心20楼',
          phone: '+86 ************',
          time: '周一至周日0:00～24:00',
        },
        // {
        //   name: '苏州',
        //   title: '苏州办公室',
        //   address: '苏州工业园区星湖街328号1栋A座4F单元',
        //   phone: '+86 ************',
        //   time: '周一至周日0:00～24:00',
        // },
        // {
        //   name: '西安',
        //   title: '西安办公室',
        //   address: '西安市高新区高新二路新世纪大厦五楼516室',
        //   phone: '+86 ************',
        //   time: '周一至周日0:00～24:00',
        // },
        // {
        //   name: '武汉',
        //   title: '武汉办公室',
        //   address: '武汉市江岸区京汉大道1268号',
        //   phone: '+86 ************',
        //   time: '周一至周日0:00～24:00',
        // },
        // {
        //   name: '宁波',
        //   title: '宁波办公室',
        //   address: '宁波市鄞州区FSV金融硅谷7号楼3楼',
        //   phone: '+86 ************',
        //   time: '周一至周日0:00～24:00',
        // },
        // {
        //   name: '厦门',
        //   title: '厦门新业务部',
        //   address: '厦门市思明区观音山台东路66号宝业大厦19楼1911室',
        //   phone: '+86 ************',
        //   time: '周一至周日0:00～24:00',
        // },
        // {
        //   name: '福州',
        //   title: '福州新业务部',
        //   address: '福州市台江区上浦路南侧侧富力中心C1栋916单元',
        //   phone: '+86 ************',
        //   time: '周一至周日0:00～24:00',
        // },
      ],
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: 'contract' });
  },
};
</script>

<style scoped lang="scss">
.contract-us {
  text-align: center;
  .section-1 {
    position: relative;
    padding: 0;
    .img-wrap,
    img {
      width: 100%;
    }
    .container {
      width: 100%;
      position: absolute;
      left: 50%;
      top: 150px;
      transform: translate3d(-50%, 0, 0);
    }
  }
  .section-2 {
    .location-wrap {
      padding-top: 8rem;
      .desc {
        text-align: left;
        width: 80%;
        margin: 0 auto 4rem;
        &.flex {
          display: flex;
          flex-wrap: wrap;
          .block {
            width: 50%;
            margin-bottom: 2rem;
          }
        }
        .block {
          padding-left: 4rem;
        }
        .title {
          font-size: 18px;
          margin-bottom: 1.5rem;
          color: #323232;
        }
        p {
          margin: 5px 0;
          color: #888;
          font-size: 14px;
        }
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .contract-us {
    .section-headline {
      margin-bottom: 30px;
    }
    .section-subtitle {
      font-size: 12px;
    }
    .section-1 {
      .container {
        top: 50px;
        width: 100%;
      }
      .article-headline {
        font-size: 28px;
        line-height: 40px;
      }
    }
    .mobile-content {
      .line {
        margin-bottom: 2.75rem;
        .title {
          font-size: 15px;
        }
        .desc {
          color: #888;
          padding: 8px 0;
        }
      }
      .el-col-16 {
        padding-right: 3rem;
        text-align: left;
      }
    }
  }
}
</style>
