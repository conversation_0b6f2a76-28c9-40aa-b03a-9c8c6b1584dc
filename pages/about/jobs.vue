<template>
  <article class="jobs">
    <section class="section section-1">
		<div class="job-block" v-for="(parentItem,parentIndex) in jobList" :key="parentIndex">
			<h3 class="section-headline" >{{parentItem.type}}</h3>
			<el-collapse accordion>
				<el-collapse-item v-for="(item, index) in parentItem.list" :key="index">
					<template slot="title" >
						<span>{{item.jobTitle}}</span>
					</template>
					<div class="job-content">
						<p class="location">
						<span style="color: #00a664;">地点：</span>
						<span>{{ item.city && format(JSON.parse(item.city)) }}</span>
						</p>
						<div class="detail">
						<h4>岗位职责</h4>
						<div>{{ item.jobDuty }}</div>
						<h4>任职资格</h4>
						<div>{{ item.jobRequirement }}</div>
						</div>
						<p>
						<span style="color: #00a664;">投递方式：</span>
						<span>{{ item.resumePostType }}</span>
						</p>
					</div>
				</el-collapse-item>
			</el-collapse>
		</div>
    </section>
  </article>
</template>

<script>
import { mapActions } from 'vuex';
export default {
  name: 'Jobs',
  // layout: 'layout_about',
  head() {
    return {
      title: '招聘信息_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '上上签招聘,上上签团队',
        },
        {
          hid: 'description',
          name: 'description',
          content: '上上签招聘信息，我们的团队期待你的加入。',
        },
      ],
    };
  },
  data() {
    return {
      list: {
        job_1: [],
        job_2: [],
        job_3: [],
        job_4: [],
        job_5: [],
        job_6: [],
        job_7: [],
        job_8: [],
        job_9: [],
        job_10: [],
        job_11: [],
        job_12: [],
      },
      item: {},
      visible: false,
      jobList: [
        {
          type: '产品技术',
          list: [],
        },
        {
          type: '客户成功',
          list: [],
        },
        {
          type: '销售',
          list: [],
        },
        {
          type: '市场',
          list: [],
        },
        {
          type: 'HR行政',
          list: [],
        },
        {
          type: '法务/政府关系',
          list: [],
        },
        {
          type: '校园招聘',
          list: [],
        },
      ],
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    width() {
      return this.isMobile ? '90%' : '30%';
    },
  },
  mounted() {
    this.getRecruit().then(res => {
      res.forEach(item => {
        if (item.recruitType === 'social') {
          switch (item.jobType) {
            case 'tech':
              this.list.job_1.push(item);
              break;
            case 'success':
              this.list.job_2.push(item);
              break;
            case 'sale':
              this.list.job_3.push(item);
              break;
            case 'market':
              this.list.job_4.push(item);
              break;
            case 'hr':
              this.list.job_5.push(item);
              break;
            case 'legal':
              this.list.job_6.push(item);
              break;
          }
        } else {
          // switch (item.jobType) {
          //   case 'tech':
          //     this.list.job_7.push(item);
          //     break;
          //   case 'success':
          //     this.list.job_8.push(item);
          //     break;
          //   case 'sale':
          //     this.list.job_9.push(item);
          //     break;
          //   case 'market':
          //     this.list.job_10.push(item);
          //     break;
          //   case 'hr':
          //     this.list.job_11.push(item);
          //     break;
          //   case 'legal':
          //     this.list.job_12.push(item);
          //     break;
          // }
          this.list.job_7.push(item);
        }
      });
    });
    this.jobList.map((item, index) => {
      item.list = this.list['job_' + (index + 1)];
    });
  },
  methods: {
    ...mapActions(['getRecruit']),
    handleScroll(element) {
      this.$scrollTo(element, 500, {
        offset: -150,
      });
    },
    format(array) {
      const cities = {
        hz: '杭州',
        bj: '北京',
        sz: '深圳',
        sh: '上海',
      };
      return array.map(item => cities[item]).toString();
    },
  },
};
</script>

<style scoped lang="scss">
.jobs {
  width: 100%;
  text-align: center;
  .section-1 {
    background-color: #f7f7f7;
    /deep/ .el-collapse-item__header {
      padding: 0 10px;
    }
  }
  .section-2 {
    padding-bottom: 0;
    .section-subtitle {
      padding-bottom: 0;
    }
    .container {
      width: 930px;
      margin: 0 auto;
      padding-bottom: 100px;
      border-bottom: 1px solid #bfbfbf;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-around;
      padding: 0;
    }
    .item {
      margin-bottom: 55px;
      width: calc(100% / 3);
      text-align: center;
      cursor: pointer;
      .img {
        width: 159px;
        height: 159px;
        border-radius: 159px;
        margin: 0 auto 30px;
        background-image: url('~assets/images/about/<EMAIL>');
      }
      .img-2 {
        background-position: -161px 0;
      }
      .img-3 {
        background-position: -322px 0;
      }
      .img-4 {
        background-position: 0 -161px;
      }
      .img-5 {
        background-position: -161px -161px;
      }
      .img-6 {
        background-position: -322px -161px;
      }
    }
  }
  .section-other {
    .container {
      width: 930px;
      margin: 0 auto;
      .section-headline {
        color: #00a664;
      }
      .img {
        display: inline-block;
        margin: 100px auto 150px;
        width: 100%;
        height: 240px;
        background-image: url('~assets/images/about/<EMAIL>');
      }
      .img-1 {
        background-position: 0 -330px;
      }
      .img-2 {
        background-position: 0 -570px;
      }
      .img-3 {
        background-position: 0 -810px;
      }
      .img-4 {
        background-position: 0 -1050px;
      }
      .img-5 {
        background-position: 0 -1290px;
      }
      .img-6 {
        background-position: 0 -1530px;
      }
      .position-wrap {
        display: flex;
        flex-wrap: wrap;
        font-size: 18px;
        .position {
          width: calc(100% / 3);
          margin-bottom: 80px;
          cursor: pointer;
          &:hover {
            color: #00a664;
          }
        }
        &.school {
          border-top: 1px solid #bfbfbf;
          padding-top: 80px;
        }
      }
    }
  }
  .job-content {
    text-align: left;
    padding: 0 20px;
    .location {
      margin-top: 15px;
      font-size: 14px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e5e5e5;
    }
    .detail {
      font-size: 14px;
      padding-bottom: 30px;
      border-bottom: 1px solid #e5e5e5;
      margin-bottom: 15px;
      h4 {
        margin-top: 25px;
        margin-bottom: 10px;
        font-size: 16px;
      }
    }
  }
}
@media screen and (max-width: 767px) {
  .jobs {
    .article-headline {
      font-size: 28px;
    }
    .section-2 {
      .container {
        width: 100%;
        padding-bottom: 50px;
        .item .img {
          transform: scale(0.5);
          position: relative;
          left: -20px;
        }
      }
    }
    .section-other {
      .container {
        width: 100%;
        .position-wrap.school {
          padding-top: 40px;
        }
        .img {
          height: 90px;
          background-size: 100%;
          margin: 40px auto;
        }
        .img-1 {
          background-position: 0 -127px;
        }
        .img-2 {
          background-position: 0 -218px;
        }
        .img-3 {
          background-position: 0 -309px;
        }
        .img-4 {
          background-position: 0 -401px;
        }
        .img-5 {
          background-position: 0 -493px;
        }
        .img-6 {
          background-position: 0 -584px;
        }
      }
    }
  }
}
</style>
