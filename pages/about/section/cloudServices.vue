<!-- 云服务 -->
<template>
  <article class="cloud-services">
    <section class="section section-1">
      <div class="container">
        <h2 class="section-headline" :class="{'section-headline-en':isEN}">{{$t('aboutUS.cloudTitle')}}</h2>
        <div class="cloud-services-content" :class="{'cloud-services-content-en':isEN}">
          <div class="cloud-services-desc-p" :class="{'cloud-services-desc-en':isEN}">{{$t('aboutUS.cloudDesc1')}}</div>
          <div class="cloud-services-img" v-if="!isEN">
            <img src="https://static.bestsign.cn:443/53ad5132f7d653ecc8ea94ee201cf29dab9d6d8d.png" alt="">
          </div>
          <div class="cloud-services-desc-b" :class="{'cloud-services-desc-en':isEN}">{{$t('aboutUS.cloudDesc2')}}</div>
          
        </div>
      </div>
    </section>
  </article>
</template>

<script>
//import x from ''
export default {
  components: {},
  data() {
    return {
      isEN: false,
    };
  },
  computed: {
    language() {
      return this.$store.state.locale;
    },
  },
  methods: {},
  mounted() {
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>

<style lang='scss' scoped>
.cloud-services {
  .section-1 {
    padding: 1.5rem 0 5rem;
    background: #f5f5f7;
    .container {
      .cloud-services-content {
        margin: 0 auto;
        width: 80%;
        .cloud-services-desc-p {
          margin: 0 0 3rem;
          line-height: 1.5;
        }
        .cloud-services-desc-b {
          margin: 3rem 0 0;
          line-height: 1.5;
        }
        .cloud-services-desc-en {
          margin: 10px 0;
          font-size: 1.2rem;
        }
        .cloud-services-img {
          img {
            width: 70%;
          }
        }
      }
      .cloud-services-content-en {
        text-align: left;
      }
    }
    .section-headline-en {
      padding-bottom: 2rem;
    }
  }
}
@media screen and (max-width: 768px) {
  .cloud-services {
    .section-1 {
      padding: 1.5rem 0 5rem;
      .container {
        // position: static !important;
        .cloud-services-content {
          width: 100%;
          .cloud-services-desc-p {
            margin: 0 0 3rem;
            line-height: 1.5;
            text-align: left;
          }
          .cloud-services-desc-b {
            margin: 3rem 0 0;
            line-height: 1.5;
            text-align: left;
          }
          .cloud-services-desc-en {
            font-size: 1.3rem;
          }
          .cloud-services-img {
            img {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
</style>
