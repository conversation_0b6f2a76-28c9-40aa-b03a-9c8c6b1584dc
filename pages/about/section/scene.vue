<!-- 版本介绍 -->
<template>
  <article class="about-scene">
    <section class="section section-1">
      <div class="container" v-if="!isMobile">
        <h2 class="section-headline">{{$t('aboutUS.secene')}}</h2>
        <div class="scene-container">
          <div class="scene-content" v-for="(item,index) in sceneType1" :key="index">
            <div class="title"><i :class="['iconfont ',item.iconfont]"></i> {{item.title}}</div>
            <div class="scene-con">
              <div class="scene" v-for="(i,index1) in item.function" :key="index1"><i class="iconfont icon-dian"></i> {{i}}</div>
            </div>
          </div>
        </div>
        <div class="scene-container">
          <div class="scene-content" v-for="(item,index) in sceneType2" :key="index">
            <div class="title"><i :class="['iconfont ',item.iconfont]"></i> {{item.title}}</div>
            <div class="scene-con">
              <div class="scene" v-for="(i,index1) in item.function" :key="index1"><i class="iconfont icon-dian"></i> {{i}}</div>
            </div>
          </div>
        </div>
        <div class="scene-container-other">
          <div class="scene-content-other" v-for="(item,index) in sceneType3" :key="index">
            <div class="title"><i :class="['iconfont ',item.iconfont]"></i> {{item.title}}</div>
            <div class="scene-con-other">
              <div class="scene-centent-other">
                <div class="scene" v-for="(i,index1) in other1" :key="index1"><i class="iconfont icon-dian"></i> {{i}}</div>
              </div>
              <div class="scene-centent-other">
                <div class="scene" v-for="(i,index1) in other2" :key="index1"><i class="iconfont icon-dian"></i> {{i}}</div>
              </div>
              <div class="scene-centent-other">
                <div class="scene" v-for="(i,index1) in other3" :key="index1"><i class="iconfont icon-dian"></i> {{i}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="container" v-if="isMobile">
        <h2 class="section-headline">BestSign电子签约场景</h2>
        <div class="scene-container">
          <div class="scene-content" v-for="(item,index) in sceneType" :key="index">
            <div class="title"><i :class="['iconfont ',item.iconfont]"></i> {{item.title}}</div>
            <div class="scene-con">
              <div class="scene" v-for="(i,index1) in item.function" :key="index1"><i class="iconfont icon-dian"></i> {{i}}</div>
            </div>
          </div>
        </div>
      </div>
    </section> 
  </article>
</template>

<script>
//import x from ''
export default {
  components: {},
  data() {
    return {
      sceneType: [
        {
          iconfont: 'icon-renshi1',
          title: this.$t('aboutUS.seceneTitle1'),
          lineNumber: 1,
          function: [
            this.$t('aboutUS.seceneDesc11'),
            this.$t('aboutUS.seceneDesc12'),
            this.$t('aboutUS.seceneDesc13'),
            this.$t('aboutUS.seceneDesc14'),
            this.$t('aboutUS.seceneDesc15'),
          ],
        },
        {
          iconfont: 'icon-maimai',
          title: this.$t('aboutUS.seceneTitle2'),
          lineNumber: 1,
          function: [
            this.$t('aboutUS.seceneDesc21'),
            this.$t('aboutUS.seceneDesc22'),
            this.$t('aboutUS.seceneDesc23'),
            this.$t('aboutUS.seceneDesc24'),
          ],
        },
        {
          iconfont: 'icon-zulin',
          title: this.$t('aboutUS.seceneTitle3'),
          lineNumber: 1,
          function: [
            this.$t('aboutUS.seceneDesc31'),
            this.$t('aboutUS.seceneDesc32'),
            this.$t('aboutUS.seceneDesc33'),
            this.$t('aboutUS.seceneDesc34'),
          ],
        },
        {
          iconfont: 'icon-maimai1',
          title: this.$t('aboutUS.seceneTitle6'),
          lineNumber: 2,
          function: [
            this.$t('aboutUS.seceneDesc61'),
            this.$t('aboutUS.seceneDesc62'),
            this.$t('aboutUS.seceneDesc63'),
            this.$t('aboutUS.seceneDesc64'),
            this.$t('aboutUS.seceneDesc65'),
            this.$t('aboutUS.seceneDesc66'),
          ],
        },
        {
          iconfont: 'icon-jiedai',
          title: this.$t('aboutUS.seceneTitle4'),
          lineNumber: 2,
          function: [
            this.$t('aboutUS.seceneDesc41'),
            this.$t('aboutUS.seceneDesc42'),
            this.$t('aboutUS.seceneDesc43'),
            this.$t('aboutUS.seceneDesc44'),
          ],
        },
        {
          iconfont: 'icon-weituo',
          title: this.$t('aboutUS.seceneTitle5'),
          lineNumber: 2,
          function: [
            this.$t('aboutUS.seceneDesc51'),
            this.$t('aboutUS.seceneDesc52'),
            this.$t('aboutUS.seceneDesc53'),
            this.$t('aboutUS.seceneDesc54'),
            this.$t('aboutUS.seceneDesc55'),
            this.$t('aboutUS.seceneDesc56'),
            this.$t('aboutUS.seceneDesc57'),
            this.$t('aboutUS.seceneDesc58'),
          ],
        },

        {
          iconfont: 'icon-qita',
          title: this.$t('aboutUS.seceneTitle7'),
          lineNumber: 3,
          function: [
            this.$t('aboutUS.seceneDesc71'),
            this.$t('aboutUS.seceneDesc73'),
            this.$t('aboutUS.seceneDesc74'),
            this.$t('aboutUS.seceneDesc75'),
            this.$t('aboutUS.seceneDesc76'),
            this.$t('aboutUS.seceneDesc77'),
            this.$t('aboutUS.seceneDesc78'),
            this.$t('aboutUS.seceneDesc79'),
            this.$t('aboutUS.seceneDesc710'),
            this.$t('aboutUS.seceneDesc711'),
          ],
        },
      ],
      sceneType1: [],
      sceneType2: [],
      sceneType3: [],
      other1: [],
      other2: [],
      other3: [],
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {
    filterData() {
      this.sceneType1 = this.sceneType.filter(
        (item, index) => item.lineNumber === 1
      );
      this.sceneType2 = this.sceneType.filter(
        (item, index) => item.lineNumber === 2
      );
      this.sceneType3 = this.sceneType.filter(
        (item, index) => item.lineNumber === 3
      );
      this.other1 = this.sceneType3[0].function.filter(
        (item, index) => index <= 3
      );
      this.other2 = this.sceneType3[0].function.filter(
        (item, index) => index > 3 && index <= 7
      );
      this.other3 = this.sceneType3[0].function.filter(
        (item, index) => index > 7
      );
    },
  },
  mounted() {},
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
  created() {
    this.filterData();
  },
};
</script>

<style lang='scss' scoped>
//@import url()
.about-scene {
  .section-1 {
    background: #fff;
    padding: 1.5rem 0 5rem;
    .container {
      .scene-container {
        display: flex;
        justify-content: space-around;
        width: 80%;
        margin: 0rem auto;
        .scene-content {
          margin: 1%;
          width: 34%;
          cursor: pointer;
          background: #f5f5f7;
          padding: 50px 30px;
          border-radius: 10px;
          text-align: left;
          .title {
            text-align: left;
            margin-bottom: 2rem;
            font-size: 1.5rem;
            font-weight: 500;
            color: #000;
            .iconfont {
              color: $-color-main;
              font-size: 2rem;
            }
          }
          .scene-con {
            .scene {
              line-height: 2;
              align-items: center;
              color: #4a5a6f;
              .iconfont {
                color: $-color-main;
                font-weight: 800;
                font-size: 20px;
              }
            }
          }
        }
      }
      .scene-container-other {
        width: 80%;
        margin: 0 auto;
        .scene-content-other {
          margin: 1%;
          cursor: pointer;
          background: #f5f5f7;
          // padding: 50px 30px;
          border-radius: 10px;
          .title {
            text-align: left;
            font-size: 1.5rem;
            font-weight: 500;
            color: #000;
            padding: 50px 30px 2rem;
            .iconfont {
              color: $-color-main;
              font-size: 2rem;
            }
          }
          .scene-con-other {
            display: flex;
            .scene-centent-other {
              margin: 1%;
              width: 34%;
              padding: 0 30px 50px;
              &:first-child {
                margin-left: 0;
              }
              .scene {
                line-height: 2;
                display: flex;
                align-items: center;
                color: #4a5a6f;
                .iconfont {
                  color: $-color-main;
                  font-weight: 800;
                  font-size: 20px;
                }
              }
            }
          }
        }
      }
      .bottom-desc {
        padding: 50px 0;
        text-align: center;
      }
    }
  }
}
@media (max-width: 768px) {
  .about-scene {
    .section-1 {
      .container {
        max-width: 100%;
        width: 100%;
        .scene-container {
          display: block;
          width: 100%;
          .scene-content {
            margin: 20px 0;
            width: 100%;
            .scene-con {
              font-size: 1.2rem;
            }
          }
        }
        .bottom-desc {
          line-height: 1.5;
        }
      }
    }
  }
}
</style>
