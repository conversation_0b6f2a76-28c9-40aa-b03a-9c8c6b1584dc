<template>
  <div class="index-evaluate section">
    <div class="container">
      <h2 class="section-headline headline--main">多次得到司法机构事实认证</h2>
      <p class="little-title">行业标准参与制定者。</p>
      <div class="content">
        <div class="swiper-container" v-swiper:mySwiper="isMobile?swiperOptionMobile:swiperOption">
          <div class="swiper-wrapper">
				<div class="swiper-slide" v-for="(item, index) in data" :key="item.desc">
					<div class="item">
						   <div class="top">
								<!-- <div :class="['default-avatar', activeIndex === index? 'active-avatar':'']" :style="{backgroundImage: `url(${item.logo})`}"> -->
								<div :class="['default-avatar', activeIndex === index? 'active-avatar':'']">
									<img :src="item.logo" alt="" width="100%" height="100%">

								</div>
						   </div>
					</div>
					<div v-if="isMobile">
            <div class="name">
							<p class="label">{{ partnerInfo.desc }}</p>
						</div>
						<div class="main-content">{{ partnerInfo.content }}</div>
					</div>
				</div>
          </div>
        </div>
        <div class="arrow" v-if="!isMobile">
          <div class="ssq-button-prev-a">
            <i class="el-icon-arrow-left"></i>
          </div>
          <div class="ssq-button-next-a">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
      <div class="name" v-if="!isMobile">
			<p class="label">{{ partnerInfo.desc }}</p>
		</div>
	 	<div class="main-content" v-if="!isMobile">{{ partnerInfo.content }}</div>
      <!-- <div v-if="!demo" class="ssq-button-more transparent" @click="toCase">进一步了解我们的合作伙伴 <i class="iconfont icon-xiangyoujiantou"></i></div> -->
      <!-- <div v-if="demo" class="ssq-button-more" style="color: #fff" @click="toDemo">免费试用 ></div> -->
	<div v-if="isMobile" class="swiper-pagination swiper-pagination__evaluate" slot="pagination"></div>
    </div>
  </div>
</template>

<script>
const data = [
  {
    logo:
      'https://static.bestsign.cn/79e7d0b8166e30806c371159245f0f0dd33be6c4.png',
    desc: '好丽友 中国区法务与知识产权负责人',
    content:
      '“好丽友选择与上上签携手，一方面因为上上签的技术出众，产品体验好；另一方面也因为上上签积累了非常多的大客户服务经验。通过上上签强大的实名认证体系，好丽友与经销商签署协议更加合法合规，同时也实现了合同的智能化管理以及内部数据的系统整合，从而提升了渠道管理效率。 后续，我们希望与上上签继续深化合作，积极拓展在人力资源管理、物流等更多场景的应用。”',
  },
  {
    logo:
      'https://static.bestsign.cn/37dd0bf91bfa223b14c3c994187e050cc354352f.png',
    desc: '贝壳找房 人力资源共享服务中心高级经理',
    content:
      '“贝壳找房由链家网升级而来，是以技术驱动的品质居住服务平台，聚合和赋能全行业的优质服务者，致力于为两亿家庭提供包括二手房、新房、租赁和装修全方位居住服务。目前公司在员工入职新签合同、合同的续改签、辞职书的签署均通过上上签平台实现了电子签署。合同管理的效能提升非常明显。”',
  },
  {
    logo:
      'https://static.bestsign.cn/79e7d0b8166e30806c371159245f0f0dd33be6c4.png',
    desc: '丹鸟 法务高级专家',
    content:
      '“上上签无论在产品功能还是服务能力上都业界领先，尤其在物流领域积累了很多头部客户的服务经验，这是丹鸟最终选择与上上签携手合作的原因。通过上上签，丹鸟与快递、仓配、驿站等业务线上的众多生态合作伙伴，可以更高效地完成各类合同文件的电子化签署，从而打破数据孤岛，实现产业链多方协同。对消费者而言，物流服务的整体效率大为提高，体验也更加优质。”',
  },
];
export default {
  name: 'judicialApprove',
  props: {
    demo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeIndex: 2,
      data,
      swiperOption: {
        loop: true,
        initialSlide: 2,
        centeredSlides: true,
        speed: 700,
        slidesPerView: 3,
        navigation: {
          nextEl: '.ssq-button-next-a',
          prevEl: '.ssq-button-prev-a',
        },
      },
      swiperOptionMobile: {
        loop: true,
        initialSlide: 0,
        slidesPerView: 1,
        speed: 700,
        pagination: {
          el: '.swiper-pagination.swiper-pagination__evaluate',
        },
      },
      partnerInfo: {
        content: '',
        name: '',
        desc: '',
      },
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {
    toCase() {
      this.$router.push('/evaluate');
    },
    toDemo() {
      if (this.demo) {
        this.$scrollTo('body');
        return;
      }
      const query = sessionStorage.getItem('query');
      const url = location.href;
      if (this.isMobile) {
        _hmt.push(['_trackEvent', 'click_demo_mobile', 'click', url]);
      } else {
        _hmt.push(['_trackEvent', 'click_demo_pc', 'click', url]);
      }
      // const path = this.isMobile ? '/demo/mobile?email=yes' : '/demo?email=yes';
      this.$router.push({
        path: '/demo',
        query: {
          ...Qs.parse(query),
        },
      });
    },
    handleChange(cur) {
      const activeIndex = cur.realIndex;
      this.activeIndex = activeIndex;
      this.getInfo();
      console.log(111);
    },
    getInfo() {
      this.$nextTick(() => {
        this.partnerInfo = data.filter(
          (item, index) => index == this.activeIndex
        )[0];
        console.log(this.partnerInfo);
      });
    },
  },
  mounted() {
    const _this = this;
    this.mySwiper.on('slideChange', function() {
      _this.handleChange(this);
    });
    this.getInfo();
  },
};
</script>

<style scoped lang="scss">
.index-evaluate {
  padding-top: 2rem;
  padding-bottom: 2rem;
  background-color: #f2f2f2;
  text-align: center;
  .little-title {
    line-height: 3rem;
  }
  .content {
    width: 70%;
    position: relative;
    margin: 3rem auto;
    .swiper-container {
      max-width: 900px;
    }
    .item {
      width: 100%;
      background-color: #f2f2f2;
      padding: 20px 0px;
      .top {
        display: flex;
        align-items: center;
        // img {
        // width: 500px;
        // margin-right: 30px;
        // border-radius: 50%;
        // border: 1px solid #eee;
        // }
        .default-avatar {
          margin: 0 auto;
          img {
            // height: 90px;
            // border-radius: 50%;
            width: 70%;
          }
        }

        .name {
          text-align: left;
          font-size: 20px;
          line-height: 1.3;
        }
      }
    }
    .swiper-slide-active .default-avatar img {
      -ms-transform: scale(1.4); /* IE 9 */
      -webkit-transform: scale(1.4); /* Safari */
      transform: scale(1.4); /* 标准语法 */
      transition: transfrom 0.4s;
    }
    .swiper-slide-prev {
      .item {
        padding: 20px 0;
        .default-avatar {
          margin-left: 0;
        }
      }
    }
    .swiper-slide-next {
      .item {
        padding: 20px 0px;
        transition: all 0.5s;
        .default-avatar {
          margin-right: 0;
          transition: all 0.5s;
        }
      }
    }
  }
  .main-content {
    max-width: 40rem;
    text-align: center;
    text-align: justify;
    line-height: 1.75;
    margin: 0 auto;
    // margin-top: 1.5rem;
    // color: #717171;
    // min-height: 8.75rem;
  }
  .name {
    margin-top: 2.5rem;
    .title {
      font-size: 20px;
      font-weight: 600;
    }
    .label {
      font-size: 18px;
      margin: 1rem 0 2rem 0;
    }
  }
  .ssq-button-more {
    color: #00aa64;
  }
  .ssq-button-prev-a,
  .ssq-button-next-a {
    position: absolute;
    top: 50%;
    transform: translate3d(0, -50%, 0);
    background: transparent;
    cursor: pointer;
    > i {
      font-size: 46px;
      color: rgba(35, 24, 21, 0.5);
      outline: none;
      &:hover {
        color: rgba(35, 24, 21, 1);
      }
      &:focus {
        outline: none;
      }
    }
    &:focus {
      outline: none;
    }
  }
  .ssq-button-prev-a {
    left: -60px;
  }
  .ssq-button-next-a {
    right: -60px;
  }
  .ssq-button-primary {
    display: inline-block;
  }
}
.swiper-pagination {
  width: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 20px;
  /deep/ .swiper-pagination-bullet {
    margin: 0 10px;
  }
  /deep/ .swiper-pagination-bullet-active {
    background: #62686f;
  }
}
@media screen and (max-width: 767px) {
  .index-evaluate {
    .content {
      position: relative;
      width: 100%;
      padding: 0 10px;
      .swiper-slide-active .default-avatar img {
        -ms-transform: scale(1); /* IE 9 */
        -webkit-transform: scale(1); /* Safari */
        transform: scale(1); /* 标准语法 */
        transition: transfrom 0.4s;
      }

      .item {
        width: 100%;
        padding: 20px 15px;
        .top {
          img {
            width: 60px;
            margin-right: 20px;
            border-radius: 50%;
            border: 1px solid #eee;
          }
          .name {
            text-align: left;
            font-size: 16px;
            line-height: 1.3;
          }
        }
        .main-content {
          text-align: justify;
          line-height: 1.75;
          margin-top: 1.5rem;
          color: #717171;
        }
      }
    }

    .ssq-button-prev-a,
    .ssq-button-next-a {
      > i {
        font-size: 22px;
      }
    }
    .ssq-button-prev-a {
      left: -12px;
    }
    .ssq-button-next-a {
      right: -12px;
    }
  }
}
</style>
