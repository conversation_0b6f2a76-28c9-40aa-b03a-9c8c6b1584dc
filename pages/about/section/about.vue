<template>
<article class="about-us-number">
  <section class="section section-1">
    <div class="container">
      <div class="content flex bc">
       <div class="about">
		   <div class="main-content">
			<div class="littleTitle" :class="{'littleTitle-en':isEN}">
				<div class="item">
				<div class="top">{{$tc('aboutDesc.num1')}}</div>
				<div class="bottom">{{$t('aboutDesc.company')}}</div>
				</div>
				<div class="item">
				<div class="top">{{$tc('aboutDesc.num2')}}</div>
				<div class="bottom">{{$t('aboutDesc.percentage')}}</div>
				</div>
        <div class="item">
				<div class="top">{{$tc('aboutDesc.num3')}}</div>
				<div class="bottom">{{$tc('aboutDesc.signNum',2)}}</div>
				</div>
        <div class="item">
				<div class="top">{{$tc('aboutDesc.num4')}}</div>
				<div class="bottom">{{$tc('aboutDesc.signNum',1)}}</div>
				</div>
			</div>
    	</div>
	   </div>
      </div>
      <div class="time-con" v-if="!isMobile" :class="{'time-con-en':isEN}">
        <div class="time">{{$t('aboutDesc.time')}}</div>
      </div>
    </div>
  </section>
</article>
</template>

<script>
export default {
  name: 'about-about',
  data() {
    return {
      isEN: false,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  mounted() {
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
};
</script>

<style scoped lang="scss">
@import '../aboutUs';
.about-us-number {
  .section-1 {
    padding: 0;
    background: $-color-main;
    .container {
      max-width: 100%;
      width: 100%;
      .about {
        width: 100%;
        margin: 30px auto;
        .littleTitle {
          display: flex;
          flex-wrap: nowrap;
          margin: 3.1rem 0 0.9rem;
          justify-content: space-between;
          .item {
            margin: 1rem;
            text-align: center;
            .top {
              font-size: 2.5rem;
              color: #fff;
              font-weight: 500;
              span {
                // vertical-align: super;
                // color: #fff;
                // font-size: 1rem;
                // margin-left: 5px;
              }
            }
            .bottom {
              font-size: 14px;
              line-height: 2rem;
              max-width: 300px;
              margin-top: 10px;
              color: #fff;
            }
          }
        }
        .littleTitle-en {
          .item {
            .top {
              font-weight: 600;
              span {
              }
            }
            .bottom {
              font-size: 1.1rem;
              line-height: 1.5;
            }
          }
        }
      }
      .time-con {
        width: 80%;
        margin: 0 auto;
        .time {
          padding: 1rem 1rem 2rem;
          text-align: center;
          color: #fff;
          font-size: 0.8rem;
        }
      }
      .time-con-en {
        .time {
          font-size: 1.1rem;
        }
      }
    }
  }
}

@media only screen and (max-width: 767px) {
  .about-us-number {
    .section-1 {
      padding-top: 0;
      .container {
        width: 100%;
        .about {
          .littleTitle {
            display: flex;
            flex-wrap: nowrap;
            margin: 2rem 0;
            justify-content: space-between;
            flex-wrap: wrap;
            .item {
              margin: 1rem;
              text-align: left;
              .top {
                font-size: 2.5rem;
                color: #fff;
              }
              .bottom {
                font-size: 1.3rem;
                line-height: 2rem;
                max-width: 300px;
                margin-top: 5px;
                color: #fff;
              }
            }
          }
        }
        .time-con {
        }
      }
    }
  }
}
</style>
