<template>
  <article class="about-us">
    <section class="section section-1">
      <div class="container">
        <div class="container-left">
          <div class="title-con" :class="{'title-con-en':isEN}">
            <h1 class="article-headline">{{$t('aboutUS.bannerTitle1')}}</h1>
            <h1 class="article-headline">{{$t('aboutUS.bannerTitle2')}}</h1>
        <p class="article-subtitle">{{$t('aboutUS.bannerDesc')}}</p>
          </div>
          
        </div>
        <div class="container-right" v-if="!isMobile">
          <img src="https://static.bestsign.cn:443/4a031cfa928cfde46ae2b403711f2928f4db6171.jpg"/>
        </div>
        <div class="container-bottom" v-if="isMobile">
          <img src="https://static.bestsign.cn:443/b65ed16892db3c4380120dd66fbb7b01cabb93f1.jpg"/>
        </div>
      </div>
    </section>

    <el-dialog
      visible.sync="visible"
    >
      {{ results.msg }}
    </el-dialog>
  </article>
</template>

<script>
import { MessageBox } from 'element-ui';
import Qs from 'qs';
export default {
  name: 'ProductCheck',
  data() {
    return {
      visible: false,
      isSuccess: false,
      results: {},
      signResults: [],
      isEN: false,
    };
  },
  head() {
    return {};
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: 'product' });
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
  methods: {},
};
</script>

<style scoped lang="scss">
.about-us {
  text-align: center;
  .section-1 {
    padding: 0;
    .container {
      width: 100%;
      max-width: 100%;
      display: flex;
      align-items: flex-start;
      .container-left {
        width: 50%;
        padding-top: 5%;
        .title-con {
          margin: 0 10%;
          text-align: left;
          .article-headline {
            font-weight: 500;
            font-size: 3rem;
            margin: 20px 0;
            color: #090909;
            text-align: left;
          }
          .article-subtitle {
            margin: 20px 0;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            text-align: left;
          }
          .upload {
            margin: 65px 0;
            .is-white {
              background: #fff;
              color: #00a664;
              border: 1px solid #00a664;
              width: 220px;
              border-radius: 120px;
              height: 47px;
            }
            input {
              opacity: 0;
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              cursor: pointer;
            }
            .iconfont {
              font-size: 18px;
            }
          }
        }
        .title-con-en {
          .article-headline {
            font-weight: 600;
            font-size: 3rem;
          }
          .article-subtitle {
            font-size: 1.2rem;
          }
        }
      }
      .container-right {
        width: 50%;
      }
    }

    .img-wrap,
    img {
      width: 100%;
      vertical-align: middle;
    }
    // .ssq-button-primary {
    //   border: none;
    //   width: 220px;
    // }
    .el-button--text {
      // color: #fff;
      background: 0 0;
      padding-left: 0;
      padding-right: 0;
      // border-bottom: 1px solid #fff;
      padding-bottom: 4px;
    }
  }
}
@media (max-width: 1024px) {
  .about-us .section-1 {
    .container {
      top: 50px;
    }
    .article-subtitle {
      margin: 30px auto 40px;
    }
  }
}
@media screen and (max-width: 767px) {
  .about-us {
    .section-1 {
      // background-image: url(https://static.bestsign.cn:443/b65ed16892db3c4380120dd66fbb7b01cabb93f1.jpg);
      // background-size: cover;
      // background-position: 44%;
      // height: 110vw;
      padding: 0;
      .container {
        width: 100%;
        margin: 0;
        display: block;
        padding: 5rem 0 0;
        .container-left {
          width: 100%;
          .title-con {
            text-align: center;
            margin: 0 10%;
            .article-headline {
              font-size: 28px;
              font-weight: 500;
              // color: #fff;
              text-align: center;
            }
            .article-subtitle {
              font-size: 1.3rem;
              line-height: 24px;
              // color: #fff;
              text-align: center;
            }
          }
          .title-con-en {
            .article-headline {
              font-weight: 600;
            }
            .article-subtitle {
            }
          }
        }
        .container-bottom {
          padding: 4rem 0 0;
        }
        .mobile-wrap {
          img {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
