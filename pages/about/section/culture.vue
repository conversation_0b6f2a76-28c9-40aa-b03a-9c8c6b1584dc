<template>
  <div class="section about-culture">
    
    <!-- <div class="container"> -->
      <h2 class="section-headline">公司价值观</h2>
      <div class="culture-wrapper flex">
        <div class="culture">
          <div class="pd">
            <div class="meta">
              <img src="@/assets/images/about/culture/6.jpg" alt="">
            </div>
            <div class="title">帮助客户成功</div>
            <div class="desc">
              <ul>
                <li><span style="font-size:20px; font-weight:800;">·</span> 和客户是长期的伙伴关系；</li>
                <li><span style="font-size:20px; font-weight:800;">·</span> 解决客户问题，让客户最终获得成功；</li>
                <li><span style="font-size:20px; font-weight:800;">·</span> 以客户商业成功为中心，提升客户商业价值。 </li>
              </ul>
              </div>
          </div>
        </div>
        <div class="culture">
          <div class="pd">
            <div class="meta">
              <img src="@/assets/images/about/culture/7.jpg" alt="">
            </div>
            <div class="title">敏捷创新</div>
            <div class="desc">
              <li><span style="font-size:20px; font-weight:800;">·</span> 在组织中建立高效合作机制，以高效合作为常态，促使团队通过自身动力取得伟大成功，并让创新持续进行；</li>
              <li><span style="font-size:20px; font-weight:800;">·</span> 如果有一种方式、方法、产品、服务可以提升社会效率 5%以上，那就大胆的投入全部资源去尝试；</li>
              <li><span style="font-size:20px; font-weight:800;">·</span> 如果有一种方式、方法、产品、服务可以提升客户商业效率 50%以上，那就放心的投入尽可能多的资源去尝试；</li>
              <li><span style="font-size:20px; font-weight:800;">·</span> 如果得到公司认可的尝试创新失败了，不要为浪费了时间与金钱懊恼，让我们来开瓶香槟为你庆祝一下，将失败的经验复盘分享给大家，让每个人都会因此学习到宝贵的经验，也为你下次成功铺设了道路。</li>
          </div>
          </div>
        </div>
        <div class="culture">
          <div class="pd">
            <div class="meta">
              <img src="@/assets/images/about/culture/8.jpg" alt="">
            </div>
            <div class="title">实事求是</div>
            <div class="desc">
              <li><span style="font-size:20px; font-weight:800;">·</span> 唯结果论：以业绩、数据驱动，事实说话，不讲情面，不讲职级，公平公正，不唯上，不针对“人”；</li>
              <li><span style="font-size:20px; font-weight:800;">·</span> 时刻保持对一线敏锐度，解决问题需要到最前线客户处，切实了解客户需求及一线同事需要的支持；</li>
              <li><span style="font-size:20px; font-weight:800;">·</span> 直接沟通；</li>
              <li><span style="font-size:20px; font-weight:800;">·</span> 信任与授权。</li>
          </div>
          </div>
        </div>
        <div class="culture">
          <div class="pd">
            <div class="meta">
              <img src="@/assets/images/about/culture/9.jpg" alt="">
            </div>
            <div class="title">以奋斗者为本</div>
            <div class="desc">
              <li><span style="font-size:20px; font-weight:800;">·</span> 始终葆有艰苦奋斗的创业精神，拼搏进取。</li>
          </div>
          </div>
        </div>
        <div class="culture">
          <div class="pd">
            <div class="meta">
              <img src="@/assets/images/about/culture/10.jpg" alt="">
            </div>
            <div class="title">不作恶</div>
            <div class="desc">
              <li><span style="font-size:20px; font-weight:800;">·</span> 我们坚信，作为一家为世界做好事的公司，从长远看来，我们会得到更好的回馈——即使我们放弃一些短期收益；</li>
              <li><span style="font-size:20px; font-weight:800;">·</span> 我们所做的一切都诚实和正直；</li>
              <li><span style="font-size:20px; font-weight:800;">·</span> 在我们驶向使命的过程中，不作恶将是我们衡量航道的最终标准；</li>
              <li><span style="font-size:20px; font-weight:800;">·</span> 技术的革新应该推动人类社会的进步，创造更美好的生活，拥有越多的资源与商业价值，不作恶的选择也会越发艰难，让我们共同努力，来捍卫未来的美好。</li>
            </div>
          </div>
        </div>
      </div>
    </div>
  <!-- </div> -->
</template>

<script>
export default {
  name: 'about-culture',
  props: {
    isMobile: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style scoped lang="scss">
@import '../aboutUs';
.about-culture {
  // .vision {
  //   width: 100%;
  //   height: 25vw;
  //   background-size: 100% 160%;
  //   background-image: url(~assets/images/about/about@vis);
  //   background-position: 0 75%;
  //   background-repeat: no-repeat;
  //   font-size: pxToVw(24);
  // }
  .culture {
    flex: 1;
    width: 20%;
    padding: 15px 5px;
    .meta {
      img {
        width: 100%;
        border-radius: 2px;
      }
    }
    .title {
      margin: 20px auto;
      font-size: 20px;
      text-align: left;
    }
    .desc {
      font-size: 14px;
      text-align: left;
      line-height: 25px;
    }
    .pd {
      height: 100%;
      padding: 20px 8px;
      border-radius: 2px;
      cursor: pointer;
      transition: 0.3s all ease;
      &:hover {
        box-shadow: 0px 0px 10px 0 rgba(0, 0, 0, 0.15);
      }
    }
  }
}
@media only screen and (max-width: 767px) {
  .about-culture {
    .culture-wrapper {
      flex-wrap: wrap;
      margin-left: -5px;
      margin-right: -5px;
    }
    .culture {
      flex: initial;
      width: calc(100% / 3);
      margin: 0;
      .meta {
        img {
          width: 100%;
        }
      }
      .title {
        font-size: 16px;
        margin-bottom: 0;
      }
      .desc {
        display: none;
      }
      .pd {
        padding: 0;
        box-shadow: 7px 7px 5px 0 rgba(0, 0, 0, 0.15);
        .title {
          padding: 0 8px 20px;
        }
      }
    }
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .about-culture {
    .culture {
      .title {
        font-size: 16px;
      }
      .desc {
        display: none;
      }
    }
  }
}
</style>
