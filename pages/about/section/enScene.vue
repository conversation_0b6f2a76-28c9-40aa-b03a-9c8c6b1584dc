
<template>
  <div class="index-scene">
    <div class="container-title">
      <h2 class="section-headline">{{$t('aboutUS.secene')}}</h2>
    </div>
    <div class="container">
      <el-collapse v-model="activeNames" @change="handleChange" accordion>
        <el-collapse-item  :name="item.profession"  v-for="(item,indx) in newEvaluate" :key="indx">
          <template slot="title">
            <div class="title-con">
            <div class="img-icon">
            <i :class="['iconfont ',item.icon]"></i>
            </div>
            <div class="header">
              <h4>{{ item.profession }}</h4>
            </div>
          </div>
          </template>
        <div class="content">
        <div class="item-card">
            <div class="scenes-con">
            <div class="scenes-item" v-for="(i,index) in item.scenes" :key="index">
              <div class="item-title">{{i.title}}</div>
              <div class="item-con">
                <div class="item-list" v-for="(a,index) in i.item" :key="index">
                  · {{a}}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
        </el-collapse-item>
      </el-collapse>
      
    </div>
  </div>
</template>

<script>
import Qs from 'qs';
import without from 'lodash/without';
const getJsonEn = () =>
  import('@/static/_aboutEN.json').then(m => m.default || m);
const getJson = () => import('@/static/_about.json').then(m => m.default || m);
const getJsonJP = () =>
  import('@/static/_aboutJP.json').then(m => m.default || m);

export default {
  name: 'index-scene',
  props: {},
  data() {
    return {
      newEvaluate: [],
      activeNames: 'Retail Industry Application Scenarios',
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  methods: {
    toMoreCase() {
      const query = sessionStorage.getItem('query');
      this.$router.push({
        path: `/${this.language}/case`,
        query: {
          ...Qs.parse(query),
        },
      });
    },
    handleChange(val) {},
    getInfo() {
      this.$nextTick(() => {
        this.partnerInfo = this.newEvaluate.filter(
          (item, index) => index == this.activeIndex
        )[0];
      });
    },
  },
  mounted() {},
  async created() {
    if (this.$store.state.locale.indexOf('en') != -1) {
      var caseResponse = await getJsonEn();
    } else if (this.$store.state.locale === 'ja') {
      var caseResponse = await getJsonJP();
    } else {
      var caseResponse = await getJson();
    }
    this.newEvaluate = caseResponse;
  },
};
</script>

<style scoped lang="scss">
/deep/.el-collapse {
  border-bottom: none;
  border-top: none;
  border-radius: 10px;
}
/deep/.el-collapse-item__header {
  border-bottom: none;
  background: #f5f5f7;
  padding: 50px 0;
  border-radius: 10px;
  font-weight: 600;
}
/deep/.el-collapse-item__header.is-active {
  border-radius: 10px 10px 0 0;
}
/deep/.el-collapse-item__wrap {
  border-bottom: none;
}
/deep/.el-collapse-item__content {
  background: #f5f5f7;
  border-radius: 0 0 10px 10px;
}
/deep/.el-collapse-item {
  border-radius: 10px;
  margin-bottom: 20px;
}
/deep/.el-collapse-item__arrow {
  margin: 0 30px 0 auto;
}
.index-scene {
  padding: 2rem 0 7rem;
  text-align: center;
  .container {
    max-width: 80%;
    width: 80%;
    margin-top: 0rem;
    .title-con {
      text-align: left;
      display: flex;
      align-items: center;
      margin: 30px;
      width: 100%;
      .img-icon {
        margin-right: 20px;
        margin-left: 1%;
        .iconfont {
          font-size: 2rem;
          color: $-color-main;
        }
      }
      .header {
        font-size: 1.3rem;
        color: #000;
        font-weight: 600;
      }
    }
    .content {
      .item-card {
        margin: 0 30px;
        .scenes-con {
          display: flex;
          flex-wrap: wrap;
          .scenes-item {
            background: #fff;
            margin: 0.5%;
            width: 24%;
            text-align: left;
            padding: 20px;
            border-radius: 10px;
            .item-title {
              font-weight: 600;
              color: #000;
              margin: 10px 0;
              font-size: 1.1rem;
            }
            .item-con {
              .item-list {
                margin: 5px 0;
              }
            }
          }
        }
      }
    }
  }
  .section-headline {
    padding: 6rem 0 4.5rem;
    font-weight: 600;
    font-size: 2.5rem;
    line-height: 1.5;
  }
}
@media screen and (max-width: 767px) {
  .index-scene {
    padding: 40px 0px 80px;
    .container {
      margin-top: 5rem;
      max-width: 100%;
      width: 100%;
      .title-con {
        .header {
          font-size: 1.3rem;
          line-height: 1.5;
        }
      }
      .content {
        .item-card {
          margin: 0 30px;
          .scenes-con {
            display: block;
            .scenes-item {
              background: #fff;
              margin: 20px 0.5%;
              width: 100%;
              text-align: left;
              padding: 20px;
              border-radius: 10px;
              &:first-child {
                margin-top: 0;
              }
              .item-title {
                font-weight: 600;
                color: #000;
                margin: 10px 0;
                font-size: 1.3rem;
              }
              .item-con {
                .item-list {
                  margin: 5px 0;
                }
              }
            }
          }
        }
      }
    }
    .section-headline {
      padding: 5rem 0 0rem;
    }
  }
}
</style>
