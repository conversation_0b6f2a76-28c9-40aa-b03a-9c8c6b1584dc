<!--  -->
<template>
	<div class='section'>
		<div class="container">
			<div class="main-tag">
				<div class="item">
					<i class="iconfont icon-jishushili"></i>
					<p class="tag-title">技术实力世界认可</p>
					<p class="tag-text">第五届世界互联网大会指定服务商技术实力世界认可</p>
				</div>
				<div class="item">
					<i class="iconfont icon-zhongediyiqian"></i>
					<p class="tag-title">中俄第一签</p>
					<p class="tag-text">中国电子签约首次走出国门实现“中俄第一签”</p>
				
				</div>
				<div class="item">
					<i class="iconfont icon-zhanlvehezuo"></i>
					<p class="tag-title">战略合并</p>
					<p class="tag-text">与众签战略合并推动行业新格局</p>
				</div>
				<div class="item">
					<i class="iconfont icon-zhulihenan"></i>
					<p class="tag-title">助力河南无纸化</p>
					<p class="tag-text">助力河南省电子口岸企业入网无纸化</p>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
  components: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  methods: {},
  created() {},
  mounted() {},
};
</script>
<style lang='scss' scoped>
.section {
  padding: 5rem 2.5rem;
}
.container {
  .main-tag {
    display: flex;
    justify-content: space-around;
    .item {
      cursor: pointer;
      width: 25%;
      margin: 0 10px;
      background: #f8f8f8;
      text-align: center;
      border-radius: 2px;
      padding: 6rem 3rem;
      .iconfont {
        color: #00aa64;
        font-size: 3rem;
        padding-bottom: 2rem;
        display: block;
      }
      .tag-title {
        font-size: 20px;
        line-height: 1.5;
        margin: 0.75rem auto 1.75rem;
        color: #1d1d1f;
        font-weight: 400;
      }
      .tag-text {
        line-height: 1.5;
        color: #86868b;
        text-align: center;
      }
    }
    .item:hover {
      box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
    }
  }
}
@media screen and(max-width:768px) {
  .section {
    padding: 0;
  }
  .container {
    .main-tag {
      display: flex;
      justify-content: space-around;
      flex-wrap: wrap;
      .item {
        cursor: pointer;
        width: 100%;
        margin: 0 20px;
        background: #fff;
        text-align: center;
        border-radius: 5px;
        padding: 2rem 1rem;
        .iconfont {
          font-size: 4rem;
          color: #00aa64;
        }
        .tag-title {
          font-size: 16px;
          color: #090909;
          line-height: 2.5rem;
          font-weight: 400;
        }
        .tag-text {
          font-size: 14px;
          line-height: 1.7;
          color: #86868b;
        }
      }
      .item:hover {
        box-shadow: 0 0 0px 0 rgba(0, 0, 0, 0);
      }
    }
  }
}
</style>
