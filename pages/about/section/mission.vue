<template>
<div>
  <div class="vision" v-if="!isMobile">
    <div class="container form-wrapper">
      <div class="vision-text">
        <div class="title">公司宣传语</div>
        <div class="dsc">电子签约上上签，合同终身保安全。</div>
      </div>
    </div>
  </div>
  <div class="vision" v-else>
    <div class="container form-wrapper">
      <div class="vision-text">
        <div class="title">公司宣传语</div>
        <div class="dsc">电子签约上上签，合同终身保安全。</div>
      </div>
    </div>
  </div>
</div>
  
</template>

<script>
export default {
  name: 'about-vision',
  computed: {
    isMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {},
};
</script>

<style scoped lang="scss">
@import '../aboutUs';
.vision {
  width: 100%;
  height: 20vw;
  background-size: 100% 160%;
  background-image: url(~assets/images/about/<EMAIL>);
  background-position: 0 75%;
  background-repeat: no-repeat;
  font-size: pxToVw(24);
  .ssq-button-primary {
    display: inline-block;
    margin-top: 40px;
    font-size: 16px;
    width: 120px;
    height: 37px;
    line-height: 37px;
    padding: 0;
  }
  .form-wrapper {
    width: 100%;
    height: 100%;
    // display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: center;
  }
  .title {
    font-size: 36px;
    font-weight: 400;
    line-height: 54px;
    color: #f8f8fa;
    margin: 0 0 40px;
    padding-top: 70px;
  }
  .dsc {
    color: #f8f8fa;
  }
  .feature {
    margin-top: pxToVw(70);
    &-item {
      display: block;
      line-height: 32px;
      font-size: 14px;
      i {
        color: $base-color;
        margin-right: 8px;
      }
    }
  }
}

@media only screen and (max-width: 767px) {
  .vision {
    background-image: url(~assets/images/about/<EMAIL>);
    // background-size: 100% 50%;
    // background-repeat: no-repeat;
    // background-position: -75px bottom;
    // background-color: #f8f8fa;
    height: 20vh;
    .form-wrapper {
      display: block;
      text-align: center;
      .title {
        font-size: 20px;
        line-height: 30px;
        // margin: 0;
        padding-top: 33px;
        // padding-bottom: 16px;
        margin-bottom: 15px;
        color: #f8f8fa;
      }
      .dsc {
        color: #f8f8fa;
        font-size: 14px;
      }
      .vision-text {
        padding: 0 16px;
      }
    }
    .feature {
      width: 65%;
      margin-left: auto;
      margin-right: auto;
      text-align: left;
      .feature-item {
        font-size: 12px;
        line-height: 20px;
      }
    }
    .ssq-button-primary {
      margin-top: 25px;
    }
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vision {
    padding: 0 32px;
    .feature {
      margin-top: 16px;
    }
    .title {
      font-size: 20px;
      line-height: 30px;
      // margin: 0;
      padding-top: 33px;
      // padding-bottom: 16px;
      margin-bottom: 15px;
      color: #f8f8fa;
    }
    .dsc {
      color: #f8f8fa;
    }
  }
}
@media only screen and (min-width: 991px) and (max-width: 1024px) {
  .vision {
    padding: 0 32px;
    .feature {
      margin-top: 16px;
    }
    .title {
      font-size: 20px;
      line-height: 30px;
      // margin: 0;
      padding-top: 33px;
      // padding-bottom: 16px;
      margin-bottom: 15px;
      color: #f8f8fa;
    }
    .dsc {
      color: #f8f8fa;
    }
  }
}
</style>
