<template>
<div>
  <div class="vision" v-if="!isMobile">
    <div class="container form-wrapper">
      <div class="vision-text">
        <div class="title">公司使命</div>
        <div class="dsc">构建智能时代的契约规则。</div>
      </div>
       <div class="vision-text1" style="padding: 0 16px; border-top: 1px solid #fff; margin: 4rem 20%;">
        <div class="title">公司愿景</div>
        <div class="dsc">构建以电子签约为纽带的生态服务体系。</div>
      </div>
    </div>
</div>
<div class="vision" v-else>
    <div class="container form-wrapper">
      <div class="vision-text">
        <div class="title">公司使命</div>
        <div class="dsc">构建智能时代的契约规则。</div>
      </div>
      <div class="vision-text1" style="padding: 0 16px; border-top: 1px solid #fff; margin: 4rem 16%;">
        <div class="title">公司愿景</div>
        <div class="dsc">构建以电子签约为纽带的生态服务体系。</div>
      </div>
    </div>
</div>
</div>
  
</template>

<script>
export default {
  name: 'about-vision',
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  methods: {},
};
</script>

<style scoped lang="scss">
@import '../aboutUs';
.vision {
  width: 100%;
  height: 40vw;
  background-size: 100% 100%;
  background-image: url(~assets/images/about/<EMAIL>);
  background-position: 0 75%;
  background-repeat: no-repeat;
  font-size: pxToVw(24);
  .ssq-button-primary {
    display: inline-block;
    margin-top: 40px;
    font-size: 16px;
    width: 120px;
    height: 37px;
    line-height: 37px;
    padding: 0;
  }
  .form-wrapper {
    width: 100%;
    height: 100%;
    // display: flex;
    align-items: center;
    justify-content: space-between;
    text-align: center;
  }
  .title {
    font-size: 36px;
    font-weight: 400;
    line-height: 54px;
    color: #f8f8fa;
    margin: 0 0 40px;
    padding-top: 70px;
  }
  .dsc {
    color: #f8f8fa;
  }
  p {
    line-height: 28px;
    font-size: 16px;
    color: #888;
  }
  .feature {
    margin-top: pxToVw(70);
    &-item {
      display: block;
      line-height: 32px;
      font-size: 14px;
      i {
        color: $base-color;
        margin-right: 8px;
      }
    }
  }
}

@media only screen and (max-width: 767px) {
  .vision {
    background-image: url(~assets/images/about/<EMAIL>);
    // background-size: 100% 50%;
    // background-repeat: no-repeat;
    // background-position: -75px bottom;
    // background-color: #f8f8fa;
    height: 50vh;
    .form-wrapper {
      display: block;
      text-align: center;
      .title {
        font-size: 20px;
        line-height: 30px;
        // margin: 0;
        padding-top: 33px;
        // padding-bottom: 16px;
        margin-bottom: 15px;
        color: #f8f8fa;
      }
      .dsc {
        color: #f8f8fa;
        font-size: 14px;
      }
      p {
        line-height: 24px;
      }
      .vision-text {
        padding: 0 16px;
      }
    }
    .feature {
      width: 65%;
      margin-left: auto;
      margin-right: auto;
      text-align: left;
      .feature-item {
        font-size: 12px;
        line-height: 20px;
      }
    }
    .ssq-button-primary {
      margin-top: 25px;
    }
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .vision {
    padding: 0 32px;
    .feature {
      margin-top: 16px;
    }
    .title {
      font-size: 20px;
      line-height: 30px;
      // margin: 0;
      padding-top: 33px;
      // padding-bottom: 16px;
      margin-bottom: 15px;
      color: #f8f8fa;
    }
    .dsc {
      color: #f8f8fa;
    }
  }
}
@media only screen and (min-width: 991px) and (max-width: 1024px) {
  .vision {
    padding: 0 32px;
    .feature {
      margin-top: 16px;
    }
    .title {
      font-size: 20px;
      line-height: 30px;
      // margin: 0;
      padding-top: 33px;
      // padding-bottom: 16px;
      margin-bottom: 15px;
      color: #f8f8fa;
    }
    .dsc {
      color: #f8f8fa;
    }
  }
}
</style>
