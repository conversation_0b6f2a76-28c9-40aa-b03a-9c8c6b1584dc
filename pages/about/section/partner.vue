<template>
  <section class="section-1">
    <div class="container">
      <h1 class="section-headline" :class="{'section-headline-en':isEN}">{{$t('aboutPartner.title')}}</h1>
      <!-- <p class="sub-title">{{$t('aboutPartner.desc')}}</p> -->
      <!-- <div class="content flex bc">
        <div class="left">
          <p>上上签电子签约，中国电子签约云平台领跑者，为企业提供「合同起草」、「实名认证」、「在线审批」、「签约管理」、「合同管理」、「履约提醒」、「法律支持」、「证据链保存」等合同全生命周期的智能管理服务。</p>
          <p>自2014年成立以来，以SaaS极简方案持续领跑电子签约市场。截止2020年3月31日，上上签平台累计合同签署量达<span style="font-weight: bold;">127</span>亿次，服务超过<span style="font-weight: bold;">1273</span>万家企业。权威智库艾媒咨询《2018-2019中国电子签名市场专题报告》数据显示，上上签2018年以44.3%的市场份额强势领跑。</p>
        </div>
        <div class="right">
          <div class="video-wrapper">
            <video
              src="https://static.bestsign.cn/c276d7eaf30a1ce4a080627f47f0b15f9fb35b19.mp4"
              controls="controls"
              poster="https://static.bestsign.cn/19f77a726fbb4099b265f592d601a1a202a37c5a.jpg"
            ></video>
          </div>
        </div>
      </div> -->
      <div class="logo-container">
        <div v-for="(item,index) in data" :key="index" class="logo-each">
          <img :src="item.logo" :key="item.logo" alt="">
          <div class="title">{{item.name}}</div>
        </div>
		<i></i>
		<i></i>
		<i></i>
		<i></i>
		<i></i>
		<i></i>
		<i></i>
      </div>
    </div>
  </section>
</template>

<script>
const data = [
  {
    logo:
      'https://static.bestsign.cn/fb003b516b6ee341d2e451a409038d5e5aa3b91c.png',
    name: 'Apple',
  },
  {
    logo:
      'https://static.bestsign.cn/36354d5222bc90bdd93c5af459609aa741d1277b.png',
    name: 'AWS',
  },
  {
    logo:
      'https://static.bestsign.cn/ffad16cb42ae33347b5290edb5902829e049447.png',
    name: 'Microsoft',
  },
  {
    logo:
      'https://static.bestsign.cn:443/3988b431cbb20d67053d704b771a6af0c09e6fef.png',
    name: 'intel',
  },
  {
    logo:
      'https://static.bestsign.cn:443/587ba0b55516750e6ffe2a8ea9d0baf293d98809.png',
    name: 'Oracle',
  },
];
export default {
  name: 'partner',
  data() {
    return {
      data,
      isEN: false,
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
    language() {
      return this.$store.state.locale;
    },
  },
  mounted() {
    this.isEN = this.language.indexOf('en') != -1 ? true : false;
  },
};
</script>

<style scoped lang="scss">
@import '../aboutUs';
.section-1 {
  padding: 1.5rem 6rem 5rem;
  background-color: #f5f5f7;
  .container {
    .section-headline {
      color: #090909;
      font-size: 2rem;
      line-height: 2.5rem;
      font-weight: 450;
      margin: 0 auto;
    }
    .section-headline-en {
      font-size: 2.5rem;
      line-height: 1.5;
      font-weight: 600;
    }
  }
  .logo-container {
    padding: 4%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    justify-content: space-around;
    width: 80%;
    margin: 0 auto;
    .logo-each {
      width: 20%;
      img {
        width: 4rem;
      }
      .title {
        margin-top: 10px;
        font-size: 0.8rem;
        line-height: 1rem;
      }
    }
    i {
      width: 7rem;
      margin-right: 62px;
    }
  }
}
@media only screen and (max-width: 767px) {
  .about-us {
    .section-1 {
      padding: 1.5rem 5%;

      p {
        line-height: 2rem;
        margin: 0 2%;
        font-size: 14px;
        color: #86868b;
      }
      .logo-container {
        padding: 4%;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        .logo-each {
          margin: 2rem 1.9%;
          width: 5rem;
          img {
            width: 5rem;
          }
          .title {
            margin-top: 10px;
            font-size: 12px;
            line-height: 1.3rem;
          }
        }
      }
    }
  }
  .sub-title {
    font-size: 14px;
    color: #86868b;
    line-height: 24px;
  }
  .section-1 {
    .logo-container {
      width: 100%;
      padding: 0;
      justify-content: space-between;
      .logo-each {
        margin: 20px 8px;
        width: 5rem;
        .title {
          font-size: 14px;
          line-height: 24px;
        }
      }
    }
  }
}
</style>
