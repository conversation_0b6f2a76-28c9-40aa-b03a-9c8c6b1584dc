<template>
  <section class="section section-6">
    <div class="container">
      <h3 class="section-headline">联系我们</h3>
      <div class="location-wrap">
        <div class="desc">
          <div class="block">
            <p class="title">杭州总部</p>
            <p class="addr">杭州市西湖区万塘路317号华星世纪大楼102、2层、11层、19层</p>
          </div>
          <div v-for="item in dist" :key="item.name" class="block">
			  <div :class="['content',(item.name == '北京' || item.name == '深圳' ||item.name == '南京')?'border-content':'']">
				<p class="title">{{ item.title }}</p>
               <p class="addr">{{ item.address }}</p>
			  </div>
            
          </div>
        </div>
      </div>
      <div class="contact flex cc">
        <div class="box">
          <i class="iconfont icon-24xiaoshirexian">
          </i>
          <div>
            <p class="color">24小时服务热线</p>
            <p>************</p>
          </div>
        </div>
        <div class="box">
          <i class="iconfont icon-qiyeyouxiang">
          </i>
          <div>
            <p class="color">企业邮箱</p>
            <p><EMAIL></p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'about-address',
  data: () => ({
    dist: [
      {
        name: '北京',
        title: '北京分公司',
        address: '北京市朝阳区乐成中心B座19层',
        phone: '+86 ************',
        time: '周一至周日0:00～24:00',
      },
      {
        name: '上海',
        title: '上海分公司',
        address:
          '上海市黄浦区延安东路588号东海商业中心一期2层wework社区02-133室',
        phone: '+86 ************',
        time: '周一至周日0:00～24:00',
      },
      {
        name: '广州',
        title: '广州分公司',
        address:
          '广东省广州市天河区珠江东路28号越秀金融中心大厦WeWork 65-118室',
        phone: '+86 ************',
        time: '周一至周日0:00～24:00',
      },
      {
        name: '深圳',
        title: '深圳分公司',
        address:
          '广东省深圳市南山区海德三道（深圳湾段）166 号航天科技广场 B 座 8 层 D18',
        phone: '+86 ************',
        time: '周一至周日0:00～24:00',
      },
      {
        name: '成都',
        title: '成都分公司',
        address: '四川省成都市万象南路669号，佳辰国际中心20楼20-130室',
        phone: '+86 ************',
        time: '周一至周日0:00～24:00',
      },
      {
        name: '厦门',
        title: '厦门分公司',
        address: '福建省厦门市湖里区岭下西路273号设计公社二期202单元',
        phone: '+86 ************',
        time: '周一至周日0:00～24:00',
      },
      {
        name: '南京',
        title: '南京分公司',
        address: '江苏省南京市江宁区绿地城际空间站D-1栋12F1228',
        phone: '+86 ************',
        time: '周一至周日0:00～24:00',
      },
    ],
  }),
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
};
</script>

<style scoped lang="scss">
@import '../aboutUs';
.section-6 {
  background: #f8f8f8;
  .location-wrap {
    // padding-top: 30px;
    padding-bottom: 30px;
    .desc {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      .block {
        height: 180px;
        width: 30%;
        // box-shadow: 0 4px 16px 0 rgba(47, 67, 82, 0.1);
        padding: 20px;
        text-align: center;
        margin: 1%;
        .title {
          font-size: 18px;
          line-height: 50px;
          color: #515151;
        }
        .addr {
          font-size: 15px;
          color: #888;
          line-height: 24px;
        }
      }
      .block:nth-child(2),
      .block:nth-child(5),
      .block:nth-child(8) {
        padding: 20px 0;
      }
      .border-content {
        padding: 0 20px;
        border-left: 1px solid #e6e6e6;
        border-right: 1px solid #e6e6e6;
      }
    }
    // .desc {
    //   text-align: left;
    //   width: 80%;
    //   margin: 0 auto 4rem;
    //   &.flex {
    //     display: flex;
    //     flex-wrap: wrap;
    //     .block {
    //       width: 50%;
    //       margin-bottom: 2rem;
    //     }
    //   }
    //   .block {
    //     padding-left: 0;
    //   }
    //   .title {
    //     font-size: 18px;
    //     margin-bottom: 1.5rem;
    //     color: #323232;
    //   }
    //   p {
    //     margin: 5px 0;
    //     color: #888;
    //     font-size: 14px;
    //   }
    // }
  }
  .contact {
    .box {
      width: 50%;
      text-align: center;
      padding: 40px 60px;
      margin: 0 15px;
      background: #fff;
      font-size: 20px;
      text-align: center;
      border-radius: 2px;
      .color {
        color: #888;
        font-size: 15px;
        margin-bottom: 12px;
      }
    }
    .iconfont {
      display: block;
      font-size: 3rem;
      color: #00aa64;
      margin-bottom: 2rem;
    }
  }
}
@media only screen and (max-width: 767px) {
  .section-6 {
    .location-wrap {
      padding-top: 0;
      .desc {
        width: 100%;
        .block {
          width: 100%;
          height: 105px;
          box-shadow: 0 4px 16px 0 rgba(47, 67, 82, 0.1);
          padding: 10px;
          text-align: left;
          margin: 1%;
          .title {
            font-size: 16px;
            line-height: 35px;
            color: #515151;
          }
          .addr {
            font-size: 14px;
            color: #888;
            line-height: 24px;
          }
        }
        .border-content {
          border: none;
        }
      }
    }
    .contact.flex {
      display: block;
      width: 100%;
      .box {
        width: 100%;
        margin: 10px auto;
        font-size: 16px;
        padding: 12px 24px;
      }
      .iconfont img {
        width: 40px;
      }
    }
  }
}
</style>
