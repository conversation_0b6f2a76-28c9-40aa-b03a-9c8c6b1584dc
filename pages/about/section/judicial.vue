<template>
 <div class='section judicial'>
    <div class="container">
        <h2 class="section-headline">多次得到司法机构事实认证</h2>
        <div class="judicical-top">
            <template>
            <el-carousel indicator-position="none" :autoplay="false" type="card" height="550px" @change="getCourtIndex">
                <el-carousel-item v-for="(item,index) in imgList" :key="index" >
                <img :src="item.imgUrl" alt="">
                </el-carousel-item>
            </el-carousel>
            <p class="carousel-name">{{courtName}}</p>
            </template>
        </div>
    </div>
 </div>
</template>

<script>
export default {
  data() {
    return {
      imgList: [
        {
          name: '杭州余杭去人民法院判决书1',
          imgUrl: 'https://www.bestsign.info/_nuxt/img/41baa97.png',
        },
        {
          name: '杭州余杭去人民法院判决书2',
          imgUrl: 'https://www.bestsign.info/_nuxt/img/41baa97.png',
        },
        {
          name: '杭州余杭去人民法院判决书3',
          imgUrl: 'https://www.bestsign.info/_nuxt/img/41baa97.png',
        },
      ],
      courtIndex: 1,
      courtName: '',
    };
  },
  components: {},
  methods: {
    getCourtIndex(cur) {
      this.courtIndex = cur;
      this.courtName = this.imgList.filter(
        (item, index) => index == this.courtIndex
      )[0].name;
    },
  },
  created() {
    this.getCourtIndex(1);
  },
};
</script>

<style lang='scss' scoped>
.judicial {
  padding: 4rem 1.5rem 0;
  background: #f8f8f8;
  .section-headline {
    margin-bottom: 4rem;
  }
  .judicical-top {
    padding-bottom: 4rem;
    border-bottom: 1px solid #bfbfbf;
  }
}
</style>
