<template>
  <section class="section section-2">
    <div class="container">
		<h2 class="section-headline">发展历程</h2>
		<div class="history-box" v-if="!isMobile">
			<div class="btn-left arrow-icon" @click="changePage(1)"> <i class="el-icon-arrow-left"></i> </div>
			<div class="scroll-body">
				<div class="history" :style="{'transform':'translateX('+nowIndex*24.5+'rem)','transition-duration': '1.5s'}">
					<template v-for="year in Object.keys(list).reverse()">
					<div class="year" :key="year">
							<div v-for="(item, index) in list[year]" :key="`year${index}`" class="year-block">
								<div class="year-title">{{year}}</div>
								<div class="year-body">
									<p>{{ item.date }}</p>
									<!-- <p><span v-if="item.text1">{{'·'}}</span>{{ item.text1 }}</p>
                  <p><span v-if="item.text2">{{'·'}}</span>{{ item.text2 }}</p>
                  <p><span v-if="item.text3">{{'·'}}</span>{{ item.text3 }}</p>
                  <p><span v-if="item.text4">{{'·'}}</span>{{ item.text4 }}</p> -->
                  <ul>
                    <li v-if="item.text1"><div>{{'·'}}</div><div class="right">{{ item.text1 }}</div></li>
                    <li v-if="item.text2"><div>{{'·'}}</div><div class="right">{{ item.text2 }}</div></li>
                    <li v-if="item.text3"><div>{{'·'}}</div><div class="right">{{ item.text3 }}</div></li>
                    <li v-if="item.text4"><div>{{'·'}}</div><div class="right">{{ item.text4 }}</div></li>
                  </ul>
								</div>
								<div class="line-dot">
									<span class="liner"></span>
									<!-- <div class="circle"></div> -->
									<i class="iconfont icon-shijian circle"></i>
								</div>
							</div>
						
					</div>
				</template>
				</div>
			</div>
			<div class="btn-right arrow-icon" @click="changePage(-1)"> <i class="el-icon-arrow-right"></i> </div>
		</div>
    <div class="content" v-if="isMobile">
      <template v-for="(year,indexYear) in Object.keys(list).reverse()">
				<el-collapse v-model="activeNames" @change="handleChange" :key="year" :accordion="true">
					<el-collapse-item :title="year" :name="indexYear">
						<div class="table-item" v-for="(item, index) in list[year]" :key="`year${index}`">
							<div class="table-item-title">{{item.date}}</div>
              <!-- <div class="table-item-desc">{{item.text}}</div> -->
							<div class="table-item-desc" v-if="item.text1"><div class="left">{{'·'}}</div><div class="right">{{ item.text1 }}</div></div>
              <div class="table-item-desc" v-if="item.text2"><div class="left">{{'·'}}</div><div class="right">{{ item.text2 }}</div></div>
              <div class="table-item-desc" v-if="item.text3"><div class="left">{{'·'}}</div><div class="right">{{ item.text3 }}</div></div>
              <div class="table-item-desc" v-if="item.text4"><div class="left">{{'·'}}</div><div class="right">{{ item.text4 }}</div></div>
						</div>
					</el-collapse-item>
				</el-collapse>
      </template>
			</div>
    </div>
  </section>
</template>

<script>
export default {
  name: 'about-history',
  data: () => ({
    activeNames: 0,
    nowIndex: 0,
    list: {
      '2020': [
        {
          date: '2020.09',
          text1: '与华为、中软国际达成战略合作',
          text2: '平台累计合同付费签署量达到127亿次',
        },
        {
          date: '2020.08',
          text1: '判例总数突破500例',
        },
        {
          date: '2020.07',
          text1: 'PaaS平台战略升级，并上线AI合同',
          text2: '业内率先获得BSI ISO 22301:2019 业务连续性管理体系认证',
        },
        {
          date: '2020.06',
          text1: ' 再添广州互联网法院成功判例，判例数突破400例',
          text2:
            ' 连续两届入选《杭州独角兽&准独角兽》榜单，入选《胡润2020猎豹企业》',
          text3: ' 平台累计合同付费签署量达到101.4亿次',
          text4: ' 与全球云计算领导者AWS达成战略合作',
        },
        {
          date: '2020.04',
          text1: '发布智能法务系统，获北京互联网法院成功判例',
          text2: '荣获人民网人民战“疫”内容科技大赛三等奖、第四名',
          text3: '与百度超级链达成战略合作',
        },
        {
          date: '2020.03',
          text1: '获得国家高新技术企业证书',
        },
        {
          date: '2020.01',
          text1: '发布合同全生命周期智能管理2.0系统',
        },
      ],
      '2019': [
        {
          date: '2019.12',
          text1: '打造PaaS平台，提升产品服务交付速度',
        },
        {
          date: '2019.11',
          text1: '首届用户大会落幕，集结400余位行业高管引领智慧升级新浪潮',
        },
        {
          date: '2019.10',
          text1: ' 开启首届签引力大赛，吸引31家知名企业参与',
        },
        {
          date: '2019.09',
          text1:
            '上上签中标河南省电子口岸企业入网全程无纸化项目，行业率先服务海关系统',
        },
        {
          date: '2019.07',
          text1: '上上签与众签战略合并，推动行业进入新格局',
          text2:
            '全球率先获得由英国BSI协会颁发的ISO 38505-1数据治理安全认证证书',
        },
        {
          date: '2019.04',
          text1:
            '上上签品牌升级，提出全新Slogan“电子签约上上签，合同终身保安全”',
        },
        {
          date: '2019.03',
          text1: '上上签与俄罗斯第二大银行俄罗斯外贸银行达成战略合作',
        },
        {
          date: '2019.01',
          text1: '行业率先打造合同全生命周期智能管理服务',
        },
      ],
      '2018': [
        {
          date: '2018.12',
          text1: '携手俄罗斯电商平台UMKA，达成中俄电子合同第一签',
        },
        {
          date: '2018.11',
          text1:
            '成为第五届世界互联网大会·乌镇峰会高级合作伙伴、现场签约独家技术服务商',
          text2: '与蚂蚁区块链达成战略合作伙伴',
        },
        {
          date: '2018.10',
          text1: '与全球顶尖的企业级管理软件服务商Oracle达成战略合作',
        },
        {
          date: '2018.09',
          text1: '获老虎环球基金领投，经纬、DCM、五源跟投的C轮融资',
        },
        {
          date: '2018.08',
          text1: '上上签迎来四周年纪念，乔迁新址并发布全新VI，品牌升级',
        },
        {
          date: '2018.06',
          text1: '入选信通院“可信区块链盟”理事单位',
        },
        {
          date: '2018.05',
          text1: '全球第7家荣获ISO27018个人信息隐私保护认证',
        },
        {
          date: '2018.04',
          text1: '自主研发区块链电子合同存管服务上线，最高支持单日3亿笔签单',
        },
        {
          date: '2018.03',
          text1: '获五源领投，经纬、DCM、WPS、顺为跟投的B轮融资',
          text2: '正式成为Apple官方授权的移动端企业合作伙伴',
        },
        {
          date: '2018.01',
          text1: '通过国家工信部“可信云服务”认证',
        },
      ],
      '2017': [
        {
          date: '2017.11',
          text1: '全面收购“快签”电子签约业务',
        },
        {
          date: '2017.08',
          text1: '入驻Apple企业解决方案中心，率先成为Apple大中华区移动合作伙伴',
        },
      ],
      '2016': [
        {
          date: '2016.12',
          text1: '与金山WPS达成战略合作',
        },
        {
          date: '2016.10',
          text1: '与微软Office达成战略合作',
        },

        {
          date: '2016.08',
          text1: '获顺为领投，经纬、DCM、WPS跟投的A+轮融资',
        },
        {
          date: '2016.06',
          text1: '获公安部信息安全三级等保',
        },
      ],
      '2015': [
        {
          date: '2015.11',
          text1: '获DCM领投，经纬跟投的A轮融资',
        },
        {
          date: '2015.08',
          text1: '获经纬Pre-A轮融资',
        },
        {
          date: '2015.01',
          text1: '产品发布，通过ISO27001国际安全认证',
        },
      ],
      '2014': [
        {
          date: '2014.08',
          text1: '公司成立',
        },
      ],
    },
  }),
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    isLandMobile() {
      return this.$store.state.isLandMobile;
    },
  },
  methods: {
    changePage(value) {
      if (this.nowIndex === 0 && value === 1) {
        return;
      }
      this.nowIndex += value;
    },
    handleChange(val) {
      console.log(val);
    },
  },
};
</script>

<style scoped lang="scss">
@import '../aboutUs';
.section-2 {
  // overflow: scroll;
  width: 100%;
  // height: 36.9vw;
  background-size: 100% 100%;
  background-image: url(~assets/images/about/<EMAIL>);
  background-position: 0 75%;
  background-repeat: no-repeat;
  background-size: cover;
  font-size: pxToVw(24);
  .section-headline {
    color: #fff;
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 400;
  }
  // &:hover .arrow-icon {
  //   visibility: visible;
  // }
}
.about-us {
  .container {
    max-width: 74rem;
    width: 100%;
  }
}
.history-box {
  border-top: 2px solid #fff;
  overflow: hidden;
  width: 100%;
  //   position: relative;
  cursor: pointer;
  .arrow-icon {
    position: absolute;
    transform: translateY(-50%);
    color: #fff;
    width: 30px;
    height: 30px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    top: 65%;
    cursor: pointer;
    visibility: hidden;
  }
  /deep/ .el-collapse-item__header {
    font-size: 16px;
  }
  .btn-left {
    left: -5%;
    visibility: visible;
  }
  .btn-right {
    right: -5%;
    visibility: visible;
  }

  .history {
    display: flex;
    .year {
      display: flex;
      position: relative;
      padding-bottom: 20px;
      text-align: left;
      font-size: 14px;
      .year-block {
        min-width: 22rem;
        margin: 0 20px;
        position: relative;
        .year-title {
          color: #fff;
          font-size: 26px;
          margin: 2rem 0;
        }
        .year-body {
          color: #fff;
          line-height: 26px;
          background-color: rgba(225, 225, 225, 0.2);
          padding: 20px 50px 20px 13px;
          border-radius: 5px;
          min-height: 180px;

          p:first-child {
            font-size: 20px;
            margin-bottom: 30px;
          }
          li {
            // &::before {
            //   content: '·';
            // }
            display: flex;
            .right {
              margin-left: 5px;
            }
          }
        }
        .line-dot {
          position: absolute;
          top: 0;
          right: 30px;
          .liner {
            width: 2px;
            height: 150px;
            background: #fff;
            display: block;
          }
          .circle {
            // width: 30px;
            // height: 30px;
            // border-radius: 50%;
            // border: 2px solid #fff;
            position: absolute;
            right: -15px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 30px;
          }
        }
      }
      .dot {
        position: absolute;
        top: 10px;
        left: -11px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: $base-color;
      }
    }
  }
}
@media only screen and (max-width: 767px) {
  .about-us {
    .section-2 {
      overflow: scroll;
      width: 100%;
      height: 100%;
      background: #fff;
      padding-top: 0;
      .section-headline {
        color: #090909;
        padding-top: 10px;
      }
      &:hover .arrow-icon {
        visibility: visible;
      }
    }
    .container {
      position: relative;
    }
    .content {
      .el-collapse {
        border-bottom: none;
      }
      /deep/ .el-collapse-item__header {
        font-size: 16px;
      }
      .table-item {
        .table-item-title {
          text-align: left;
          color: #00aa64;
          font-size: 14px;
        }
        .table-item-desc {
          display: flex;
          text-align: left;
          font-size: 14px;
          color: #86868b;
          .left {
            margin: 2px 0px;
          }
          .right {
            margin: 2px 5px;
          }
        }
      }
    }
  }
}
</style>
