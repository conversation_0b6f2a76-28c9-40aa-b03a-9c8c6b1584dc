<template>
  <el-table
  	:row-key= "getRowKeys"
  	:expand-row-keys= "expandsRow"
    :data="tableData"
    style="width: 100%"
	align="center">
    <el-table-column type="expand">
      <template slot-scope="props">
		  <div class="job-des">
			  <h3>岗位职责</h3>
			  <p class="job-item" v-html="formatJob(props.row.jobDuty)"></p>
		  </div>
			<div class="job-des">
			  <h3>任职要求</h3>
			   <p class="job-item" v-html="formatJob(props.row.jobRequirement)"></p>
		  </div>
		<div class="job-apply">
			<span class="job-operation"><a :href="'mailto:'+props.row.resumePostType+'?subject='+ props.row.jobTitle+' 岗位申请&subject=&body='">立即申请</a> <span class="el-icon-arrow-right"></span></span>
			<span class="email-icon">
				<svg t="1591673777745" class="icon" viewBox="0 0 1374 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8442" width="200" height="200"><path d="M1202.904679 0.00117H171.885321A171.153895 171.153895 0 0 0 0.146285 171.155065v681.68987A171.153895 171.153895 0 0 0 171.885321 1023.99883h1031.019358a171.256294 171.256294 0 0 0 171.885321-171.153895V171.155065A171.256294 171.256294 0 0 0 1202.904679 0.00117z m0 84.845521a65.667507 65.667507 0 0 1 23.698232 5.851415L718.261215 589.531251a45.699553 45.699553 0 0 1-61.878715 0L145.115097 90.698106a83.602095 83.602095 0 0 1 26.770224-5.851415h1031.019358z m86.015803 767.998244a86.688716 86.688716 0 0 1-86.015803 86.308374H171.885321a86.761859 86.761859 0 0 1-85.869518-86.308374V171.155065a89.117053 89.117053 0 0 1 3.071993-16.091392l507.025127 495.907438a128.731134 128.731134 0 0 0 91.282077 36.571345 135.650433 135.650433 0 0 0 91.428363-36.571345l506.439985-498.833146a79.798675 79.798675 0 0 1 3.657134 19.0171v681.68987z" fill="#313131" p-id="8443"></path></svg>
			</span>
			<span>简历投递邮箱：{{props.row.resumePostType}}</span>
		</div>
        
      </template>
    </el-table-column>
    <el-table-column 
      label="职位"
      prop="jobTitle">
	   <template slot-scope="scope" >
		   <span style="cursor:pointer" @click="changeExpand(scope.row.num,scope.row.id)">{{ scope.row.jobTitle}}</span>
	   </template>
    </el-table-column>
    <el-table-column
      label="地点"
      prop="city">
	   <template slot-scope="scope">
		   <span>{{ scope.row.city && format(JSON.parse(scope.row.city)) }}</span>
	   </template>
    </el-table-column>
    <el-table-column
      label="申请"
	  align="center"
      prop="desc">
	   <template slot-scope="scope">   
		   <span class="job-operation" style="padding-left:32px"><a :href="'mailto:'+scope.row.resumePostType+'?subject='+ scope.row.jobTitle+' 岗位申请&subject=&body='">立即申请 <i class="iconfont icon-xiangyoujiantou"></i></a></span>
	   </template>
    </el-table-column>
  </el-table>
</template>

<style>
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
</style>

<script>
export default {
  props: {
    tableData: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      expandsRow: [],
      getRowKeys(row) {
        return row.id;
      },
    };
  },
  mounted() {},
  methods: {
    format(array) {
      const cities = {
        hz: '杭州',
        bj: '北京',
        sz: '深圳',
        sh: '上海',
      };
      return array
        .map(item => cities[item])
        .filter(itemChild => itemChild != undefined)
        .toString()
        .replace(/\,/g, '/');
    },
    formatJob(string) {
      // return string.split('\n')    //Dom 需要循环显示
      return string.replace(/[\r\n]/g, '<br>');
    },
    changeExpand(row, id) {
      if (this.expandsRow.length === 0) {
        this.expandsRow = [id];
      } else {
        this.expandsRow = [];
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.job-operation {
  cursor: pointer;
  a {
    color: #00aa64;
  }
  .iconfont {
    font-size: 12px;
  }
}
.job-des {
  h3 {
    margin: 20px 0;
  }
  .job-item {
    line-height: 1.7;
  }
}
.job-apply {
  margin-top: 30px;
  .el-icon-arrow-right {
    color: #00aa64;
  }
  .email-icon {
    margin-left: 45px;
  }
}
</style>
