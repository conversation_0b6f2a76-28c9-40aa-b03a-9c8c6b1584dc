<template>
  <article class="join-us">
    <section class="section section-1">
      <div class="container">
        <h1 class="article-headline">选择适合你的职位</h1>
		<h1 class="article-headline">加入我们</h1>
		<p class="section-subtitle">“相信我，你加入的是一个‘梦工厂’——这里可以孵化出包括你在内的每一位同事色彩斑斓的梦想。这个名叫上上签/BestSign的梦工厂会为大家提供创新、开放、平等、自由、公正、诚信、坦诚、无畏的土壤，只有这样的土壤才可以长出参天大树，才能种下伟大公司基业长青的基因。”</p>
		<p class="section-subtitle-name">——创始人&CEO 万敏</p>
	  </div>
      <div class="img-wrap">
        <img v-if="!isMobile" src="@/assets/images/about/<EMAIL>">
        <img v-if="isMobile" src="@/assets/images/about/<EMAIL>">
      </div>
    </section>
    <section class="section section-2">
      <div class="container">
        <h3 class="section-headline">
			<p>现招职位，我们提供有竞争力的薪酬福利。</p>
		</h3>
        <p class="section-subtitle">
         在上上签，你可以享受每年全员Outing、全员补充商业保险、免费体检、免费的加班餐和零食等员工福利。还有广阔的学习和发展空间，丰富的员工培养计划以及导师计划，为你的事业成功助力、加速。
        </p>
        <div class="job-type" v-if="!isMobile">
          <div class="type" :class="{ newStyle:0===jobType}" @click="chooseJobType(0)">
            <i class="iconfont icon-gongwenbao1"></i>
            <div class="job-dsc">社会招聘</div>
          </div>
          <div class="type" :class="{ newStyle:1===jobType}" @click="chooseJobType(1)">
            <i class="iconfont icon-xueshimaoxuexibiye"></i>
            <div class="job-dsc">校园招聘</div>
          </div>
        </div>

		<div class="job-list" v-if="!isMobile && jobType===0">
			<el-tabs :stretch="true">
				<el-tab-pane label="全部">
					<job-content :tableData="allJobs"></job-content>
				</el-tab-pane>
				<el-tab-pane label="产品技术 ">
					<job-content :tableData="list.job_1"></job-content>
				</el-tab-pane>
				<el-tab-pane label="客户成功">
					<job-content :tableData="list.job_2"></job-content>
				</el-tab-pane>
				<el-tab-pane label="销售">
					<job-content :tableData="list.job_3"></job-content>
				</el-tab-pane>
				<el-tab-pane label="市场">
					<job-content :tableData="list.job_4"></job-content>
				</el-tab-pane>
				<el-tab-pane label="HR行政">
					<job-content :tableData="list.job_5"></job-content>
				</el-tab-pane>
				<el-tab-pane label="法务/政府关系">
					<job-content :tableData="list.job_6"></job-content>
				</el-tab-pane>
			</el-tabs>
		</div>
    <div class="job-school-list" v-if="!isMobile&&jobType===1">
    <job-content :tableData="list.job_7"></job-content>
    </div>
		<div class="" v-if="isMobile"> 
			<jobs></jobs>
		</div>
      </div>
    </section>
  </article>
</template>

<script>
import JobContent from './job-content.vue';
import Jobs from './jobs.vue';
import { mapActions } from 'vuex';
export default {
  name: 'JoinUs',
  components: {
    JobContent,
    Jobs,
  },
  // layout: 'layout_about',
  head() {
    return {
      title: '招聘信息_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '上上签招聘,上上签团队',
        },
        {
          hid: 'description',
          name: 'description',
          content: '上上签招聘信息，我们的团队期待你的加入。',
        },
      ],
      link: [
        {
          rel: 'canonical',
          href: 'https://www.bestsign.cn/join-us',
        },
      ],
    };
  },
  data() {
    return {
      jobType: 0,
      swiperOption: {
        loop: true,
        navigation: {
          nextEl: '.ssq-button-next',
          prevEl: '.ssq-button-prev',
        },
      },
      allJobs: [],
      list: {
        job_1: [],
        job_2: [],
        job_3: [],
        job_4: [],
        job_5: [],
        job_6: [],
        job_7: [],
        job_8: [],
        job_9: [],
        job_10: [],
        job_11: [],
        job_12: [],
      },
    };
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
    getList() {
      return Array(9)
        .fill(1)
        .map((item, index) => {
          return require(`@/assets/images/about/join/join${index + 1}.jpg`);
        });
    },
  },
  methods: {
    ...mapActions(['getRecruit']),
    chooseJobType(num) {
      this.jobType = num;
    },
  },
  mounted() {
    this.$store.commit('changePath', { path: 'about-us' });
    this.getRecruit().then(res => {
      this.allJobs = res.filter((item, index) => item.recruitType === 'social');
      res.forEach(item => {
        if (item.recruitType === 'social') {
          switch (item.jobType) {
            case 'tech':
              this.list.job_1.push(item);
              break;
            case 'success':
              this.list.job_2.push(item);
              break;
            case 'sale':
              this.list.job_3.push(item);
              break;
            case 'market':
              this.list.job_4.push(item);
              break;
            case 'hr':
              this.list.job_5.push(item);
              break;
            case 'legal':
              this.list.job_6.push(item);
              break;
          }
        } else {
          // switch (item.jobType) {
          //   case 'tech':
          //     this.list.job_7.push(item);
          //     break;
          //   case 'success':
          //     this.list.job_8.push(item);
          //     break;
          //   case 'sale':
          //     this.list.job_9.push(item);
          //     break;
          //   case 'market':
          //     this.list.job_10.push(item);
          //     break;
          //   case 'hr':
          //     this.list.job_11.push(item);
          //     break;
          //   case 'legal':
          //     this.list.job_12.push(item);
          //     break;
          // }
          this.list.job_7.push(item);
        }
      });
    });
  },
};
</script>

<style scoped lang="scss">
.join-us {
  text-align: center;
  .section-1 {
    position: relative;
    padding: 0;
    min-height: 400px;
    .article-headline {
      font-weight: 500;
      color: #fff;
      font-size: 2.875rem;
      margin: 0;
    }
    .section-subtitle {
      font-size: 1.2rem;
      line-height: 1.7;
      font-weight: 400;
      margin: 1.5rem auto;
      padding: 6px 10px;
      color: #fff;
    }
    .section-subtitle-name {
      text-align: right;
      margin: 20px auto 0;
      max-width: 700px;
      color: #fff;
    }
    .img-wrap,
    img {
      width: 100%;
    }
    .container {
      position: absolute;
      left: 50%;
      top: 12rem;
      transform: translate3d(-50%, 0, 0);
    }
  }
  .section-2 {
    background: #f8f8f8;
    .section-subtitle {
      color: #888;
      max-width: 900px;
      margin: 0 auto 30px;
      text-align: justify;
      font-size: 16px;
      &:last-child {
        text-align: center;
      }
    }
    .job-type {
      display: flex;
      justify-content: space-around;
      padding: 0 40%;
      border-bottom: solid #e6e6e6 1px;
      .type {
        padding: 10px;
        cursor: pointer;
        .job-dsc {
          font-size: 14px;
          font-weight: 500;
        }
        .iconfont {
          font-size: 40px;
          color: #090909;
          font-weight: 400;
        }
      }
    }
    .newStyle {
      border-bottom: solid #090909 1px;
    }
    .job-list {
      margin: 5rem 0;
      /deep/ .el-tabs__nav {
        background: #efefef;
        color: #fff;
        .el-tabs__item {
          color: #333;
          cursor: pointer;
        }
        .el-tabs__item:hover {
          color: #fff;
          background: #3f3f3f;
        }
        .el-tabs__item.is-active {
          color: #fff;
          background: #3f3f3f;
        }
        .el-tabs__active-bar {
          display: none;
        }
      }
      /deep/ .el-tabs__content {
        margin-top: 45px;
        background: #fff;
        border-radius: 5px;
        padding: 40px 50px;
        box-sizing: border-box;
        .el-tab-pane {
          border: 1px solid #e6e6e6;
        }
      }
    }
    .job-school-list {
      margin: 5rem 0;
      background: #fff;
      border-radius: 5px;
      padding: 40px 50px;
      box-sizing: border-box;
      .el-table {
        border: 1px solid #e6e6e6;
      }
    }
  }
  .section-3 {
    background-color: #fafbfc;
    .content {
      margin-top: 100px;
      img {
        width: 100%;
      }
      p {
        margin-top: 40px;
      }
    }
  }
  .section-4 .ssq-button-primary {
    margin-top: 95px;
  }
}
@media screen and (max-width: 767px) {
  .join-us {
    .section-subtitle {
      font-size: 12px;
    }
    .section-1 {
      .container {
        width: 100%;
        top: 2rem;
      }
      .article-headline {
        margin-top: 30px;
        max-width: 640px;
        font-size: 28px;
        line-height: 40px;
      }
    }
    .section-2 {
      background: #f8f8f8;
      .section-headline {
        // margin-bottom: 75px;
      }
      .section-subtitle {
        color: #86868b;
        max-width: 900px;
        margin: 0 auto 30px;
        text-align: justify;
        font-size: 14px;
        &:last-child {
          text-align: center;
        }
      }
      .job-list {
        margin: 5rem 0;
        /deep/ .el-tabs__nav {
          background: #efefef;
          color: #fff;
          .el-tabs__item {
            color: #333;
            cursor: pointer;
          }
          .el-tabs__item:hover {
            color: #fff;
            background: #3f3f3f;
          }
          .el-tabs__item.is-active {
            color: #fff;
            background: #3f3f3f;
          }
          .el-tabs__active-bar {
            display: none;
          }
        }
        /deep/ .el-tabs__content {
          margin-top: 45px;
          background: #fff;
          border-radius: 5px;
          padding: 0;
          box-sizing: border-box;
          .el-tab-pane {
            border: 1px solid #e6e6e6;
          }
        }
      }
    }
    .section-3 {
      padding-left: 0;
      padding-right: 0;
      .section-subtitle {
        padding: 0 10px;
        margin-bottom: 50px;
      }
      .mobile-content {
        width: 100%;
        height: 270px;
        img {
          width: 100%;
          height: 270px;
        }
      }
    }
  }
}
</style>
