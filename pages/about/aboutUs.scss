$base-color: #00aa64;
@function pxToVw($fz) {
  @return ($fz / 1920) * 100vw;
}
.about-us {
  .section {
    padding: 5rem 6rem 6rem;
  }
  .container {
    max-width: 1180px;
    width: 100%;
  }
  .section-headline {
    // margin-bottom: 50px;
	font-weight: 400;
	margin: 0;
  }
  .flex {
    display: flex;
    &.cc {
      align-items: center;
      justify-content: center;
    }
    &.bc {
      justify-content: space-between;
      align-items: center;
    }
  }
}

@media only screen and (max-width: 767px) {
  .about-us .section {
    padding: 36px 12px;
    .section-headline {
      margin-bottom: 24px;
    }
  }
  .section-6 {
    .mobile-content {
      .line {
        margin-bottom: 2.75rem;
        .title {
          font-size: 15px;
        }
        .desc {
          color: #888;
          padding: 8px 0;
        }
      }
      .el-col-16 {
        padding-right: 3rem;
        text-align: left;
      }
    }
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px){
}
@media only screen and (min-width: 992px) {
  .section-headline {
    font-size: 36px;
  }
}

