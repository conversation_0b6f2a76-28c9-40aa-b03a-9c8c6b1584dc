<template>
    <div class="protocol-content">
        <p v-if="isRegister">
            <strong class="tip">【审慎阅读】</strong>
            您在申请注册流程中点击同意前，应当认真阅读以下协议。
            <strong>请您务必审慎阅读、充分理解协议中相关条款内容，其中包括但不限于：</strong>
        </p>
        <p v-else>
            【审慎阅读】上上签平台更新了《隐私政策》，请您务必审慎阅读、充分理解协议中相关条款内容，其中包括但不限：
        </p>
        <p><strong>1、与您约定免除或限制责任的条款；</strong></p>
        <p><strong>2、与您约定的个人信息收集和使用的条款；</strong></p>
        <p><strong>3、与您约定的数字证书的申请和使用的条款；</strong></p>
        <p><strong>4、其他以粗体标识的重要条款内容</strong>。</p>
        <p>如您对协议有任何疑问，可向平台客服咨询。</p>
        <template v-if="isRegister">
            <p>
                【特别提示】当您按照注册页面提示填写信息、阅读并同意协议且完成全部注册程序后，即表示您已充分阅读、理解并接受协议的全部内容。
            </p>
            <p>
                <strong>阅读协议的过程中，如果您不同意相关协议或其中任何条款约定，您应立即停止注册程序。</strong>
            </p>
        </template>
        <template v-else>
            <p >
                <strong>【特别提示】点击“同意协议”，表示您已充分阅读、理解并接受协议的全部内容，可继续使用上上签平台服务。</strong>
            </p>
        </template>
        <div style="margin-top: 20px">
            <p v-for="(item, index) in protocolList" :key="index">
                <!-- <nuxt-link :to="protocolMap[item].path" target="_blank">{{protocolMap[item].name}}</nuxt-link> -->
                <a :href="protocolMap[item].path" target="_blank">{{protocolMap[item].name}}</a>
            </p>
        </div>
    </div>
</template>

<script>
export default {
  name: 'RegistrationProtocol',
  data() {
    return {
      protocolMap: {
        service: {
          name: '《上上签服务协议》',
          // path: '/protocol/service-agreement',
          path: 'https://ent.bestsign.cn/legal-agreement/service-agreement',
        },
        privacy: {
          name: '《隐私政策》',
          // path: '/protocol/privacy-policy',
          path: 'https://ent.bestsign.cn/legal-agreement/privacy-policy',
        },
        digital: {
          name: '《数字证书使用协议》',
          // path: '/protocol/digital-certificate-protocol',
          path:
            'https://ent.bestsign.cn/legal-agreement/digital-certificate-protocal',
        },
      },
    };
  },
  props: {
    isRegister: {
      type: Boolean,
      default: true,
    },
    protocolList: {
      type: Array,
      default: () => {
        return ['service', 'privacy', 'digital'];
      },
    },
  },
};
</script>

<style scoped lang="scss">
.protocol-content {
  p {
    margin-top: 8px;
    line-height: 20px;
    font-size: 12px;
    color: #666;
  }
  strong {
    text-decoration: underline;
    font-weight: 700;
    color: #000;
  }
  .tip {
    text-decoration: none;
  }
  a {
    font-size: 14px;
    font-weight: 700;
    color: #00aa64;
  }
}
</style>
