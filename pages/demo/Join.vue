<template>
<div>
  
  <el-form :model="form" :rules="rules" ref="form" class="ruleForm register">
    <div class="text" v-if="text" v-html="text"></div>
    <div class="text-little">
      <span class="text" v-html="activityName"></span>
      <!-- <span class="text2" v-if="text2" v-html="text2"></span>
      <span class="text3" v-if="text1" v-html="text3"></span> -->
    </div>
    <!-- <div class="title">注册成功后，你将即刻免费获得5份电子合同<br>实名认证后再获得5份</div> -->
    <el-form-item prop="userPhone">
      <el-input v-model="form.userPhone" placeholder="请输入你的手机号"></el-input>
    </el-form-item>
    <el-form-item prop="verifyCode">
      <el-input v-model.trim="form.verifyCode" placeholder="请输入6位验证码">
        <template slot="append">
          <count-down class="countDown code-sent" :clickedFn="send" :disabled="countDownDisabled" ref="btn" :second="60"></count-down>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item v-if="showPictureVerCon" class="pictureVer-item" prop="imageCode">
      <el-input
        class="pictureVer"
        placeholder="请填写4位图形验证码"
        :maxlength="4"
        v-model.trim="form.imageCode"
      >
        <template slot="append">
          <PictureVerify
            class="form-pictureVerify"
            ref="pictureVerify"
            :imageKey="form.imageKey"
            @change-imageKey="changeImageKey"
          />
        </template>
      </el-input>
    </el-form-item>
    <el-form-item>
      <el-button class="ssq-button-primary" @click="nextStep" :loading="loading">下一步</el-button>
    </el-form-item>
  </el-form>
  <el-dialog :title="done?'':'问卷调查'" center :visible.sync="dialogFormVisible" :fullscreen="!done" width="100%">
    <el-form :model="formSubmit" label-width="60px" ref="formSubmit"  :rules="rulesParams" v-if="!done">
        <el-form-item label="姓名:" prop="name">
            <el-input v-model="formSubmit.name"></el-input>
        </el-form-item>
        <el-form-item label="公司:" prop="company">
            <el-input v-model="formSubmit.company"></el-input>
        </el-form-item>
        <el-form-item label="职位:" prop="position">
            <el-input v-model="formSubmit.position"></el-input>
        </el-form-item>
        <div class="question" v-for="(item,index) in content" :key="item.question">{{item.question}}
            <el-form-item v-if="!item.isOnly" prop="type" label-width="0px">
                <el-checkbox-group v-model="formSubmit.type['answer' + index]" @change="handleCheck">
                <el-checkbox :label="opt.value"  v-for="opt in item.options" :key="opt.value" class="checkBlock">{{opt.value}}</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item v-if="item.isOnly" prop="type" label-width="0px">
                <el-radio-group v-model="formSubmit.type['answer' + index]">
                <el-radio :label="opt.value" :name="item.question" v-for="(opt) in item.options"  :key="opt.value" class="checkBlock" ></el-radio>
                </el-radio-group>
            </el-form-item>
        </div>
        </el-form>
    <div slot="footer" class="dialog-footer" v-if="!done">
        <el-button type="primary" @click="isOK" :loading="loading">提交</el-button>
    </div>
    <div class="doneBox" v-if="done">
        <div >问卷填写成功。</div>
        <div>客户经理会在24小时内和你联系。</div>
    </div>
  </el-dialog>
    
    
</div>
</template>

<script>
import resRules from '@/assets/utils/regs.js';
import CountDown from '@/components/CountDown.vue';
import PictureVerify from '@/components/PictureVerify.vue';
import { isPhoneOrMail } from '@/assets/utils/reg.js';
import aes from '@/assets/utils/aes.js';
import Qs from 'qs';
import RegistrationProtocol from '../protocol/RegisterProtocol';

export default {
  name: 'Register',
  components: {
    RegistrationProtocol,
    CountDown,
    PictureVerify,
  },
  props: {
    text: {
      type: String,
      default: '',
    },
  },
  data() {
    var validateSelect = (rule, value, callback) => {
      let flag = Object.keys(value).some(item => {
        return value[item] === '' || value[item] === [];
      });
      if (flag) {
        callback(new Error('至少勾一个选项'));
      } else {
        callback();
      }
    };
    return {
      // protocolVisible: false,
      done: false,
      showPictureVerCon: false,
      countDownDisabled: false,
      codeDisabled: true,
      // isChecked: false,
      loading: false,
      form: {
        verifyCode: '',
        verifyKey: '',
        userPhone: '',
      },
      formSubmit: {
        name: '',
        company: '',
        position: '',
        type: {},
      },
      content: [],
      dialogFormVisible: false,
      activityName: '立即领取电子合同，免费试用',
      rules: {
        userPhone: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur',
          },
        ],
        verifyCode: [
          {
            required: true,
            message: '请输入验证码',
            trigger: 'blur',
          },
          {
            pattern: /^\d{6}.*$/,
            message: '请输入正确的验证码',
            trigger: 'blur',
          },
        ],
        imageCode: [
          {
            required: true,
            message: '请输入图形验证码',
            trigger: 'blur',
          },
          {
            pattern: /^\d{4}.*$/,
            message: '请输入正确的图形验证码',
            trigger: 'blur',
          },
        ],
      },
      rulesParams: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        company: [{ required: true, message: '请输入公司', trigger: 'blur' }],
        position: [{ required: true, message: '请输入职位', trigger: 'blur' }],
        type: [{ validator: validateSelect, trigger: 'blur' }],
      },
    };
  },
  methods: {
    handleCheck(value) {
      // console.log(value);
    },
    // 更改图形验证码
    changeImageKey(value) {
      this.codeDisabled = false;
      this.form.imageKey = value;
    },
    // 发送验证码
    send() {
      const { userPhone, imageCode, imageKey } = this.form;
      this.$refs['form'].validateField(['userPhone'], error => {
        if (error) {
          return false;
        }
        let headersObj = {};
        if (imageCode !== '' && imageKey !== '') {
          headersObj = {
            // 'Content-Type': 'application/json; charset=utf-8',
            additionalImgVerCode: JSON.stringify({
              imageCode,
              imageKey,
            }),
            'request-source': 'WEB',
          };
        }
        // let host = 'https://ent.bestsign.info';
        // if (process.env.baseUrl.indexOf('cn') > -1) {
        //   host = 'https://ent.bestsign.cn';
        // }
        let host = '';
        if (process.env.NODE_ENV !== 'development') {
          host =
            process.env.baseUrl.indexOf('cn') > -1
              ? 'https://ent.bestsign.cn'
              : 'https://ent.bestsign.info';
        }
        this.loading = true;
        this.$axios({
          url: `${host}/users/ignore/captcha/notice?encryptToken=${aes(
            'B001',
            userPhone
          )}`,
          method: 'post',
          headers: headersObj,
          data: {
            code: 'B001',
            sendType: isPhoneOrMail(userPhone) === 'phone' ? 'S' : 'E',
            target: userPhone,
            imageCode,
            imageKey,
          },
        })
          .then(res => {
            this.loading = false;
            if (res) {
              this.$MessageToast.success('发送成功！');
              this.countDownDisabled = true;
              setTimeout(this.sended, 0);
              this.form.verifyKey = res.value;
              this.codeDisabled = false;
            }
          })
          .catch(err => {
            this.loading = false;
            const res = err.response.data;
            if (res.code === '902' || res.code === '100006') {
              if (this.showPictureVerCon) {
                setTimeout(() => {
                  this.$refs.pictureVerify.changeImg();
                }, 20);
              } else {
                this.showPictureVerCon = true;
              }
              if (res.message !== '图片验证码不能为空') {
                this.$MessageToast.error(res.message);
              } else {
                this.$MessageToast.error('请先填写图形验证码');
              }
            } else {
              this.$MessageToast.error(res.message);
            }
            this.$refs.btn.reset();
          });
      });
    },

    sended() {
      this.$refs.btn.run();
      this.countDownDisabled = false;
    },
    // 检查校验码是否正确
    checkCode() {
      const { userPhone, verifyCode, verifyKey } = this.form;
      const params = {
        target: userPhone,
        code: 'B001',
        verifyKey,
        verifyCode,
      };
      return new Promise((resolve, reject) => {
        this.$refs['form'].validate(valid => {
          if (!valid) {
            this.loading = false;
            reject();
            // return false;
          }
          // let host = 'https://ent.bestsign.info';
          // if (process.env.baseUrl.indexOf('cn') > -1) {
          //   host = 'https://ent.bestsign.cn';
          // }
          let host = '';
          if (process.env.NODE_ENV !== 'development') {
            host =
              process.env.baseUrl.indexOf('cn') > -1
                ? 'https://ent.bestsign.cn'
                : 'https://ent.bestsign.info';
          }
          this.$axios
            .post(`${host}/users/ignore/captcha/verify`, params, {
              headers: {
                'Content-Type': 'application/json',
              },
            })
            .then(res => {
              // this.loading = false;
              if (res.value) {
                resolve();
              } else {
                this.$MessageToast.error(res.message);
                reject();
              }
            })
            .catch(err => {
              const res = err.response.data;
              this.$MessageToast.error(res.message);
              reject();
            });
        });
      });
    },
    nextStep() {
      let self = this;
      this.loading = true;
      this.checkCode()
        .then(() => {
          self.getQuestionnaire();
          self.dialogFormVisible = true;
        })
        .finally(() => {
          self.loading = false;
        });

      // debugger;
    },
    getQuestionnaire() {
      const query = sessionStorage.getItem('query');
      const code = Qs.parse(query);
      this.$axios
        .post(`www/api/web/questionnaire/${code.utm_source}`)
        .then(res => {
          this.content = JSON.parse(res.result.content);
          // const types = {};
          this.content.forEach((_c, index) => {
            if (_c.isOnly) {
              this.$set(this.formSubmit.type, `answer${index}`, '');
              return;
            }

            this.$set(this.formSubmit.type, `answer${index}`, []);
          });
        });
    },
    getInfo() {
      const query = sessionStorage.getItem('query');
      const code = Qs.parse(query);
      this.$axios
        .get('www/api/web/category/info', {
          params: {
            categoryName: 'utm_source',
            code: code.utm_source,
          },
        })
        .then(res => {
          this.activityName = res.codeValue;
        });
    },
    isOK() {
      const query = sessionStorage.getItem('query');
      const code = Qs.parse(query);
      const data = [];

      this.content.map((item, index) => {
        data.push({
          question: item.question,
          answer: this.formSubmit.type['answer' + index],
        });
        return;
      });
      //   const query = sessionStorage.getItem('query');
      //   const code = Qs.parse(query);
      this.$refs['formSubmit'].validate(valid => {
        if (valid) {
          this.loading = true;
          this.$axios
            .post('www/api/web/regionalCustomer/apply', {
              customerName: this.formSubmit.name,
              companyName: this.formSubmit.company,
              position: this.formSubmit.position,
              contact: this.form.userPhone,
              content: JSON.stringify(data),
              applyUrl: location.href,
              applyProvider: code.utm_source,
            })
            .then(res => {
              this.loading = false;
              if (res.code === '150001') {
                this.done = true;
                this.$message({
                  message: '保存成功',
                  type: 'success',
                });
                // this.dialogFormVisible = false;
                this.form = {};
              } else {
                this.$message.error(res.message);
              }
            })
            .catch(err => {
              //   const res = err.response.data;
              this.loading = false;

              //   this.$message.error(res.message);
            });
        }
      });
    },
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
  mounted() {
    // this.getInfo();
  },
};
</script>
<style lang="scss">
.doneBox {
  padding: 2rem;
  position: relative;
  background-color: #fff;
  color: #515151;
  margin: 0;
  text-align: center;
  line-height: 30px;
}
.register {
  // .el-form-item.is-success .el-input__inner {
  //   border-color: #dcdfe6;
  // }
  // .el-input.is-active .el-input__inner,
  // .el-input__inner:focus {
  //   border-color: #00aa64;
  //   box-shadow: 0 0 2px rgba(0, 170, 100, 0.5);
  // }

  .el-radio__input.is-checked .el-radio__inner {
    border-color: #00aa64;
    background: #00aa64;
  }
  .el-radio__input.is-checked + .el-radio__label {
    color: #00aa64;
  }
  .el-form-item {
    margin-bottom: 20px;
  }
  .el-checkbox {
    margin-right: 8px;
  }
  .el-checkbox__inner:hover {
    border-color: #00aa64;
  }
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #00aa64;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #00aa64;
    border-color: #00aa64;
  }
  // .el-input__inner {
  //   border-radius: 0;
  // }
  .el-input-group__append {
    border-radius: 0;
    width: 125px;
    background-color: transparent;
  }
  .el-button--primary {
    background-color: #00aa64;
    border-color: #00aa64;
    width: 25%;
    height: 40px;
    border-radius: 4px;
    font-size: 17px;
  }
  .title {
    text-align: center;
    margin-bottom: 1rem;
    line-height: 1.5rem;
    color: #818181;
    font-size: 1.5rem;
    font-weight: 600;
  }
  .ssq-button-primary {
    // float: right;
    border-radius: 4px;
    height: 40px;
    line-height: 40px;
    width: 100%;
  }
  .el-checkbox__label {
    width: 100%;
    white-space: normal;
  }
}
</style>
<style scoped lang="scss">
.countDown {
  background-color: #fff;
  font-size: 14px;
  width: 100%;
  border: none;
  color: #00aa64;
}
.checkBlock {
  width: 100%;
  height: 30px;
  line-height: 30px;
}
.register {
  padding: 2rem;
  position: relative;
  background-color: #fff;
  color: #515151;
  margin: 5rem 0;
  .text {
    text-align: center;
    margin-bottom: 1rem;
    line-height: 1rem;
    color: #818181;
    font-size: 1rem;
    font-weight: 600;
  }
  .text-little {
    text-align: center;
    margin-bottom: 1rem;
    .text {
      margin-bottom: 1rem;
      line-height: 2rem;
      color: #545454;
      font-size: 1.4rem;
      font-weight: 600;
      //   color: #00aa64;
    }
    // .text2 {
    //   margin-bottom: 1rem;
    //   line-height: 2rem;
    //   color: #545454;
    //   font-size: 1.2rem;
    //   font-weight: 600;
    //   color: #00aa64;
    // }
    // .text3 {
    //   margin-bottom: 1rem;
    //   line-height: 2rem;
    //   color: #545454;
    //   font-size: 1.2rem;
    //   font-weight: 600;
    // }
  }
}
</style>
