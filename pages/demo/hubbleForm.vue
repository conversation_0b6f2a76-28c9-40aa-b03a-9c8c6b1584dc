
<!-- 哈勃表单 -->
<template>
  <div class='hubble-form'>
    <el-form ref="hubbleForm" :model="form" label-width="100px" :rules="rules">
      <el-form-item label="手机号：" prop="account">
        <el-input v-model="form.account	"></el-input>
      </el-form-item>
      <el-form-item label="企业名称：" prop="entName">
        <el-input v-model="form.entName"></el-input>
      </el-form-item>
      <el-form-item label="姓名：" prop="username">
        <el-input v-model="form.username"></el-input>
      </el-form-item>
      <el-form-item label="岗位名称：" prop="post">
        <el-select v-model="form.post" placeholder="请选择岗位名称">
          <el-option label="企业法人" value="企业法人"></el-option>
          <el-option label="人力资源" value="人力资源"></el-option>
          <el-option label="采购管理" value="采购管理"></el-option>
          <el-option label="财务管理" value="财务管理"></el-option>
          <el-option label="法务管理" value="财务管理"></el-option>
          <el-option label="市场营销" value="市场营销"></el-option>
          <el-option label="产品设计" value="产品设计"></el-option>
          <el-option label="IT技术" value="IT技术"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="签约场景：" prop="scenes">
        <el-checkbox-group v-model="form.scenes">
          <el-checkbox label="人事"></el-checkbox>
          <el-checkbox label="采购销售"></el-checkbox>
          <el-checkbox label="物流单据"></el-checkbox>
          <el-checkbox label="知识产权"></el-checkbox>
          <el-checkbox label="教育培训"></el-checkbox>
          <el-checkbox label="广告协议"></el-checkbox>
          <el-checkbox label="商务合作"></el-checkbox>
          <el-checkbox label="其他"></el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="需求描述" prop="addInfo">
        <el-input type="textarea" v-model="form.addInfo" maxlength="100" placeholder="最多可输入100个字（包含标点符号以及其他字符）"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">立即留言</el-button>
        <el-button @click="onClose">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
//import x from ''
export default {
  components: {},
  data() {
    return {
      form: {
        account: '',
        username: '',
        entName: '',
        addInfo: '',
        scenes: [],
        post: '',
      },
      rules: {
        account: [
          { required: true, message: '请输入手机号', trigger: 'change' },
        ],
        username: [
          { required: true, message: '请输入姓名', trigger: 'change' },
        ],
        entName: [
          { required: true, message: '请输入企业名称', trigger: 'change' },
        ],
        post: [
          { required: true, message: '请选择岗位名称', trigger: 'change' },
        ],
        scenes: [
          {
            type: 'array',
            required: true,
            message: '请至少选择一个签约场景',
            trigger: 'change',
          },
        ],
        addInfo: [
          { required: true, message: '请填写需求描述', trigger: 'blur' },
        ],
      },
    };
  },
  computed: {},
  methods: {
    onSubmit() {
      this.$refs['hubbleForm'].validate(valid => {
        if (!valid) {
          return false;
        } else {
          const url = location.href;
          this.$axios
            .post('www/api/web/hubbleinformation', {
              ...this.form,
              submitUrl: url,
            })
            .then(res => {
              this.$emit('setHubbleDialogVisible', false);
              this.$refs['hubbleForm'].resetFields();
            });
        }
      });
    },
    onClose() {
      this.$refs['hubbleForm'].resetFields();
      this.$emit('setHubbleDialogVisible', false);
    },
  },
  mounted() {},
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>

<style lang='scss' scoped>
//@import url()
</style>
