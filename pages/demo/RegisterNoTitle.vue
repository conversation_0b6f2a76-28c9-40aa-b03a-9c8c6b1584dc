<template>
  <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="ruleForm register">
    <div class="text" v-if="text" v-html="text"></div>
    <div class="text-little">
      <span class="text1" v-if="text1" v-html="text1"></span>
      <span class="text2" v-if="text2" v-html="text2"></span>
      <span class="text3" v-if="text1" v-html="text3"></span>
      <span class="text4" v-if="text4" v-html="text4"></span>
    </div>
    <!-- <div class="title">注册成功后，你将即刻免费获得5份电子合同<br>实名认证后再获得5份</div> -->
    <el-form-item prop="fullName">
      <el-input v-model.trim="ruleForm.fullName" placeholder="姓名 *"></el-input>
    </el-form-item>
    <el-form-item prop="account">
      <el-input v-model.trim="ruleForm.account"  placeholder="手机 *"></el-input>
    </el-form-item>
    <el-form-item prop="verifyCode">
      <el-input v-model.trim="ruleForm.verifyCode" placeholder="6位验证码 *">
        <template slot="append">
          <count-down class="countDown code-sent" :clickedFn="send" :disabled="countDownDisabled" ref="btn" :second="60"></count-down>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item v-if="showPictureVerCon" class="pictureVer-item" prop="imageCode">
      <el-input
        class="pictureVer"
        placeholder="请填写4位图形验证码"
        :maxlength="4"
        v-model.trim="ruleForm.imageCode"
      >
        <template slot="append">
          <PictureVerify
            class="form-pictureVerify"
            ref="pictureVerify"
            :imageKey="ruleForm.imageKey"
            @change-imageKey="changeImageKey"
          />
        </template>
      </el-input>
    </el-form-item>
    <!-- <el-form-item prop="contractSize" class="radio-box">
        <label style="margin-right:13px;">年合同签署量</label>
        <el-radio v-model="ruleForm.contractSize" label="0">0-999</el-radio>
        <el-radio v-model="ruleForm.contractSize" label="1">1000以上</el-radio>
    </el-form-item> -->
     <el-form-item label="" prop="industrySector">
    <el-select v-model="ruleForm.industrySector" placeholder="请选择所属行业 *" :popper-append-to-body="false">
      <el-option label="互金" value="1"></el-option>
      <el-option label="金融" value="2"></el-option>
      <el-option label="企业服务" value="3"></el-option>
      <el-option label="物流" value="4"></el-option>
      <el-option label="地产服务" value="5"></el-option>
      <el-option label="汽车交通" value="6"></el-option>
      <el-option label="电子商务" value="7"></el-option>
      <el-option label="制造" value="8"></el-option>
      <el-option label="零售" value="9"></el-option>
      <el-option label="文娱生活" value="10"></el-option>
      <el-option label="教育培训" value="11"></el-option>
      <el-option label="医疗健康" value="12"></el-option>
      <el-option label="其他" value="13"></el-option>
    </el-select>
  </el-form-item>
  <el-form-item label="" prop="organizationSize">
    <el-select v-model="ruleForm.organizationSize" placeholder="请选择公司规模 *" :popper-append-to-body="false">
      <el-option label="0-250人" value="1"></el-option>
      <el-option label="250-1000人" value="2"></el-option>
      <el-option label="1000-5000人" value="3"></el-option>
      <el-option label="5000人以上" value="4"></el-option>
    </el-select>
  </el-form-item>
  <el-form-item label="" prop="department">
    <el-select v-model="ruleForm.department" placeholder="请选择所属部门 *" :popper-append-to-body="false">
      <el-option label="人事" value="1"></el-option>
      <el-option label="运营" value="2"></el-option>
      <el-option label="风险合规" value="3"></el-option>
      <el-option label="经销商管理" value="4"></el-option>
      <el-option label="财务" value="5"></el-option>
      <el-option label="市场" value="6"></el-option>
      <el-option label="法务" value="7"></el-option>
      <el-option label="IT" value="8"></el-option>
      <el-option label="采购与供应链管理" value="9"></el-option>
      <el-option label="其他" value="10"></el-option>
    </el-select>
  </el-form-item>
    <el-form-item prop="clueMail">
      <el-input v-model.trim="ruleForm.clueMail"  placeholder="邮箱 *"></el-input>
    </el-form-item>
    <el-form-item prop="companyName">
      <el-input v-model.trim="ruleForm.companyName"  placeholder="公司名称"></el-input>
    </el-form-item>
    <el-form-item prop="loginPassword">
      <el-input v-model.trim="ruleForm.loginPassword" type="password"  maxlength= 18 placeholder="密码 * 6-18位数字、大小写字母组成"></el-input>
    </el-form-item>
    <div class="checkbox">
      <el-checkbox name="type" v-model="isChecked">注册即同意</el-checkbox>
      <a class="protocol" @click="protocolVisible = true">注册协议</a>
    </div>
    <div>
      <el-button :loading="loading" :disabled="!isChecked || ruleForm.loginPassword.length < 6" type="primary" class="submit" @click="toRegisterOther">立即注册，免费试用</el-button>
    </div>
    <div class="tips">注：若注册遇到问题，请<a @click="toService">联系客服</a>，或拨打 ************</div>
    <!-- <div class="login">已有账号？<a class="protocol" @click="toLogin">立即登录</a></div> -->
    <el-dialog title="注册协议" :visible.sync="protocolVisible">
      <registration-protocol></registration-protocol>
    </el-dialog>
  </el-form>
</template>

<script>
import resRules from '@/assets/utils/regs.js';
import CountDown from '@/components/CountDown.vue';
import PictureVerify from '@/components/PictureVerify.vue';
import { isPhoneOrMail } from '@/assets/utils/reg.js';
import aes from '@/assets/utils/aes.js';
import Qs from 'qs';
import RegistrationProtocol from '../protocol/RegisterProtocol';

export default {
  name: 'Register',
  components: {
    RegistrationProtocol,
    CountDown,
    PictureVerify,
  },
  props: {
    text: {
      type: String,
      default: '',
    },
    text1: {
      type: String,
      default: '',
    },
    text2: {
      type: String,
      default: '',
    },
    text3: {
      type: String,
      default: '',
    },
    text4: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      protocolVisible: false,
      loading: false,
      showPictureVerCon: false,
      countDownDisabled: false,
      codeDisabled: true,
      isChecked: false,
      ruleForm: {
        account: '',
        clueMail: '',
        fullName: '',
        imageCode: '',
        imageKey: '',
        verifyCode: '',
        verifyKey: '',
        loginPassword: '',
        registerSource: '',
        department: '',
        organizationSize: '',
        industrySector: '',
        contractSize: '',
      },
      rules: {
        fullName: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z]{2,10}$/,
            message: '请输入正确的姓名',
            trigger: 'blur',
          },
        ],
        account: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'blur',
          },
          {
            pattern: resRules.userPhone,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
        verifyCode: [
          {
            required: true,
            message: '请输入验证码',
            trigger: 'blur',
          },
          {
            pattern: /^\d{6}.*$/,
            message: '请输入正确的验证码',
            trigger: 'blur',
          },
        ],
        imageCode: [
          {
            required: true,
            message: '请输入图形验证码',
            trigger: 'blur',
          },
          {
            pattern: /^\d{4}.*$/,
            message: '请输入正确的图形验证码',
            trigger: 'blur',
          },
        ],
        companyName: [
          // { required: true, message: '请输入公司名称', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5a-zA-Z0-9（）]{1,128}$/,
            message: '请输入正确的公司名称',
            trigger: 'blur',
          },
        ],
        loginPassword: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          {
            pattern: resRules.pass,
            message:
              '密码需包含6-18位数字和大小写字母，不能包含特殊字符，请重新设置',
            trigger: 'blur',
          },
        ],
        clueMail: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          {
            pattern: resRules.userEmail,
            message: '请输入正确的邮箱',
            trigger: 'blur',
          },
        ],
        industrySector: [
          { required: true, message: '请选择所属行业', trigger: 'change' },
        ],
        organizationSize: [
          { required: true, message: '请选择公司规模', trigger: 'change' },
        ],
        department: [
          { required: true, message: '请选择所属部门', trigger: 'change' },
        ],
      },
    };
  },
  methods: {
    // 更改图形验证码
    changeImageKey(value) {
      this.codeDisabled = false;
      this.ruleForm.imageKey = value;
    },
    // 发送验证码
    send() {
      const { account, imageCode, imageKey } = this.ruleForm;
      this.$refs['ruleForm'].validateField(['account'], error => {
        if (error) {
          return false;
        }
        let headersObj = {};
        if (imageCode !== '' && imageKey !== '') {
          headersObj = {
            // 'Content-Type': 'application/json; charset=utf-8',
            additionalImgVerCode: JSON.stringify({
              imageCode,
              imageKey,
            }),
            'request-source': 'WEB',
          };
        }
        // let host = 'https://ent.bestsign.info';
        // if (process.env.baseUrl.indexOf('cn') > -1) {
        //   host = 'https://ent.bestsign.cn';
        // }
        let host = '';
        if (process.env.NODE_ENV !== 'development') {
          host =
            process.env.baseUrl.indexOf('cn') > -1
              ? 'https://ent.bestsign.cn'
              : 'https://ent.bestsign.info';
        }
        this.$axios({
          url: `${host}/users/ignore/captcha/notice?encryptToken=${aes(
            'B001',
            account
          )}`,
          method: 'post',
          headers: headersObj,
          data: {
            code: 'B001',
            sendType: isPhoneOrMail(account) === 'phone' ? 'S' : 'E',
            target: account,
            imageCode,
            imageKey,
          },
        })
          .then(res => {
            if (res) {
              this.$MessageToast.success('发送成功！');
              this.countDownDisabled = true;
              setTimeout(this.sended, 0);
              this.ruleForm.verifyKey = res.value;
              this.codeDisabled = false;
            }
          })
          .catch(err => {
            const res = err.response.data;
            if (res.code === '902' || res.code === '100006') {
              if (this.showPictureVerCon) {
                setTimeout(() => {
                  this.$refs.pictureVerify.changeImg();
                }, 20);
              } else {
                this.showPictureVerCon = true;
              }
              if (res.message !== '图片验证码不能为空') {
                this.$MessageToast.error(res.message);
              } else {
                this.$MessageToast.error('请先填写图形验证码');
              }
            } else {
              this.$MessageToast.error(res.message);
            }
            this.$refs.btn.reset();
          });
      });
    },

    sended() {
      this.$refs.btn.run();
      this.countDownDisabled = false;
    },

    // 申请试用
    toRegister() {
      this.loading = true;
      // 百度统计当用户点击“申请免费试用”成功提交信息之后，需要将当前页面URL地址，客户地区记录
      const query = sessionStorage.getItem('query');
      const queryString = Qs.stringify({
        utm_source: '',
        ...Qs.parse(query),
        ...this.$route.query,
      });
      const url = `${location.origin}/demo?${queryString}`;
      window._hmt &&
        window._hmt.push(['_trackEvent', 'register_url_pc', 'submit', url]);
      if (this.isMobile) {
        this.ruleForm.registerSource = 'WAP';
      } else {
        this.ruleForm.registerSource = 'WEB';
      }
      const params = {
        ...this.ruleForm,
        callBackFlag: true,
      };
      // 去掉旗舰版接口不需要个字段
      const filterItem = [
        'industrySector',
        'organizationSize',
        'department',
        'imageKey',
        'imageCode',
        'contractSize',
      ];
      for (let item of filterItem) {
        delete params[item];
      }
      this.$refs['ruleForm'].validate(valid => {
        if (!valid) {
          this.loading = false;
          this.ruleForm.loginPassword = '';
          return false;
        }
        // let host = 'https://ent.bestsign.info';
        // if (process.env.baseUrl.indexOf('cn') > -1) {
        //   host = 'https://ent.bestsign.cn';
        // }
        let host = '';
        if (process.env.NODE_ENV !== 'development') {
          host =
            process.env.baseUrl.indexOf('cn') > -1
              ? 'https://ent.bestsign.cn'
              : 'https://ent.bestsign.info';
        }
        this.$axios
          .post(`${host}/auth-center/user/v2/person-register`, params, {
            headers: {
              'Content-Type': 'application/json',
              'bestsign-registerTrackUrl': url,
            },
          })
          .then(res => {
            this.loading = false;
            document.cookie = `access_token=${res.access_token};refresh_token=${
              res.refresh_token
            }`;
            // const home = this.$store.state.isLandMobile
            //   ? '/mobile/home'
            //   : '/home';
            // SAAS-8597
            const home = '/register/done';
            const entUrl =
              location.origin.indexOf('cn') > -1
                ? 'https://ent.bestsign.cn'
                : 'https://ent.bestsign.info';
            location.href = `${entUrl}${home}?access_token=${
              res.access_token
            }&refresh_token=${res.refresh_token}`;
          })
          .catch(err => {
            this.loading = false;
            const res = err.response.data;
            if (res.code === '010001') {
              this.$MessageToast.error(res.message + '，跳转登陆');
              setTimeout(() => {
                this.toLogin();
              }, 2000);
            } else {
              this.$MessageToast.error(res.message);
            }
          });
      });
    },
    toRegisterOther() {
      // debugger;
      this.$axios.post('www/api/web/register/market', {
        account: this.ruleForm.account,
        contractSize: this.ruleForm.contractSize,
        department: this.ruleForm.department,
        organizationSize: this.ruleForm.organizationSize,
        industrySector: this.ruleForm.industrySector,
      });
      this.toRegister();
    },

    // toService() {
    //   window.open('//bestsign.udesk.cn/im_client/?web_plugin_id=23490');
    // },
    toService() {
      const url = this.tolink(
        'http://bestsign.udesk.cn/im_client/?web_plugin_id=23490'
      );
      window.open(url);
    },
    tolink(route) {
      var query = Object.entries(this.$route.query)
        .map(([key, value]) => {
          return `${key}=${value}`;
        })
        .join('&');
      // debugger;
      if (route.indexOf('?') > -1) {
        route = route + '&' + query;
      } else {
        route = route + '?' + query;
      }
      return route;
    },
    toLogin() {
      const entUrl =
        location.origin.indexOf('cn') > -1
          ? 'https://ent.bestsign.cn/login'
          : 'https://ent.bestsign.info/login';
      location.href = entUrl;
    },
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
};
</script>
<style lang="scss">
.register {
  .el-form-item.is-success .el-input__inner {
    border-color: #dcdfe6;
  }
  .el-input.is-active .el-input__inner,
  .el-input__inner:focus {
    border-color: #00aa64;
    box-shadow: 0 0 2px rgba(0, 170, 100, 0.5);
  }
  .el-form-item {
    margin-bottom: 15px;
  }
  .el-form-item__error {
    padding-top: 2px;
  }
  .el-checkbox {
    margin-right: 8px;
  }
  .el-checkbox__inner:hover {
    border-color: #00aa64;
  }
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #00aa64;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #00aa64;
    border-color: #00aa64;
  }
  .el-input__inner {
    border-radius: 0;
  }
  .el-input-group__append {
    border-radius: 0;
    width: 125px;
    background-color: transparent;
  }
  .el-button--primary {
    background-color: #00aa64;
    border-color: #00aa64;
    width: 100%;
    border-radius: 0;
    font-size: 17px;
  }
  .title {
    text-align: center;
    margin-bottom: 1rem;
    line-height: 1.5rem;
    color: #818181;
    font-size: 1.5rem;
    font-weight: 600;
  }
  .radio-box {
    text-align: left;
  }
  .el-radio {
    margin-right: 15px;
  }
  .el-radio__input.is-checked .el-radio__inner {
    border-color: #00aa64;
    background: #00aa64;
  }
  .el-radio__input.is-checked + .el-radio__label {
    color: #00aa64;
  }
  .el-select {
    width: 100%;
  }
  .el-select .el-input.is-focus .el-input__inner {
    border-color: #00aa64;
  }
  .el-select-dropdown__item {
    text-align: left;
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #e7f5f1;
  }
  .el-select-dropdown__item.selected {
    color: #00aa64;
  }
}
</style>
<style scoped lang="scss">
.countDown {
  background-color: #fff;
  font-size: 14px;
  width: 100%;
  border: none;
  color: #00aa64;
}
.radio-box {
  text-align: left;
}
.register {
  padding: 2.5rem 2rem 4rem;
  position: relative;
  background-color: #fff;
  color: #515151;
  .text {
    text-align: center;
    margin-bottom: 1rem;
    line-height: 1rem;
    color: #818181;
    font-size: 1rem;
    font-weight: 600;
  }
  .text-little {
    text-align: center;
    margin-bottom: 1rem;
    .text1 {
      margin-bottom: 1rem;
      line-height: 2rem;
      color: #545454;
      font-size: 1.2rem;
      font-weight: 600;
    }
    .text2 {
      margin-bottom: 1rem;
      line-height: 2rem;
      color: #545454;
      font-size: 1.2rem;
      font-weight: 600;
      color: #00aa64;
    }
    .text3 {
      margin-bottom: 1rem;
      line-height: 2rem;
      color: #545454;
      font-size: 1.2rem;
      font-weight: 600;
    }
    .text4 {
      margin-bottom: 1rem;
      line-height: 2rem;
      color: #333;
      font-size: 1.2rem;
      // font-weight: 600;
    }
  }

  .checkbox {
    margin-bottom: 20px;
    text-align: left;
  }
  .login {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 3rem;
    line-height: 3rem;
    text-align: center;
    font-size: 0.875rem;
    background-color: #f0f0f0;
  }
  .protocol {
    color: #00aa64;
    font-size: 14px;
    cursor: pointer;
  }
  .tips {
    padding-top: 1rem;
    font-size: 0.875rem;
    a {
      color: #00aa64;
      cursor: pointer;
    }
  }
}
</style>
