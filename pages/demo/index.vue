<template>
  <div class="demo">
    <demo-header></demo-header>
    <div class="demo-card">
      <div class="card-body">
        <div class="description">
           <div class="title">立即注册，体验电子合同签署流程</div>
          <div class="line-2" v-if="!isMobile">仅需1分钟，即可了解电子合同签约全流程，体验合同秒发秒签，适配各种终端，线上线下无缝切换。<br>助力企业实现降本增效，效率提升看得见。</div>
          <!-- <img src="./banner.jpg" alt=""> -->
		  <div class="register-des">注册实名后，免费获得10份电子合同</div>
        </div>
        <register></register>
      </div>
    </div>
	<!-- <div class="register-footer"></div> -->
  </div>
</template>

<script>
import DemoHeader from '@/components/DemoHeader.vue';
import Register from './Register';

export default {
  name: 'demo',
  layout: 'blank',
  head() {
    return {
      title: '注册|免费试用_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '电子合同,电子签名,电子签章,电子印章,电子签约,网上签约',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签是业内领先的在线电子合同签署，电子签名，电子签章的云服务平台，提供多种行业专属解决方案。',
        },
      ],
    };
  },
  components: {
    DemoHeader,
    Register,
  },
  computed: {
    isMobile() {
      return this.$store.state.isMobile;
    },
  },
};
</script>
<style lang="scss">
.demo {
  .el-select .el-input.is-focus .el-input__inner {
    border-color: #f3c51e;
  }
  .el-button--primary.is-active,
  .el-button--primary:active {
    background: #f3c51e;
  }
}
.el-select-dropdown__item {
  text-align: left;
}
.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: #e7f5f1;
}
.el-select-dropdown__item.selected {
  color: #f3c51e;
}
</style>
<style scoped lang="scss">
.demo {
  width: 100%;
  //   height: 100vh;
  position: relative;
  background: #fafafa;
  .demo-card {
    margin: 3rem auto 0;
    max-width: 1200px;
    width: 100%;
    .card-header {
      height: 52px;
      line-height: 52px;
      padding: 0 32px;
      color: #f3c51e;
      background-color: #e7f5f1;
      text-align: center;
    }
    .card-body {
      text-align: center;
    }
    .description {
      .title {
        margin-bottom: 1.5rem;
        color: #090909;
        font-size: 2rem;
        line-height: 2.5rem;
        font-weight: 400;
      }
      .line-1 {
        margin-bottom: 1.75rem;
      }
      .line-2 {
        margin-bottom: 2rem;
        line-height: 1.5;
        color: #86868b;
      }
      .register-des {
        font-size: 20px;
        font-weight: bold;
        //  margin-bottom: 3rem;
      }
    }
    .register {
      width: 30rem;
      margin: 0 auto;
      padding: 1.5rem 2rem 4rem;
    }
  }
  .register-footer {
    height: 200px;
    background: #fff;
  }
  .form-pictureVerify {
    width: 80px;
    height: 36px;
  }
}
@media screen and (max-width: 767px) {
  .demo {
    .demo-card {
      padding: 0 1rem;
      .card-body {
        display: block;
      }
      .description {
        text-align: center;
        img {
          display: none;
        }
      }
      .register {
        width: 100%;
      }
    }
  }
}
@media screen and (min-width: 767px) and (max-width: 1024px) {
  .demo {
    .demo-card {
      padding: 0 1rem;
      .description {
        img {
          width: 75%;
        }
      }
      .register {
        width: 40rem;
      }
    }
  }
}
</style>
