<template>
  <div class="sitemap">
    <div class="container">
      <!--<h1 class="headline">网站地图</h1>-->
      <div class="block">
        <h3 class="block-header">产品</h3>
        <nuxt-link to="/product-function">· 电子合同功能</nuxt-link>
        <nuxt-link to="/product-check">· 在线验签</nuxt-link>
        <nuxt-link to="/product-judicial">· 公证仲裁</nuxt-link>
        <nuxt-link to="/product-download">· APP下载</nuxt-link>
        <a href="https://openapi.bestsign.cn">· 签约工具系列</a>
        <a href="https://src.bestsign.cn/index.html">· 安全响应中心</a>
      </div>
      <div class="block">
        <h3 class="block-header">
          <nuxt-link to="/product-price" style="color: #515151;">价格</nuxt-link>
        </h3>
      </div>
      <div class="block">
        <h3 class="block-header">解决方案</h3>
        <template v-for="solution in solutions">
          <!-- <a :key="solution.id" @click="toSolution(solution.id)">· {{ solution.solutionName }}</a> -->
          <nuxt-link :key="solution.id" :to="`/solution/${solutionsId[solution.id]}`">· {{ solution.solutionName }}</nuxt-link>
          <!-- <a :key="solution.id" @click="toSolution(solution.id)" :href="`/solution/${solutionsId[solution.id]}`">· {{ solution.solutionName }}</a> -->
        </template>
      </div>
      <div class="block">
        <h3 class="block-header">客户案例</h3>
        <nuxt-link to="/case">· 成功案例</nuxt-link>
        <nuxt-link to="/evaluate">· 客户证言</nuxt-link>
      </div>
    
      <div class="block">
        <h3 class="block-header">资源中心</h3>
            <nuxt-link to="/activity">· 品牌活动</nuxt-link>
            <nuxt-link to="/report">· 行业报告</nuxt-link>
            <nuxt-link to="/video">· 操作视频</nuxt-link>
            <nuxt-link to="/policy">· 政策解读</nuxt-link>
            <nuxt-link to="/wiki">· 电子合同百科</nuxt-link>
            <nuxt-link to="/help/FAQ">· 帮助中心</nuxt-link>
      </div>
      <div class="block">
        <h3 class="block-header">知识专栏</h3>
        <nuxt-link to="/dianzihetong">· 电子合同</nuxt-link>
            <nuxt-link to="/dianziqianming">· 电子签名</nuxt-link>
            <nuxt-link to="/dianziqianyue">· 电子签约</nuxt-link>
            <nuxt-link to="/dianziqianzhang">· 电子签章</nuxt-link>
            <nuxt-link to="/dianziyinzhang">· 电子印章</nuxt-link>
            
      </div>
       <div class="block">
        <h3 class="block-header">关于我们</h3>
        <nuxt-link to="/about-us">· 公司简介</nuxt-link>
        <nuxt-link to="/news">· 新闻动态</nuxt-link>
        <nuxt-link to="/join-us">· 加入我们</nuxt-link>
        <nuxt-link to="/about-us#contact">· 联系我们</nuxt-link>
        <nuxt-link to="/cooperation/channel">· 渠道合作</nuxt-link>
            <nuxt-link to="/cooperation/market">· 市场合作</nuxt-link>
      </div>
    </div>
  </div>
</template>

<script>
// const getJson = () =>
//   import('@/static/solution.json').then(m => m.default || m);
const getJson = () => import('@/static/_new.json').then(m => m.default || m);

export default {
  name: 'sitemap',
  layout: 'default',
  head() {
    return {
      title: '网站地图_上上签电子签约云平台',
      meta: [
        {
          hid: 'keywords',
          name: 'keywords',
          content: '上上签网站地图,上上签网站导航',
        },
        {
          hid: 'description',
          name: 'description',
          content:
            '上上签网站地图页面为你提供上上签各板块页面访问入口及模块导航',
        },
      ],
    };
  },
  data() {
    return {
      solutionsId: {
        31: 'other',
        25: 'car',
        30: 'business',
        22: 'logistics',
        26: 'it',
        23: 'retail',
        21: 'fang',
        19: 'hr',
        18: 'finance',
      },
    };
  },
  async asyncData({ store, app, redirect }) {
    try {
      const res = await getJson();
      const solutions = res.map(o => ({
        id: o.id,
        solutionName: o.solutionName,
      }));
      return {
        solutions: solutions || [],
      };
    } catch (e) {
      redirect('/404');
    }
  },
  methods: {
    // toSolution(industryId) {
    //   const solutions = {
    //     // 34: 'education',
    //     // 33: 'supplyChain',
    //     // 30: 'B2B',
    //     // 22: 'logistics',
    //     // 26: 'internet',
    //     // 25: 'finance',
    //     // 23: 'industry',
    //     // 31: 'fintech',
    //     // 21: 'rent',
    //     // 19: 'HR',
    //     // 18: 'insurance',
    //     13: 'other',
    //     25: 'car',
    //     30: 'business',
    //     22: 'logistics',
    //     26: 'it',
    //     23: 'retail',
    //     21: 'fang',
    //     19: 'hr',
    //     18: 'finance',
    //   };
    //   this.$router.push(`/solution/${solutions[industryId]}`);
    // },
  },
};
</script>

<style scoped lang="scss">
.sitemap {
  padding: 8rem 0 5rem;
  .container {
    max-width: 980px;
  }
  .block {
    margin-bottom: 2.5rem;
    .block-header {
      margin-bottom: 2.5rem;
    }
    a {
      display: inline-block;
      padding-bottom: 2rem;
      width: 24%;
      min-width: 100px;
      color: #808080;
      &:hover {
        color: #00a664;
      }
    }
  }
}
</style>
