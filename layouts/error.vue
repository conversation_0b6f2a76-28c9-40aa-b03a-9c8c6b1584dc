<template>
  <div class="not-found">
    <v-header @closeAd = "changeAdvertising" @changeOnline = "changeOnline"></v-header>
    <div class="content">
      <!-- <div class="left">
        <img src="https://static.bestsign.cn/e4330b02b26997e41c9628b46a4ab5b7b5b293bc.png" alt="">
      </div> -->
      <div class="right">
        <h1>404</h1>
        <h1>Not Found</h1>
        <p>{{$t('header.error')}}</p>
        <div class="button">
          <nuxt-link to="/" class="to">{{$t('header.to')}}</nuxt-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Header from '@/components/Header.vue';
export default {
  name: 'NotFound',
  layout: 'blank',
  components: {
    'v-header': Head<PERSON>,
  },
};
</script>

<style scoped lang="scss">
.not-found {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #f5f5f7;
  position: relative;
}
.not-found .content {
  width: 60%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
}
.left {
  float: left;
  width: 50%;
}
.left img {
  width: 73%;
}
.right {
  /* float: right; */
  text-align: center;
  /* color: #fff; */
}
.right h1 {
  font-size: 62px;
  text-shadow: -5px 0 0 #f3c51e;
}
.right p {
  font-size: 24px;
  text-shadow: 0 -3px 0 #f3c51e;
  margin-bottom: 28px;
}
.right .button {
  display: inline-block;
  width: 150px;
  height: 42px;
  line-height: 42px;
  font-size: 20px;
  text-align: center;
  // background-color: $-color-main;
  cursor: pointer;
  border-radius: 10px;
  // color: #fff;
  text-decoration: none;
  // box-shadow: 0 0px 30px 0 rgba(0, 0, 0, 0.1);
  .to {
    color: $-color-deep;
    font-weight: 500;
    text-decoration: underline;
  }
}
</style>
