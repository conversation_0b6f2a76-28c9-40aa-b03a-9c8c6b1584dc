<template>
  <div>
    <!-- <v-header></v-header>
    <nuxt class="main"></nuxt> -->
    <v-header @closeAd = "changeAdvertising" @changeOnline = "changeOnline"></v-header>
    <nuxt id="body" class="main" :class="{ newMain:!adShow||changeAd===false}"></nuxt>
	<v-bestSign></v-bestSign>
    <v-footer></v-footer>
    <float-help></float-help>
  </div>
</template>

<script>
import Header from '@/components/Header.vue';
import Footer from '@/components/Footer.vue';
import FloatHelp from '~/components/FloatHelp.vue';
import BeginSign from '@/components/BeginSign.vue';
export default {
  name: 'LayoutCooperation',
  components: {
    'v-header': Header,
    'v-footer': Footer,
    FloatHelp,
    'v-bestSign': BeginSign,
  },
  data() {
    return {
      changeAd: true,
      adShow: true,
    };
  },
  methods: {
    changeAdvertising(item) {
      this.changeAd = item;
      // console.log(this.changeAd);
    },
    changeOnline(itemOnline) {
      this.adShow = itemOnline;
    },
  },
  mounted() {
    // document.addEventListener('touchmove', () => {
    //   this.$store.commit('setMenuVisible', false);
    // });
    this.changeAdvertising();
    this.changeOnline();
    // this.adShow = this.$refs.isAdShow.getAdType.pcImgUrl;
    //为了跳动ent语言统一，种cookie
    // const lang = this.$store.state.locale;
    // this.$cookies.set('language', lang);
  },
};
</script>

<style>
.main {
  /* padding-top: 76px; */
  padding-top: 136px;
}
.newMain {
  padding-top: 76px;
}
@media screen and (max-width: 767px) {
  .main {
    padding-top: 56px;
  }
}
</style>
