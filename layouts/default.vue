<template>
  <div>
    <v-header @closeAd = "changeAdvertising" @changeOnline = "changeOnline"></v-header>
    <nuxt id="body" class="main" :class="{ newMain:!adShow||changeAd===false}"></nuxt>
    <begin-sign></begin-sign>
    <v-footer :showlink = "homepage"></v-footer>
    <!-- <float-help></float-help> -->
  </div>
</template>

<script>
import Header from '@/components/Header.vue';
import Footer from '@/components/Footer.vue';
// import FloatHelp from '@/components/FloatHelp.vue';
import BeginSign from '@/components/BeginSign.vue';

import Qs from 'qs';

export default {
  components: {
    'v-header': Header,
    'v-footer': Footer,
    // FloatHelp,
    BeginSign,
  },
  data() {
    return {
      homepage: false,
      changeAd: true,
      adShow: true,
    };
  },
  methods: {
    changeAdvertising(item) {
      this.changeAd = item;
      // console.log(this.changeAd);
    },
    changeOnline(itemOnline) {
      this.adShow = itemOnline;
      // console.log('111');
      // console.log(this.adShow);
    },
  },
  mounted() {
    //移动端滑动菜单看不到
    // document.addEventListener('touchmove', () => {
    //   this.$store.commit('setMenuVisible', false);
    // });
    this.changeAdvertising();
    this.changeOnline();
    // this.adShow = this.$refs.isAdShow.onlineAd ? true : false;
    // console.log('111');
    // console.log(this.adShow);
    // console.log(this.$refs.isAdShow.onlineAd);

    //为了跳动ent语言统一，种cookie
    // const lang = this.$store.state.locale;
    // if (window.location.href.indexOf('.com') > -1) {
    //   this.$cookies.set('language', lang, {
    //     expires: 7,
    //     path: '',
    //     domain: '.bestsign.com',
    //   });
    // } else {
    //   this.$cookies.set('language', lang, {
    //     expires: 7,
    //     path: '',
    //     domain: '.bestsign.tech',
    //   });
    // }
    // debugger;
    // window.document.cookie = 'language=ja;domain=' + location.origin;
  },
};
</script>
<style>
.main {
  padding-top: 76px;
  /* padding-top: 136px; */
}
.newMain {
  padding-top: 76px;
}
@media screen and (max-width: 767px) {
  .main {
    padding-top: 56px;
  }
}
</style>
